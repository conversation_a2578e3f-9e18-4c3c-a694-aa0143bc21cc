import { chatTest, watcherCountTest, exitAndRejoinClass, guestRedirectedFromClassWithNoParties, participantPannelTest, activeSpeakerTest, metronomeOnRemoteParticipants } from './classTests';
import { previewButtonsTest } from './classTests/previewButtons';
import { createClassSetup } from './utils';
import { test } from '@playwright/test';

test('Other class functionality', async ({ page, context, browserName }) => {
  test.skip(browserName === 'webkit');
  test.setTimeout(process.env.CI ? 500000 : 300000);
  const { openBrowsers, classroomId } = await createClassSetup(
    page,
    context,
    browserName,
    { main: 11, student: 15, teacher: 16, guest: 17 },
  );

  await activeSpeakerTest(page, openBrowsers);
  await metronomeOnRemoteParticipants(page, openBrowsers);

  await chatTest(page, openBrowsers);
  await participantPannelTest(page);
  await previewButtonsTest(page, openBrowsers);
  await watcherCountTest(page, openBrowsers);
  await exitAndRejoinClass(page, classroomId);
  await guestRedirectedFromClassWithNoParties(page, openBrowsers, classroomId);
});
