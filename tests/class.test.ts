/* eslint-disable */
import { test, expect, chromium, Page, BrowserContext } from '@playwright/test';
import {
  reliableGoto,
  loginWithFullAccess,
  createClassroom,
  createSimpleWrapper,
  createClassTestWrapper,
  type InviteLinks,
  blockAdvancedMediaPermissions,
  setupAdvancedEmulatedDevices,
  connectToClass,
  audioDeviceTestBefore,
  audioDeviceTestAfter,
  inviteToClass,
} from './utils';
import { newClass } from './utils';
import { videoDeviceSettingsTest, activeClassDashboardTest, testAllParticipantViews } from './classTests';

// This is the way we will share browsers and pages between tests
let globalSetup: {
  inviteLinks: InviteLinks;
  openBrowsers: import('@playwright/test').Browser[];
  classroomId: string;
} | null = null;

let classTestWrapperLong: ReturnType<typeof createClassTestWrapper>;

// Helper function to navigate to teacher link, login as Test1, and wait for class
async function navigateAndLoginAsTest1(
  page: Page,
  context: BrowserContext,
  teacherLink: string,
) {
  console.log('Navigating to teacher link and logging in as Test1...');
  await setupAdvancedEmulatedDevices(page, context);
  await page.goto(teacherLink, { waitUntil: 'domcontentloaded' });

  await expect(page.getByTestId('sign-in-button')).toBeVisible();
  await expect(page).toHaveURL('/?login=true');

  await expect(page.getByTestId('sign-in-email-input')).toBeVisible();
  await page.getByTestId('sign-in-email-input').click();
  await page.getByTestId('sign-in-email-input').fill('<EMAIL>');
  await page.getByTestId('sign-in-password-input').click();
  await page.getByTestId('sign-in-password-input').fill('<EMAIL>');
  await page.getByTestId('dialog-sign-in-button').click();

  await page.waitForLoadState('domcontentloaded');

  await connectToClass(page, true, 'teacher', globalSetup?.classroomId || '');

  console.log('Test1 successfully logged in and in class');
}

async function ensureMainPageInClass(page: Page, context: BrowserContext) {
  if (!globalSetup) throw new Error('Setup not completed');
  await navigateAndLoginAsTest1(page, context, globalSetup.inviteLinks.teacher);
}

const installReplaceTrackHook = () => {
  type WindowWithHook = typeof window & {
    __replaceTrackHookInstalled?: boolean;
    __replaceTrackCalls?: number;
  };

  const win = window as WindowWithHook;

  if (win.__replaceTrackHookInstalled) return;
  win.__replaceTrackHookInstalled = true;
  win.__replaceTrackCalls = 0;

  const originalReplaceTrack = RTCRtpSender.prototype.replaceTrack;

  RTCRtpSender.prototype.replaceTrack = function (
    ...args: Parameters<typeof originalReplaceTrack>
  ): ReturnType<typeof originalReplaceTrack> {
    win.__replaceTrackCalls = (win.__replaceTrackCalls || 0) + 1;
    return originalReplaceTrack.apply(this, args);
  };
};

const setupTestModeForAudio = async (page: Page) => {
  await page.addInitScript(() => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).testMode = true;
    console.log('Test mode enabled for audio testing');
  });
};

test.describe.serial('Main test suite', () => {
  test.beforeAll(async () => {
    const timeout = process.env.CI ? 400000 : 200000; // 5 minutes in CI, 2.5 minutes locally
    test.setTimeout(timeout);
  });

  test('Setup classroom and participants', async ({
    page,
    context,
    browserName,
  }) => {
    test.skip(browserName === 'webkit');

    const simpleWrapper = createSimpleWrapper(page, {
      timeout: 75000,
      retries: process.env.CI ? 2 : 0,
    });

    await blockAdvancedMediaPermissions(page, context);
    await reliableGoto(page, '/');
    await simpleWrapper.run(loginWithFullAccess, page);
    const classroomId = await simpleWrapper.run(createClassroom, page);
    await simpleWrapper.run(newClass, page, context, classroomId);

    const inviteLinks = await inviteToClass(page);

    const currentBrowserType = context.browser()?.browserType() || chromium;
    const browserConfigs = [
      { type: currentBrowserType, device: null, name: 'Browser 1' },
      { type: currentBrowserType, device: null, name: 'Browser 2' },
      { type: currentBrowserType, device: null, name: 'Browser 3' },
    ];

    const roles = ['student', 'teacher', 'guest'];
    const openBrowsers: import('@playwright/test').Browser[] = [];

    for (let i = 0; i < 3; i++) {
      const role = roles[i];
      const inviteLink = inviteLinks[role as keyof InviteLinks];
      const browserConfig = browserConfigs[i];
      const newBrowser = await browserConfig.type.launch();
      openBrowsers.push(newBrowser);

      const newContext = await newBrowser.newContext();
      const newPage = await newContext.newPage();

      await setupAdvancedEmulatedDevices(newPage, newContext);

      // Set up test mode for audio testing
      await setupTestModeForAudio(newPage);

      await reliableGoto(newPage, inviteLink);
      await expect(newPage.getByTestId('sign-in-button')).toBeVisible();
      await expect(newPage).toHaveURL('/?login=true');

      const email = `Test${i + 2}@gmail.com`;
      const password = `Test${i + 2}@gmail.com`;

      await expect(newPage.getByTestId('sign-in-email-input')).toBeVisible();
      await newPage.getByTestId('sign-in-email-input').click();
      await newPage.getByTestId('sign-in-email-input').fill(email);
      await newPage.getByTestId('sign-in-password-input').click();
      await newPage.getByTestId('sign-in-password-input').fill(password);
      await newPage.getByTestId('dialog-sign-in-button').click();

      await connectToClass(
        newPage,
        true,
        role as 'student' | 'teacher' | 'guest',
        classroomId,
      );
    }

    // Set up test mode for the main page as well
    await setupTestModeForAudio(page);

    classTestWrapperLong = createClassTestWrapper(
      { timeout: 200000, retries: process.env.CI ? 2 : 0 },
      inviteLinks,
      openBrowsers,
    );

    globalSetup = {
      inviteLinks,
      openBrowsers,
      classroomId,
    };
  });

    test('Test active class dashboard', async ({
      page,
      browserName,
      context,
    }) => {
      test.skip(browserName === 'webkit');
      test.setTimeout(200000);
      if (!globalSetup) throw new Error('Setup not completed');

      await ensureMainPageInClass(page, context);

      // Test with teacher (main page)
      await activeClassDashboardTest(page, 'teacher', globalSetup.classroomId);

      // Test with guest user (browser 2 = guest)
      const guestContext = globalSetup.openBrowsers[2].contexts()[0];
      const guestPage = guestContext.pages()[0];
      await activeClassDashboardTest(guestPage, 'guest', globalSetup.classroomId);
    });

    // test('Test video', async ({ page, browserName, context }) => {
    //   test.skip(browserName === 'webkit');
    //   test.setTimeout(200000);

    //   if (!globalSetup) throw new Error('Setup not completed');

    //   await context.addInitScript(installReplaceTrackHook);
    //   await setupAdvancedEmulatedDevices(page, context);
    //   await ensureMainPageInClass(page, context);
    //   await classTestWrapperLong.run(
    //     page,
    //     videoDeviceSettingsTest,
    //     page,
    //     globalSetup.openBrowsers,
    //   );
    // });

    test('Test participant views', async ({ page, browserName, context }) => {
      test.skip(browserName === 'webkit');
      test.setTimeout(300000);
      if (!globalSetup) throw new Error('Setup not completed');

      await ensureMainPageInClass(page, context);
      const { initialRemoteStreamCounts, initialRemoteStreamIds } =
        await classTestWrapperLong.run(
          page,
          audioDeviceTestBefore,
          page,
          globalSetup.openBrowsers,
        );

      console.log('Test participant views');
      await testAllParticipantViews(
        page,
        globalSetup.openBrowsers,
        globalSetup.classroomId,
      );

      console.log('Test audio device after');
      await classTestWrapperLong.run(
        page,
        audioDeviceTestAfter,
        page,
        globalSetup.openBrowsers,
        initialRemoteStreamCounts,
        initialRemoteStreamIds,
      );
    });

  test.afterAll(async () => {
    // Cleanup browsers
    if (globalSetup?.openBrowsers) {
      for (const browser of globalSetup.openBrowsers) {
        await browser.close();
      }
    }
  });
});
