import { test, expect } from '@playwright/test';
import { loginAsGuest, logout, createClassroom, createClass, createInvites, reliableGoto, setupAdvancedEmulatedDevices, cleanupBrowserState } from '../utils';

test.describe('Ended Class Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test('endedClass', async ({ page, context, browserName }) => {
    test.skip(browserName === 'webkit');
  await setupAdvancedEmulatedDevices(page, context);
  await loginAsGuest(page);
  const classroomId = await createClassroom(page);
  await createClass(page, classroomId);
  await expect(page.getByTestId('action-bar-invite-button')).toBeVisible();
  await page.getByTestId('action-bar-invite-button').click();
  await page.waitForTimeout(2000);
  const inviteLinks = await createInvites(page, true, false, false);
  await expect(page.getByTestId('action-bar-exit-button')).toBeVisible();
  await page.getByTestId('action-bar-exit-button').click();
  await page.waitForTimeout(8000);
  await logout(page);
  await loginAsGuest(page);
  await reliableGoto(page, inviteLinks.teacher);
  // Check for either notification or welcome dialog - test passes if either is found
  try {
    await expect(page.getByTestId('notification-description')).toBeVisible({ timeout: 5000 });
    console.log('Notification found - test passes');
  } catch (error) {
    console.log('Notification not found, trying welcome dialog...', error);
    try {
      await expect(page.getByTestId('welcome-dialog-continue-button')).toBeVisible({ timeout: 5000 });
      console.log('Welcome dialog found - test passes');
    } catch (error2) {
      console.log('Both elements not found - test fails, error: ', error2);
      throw new Error('Neither notification-description nor welcome-dialog-continue-button was found');
    }
  }
  });
});