import { test, expect } from "@playwright/test";
import { loginAsGuest, logout, createClassroom, deleteClassroom, reliableGoto, createInvites, cleanupBrowserState } from "../utils";

test.describe('Deleted Classroom Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test("deleted classroom", async ({ page }) => {
    await loginAsGuest(page);
  const classroomId = await createClassroom(page);
  await page.waitForTimeout(1000);
  await expect(page.getByTestId('invite-trigger-student')).toBeVisible();
  await page.getByTestId('invite-trigger-student').click();
  await page.waitForTimeout(3000);
  const inviteLinks = await createInvites(page, true, false, false);
  await deleteClassroom(page);
  await page.waitForTimeout(1000);
  await reliableGoto(page, `/classrooms/${classroomId}`);

  await expect(page.getByTestId('notification-description')).toBeVisible({ timeout: 10000 });
  await page.getByTestId('notification-close-button').click();
  await logout(page);
  await loginAsGuest(page);
  await reliableGoto(page, inviteLinks.teacher);
  await expect(page.getByTestId('notification-description')).toBeVisible({ timeout: 10000 });
  });
});