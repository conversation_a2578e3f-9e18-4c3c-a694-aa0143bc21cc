// import { test, expect } from '@playwright/test';
// import { loginWithFullAccess} from './utils/loginUtils';
// import { reliableGoto } from './utils/smartRetry';

// test('History player', async ({ page }) => {
//     await loginWithFullAccess(page, 5);
//     await reliableGoto(page, '/classrooms/68546dbecab3f5e0a82cdb0f');
//     await page.waitForLoadState('networkidle');

//     // Target the first history card that contains a "View More" button
//     const targetHistoryEntry = page.locator('.bg-card.rounded-xl').filter({
//         has: page.getByText('View More')
//     }).first();
//     await targetHistoryEntry.scrollIntoViewIfNeeded({timeout: 10000});
//     await expect(targetHistoryEntry).toBeVisible();
//     const viewMoreButton = targetHistoryEntry.getByText('View More');
//     await expect(viewMoreButton).toBeVisible();
//     await targetHistoryEntry.getByText('View More').click();
//     await page.getByTestId('history-dialog-title').scrollIntoViewIfNeeded();
//     await expect(page.getByTestId('history-dialog-title')).toBeVisible();
//     await page.getByTestId('history-dialog-participants-title').scrollIntoViewIfNeeded();
//     await page.waitForLoadState('domcontentloaded');
//     const participantHeadings = page.locator('h1, h2, h3, h4, h5, h6').filter({ hasText: /Teacher|Student/ });
//     await expect(participantHeadings.first()).toBeVisible();
//     await page.getByTestId('history-dialog-close-button').click();
//     await expect(targetHistoryEntry.getByText('View More')).toBeVisible();
// });
