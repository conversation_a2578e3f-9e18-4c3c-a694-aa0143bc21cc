import { test, expect, Browser, B<PERSON>er<PERSON>ontext, Page } from '@playwright/test';
import { reliableGoto, loginAsG<PERSON>, createClassroom, createInvites, InputValidationTester, connectToClass, deleteZendeskTicket, setupAdvancedEmulatedDevices, cleanupBrowserState } from '../utils';
import path from 'path';
import { promises as fs } from 'fs';

interface MaterialReportData {
  report_type: string;
  description: string;
  classroom_id: string;
  content_id: string;
}

interface ZendeskTicketResponse {
  id: number;
  url: string;
  subject: string;
  description: string;
  status: string;
  priority: string;
  type: string;
  tags: string[];
  created_at: string;
  updated_at: string;
}

interface MaterialReportData {
  report_type: string;
  description: string;
  classroom_id: string;
  content_id: string;
}

test.describe('Upload Material Test', () => {
  let studentBrowser: Browser;
  let studentContext: BrowserContext;
  let studentPage: Page;

  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test.beforeAll(async ({ browser }) => {
    studentBrowser = browser;
    studentContext = await studentBrowser.newContext();
    studentPage = await studentContext.newPage();
  });

  test.afterAll(async () => {
    await studentContext?.close();
  });

  test('Upload material workflow with two browsers', async ({ page, context }) => {
    test.setTimeout(process.env.CI ? 150000 : 120000);
    let classroomId: string;
    let inviteLinks: { [key: string]: string };
    await setupAdvancedEmulatedDevices(page, context);
    await setupAdvancedEmulatedDevices(studentPage, studentContext);

    await test.step('Teacher: Login as guest and create classroom', async () => {
      await reliableGoto(page, '/');
      await page.waitForLoadState('domcontentloaded');
      
      await loginAsGuest(page);
      await page.waitForLoadState('domcontentloaded');
      
      await reliableGoto(page, '/classrooms');
      await page.waitForLoadState('domcontentloaded');
      
      classroomId = await createClassroom(page);
      console.log(`Created classroom with ID: ${classroomId}`);
    });

    await test.step('Teacher: Generate invite links', async () => {
      await expect(page.getByTestId('invite-trigger-student')).toBeVisible();
      await page.getByTestId('invite-trigger-student').click();
      await page.waitForTimeout(1000);
      inviteLinks = await createInvites(page, false, true, false);
      console.log('Generated invite links:', inviteLinks);
    });

    await test.step('Student: Join classroom using invite link', async () => {
      await studentPage.goto('/');
      await studentPage.waitForLoadState('domcontentloaded');
      await loginAsGuest(studentPage);
      await reliableGoto(studentPage, inviteLinks.student);
      await studentPage.waitForLoadState('domcontentloaded');
      
      await expect(studentPage.getByTestId('classroom-title')).toBeVisible();
    });

    await test.step('Student: Navigate to upload material section', async () => {
      await page.getByTestId('upload-material-button').scrollIntoViewIfNeeded();
      
      await expect(page.getByTestId('upload-material-button')).toBeVisible();
      
      await page.getByTestId('upload-material-button').click();
      
      await expect(page.getByTestId('upload-material-dialog')).toBeVisible();
      await expect(page.getByTestId('upload-material-dialog-title')).toBeVisible();
      await expect(page.getByTestId('upload-material-form')).toBeVisible();
    });

    await test.step('Student: Test file upload functionality', async () => {
      const testPdfPath = path.join(process.cwd(), 'tests', 'utils', 'test.pdf');
      
      await page.getByTestId('upload-material-file-input').setInputFiles(testPdfPath);
      
      await expect(page.getByTestId('upload-material-file-text')).toContainText('test.pdf');
      
      await expect(page.getByTestId('upload-material-file-error')).not.toBeVisible();
    });

    await test.step('Student: Test title input validation', async () => {
      const titleInputTester = new InputValidationTester(page, {
        inputTestId: 'upload-material-title-input',
        submitButtonTestId: 'upload-material-submit-button',
        errorElementTestId: 'upload-material-title-error',
        triggerValidation: 'enter',
        clearBetweenTests: true,
        disableButtonOnInvalid: true
      });

      await titleInputTester.testConstraints([
        {
          name: 'empty title',
          value: '',
          shouldDisableButton: true,
          shouldShowError: true
        },
        {
          name: 'too short title (4 chars)',
          value: 'Test',
          shouldDisableButton: true,
          shouldShowError: true
        },
        {
          name: 'too long title (51 chars)',
          value: 'a'.repeat(51),
          shouldDisableButton: true,
          shouldShowError: true
        },
        {
          name: 'valid title',
          value: 'Test Upload',
          shouldDisableButton: true,
          shouldShowError: false
        }
      ]);
    });

    let materialId: string;

    await test.step('Extract material ID from teacher page', async () => {
      await page.waitForLoadState('domcontentloaded');
      
      await page.getByTestId('classroom-materials-container').scrollIntoViewIfNeeded();
      await expect(page.getByTestId('classroom-materials-container')).toBeVisible({ timeout: 10000 });
      
      const uploadedMaterialCard = page.locator('[data-material-title="Test Upload"]');
      await expect(uploadedMaterialCard).toBeVisible({ timeout: 10000 });
      
      const cardTestId = await uploadedMaterialCard.getAttribute('data-testid');
      materialId = cardTestId!.replace('material-card-', '');
      
      console.log(`Extracted material ID: ${materialId}`);
      expect(materialId).toBeTruthy();
      expect(materialId.length).toBeGreaterThan(10);
    });

    await test.step('Verify material was uploaded successfully on student page', async () => {
      await studentPage.reload();
      await studentPage.waitForLoadState('domcontentloaded');
      
      await studentPage.keyboard.press('End');
      
      const materialTitle = studentPage.getByTestId(`material-title-${materialId}`);
      await expect(materialTitle).toContainText('Test Upload');
      
      const pdfIcon = studentPage.getByTestId(`material-pdf-icon-${materialId}`);
      await expect(pdfIcon).toBeVisible();
      
      const viewButton = studentPage.getByTestId(`material-view-button-${materialId}`);
      const deleteButton = studentPage.getByTestId(`material-delete-button-${materialId}`);
      await expect(viewButton).toBeVisible();
      await expect(deleteButton).toBeVisible();
      
      console.log('Material "Test Upload" uploaded and verified successfully on student page');
    });

    await test.step('Student: Open and view the uploaded PDF', async () => {
      const viewButton = studentPage.getByTestId(`material-view-button-${materialId}`);
      await viewButton.click();
      
      await studentPage.waitForLoadState('domcontentloaded');
      await studentPage.waitForTimeout(3000);
      
      const pdfViewerTitle = studentPage.getByTestId('pdf-viewer-title');
      await expect(pdfViewerTitle).toBeVisible({ timeout: 10000 });
      await expect(pdfViewerTitle).toContainText('Test Upload');
      
      try {
        const pdfContent = studentPage.locator('text=Test PDF');
        await expect(pdfContent).toBeVisible({ timeout: 5000 });
        console.log('PDF content "Test PDF" found in viewer');
      } catch (error) {
        console.log('PDF content not visible (expected for PDF files) - viewer opened successfully', error);
      }
      
      const backButton = studentPage.getByTestId('pdf-viewer-back-button');
      await expect(backButton).toBeVisible();
      await backButton.click();
      
      await studentPage.waitForLoadState('domcontentloaded');
      await expect(pdfViewerTitle).not.toBeVisible({ timeout: 5000 });
      
      console.log('PDF viewer opened, verified title, and closed successfully');
    });

    await test.step('Teacher: Delete the uploaded material', async () => {
      const uploadedMaterialCard = page.getByTestId(`material-card-${materialId}`);
      await expect(uploadedMaterialCard).toBeVisible();
      
      const deleteButton = page.getByTestId(`material-delete-button-${materialId}`);
      await deleteButton.click();
      
      const deleteDialog = page.getByTestId(`material-delete-dialog-${materialId}`);
      await expect(deleteDialog).toBeVisible({ timeout: 5000 });
      
      const dialogTitle = page.getByTestId(`material-delete-dialog-title-${materialId}`);
      const dialogDescription = page.getByTestId(`material-delete-dialog-description-${materialId}`);
      await expect(dialogTitle).toBeVisible();
      await expect(dialogDescription).toBeVisible();
      
      const confirmDeleteButton = page.getByTestId(`material-delete-confirm-button-${materialId}`);
      await expect(confirmDeleteButton).toBeVisible();
      await confirmDeleteButton.click();
      
      await expect(deleteDialog).not.toBeVisible({ timeout: 10000 });
      
      console.log(`Delete dialog completed for material ID: ${materialId}`);
    });

    await test.step('Verify material was deleted successfully', async () => {
      await studentPage.reload();
      await studentPage.waitForLoadState('domcontentloaded');
      await studentPage.waitForTimeout(2000);
      
      await studentPage.keyboard.press('End');
      
      const deletedMaterialCard = studentPage.getByTestId(`material-card-${materialId}`);
      await expect(deletedMaterialCard).not.toBeVisible({ timeout: 5000 });
      
      const materialByTitle = studentPage.locator('[data-material-title="Test Upload"]');
      await expect(materialByTitle).not.toBeVisible({ timeout: 5000 });
      
      const materialByText = studentPage.locator('text=Test Upload');
      await expect(materialByText).not.toBeVisible({ timeout: 5000 });
      
      console.log(`Material "Test Upload" with ID ${materialId} successfully deleted`);
    });


    let firstMaterialId: string;
    let secondMaterialId: string;

    await test.step('Teacher: Upload new material from teacher page and record its ID', async () => {
      await reliableGoto(page, `/classrooms/${classroomId}`);
      await page.waitForLoadState('domcontentloaded');
      
      await page.getByTestId('upload-material-button').scrollIntoViewIfNeeded();
      await expect(page.getByTestId('upload-material-button')).toBeVisible();
      
      await page.getByTestId('upload-material-button').click();
      await expect(page.getByTestId('upload-material-dialog')).toBeVisible();
      
      const testPdfPath = path.join(process.cwd(), 'tests', 'utils', 'test.pdf');
      await page.getByTestId('upload-material-file-input').setInputFiles(testPdfPath);
      await page.getByTestId('upload-material-title-input').fill('First Material from Teacher');
      await page.getByTestId('upload-material-submit-button').click();
      
      await expect(page.getByTestId('upload-material-dialog')).not.toBeVisible();
      await page.waitForTimeout(2000);
      
      await page.getByTestId('classroom-materials-container').scrollIntoViewIfNeeded();
      const uploadedMaterialCard = page.locator('[data-material-title="First Material from Teacher"]');
      await expect(uploadedMaterialCard).toBeVisible({ timeout: 10000 });
      
      const cardTestId = await uploadedMaterialCard.getAttribute('data-testid');
      firstMaterialId = cardTestId!.replace('material-card-', '');
      
      console.log(`First material ID recorded: ${firstMaterialId}`);
      expect(firstMaterialId).toBeTruthy();
      expect(firstMaterialId.length).toBeGreaterThan(10);
    });

    await test.step('Navigate both teacher and student to class, scroll to top, and connect to class', async () => {
      await reliableGoto(page, `/classrooms/${classroomId}`);
      await page.waitForLoadState('domcontentloaded');
      await connectToClass(page, true, 'teacher', classroomId);
      console.log('Teacher connected to class successfully');
      
      await studentPage.reload();
      await studentPage.waitForLoadState('domcontentloaded');
      await studentPage.waitForTimeout(1000);
      await expect(studentPage.getByTestId('classroom-active-join-button')).toBeVisible();
      await studentPage.getByTestId('classroom-active-join-button').click();
      await studentPage.waitForLoadState('domcontentloaded');
      await expect(studentPage.getByTestId('welcome-dialog-continue-button')).toBeVisible();
      await studentPage.getByTestId('welcome-dialog-continue-button').click();
      console.log('Student connected to class successfully');
    });

    await test.step('Teacher: Expect and click action-bar-materials-button', async () => {
      await expect(page.getByTestId('action-bar-materials-button')).toBeVisible({ timeout: 10000 });
      
      await page.getByTestId('action-bar-materials-button').click();
      console.log('Teacher clicked action-bar-materials-button');
    });

    await test.step('Teacher: Find material with class-material-content-materialID test id', async () => {
      const materialContent = page.getByTestId(`class-material-content-${firstMaterialId}`);
      await expect(materialContent).toBeVisible({ timeout: 10000 });
      
      console.log(`Found material with test id: class-material-content-${firstMaterialId}`);
      
      await expect(materialContent).toBeEnabled();
    });

    await test.step('Teacher: Click materials-upload-button, upload material, and get its ID', async () => {
      await expect(page.getByTestId('materials-upload-button')).toBeVisible();
      await page.getByTestId('materials-upload-button').click();
      
      await expect(page.getByTestId('upload-material-dialog')).toBeVisible();
      
      const testPdfPath = path.join(process.cwd(), 'tests', 'utils', 'test.pdf');
      await page.getByTestId('upload-material-file-input').setInputFiles(testPdfPath);
      await page.getByTestId('upload-material-title-input').fill('Second Material from Class');
      await page.getByTestId('upload-material-submit-button').click();
      
      await expect(page.getByTestId('upload-material-dialog')).not.toBeVisible();
      await page.waitForTimeout(2000);
      
      const secondUploadedMaterialCard = page.locator('h1.text-sm.text-white.font-medium:has-text("Second Material from Class")').locator('xpath=ancestor::*[starts-with(@data-testid, "class-material-content-")]');
      await expect(secondUploadedMaterialCard).toBeVisible({ timeout: 10000 });
      
      const secondCardTestId = await secondUploadedMaterialCard.getAttribute('data-testid');
      secondMaterialId = secondCardTestId!.replace('class-material-content-', '');
      
      console.log(`Second material ID recorded: ${secondMaterialId}`);
      expect(secondMaterialId).toBeTruthy();
      expect(secondMaterialId.length).toBeGreaterThan(10);
      expect(secondMaterialId).not.toBe(firstMaterialId);
    });

    await test.step('Student: Open materials and verify both materials are present', async () => {
      await expect(studentPage.getByTestId('action-bar-materials-button')).toBeVisible({ timeout: 10000 });
      
      await studentPage.getByTestId('action-bar-materials-button').click();
      console.log('Student clicked action-bar-materials-button');
      
      const firstMaterialContent = studentPage.getByTestId(`class-material-content-${firstMaterialId}`);
      await expect(firstMaterialContent).toBeVisible({ timeout: 10000 });
      console.log(`Student can see first material with ID: ${firstMaterialId}`);
      
      const secondMaterialContent = studentPage.getByTestId(`class-material-content-${secondMaterialId}`);
      await expect(secondMaterialContent).toBeVisible({ timeout: 10000 });
      console.log(`Student can see second material with ID: ${secondMaterialId}`);
      
      await expect(firstMaterialContent).toContainText('First Material from Teacher');
      await expect(secondMaterialContent).toContainText('Second Material from Class');
      
      console.log('Both materials are visible and accessible to student');
    });

    await test.step('Teacher: Delete one material and verify it disappears', async () => {
      const firstMaterialCard = page.getByTestId(`class-material-content-${firstMaterialId}`);
      await expect(firstMaterialCard).toBeVisible();
      
      const deleteButton = page.getByTestId(`material-delete-button-${firstMaterialId}`);
      await deleteButton.click();
      
      const deleteDialog = page.getByTestId(`material-delete-dialog-${firstMaterialId}`);
      await expect(deleteDialog).toBeVisible({ timeout: 5000 });
      
      const confirmDeleteButton = page.getByTestId(`material-delete-confirm-button-${firstMaterialId}`);
      await expect(confirmDeleteButton).toBeVisible();
      await confirmDeleteButton.click();
      
      await expect(deleteDialog).not.toBeVisible({ timeout: 10000 });
      console.log(`Deleted first material with ID: ${firstMaterialId}`);
      
      await expect(firstMaterialCard).not.toBeVisible({ timeout: 5000 });
      console.log('First material successfully removed from teacher page');
      
      const secondMaterialCard = page.getByTestId(`class-material-content-${secondMaterialId}`);
      await expect(secondMaterialCard).toBeVisible({ timeout: 5000 });
      console.log('Second material still present on teacher page');
    });

    await test.step('Student: Verify deleted material is no longer visible', async () => {
      await studentPage.reload();
      await studentPage.waitForLoadState('domcontentloaded');
      
      await expect(studentPage.getByTestId('welcome-dialog-continue-button')).toBeVisible({ timeout: 10000 });
      await studentPage.getByTestId('welcome-dialog-continue-button').click();
      await expect(studentPage.getByTestId('action-bar-materials-button')).toBeVisible({ timeout: 10000 });
      await studentPage.getByTestId('action-bar-materials-button').click();
      
      const deletedMaterialContent = studentPage.getByTestId(`class-material-content-${firstMaterialId}`);
      await expect(deletedMaterialContent).not.toBeVisible({ timeout: 5000 });
      console.log('Deleted material no longer visible on student page');
      
      const remainingMaterialContent = studentPage.getByTestId(`class-material-content-${secondMaterialId}`);
      await expect(remainingMaterialContent).toBeVisible({ timeout: 5000 });
      await expect(remainingMaterialContent).toContainText('Second Material from Class');
      console.log('Remaining material still visible on student page');
    });
    
    await test.step('Teacher: Cleanup - Delete classroom', async () => {
      await reliableGoto(page, `/classrooms/${classroomId}`);
      await page.waitForLoadState('domcontentloaded');
      
      await page.getByTestId('settings-dialog-button').click();
      await expect(page.getByTestId('delete-classroom-button')).toBeVisible();
      await page.getByTestId('delete-classroom-button').click();
      await expect(page.getByTestId('delete-classroom-confirm-button')).toBeVisible();
      await page.getByTestId('delete-classroom-confirm-button').click();
      
      await expect(page).toHaveURL('/classrooms');
    });
  });

  test('Upload material validation edge cases', async ({ page }) => {
    let classroomId: string;

    await test.step('Setup classroom for validation testing', async () => {
      await reliableGoto(page, '/');
      await page.waitForLoadState('domcontentloaded');
      await loginAsGuest(page);
      await reliableGoto(page, '/classrooms');
      classroomId = await createClassroom(page);
    });

    await test.step('Test file validation edge cases', async () => {
      await page.getByTestId('upload-material-button').click();
      await expect(page.getByTestId('upload-material-dialog')).toBeVisible();
      
      const invalidFilePath = path.join(process.cwd(), 'package.json');
      await page.getByTestId('upload-material-file-input').setInputFiles(invalidFilePath);
      
      await expect(page.getByTestId('upload-material-file-error')).toBeVisible({ timeout: 5000 });
      
      await expect(page.getByTestId('upload-material-submit-button')).toBeDisabled();
      
      await test.step('Test .txt file rejection', async () => {
        const txtContent = 'This is a test text file that should be rejected';
        const txtFilePath = path.join(process.cwd(), 'tests', 'utils', 'temp-test.txt');
        await fs.writeFile(txtFilePath, txtContent);
        
        try {
          await page.getByTestId('upload-material-file-input').setInputFiles(txtFilePath);
          
          await expect(page.getByTestId('upload-material-file-error')).toBeVisible({ timeout: 5000 });
          
          await expect(page.getByTestId('upload-material-submit-button')).toBeDisabled();
          
          console.log('.txt file correctly rejected');
        } finally {
          await fs.unlink(txtFilePath).catch(() => {});
        }
      });
      
      await test.step('Test file size limit (10MB)', async () => {
        const largeBuffer = Buffer.alloc(11 * 1024 * 1024, 65);
        const largeTempPath = path.join(process.cwd(), 'tests', 'utils', 'temp-large-file.pdf');

        await fs.writeFile(largeTempPath, largeBuffer);
        
        try {
          await page.getByTestId('upload-material-file-input').setInputFiles(largeTempPath);
          
          await expect(page.getByTestId('upload-material-file-error')).toBeVisible({ timeout: 5000 });
          
          await expect(page.getByTestId('upload-material-submit-button')).toBeDisabled();
          
          console.log('File size validation (>10MB) working correctly');
          
        } finally {
          await fs.unlink(largeTempPath).catch(() => {});
        }
      });
      
      const testPdfPath = path.join(process.cwd(), 'tests', 'utils', 'test.pdf');
      await page.getByTestId('upload-material-file-input').setInputFiles(testPdfPath);
      
      await expect(page.getByTestId('upload-material-file-error')).not.toBeVisible();
      
      await page.getByTestId('upload-material-title-input').fill('Valid Test Title');
      
      await expect(page.getByTestId('upload-material-submit-button')).toBeEnabled();
      
      await page.keyboard.press('Escape');
    });

          await test.step('Test report button', async () => {
        let reportRequestData: MaterialReportData | null = null;
        let reportResponseData: ZendeskTicketResponse | null = null;
        
        await page.getByTestId('upload-material-button').click();
        await expect(page.getByTestId('upload-material-dialog')).toBeVisible();
      
      const testPdfPath = path.join(process.cwd(), 'tests', 'utils', 'test.pdf');
      await page.getByTestId('upload-material-file-input').setInputFiles(testPdfPath);
      await page.getByTestId('upload-material-title-input').fill('Test Report Material');
      await page.getByTestId('upload-material-submit-button').click();
      
      await expect(page.getByTestId('upload-material-dialog')).not.toBeVisible();
      
      const uploadedMaterialCard = page.locator('[data-material-title="Test Report Material"]');
      await expect(uploadedMaterialCard).toBeVisible();
      
      const cardTestId = await uploadedMaterialCard.getAttribute('data-testid');
      const reportMaterialId = cardTestId!.replace('material-card-', '');
      
      const reportButton = page.getByTestId(`material-report-button-${reportMaterialId}`);
      await expect(reportButton).toBeVisible();
      
      const [reportPage] = await Promise.all([
        page.context().waitForEvent('page'),
        reportButton.click()
      ]);
      
      await reportPage.waitForLoadState('domcontentloaded');
      
      await expect(reportPage.getByTestId('report-form-description-textarea')).toBeVisible();
      
      reportPage.on('request', async (request) => {
        if (request.url().includes('/api/v1/reports') && request.method() === 'POST') {
          try {
            const postData = request.postData();
            if (postData) {
              const jsonMatch = postData.match(/\{.*\}/s);
              if (jsonMatch) {
                reportRequestData = JSON.parse(jsonMatch[0]);
                console.log('Captured report request data:', reportRequestData);
              }
            }
          } catch (error) {
            console.log('Error parsing request data:', error);
          }
        }
              });

        reportPage.on('response', async (response) => {
          if (response.url().includes('/api/v1/reports') && response.request().method() === 'POST') {
            try {
              reportResponseData = await response.json() as ZendeskTicketResponse;
              console.log('Captured Zendesk response:', reportResponseData);
            } catch (error) {
              console.log('Error parsing response:', error);
            }
          }
        });
  
        await reportPage.getByTestId('report-form-description-textarea').fill('Test Report Description');
      await reportPage.getByTestId('report-form-submit-button').click();
      
      await reportPage.waitForTimeout(3000);
      
      expect(reportRequestData).toBeTruthy();
      expect(reportRequestData).toHaveProperty('report_type');
      expect(reportRequestData).toHaveProperty('description');
      expect(reportRequestData).toHaveProperty('classroom_id');
      expect(reportRequestData).toHaveProperty('content_id');
      
      if (reportRequestData) {
        const data = reportRequestData as MaterialReportData;
        expect(data.report_type).toBe('inappropriate_content');
        expect(data.description).toBe('Test Report Description');
        expect(data.classroom_id).toBe(classroomId);
        expect(data.content_id).toBe(reportMaterialId);
        
        console.log('Report request data validation successful');
        console.log('Report type:', data.report_type);
        console.log('Description:', data.description);
        console.log('Classroom ID:', data.classroom_id);
        console.log('Content ID:', data.content_id);
              }

        if (reportResponseData && 'id' in reportResponseData) {
          const ticket = reportResponseData as ZendeskTicketResponse;
          await deleteZendeskTicket(ticket.id.toString());
          console.log(`Deleted ticket ${ticket.id}`);
        } else {
          console.log('No ticket ID found in response, skipping deletion');
        }

        await reportPage.close();
    });

    await test.step('Cleanup validation test classroom', async () => {
      await reliableGoto(page, `/classrooms/${classroomId}`);
      await page.getByTestId('settings-dialog-button').click();
      await page.getByTestId('delete-classroom-button').click();
      await page.getByTestId('delete-classroom-confirm-button').click();
    });
  });
});