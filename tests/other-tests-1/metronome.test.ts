import { metronomeAudioParameterTest, metronomeTest } from '../classTests';
import { test } from '@playwright/test';
import { loginWithFullAccess, createClassroom, createClass, setupAdvancedEmulatedDevices, cleanupBrowserState } from '../utils';

test.describe('Metronome Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test('Metronome test', async ({ page, context, browserName }) => {
    test.skip(browserName === 'webkit');
    test.setTimeout(process.env.CI ? 150000 : 120000);
    await setupAdvancedEmulatedDevices(page, context);
    await loginWithFullAccess(page, 26);
    const classroomId = await createClassroom(page);
    await createClass(page, classroomId);
    await metronomeTest(page);
    await metronomeAudioParameterTest(page, classroomId);
  });
});
