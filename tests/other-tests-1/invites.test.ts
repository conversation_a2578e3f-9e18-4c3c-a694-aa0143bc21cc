import { invites } from '../classroomsTests';
import { chromium, test, Browser } from '@playwright/test';
import { cleanupBrowserState } from '../utils';

test.describe('Invites Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test('Invites test', async ({ page, browserName }) => {
    test.skip(browserName === 'webkit');
    test.setTimeout(process.env.CI ? 150000 : 120000);

    const openBrowsers: Browser[] = [];

    for (let i = 0; i < 3; i++) {
        const browser = await chromium.launch();
        openBrowsers.push(browser);
    }

    await invites(page, openBrowsers);

    for (const browser of openBrowsers) {
        await browser.close();
    }
  });
});
