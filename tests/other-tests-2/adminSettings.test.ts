import { test, expect } from '@playwright/test';
import { setupAdvancedEmulatedDevices, reliableGoto, loginAsGuest, createClassroom, disableAllAdminSettings, connectToClass, cleanupBrowserState } from '../utils';

test.describe('Admin Settings Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test('Admin Settings - Disable All Settings', async ({ page, context, browserName }) => {
    test.skip(browserName === 'webkit');
    test.setTimeout(process.env.CI ? 150000 : 120000);
    await setupAdvancedEmulatedDevices(page, context);
    await page.context().grantPermissions(['clipboard-read', 'clipboard-write', 'notifications']);
    await reliableGoto(page, '/');
    await loginAsGuest(page);
    const classroomId = await createClassroom(page);
    
    await disableAllAdminSettings(page);

    await page.getByTestId('invite-trigger-guest').scrollIntoViewIfNeeded();
    await expect(page.getByTestId('invite-trigger-guest')).toBeVisible();
    await expect(page.getByTestId('invite-trigger-guest')).toBeDisabled();

    await page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
    });
    await expect(page.getByTestId('upload-material-button')).not.toBeVisible();

    await page.evaluate(() => {
        window.scrollTo(0, 0);
    });
   
    await expect(page.getByTestId('create-class-trigger-header')).toBeVisible();
    await page.getByTestId('create-class-trigger-header').click();
    await expect(page.getByTestId('welcome-dialog-continue-button')).toBeVisible({ timeout: 10000 });
    await page.getByTestId('welcome-dialog-continue-button').click();

    await expect(page.getByTestId('action-bar-materials-button')).toBeVisible();
    await expect(page.getByTestId('action-bar-materials-button')).toBeDisabled();

    await expect(page.getByTestId('action-bar-chat-button')).toBeVisible();
    await expect(page.getByTestId('action-bar-chat-button')).toBeDisabled();

    await expect(page.getByTestId('action-bar-invite-button')).toBeVisible();
    await page.getByTestId('action-bar-invite-button').click();
    await expect(page.getByTestId('invite-form-role-guest')).not.toBeVisible();

    await expect(page.getByTestId('invite-share-copy-button')).toBeVisible();
    await page.getByTestId('invite-share-copy-button').click();
    await page.waitForTimeout(3000);
            const studentLink = await page.evaluate(async () => {
                try {
                    const text = await navigator.clipboard.readText();
                    console.log('Student invite link copied:', text);
                    return text;
                } catch (error) {
                    console.log('Failed to read clipboard for student:', error);
                    return null;
                }
            });

    if (!studentLink) {
        throw new Error('Failed to get student invite link from clipboard');
    }

    console.log('=== STEP 2: Creating student invite and testing with separate browser ===');

    await page.keyboard.press('Escape');
    
    const currentBrowserType = context.browser()?.browserType();
    if (!currentBrowserType) {
        throw new Error('Could not determine browser type');
    }
    
    const guestBrowser = await currentBrowserType.launch();
    const guestContext = await guestBrowser.newContext();
    const guestPage = await guestContext.newPage();
    
    try {
        await setupAdvancedEmulatedDevices(guestPage, guestContext);
        await loginAsGuest(guestPage);
        await reliableGoto(guestPage, studentLink);
        
        await connectToClass(guestPage, true, 'student', classroomId);
        
        await expect(guestPage.getByTestId('action-bar-materials-button')).toBeVisible();
        await expect(guestPage.getByTestId('action-bar-materials-button')).toBeDisabled();
        
        await expect(guestPage.getByTestId('action-bar-chat-button')).toBeVisible();
        await expect(guestPage.getByTestId('action-bar-chat-button')).toBeDisabled();
        
        await expect(guestPage.getByTestId('action-bar-invite-button')).toBeVisible();
        await guestPage.getByTestId('action-bar-invite-button').click();
        await expect(guestPage.getByTestId('invite-form-role-guest')).not.toBeVisible();
        await guestPage.keyboard.press('Escape');
        await guestPage.waitForTimeout(1000);

        console.log('All action bar buttons are disabled for student in class');
        
        await guestPage.getByTestId('action-bar-exit-button').click();
        await guestPage.waitForTimeout(1000);
        
        await expect(guestPage.getByTestId('invite-trigger-guest')).toBeVisible();
        await expect(guestPage.getByTestId('invite-trigger-guest')).toBeDisabled();
        
        console.log('All invite buttons are disabled on classroom page');
        
        await guestPage.evaluate(() => {
            window.scrollTo(0, document.body.scrollHeight);
        });
        
        await expect(guestPage.getByTestId('upload-material-button')).not.toBeVisible();
        console.log('Upload materials section is not visible when scrolled to bottom');
        
    } finally {
        await guestBrowser.close();
    }
    
    console.log('All admin settings tests completed successfully');
  });
});