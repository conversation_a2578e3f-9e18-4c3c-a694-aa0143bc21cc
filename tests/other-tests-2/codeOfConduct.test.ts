import { loginAsGuest, reliableGoto, createClassroom, setupAdvancedEmulatedDevices, createInvites, cleanupBrowserState } from "../utils";
import { test, expect } from "@playwright/test";

test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
});

test('Code of Conduct ', async ({ page, context }) => {
    await setupAdvancedEmulatedDevices(page, context);
    await reliableGoto(page, '/');
    await page.waitForLoadState('domcontentloaded');
    await loginAsGuest(page);
    await page.waitForLoadState('domcontentloaded');
    await createClassroom(page);
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('create-class-trigger-header')).toBeVisible();
    await page.getByTestId('create-class-trigger-header').click();
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('welcome-dialog-code-of-conduct-link')).toBeVisible();
    await page.getByTestId('welcome-dialog-code-of-conduct-link').click();
    
    await page.waitForLoadState('domcontentloaded');
    
    await expect(page.getByTestId('coc-respect-step')).toBeVisible();
    await expect(page.getByTestId('coc-respect-image')).toBeVisible();
    await expect(page.getByTestId('coc-respect-title')).toBeVisible();
    await expect(page.getByTestId('coc-respect-description')).toBeVisible();
    await expect(page.getByTestId('coc-respect-text1')).toBeVisible();
    await expect(page.getByTestId('coc-respect-text2')).toBeVisible();
    await expect(page.getByTestId('coc-respect-text3')).toBeVisible();
    await expect(page.getByTestId('coc-respect-close-button')).toBeVisible();
    await expect(page.getByTestId('coc-respect-next-button')).toBeVisible();
    
    await page.getByTestId('coc-respect-next-button').click();
    await page.waitForLoadState('domcontentloaded');
    
    await expect(page.getByTestId('coc-smart-step')).toBeVisible();
    await expect(page.getByTestId('coc-smart-image')).toBeVisible();
    await expect(page.getByTestId('coc-smart-title')).toBeVisible();
    await expect(page.getByTestId('coc-smart-description')).toBeVisible();
    await expect(page.getByTestId('coc-smart-text1')).toBeVisible();
    await expect(page.getByTestId('coc-smart-text2')).toBeVisible();
    await expect(page.getByTestId('coc-smart-text3')).toBeVisible();
    await expect(page.getByTestId('coc-smart-back-button')).toBeVisible();
    await expect(page.getByTestId('coc-smart-next-button')).toBeVisible();
    
    await page.getByTestId('coc-smart-back-button').click();
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('coc-respect-step')).toBeVisible();
    
    await page.getByTestId('coc-respect-next-button').click();
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('coc-smart-step')).toBeVisible();
    
    await page.getByTestId('coc-smart-next-button').click();
    await page.waitForLoadState('domcontentloaded');
    
    await expect(page.getByTestId('coc-report-step')).toBeVisible();
    await expect(page.getByTestId('coc-report-image')).toBeVisible();
    await expect(page.getByTestId('coc-report-title')).toBeVisible();
    await expect(page.getByTestId('coc-report-description')).toBeVisible();
    await expect(page.getByTestId('coc-report-text1')).toBeVisible();
    await expect(page.getByTestId('coc-report-text2')).toBeVisible();
    await expect(page.getByTestId('coc-report-link')).toBeVisible();
    await expect(page.getByTestId('coc-report-full-link')).toBeVisible();
    await expect(page.getByTestId('coc-report-back-button')).toBeVisible();
    await expect(page.getByTestId('coc-report-close-button')).toBeVisible();
    
    const [newPage] = await Promise.all([
      context.waitForEvent('page'),
      page.getByTestId('coc-report-link').click()
    ]);
    
    await newPage.waitForLoadState('domcontentloaded');
    
    await expect(newPage.getByTestId('report-type-selection-container')).toBeVisible();
    
    await newPage.close();
    
    await page.getByTestId('coc-report-back-button').click();
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('coc-smart-step')).toBeVisible();
    
    await page.getByTestId('coc-smart-next-button').click();
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('coc-report-step')).toBeVisible();
    
    const [newPage2] = await Promise.all([
      context.waitForEvent('page'),
      page.getByTestId('coc-report-full-link').click()
    ]);
    
    await newPage2.waitForLoadState('domcontentloaded');
    
    await expect(newPage2.locator('text=This master Code of Conduct contains VirtuosoHub')).toBeVisible();
    
    await newPage2.close();
    
    await page.getByTestId('coc-report-close-button').click();
    await page.waitForLoadState('domcontentloaded');
    
    await expect(page.getByTestId('welcome-dialog-code-of-conduct-link')).toBeVisible();
    await page.getByTestId('welcome-dialog-continue-button').click();
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('action-bar-invite-button')).toBeVisible();
    await page.getByTestId('action-bar-invite-button').click();
    await page.waitForLoadState('domcontentloaded');
    const inviteLinks = await createInvites(page, false, false, true);

    const newContext = await context.browser()?.newContext();
    const newPage3 = await newContext?.newPage();
    
    if (!newPage3 || !newContext) {
        throw new Error('Failed to create new browser context');
    }

    await setupAdvancedEmulatedDevices(newPage3, newContext);
    
    await reliableGoto(newPage3, '/');
    await newPage3.waitForLoadState('domcontentloaded');
    await loginAsGuest(newPage3);
    await newPage3.waitForLoadState('domcontentloaded');
    
    await reliableGoto(newPage3, inviteLinks.guest);
    await newPage3.waitForLoadState('domcontentloaded');
    
    await expect(newPage3.getByTestId('welcome-dialog-code-of-conduct-link')).toBeVisible();
    await newPage3.getByTestId('welcome-dialog-code-of-conduct-link').click();
    await newPage3.waitForLoadState('domcontentloaded');
    
    await expect(newPage3.getByTestId('coc-respect-step')).toBeVisible();
    await expect(newPage3.getByTestId('coc-respect-image')).toBeVisible();
    await expect(newPage3.getByTestId('coc-respect-title')).toBeVisible();
    await expect(newPage3.getByTestId('coc-respect-description')).toBeVisible();
    await expect(newPage3.getByTestId('coc-respect-text1')).toBeVisible();
    await expect(newPage3.getByTestId('coc-respect-text2')).toBeVisible();
    await expect(newPage3.getByTestId('coc-respect-text3')).toBeVisible();
    await expect(newPage3.getByTestId('coc-respect-close-button')).toBeVisible();
    await expect(newPage3.getByTestId('coc-respect-next-button')).toBeVisible();
    
    await newPage3.getByTestId('coc-respect-next-button').click();
    await newPage3.waitForLoadState('domcontentloaded');
    
    await expect(newPage3.getByTestId('coc-smart-step')).toBeVisible();
    await expect(newPage3.getByTestId('coc-smart-image')).toBeVisible();
    await expect(newPage3.getByTestId('coc-smart-title')).toBeVisible();
    await expect(newPage3.getByTestId('coc-smart-description')).toBeVisible();
    await expect(newPage3.getByTestId('coc-smart-text1')).toBeVisible();
    await expect(newPage3.getByTestId('coc-smart-text2')).toBeVisible();
    await expect(newPage3.getByTestId('coc-smart-text3')).toBeVisible();
    await expect(newPage3.getByTestId('coc-smart-back-button')).toBeVisible();
    await expect(newPage3.getByTestId('coc-smart-next-button')).toBeVisible();
    
    await newPage3.getByTestId('coc-smart-next-button').click();
    await newPage3.waitForLoadState('domcontentloaded');
    
    await expect(newPage3.getByTestId('coc-report-step')).toBeVisible();
    await expect(newPage3.getByTestId('coc-report-image')).toBeVisible();
    await expect(newPage3.getByTestId('coc-report-title')).toBeVisible();
    await expect(newPage3.getByTestId('coc-report-description')).toBeVisible();
    await expect(newPage3.getByTestId('coc-report-text1')).toBeVisible();
    await expect(newPage3.getByTestId('coc-report-text2')).toBeVisible();
    await expect(newPage3.getByTestId('coc-report-link')).toBeVisible();
    await expect(newPage3.getByTestId('coc-report-full-link')).toBeVisible();
    await expect(newPage3.getByTestId('coc-report-back-button')).toBeVisible();
    await expect(newPage3.getByTestId('coc-report-close-button')).toBeVisible();
    
    const [newPageFromSecondContext] = await Promise.all([
        newContext.waitForEvent('page'),
        newPage3.getByTestId('coc-report-link').click()
    ]);
    
    await newPageFromSecondContext.waitForLoadState('domcontentloaded');
    
    await expect(newPageFromSecondContext.getByTestId('report-type-selection-container')).toBeVisible();
    
    await newPageFromSecondContext.close();
    
    const [newPage2FromSecondContext] = await Promise.all([
        newContext.waitForEvent('page'),
        newPage3.getByTestId('coc-report-full-link').click()
    ]);
    
    await newPage2FromSecondContext.waitForLoadState('domcontentloaded');
    
    await expect(newPage2FromSecondContext.locator('text=This master Code of Conduct contains VirtuosoHub')).toBeVisible();
    
    await newPage2FromSecondContext.close();
    
    await newPage3.getByTestId('coc-report-close-button').click();
    await newPage3.waitForLoadState('domcontentloaded');
    
    await expect(newPage3.getByTestId('welcome-dialog-code-of-conduct-link')).toBeVisible();
    
    await newContext.close();

    await expect(page.getByTestId('action-bar-invite-button')).toBeVisible();
});