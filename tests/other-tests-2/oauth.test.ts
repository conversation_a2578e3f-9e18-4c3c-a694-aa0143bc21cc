// import { test, expect } from "@playwright/test";
// import { reliableGoto } from "./utils/smartRetry";

// test("Sign in with Google", async ({ page }) => {
//     test.skip(process.env.BROWSER === "webkit");
//     const email = `<EMAIL>`;
//     const password = `bvfhbhdjGF12GH#@@h`;

//     await reliableGoto(page, "/");
//     await page.waitForLoadState("domcontentloaded");
//     await page.getByTestId("sign-in-button").click();
//     await expect(page.getByTestId("dialog-google-button")).toBeVisible();
//     await page.getByTestId("dialog-google-button").click();

//     await page.waitForLoadState("domcontentloaded");
//     await page.getByLabel(/Email or phone/i).fill(email);
//     await page.getByText("Next").click();

//     await page.waitForTimeout(3000);
//     await expect(page.getByLabel(/Enter your password/i)).toBeVisible();
//     await page.getByLabel(/Enter your password/i).fill(password);
//     await page.getByText("Next").click();

//     await page.waitForTimeout(3000);
//     await page.getByText("Continue").scrollIntoViewIfNeeded();
//     await expect(page.getByText("Continue")).toBeVisible();
//     await page.getByText("Continue").click();


// });