import { test, expect, Page, BrowserContext } from '@playwright/test';
import { reliableGoto, setupAdvancedEmulatedDevices, cleanupBrowserState } from '../utils';

test.describe('Instant Class Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test('Instant Class', async ({ page, context }: { page: Page, context: BrowserContext }) => {
    await setupAdvancedEmulatedDevices(page, context);
    await reliableGoto(page, '/');
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('instant-class-button')).toBeVisible();
    await page.getByTestId('instant-class-button').click();
    await expect(page.getByTestId('guest-display-name-input')).toBeVisible();
    await page.getByTestId('guest-display-name-input').click();
    await page.getByTestId('guest-display-name-input').fill('Test1');
    await page.getByTestId('guest-terms-checkbox').click();
    await page.getByTestId('guest-submit-button').click();
    await expect(page.getByTestId('welcome-dialog-continue-button')).toBeVisible({ timeout: 15000 });
  });
});