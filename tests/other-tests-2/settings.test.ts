import { test, expect } from '@playwright/test';
import { loginWithFullAccess, loginAsGuest, InputValidationTester, DisplayNameConstraints, openSettingsDialog, updateUserSettings, resetUserSettings, reliableGoto, cleanupBrowserState } from '../utils';

test.describe('Settings Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test('Settings with full access', async ({ page, browserName }) => {
    test.skip(browserName === 'webkit');

  const loginIndex = 29;

  await loginWithFullAccess(page, loginIndex);

  //Open profile page
  await page.waitForLoadState('domcontentloaded');
  await expect(page.getByRole('dialog')).toBeHidden({ timeout: 15000 });
  const sidebarTrigger1 = page.getByTestId('sidebar-trigger');
  await expect(sidebarTrigger1).toBeVisible();
  await sidebarTrigger1.click();
  await expect(page.getByTestId('sidebar-profile-button')).toBeVisible({
    timeout: 15000,
  });
  await page.getByTestId('sidebar-profile-button').click();
  await expect(
    page.getByTestId('sidebar-profile-dropdown-profile-link'),
  ).toBeVisible({ timeout: 15000 });
  await page.getByRole('menuitem', { name: /profile/i }).click();

  //Check the basic information
  await page.waitForLoadState('domcontentloaded');
  await expect(
    page.getByTestId('profile-basic-information-display-name'),
  ).toBeVisible({ timeout: 15000 });
  await expect(
    page.getByTestId('profile-basic-information-display-name'),
  ).toHaveText(`Test`);
  await expect(page.getByTestId('profile-basic-information-email')).toHaveText(
    `Test${loginIndex}@gmail.com`,
  );

  //Check the advanced information
  await expect(
    page.getByTestId('profile-advanced-information-display-name'),
  ).toHaveText(`Test`);
  await expect(
    page.getByTestId('profile-advanced-information-registred-at'),
  ).toHaveText(/^(25\.05\.2025|26\.05\.2025|05\.06\.2025)$/);
  await expect(
    page.getByTestId('profile-advanced-information-type'),
  ).toHaveText('Full Access');
  await expect(
    page.getByTestId('profile-advanced-information-language-locale'),
  ).toHaveText('English');
  await expect(
    page.getByTestId('profile-advanced-information-speaking-language-speaking'),
  ).toHaveText('English');
  await expect(
    page.getByTestId('profile-advanced-information-translation-language-translation'),
  ).toHaveText('English');

  //Open settings pop up
  await openSettingsDialog(page);

  //Change the settings
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await expect(page.getByTestId('settings-email-input')).toBeVisible({
    timeout: 15000,
  });

  // Test display name field with validation
  const displayNameValidator = new InputValidationTester(page, {
    inputTestId: 'settings-display-name-input',
    submitButtonTestId: 'settings-email-input',
    clearBetweenTests: false,
  });

  await displayNameValidator.testConstraints([DisplayNameConstraints.valid()]);

  // Set the final test value via util
  await updateUserSettings(page, false, {
    displayName: 'TestRenamed',
    locale: 'ru',
    speaking: 'de',
    translation: 'ru',
  });

  //Check the changes in the profile page
  //Open profile page
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.getByTestId('sidebar-profile-button').click();
  await expect(
    page.getByTestId('sidebar-profile-dropdown-profile-link'),
  ).toBeVisible({ timeout: 15000 });
  await page.getByRole('menuitem', { name: /профиль/i }).click();

  //Check the changes in the profile page
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await expect(page.getByTestId('profile-basic-information-email')).toHaveText(
    `Test${loginIndex}@gmail.com`,
  );
  await expect(
    page.getByTestId('profile-advanced-information-display-name'),
  ).toHaveText('TestRenamed');
  await expect(
    page.getByTestId('profile-advanced-information-language-locale'),
  ).toHaveText('Русский');
  await expect(
    page.getByTestId('profile-advanced-information-speaking-language-speaking'),
  ).toHaveText('Deutsch');
  await expect(
    page.getByTestId('profile-advanced-information-translation-language-translation'),
  ).toHaveText('Русский');

  //Reset changes in the settings via util
  await resetUserSettings(page, { email: `Test${loginIndex}@gmail.com` });
});

test('Settings and Profile with guest access', async ({ page, browserName }) => {
  test.skip(browserName === 'webkit');

  await loginAsGuest(page);

  //Open profile page
  await expect(page.getByRole('dialog')).toBeHidden({ timeout: 15000 });
  const sidebarTrigger4 = page.getByTestId('sidebar-trigger');
  await expect(sidebarTrigger4).toBeVisible();
  await sidebarTrigger4.click();
  await expect(page.getByTestId('sidebar-profile-button')).toBeVisible({
    timeout: 15000,
  });
  await page.getByTestId('sidebar-profile-button').click();
  await expect(
    page.getByTestId('sidebar-profile-dropdown-profile-link'),
  ).toBeVisible({ timeout: 15000 });
  await page.getByRole('menuitem', { name: /profile/i }).click();

  //Check the basic information
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await expect(
    page.getByTestId('profile-basic-information-display-name'),
  ).toBeVisible({ timeout: 15000 });
  await expect(
    page.getByTestId('profile-basic-information-display-name'),
  ).toHaveText('Test1');
  await expect(page.getByTestId('profile-basic-information-email')).toHaveText(
    'Guest Access',
  );

  //Check the advanced information
  await expect(
    page.getByTestId('profile-advanced-information-display-name'),
  ).toHaveText('Test1');
  await expect(
    page.getByTestId('profile-advanced-information-registred-at'),
  ).toBeVisible();
  await expect(
    page.getByTestId('profile-advanced-information-type'),
  ).toHaveText('Limited Access');
  await expect(
    page.getByTestId('profile-advanced-information-language-locale'),
  ).toHaveText('English');
  await expect(
    page.getByTestId('profile-advanced-information-speaking-language-speaking'),
  ).toHaveText('English');
  await expect(
    page.getByTestId('profile-advanced-information-translation-language-translation'),
  ).toHaveText('English');

  //Open settings pop up
  await reliableGoto(page, '/classrooms');
  await openSettingsDialog(page);

  //Change the settings
  await page.waitForLoadState('domcontentloaded');
  await expect(page.getByTestId('settings-display-name-input')).toBeVisible({
    timeout: 15000,
  });

  // Test display name field with validation
  const displayNameValidator = new InputValidationTester(page, {
    inputTestId: 'settings-display-name-input',
    submitButtonTestId: 'settings-save-button',
    clearBetweenTests: false,
  });

  await displayNameValidator.testConstraints([DisplayNameConstraints.valid()]);

  // Set the final test value via util
  await updateUserSettings(page, false, {
    displayName: 'TestRenamed',
    locale: 'ru',
    speaking: 'de',
    translation: 'ru',
  });

  //Check the changes in the profile page
  //Open profile page
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.getByTestId('sidebar-profile-button').click();
  await expect(
    page.getByTestId('sidebar-profile-dropdown-profile-link'),
  ).toBeVisible({ timeout: 15000 });
  await page.getByRole('menuitem', { name: /профиль/i }).click();

  //Check the changes in the profile page
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await expect(
    page.getByTestId('profile-advanced-information-display-name'),
  ).toHaveText('TestRenamed');
  await expect(
    page.getByTestId('profile-advanced-information-language-locale'),
  ).toHaveText('Русский');
  await expect(
    page.getByTestId('profile-advanced-information-speaking-language-speaking'),
  ).toHaveText('Deutsch');
  await expect(
    page.getByTestId('profile-advanced-information-translation-language-translation'),
  ).toHaveText('Русский');
  });
});
