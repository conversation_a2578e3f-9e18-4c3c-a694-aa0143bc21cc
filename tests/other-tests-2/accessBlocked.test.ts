import { test, expect } from '@playwright/test';
import { reliableGoto, cleanupBrowserState } from '../utils';

test.describe('Access Blocked Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test('Classroom access blocked when not logged in', async ({ page }) => {
    await reliableGoto(page, '/');
    await page.waitForLoadState('domcontentloaded');
    await reliableGoto(page, '/classrooms');
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(3000);
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await expect(page).toHaveURL('/');

    await reliableGoto(page, '/classrooms/6856bd9ecab3f5e0a82cde99');
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await expect(page).toHaveURL('/');

    await reliableGoto(page, '/profile');
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await expect(page).toHaveURL('/');
  });
});
