import { test, expect } from '@playwright/test';
import { reliableGoto, cleanupBrowserState } from '../utils';

declare global {
  interface Window {
    gtag?: (command: string, action: string, params: Record<string, string>) => void;
    gtagCalls?: Array<{ command: string; action: string; params: Record<string, string> }>;
    gaButtonTrackerStarted?: boolean;
    cookieConsentTest?: boolean;
  }
}

test.describe('Cookie Consent E2E Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test.beforeEach(async ({ page }) => {
    await page.addInitScript(() => {
      window.cookieConsentTest = true;
      localStorage.removeItem('userConsent');
    });
  });

  test('Cookie consent dialog appears on first visit and accepts cookies', async ({ page }) => {
    await reliableGoto(page, '/');
    await page.waitForLoadState('domcontentloaded');

    await page.waitForTimeout(1000);
    await expect(page.getByTestId('cookie-consent-title')).toBeVisible();
    await expect(page.getByTestId('cookie-consent-accept-button')).toBeVisible();
    await expect(page.getByTestId('cookie-consent-decline-button')).toBeVisible();

    await expect(page.getByTestId('cookie-consent-title')).toHaveText('Cookie Consent');
    await expect(page.getByTestId('cookie-consent-accept-button')).toHaveText('Accept & Continue');
    await expect(page.getByTestId('cookie-consent-decline-button')).toHaveText('Decline Cookies');

    await page.getByTestId('cookie-consent-accept-button').click();

    await expect(page.getByTestId('cookie-consent-dialog')).not.toBeVisible({ timeout: 10000 });

    const userConsent = await page.evaluate(() => {
      return localStorage.getItem('userConsent');
    });
    expect(userConsent).toBe('true');

    const buttonTrackerStarted = await page.evaluate(() => {
      return window.gaButtonTrackerStarted === true;
    });
    expect(buttonTrackerStarted).toBe(true);
  });

  test('Cookie consent dialog appears on first visit and declines cookies', async ({ page }) => {
    await reliableGoto(page, '/');
    await page.waitForLoadState('domcontentloaded');

    await page.waitForTimeout(1000);

    await page.getByTestId('cookie-consent-decline-button').click();

    await expect(page.getByTestId('cookie-consent-dialog')).not.toBeVisible({ timeout: 10000 });

    const userConsent = await page.evaluate(() => {
      return localStorage.getItem('userConsent');
    });
    expect(userConsent).toBe('false');

    const buttonTrackerStarted = await page.evaluate(() => {
      return window.gaButtonTrackerStarted === true;
    });
    expect(buttonTrackerStarted).toBe(false);
  });

  test('Cookie consent dialog appears on first visit and sees privacy policy link', async ({ page, context }) => {
    await reliableGoto(page, '/');
    await page.waitForLoadState('domcontentloaded');
  
    await expect(page.getByTestId('cookie-consent-accept-button')).toBeVisible();
    await expect(page.getByTestId('cookie-consent-decline-button')).toBeVisible();
  
    await expect(page.getByText('Privacy Policy')).toBeVisible();
    
    const [newPage] = await Promise.all([
      context.waitForEvent('page'),
      page.getByText('Privacy Policy').click()
    ]);
    
    await newPage.waitForLoadState('domcontentloaded');
    await expect(newPage).toHaveURL(/.*\/legal\/privacy-policy/);
    await expect(newPage.getByText('Welcome to VirtuosoHub! Your privacy and trust are important to us')).toBeVisible();
    await newPage.close();
  });
});
