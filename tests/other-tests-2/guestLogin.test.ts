import { test, expect } from '@playwright/test';
import { reliableGoto, InputValidationTester, cleanupBrowserState } from '../utils';

test.describe('Guest Login Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test('Guest login with display name validation', async ({ page, browserName }) => {
    test.skip(browserName === 'webkit');
    test.setTimeout(process.env.CI ? 400000 : 200000);
  await reliableGoto(page, '/');
  await page.waitForLoadState('domcontentloaded');
  await expect(page.getByTestId('guest-login-button')).toBeVisible({
    timeout: 15000,
  });
  await page.getByTestId('guest-login-button').click();
  await page.waitForLoadState('domcontentloaded');
  await expect(page.getByTestId('guest-display-name-input')).toBeVisible({
    timeout: 15000,
  });

  await page.getByTestId('guest-terms-checkbox').click();

  // Test display name validation using enhanced InputValidationTester
  const displayNameTester = new InputValidationTester(page, {
    inputTestId: 'guest-display-name-input',
    submitButtonTestId: 'guest-submit-button',
    validationMode: 'submit', // Guest form uses submit-triggered validation
    disableButtonOnInvalid: false, // Button stays enabled with invalid input
    useI18nKeyMatching: true, // Robust i18n key matching (no text matching)
    language: 'en',
  });

  // Test validation constraints (only failures)
  await displayNameTester.testConstraints([
    {
      name: 'empty display name',
      value: '',
      validationTrigger: 'submit',
      shouldShowErrorAfterSubmit: true,
      shouldPreventSubmission: true,
      errorMessageKey:
        'globalFields.displayName.displayName.validation.required',
    },
    {
      name: 'display name too short',
      value: 'Te',
      validationTrigger: 'submit',
      shouldShowErrorAfterSubmit: true,
      shouldPreventSubmission: true,
      errorMessageKey:
        'globalFields.displayName.displayName.validation.minLength',
    },
    {
      name: 'display name too long',
      value: 'VeryLongDisplayNameThatExceedsLimit',
      validationTrigger: 'submit',
      shouldShowErrorAfterSubmit: true,
      shouldPreventSubmission: true,
      errorMessageKey:
        'globalFields.displayName.displayName.validation.maxLength',
    },
  ]);

  // Test that we cant submit without accepting terms
  await page.getByTestId('guest-display-name-input').fill('Test1');
  await page.getByTestId('guest-terms-checkbox').click();
  await page.getByTestId('guest-submit-button').click();
  await expect(page).not.toHaveURL(/\/classrooms/, { timeout: 1000 });

  //Test successful submission
  await page.getByTestId('guest-terms-checkbox').click();
  await page.getByTestId('guest-submit-button').click();

  await expect(page.getByTestId('create-classroom-button')).toBeVisible({
    timeout: 15000,
  });
  });
});
