import { expect, test } from "@playwright/test";
import { reliableGoto, InputValidationTester, deleteZendeskTicket, cleanupBrowserState } from "../utils";
import path from "path";
import fs from "fs";

interface ZendeskResponse {
    id: number;
    url: string;
    subject: string;
    description: string;
    status: string;
    priority: string;
    type: string;
    tags: string[];
    created_at: string;
    updated_at: string;
}

test.describe('Report Form Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test("Report Form - Complete Validation and Submission Flow", async ({ page }) => {
    let reportSubmissionResponse: ZendeskResponse | null = null;
    
    page.on('response', async (response) => {
        if (response.url().includes('/api/v1/reports') && response.request().method() === 'POST') {
            try {
                reportSubmissionResponse = await response.json();
                console.log('Captured Zendesk response:', reportSubmissionResponse);
            } catch (error) {
                console.log('Error parsing response:', error);
            }
        }
    });

    await reliableGoto(page, "/legal/report");
    
    await expect(page.getByTestId("report-type-bug")).toBeVisible();
    await page.getByTestId("report-type-bug").click();
    
    await expect(page.getByTestId("report-form")).toBeVisible();
    await expect(page.getByTestId("report-form-description-textarea")).toBeVisible();
    await expect(page.getByTestId("report-form-upload-wrapper")).toBeVisible();
    const descriptionTester = new InputValidationTester(page, {
        inputTestId: 'report-form-description-textarea',
        submitButtonTestId: 'report-form-submit-button',
        triggerValidation: 'enter',
        clearBetweenTests: true
    });

    await descriptionTester.testConstraints([
        {
            name: 'zero characters (empty input)',
            value: '',
            errorMessageKey: 'report.steps.form.description.required',
            shouldShowError: true
        },
        {
            name: '201 characters (exceeding typical limits)',
            value: 'a'.repeat(201),
            errorMessageKey: 'report.steps.form.description.maxLength',
            shouldShowError: true
        },
    ]);
    
    await test.step("File Upload Tests", async () => {
        await expect(page.getByTestId("report-form-upload-wrapper")).toBeVisible();
        
        await test.step("Test 1: Upload .txt file (should fail - images only)", async () => {
            const txtFilePath = path.join(process.cwd(), 'tests', 'utils', 'test.txt');
            
            const fileInput = page.getByTestId('upload-files-input');
            await fileInput.setInputFiles(txtFilePath);
            
            await expect(page.getByTestId('upload-files-error-message')).toBeVisible();
            await expect(page.getByTestId('upload-files-error-message')).toContainText('image');
        });

        await test.step("Test 2: Upload large JPG file (>10MB, should fail)", async () => {
            const largeJPGBuffer = generateJPGBuffer(12);
            const tempLargeFile = path.join(process.cwd(), 'tests', 'utils', 'temp-large.jpg');
            fs.writeFileSync(tempLargeFile, largeJPGBuffer);
            
            try {
                const fileInput = page.getByTestId('upload-files-input');
                await fileInput.setInputFiles(tempLargeFile);
                
                await expect(page.getByTestId('upload-files-error-message')).toBeVisible();
                await expect(page.getByTestId('upload-files-error-message')).toContainText('10');
            } finally {
                if (fs.existsSync(tempLargeFile)) {
                    fs.unlinkSync(tempLargeFile);
                }
            }
        });

        await test.step("Test 3: Upload 3 files and verify max limit behavior", async () => {
            const tempFiles: string[] = [];
            
            try {
                for (let i = 1; i <= 3; i++) {
                    const normalJPGBuffer = generateJPGBuffer(0.5);
                    const tempFile = path.join(process.cwd(), 'tests', 'utils', `temp-test${i}.jpg`);
                    fs.writeFileSync(tempFile, normalJPGBuffer);
                    tempFiles.push(tempFile);
                }
                
                const fileInput = page.getByTestId('upload-files-input');
                
                for (let i = 0; i < 3; i++) {
                    await fileInput.setInputFiles(tempFiles[i]);
                    await page.waitForTimeout(500);
                    await expect(page.getByTestId(`upload-files-card-${i}`)).toBeVisible();
                }
                
                await expect(page.getByTestId('upload-files-card-0')).toBeVisible();
                await expect(page.getByTestId('upload-files-card-1')).toBeVisible();
                await expect(page.getByTestId('upload-files-card-2')).toBeVisible();
                
                await expect(page.getByTestId('upload-files-input')).not.toBeVisible();
                await expect(page.getByTestId('upload-files-prompt')).not.toBeVisible();
                await expect(page.getByTestId('upload-files-error-message')).not.toBeVisible();
                
            } finally {
                tempFiles.forEach(file => {
                    if (fs.existsSync(file)) {
                        fs.unlinkSync(file);
                    }
                });
            }
        });

        await test.step("Test 4: Upload valid JPG files (should succeed)", async () => {
            const tempFiles: string[] = [];
            
            try {
                await page.reload();
                await expect(page.getByTestId("report-type-bug")).toBeVisible();
                await page.getByTestId("report-type-bug").click();
                await expect(page.getByTestId("report-form-upload-wrapper")).toBeVisible();
                
                for (let i = 1; i <= 2; i++) {
                    const normalJPGBuffer = generateJPGBuffer(0.5);
                    const tempFile = path.join(process.cwd(), 'tests', 'utils', `temp-valid${i}.jpg`);
                    fs.writeFileSync(tempFile, normalJPGBuffer);
                    tempFiles.push(tempFile);
                }
                
                const fileInput = page.getByTestId('upload-files-input');
                
                for (let i = 0; i < 2; i++) {
                    await fileInput.setInputFiles(tempFiles[i]);
                    await page.waitForTimeout(500);
                    await expect(page.getByTestId(`upload-files-card-${i}`)).toBeVisible();
                    await expect(page.getByTestId(`upload-files-name-${i}`)).toContainText('.jpg');
                    await expect(page.getByTestId(`upload-files-size-${i}`)).toContainText('MB');
                }
                
                await expect(page.getByTestId('upload-files-card-0')).toBeVisible();
                await expect(page.getByTestId('upload-files-card-1')).toBeVisible();
                await expect(page.getByTestId('upload-files-error-message')).not.toBeVisible();
                
                await test.step("Test file removal", async () => {
                    await page.getByTestId('upload-files-remove-0').click();
                    await expect(page.getByTestId('upload-files-card-0')).toBeVisible();
                    await expect(page.getByTestId('upload-files-card-1')).not.toBeVisible();
                    
                    await page.getByTestId('upload-files-remove-0').click();
                    await expect(page.getByTestId('upload-files-card-0')).not.toBeVisible();
                    await expect(page.getByTestId('upload-files-list')).not.toBeVisible();
                });
                
            } finally {
                tempFiles.forEach(file => {
                    if (fs.existsSync(file)) {
                        fs.unlinkSync(file);
                    }
                });
            }
        });
    });

    await test.step("Clear form for valid submission test", async () => {
        await page.reload();
        await expect(page.getByTestId("report-type-bug")).toBeVisible();
        await page.getByTestId("report-type-bug").click();
        await expect(page.getByTestId("report-form")).toBeVisible();
        await expect(page.getByTestId("report-form-description-textarea")).toBeVisible();
        await expect(page.getByTestId("report-form-upload-wrapper")).toBeVisible();
    });
    
    await test.step("Fill valid description", async () => {
        const validDescription = "This is a detailed bug report describing an issue I encountered while using the application. The bug occurs when clicking the submit button multiple times rapidly.";
        
        const descriptionField = page.getByTestId("report-form-description-textarea");
        await descriptionField.clear();
        await descriptionField.fill(validDescription);
        
        await expect(descriptionField).toHaveValue(validDescription);
        await expect(page.getByTestId("report-form-description-error")).not.toBeVisible();
    });
    
    await test.step("Submit form and wait for server response", async () => {
        const submitButton = page.getByTestId("report-form-submit-button");
        await expect(submitButton).toBeEnabled();
        
        await submitButton.click();
        
        console.log('Form submitted, waiting for response...');
        
        await page.waitForTimeout(6000);
        
        const responseData = reportSubmissionResponse;
        
        console.log('Final response data:', responseData);
        console.log('Response type:', typeof responseData);
        
        if (!responseData) {
            console.log('No response data available from either listener or direct parsing');
            expect(responseData).toBeTruthy();
            return;
        }
        
        expect(responseData).toHaveProperty('id');
        expect(responseData).toHaveProperty('url');
        expect(responseData).toHaveProperty('subject');
        expect(responseData).toHaveProperty('description');
        expect(responseData).toHaveProperty('status');
        expect(responseData).toHaveProperty('priority');
        expect(responseData).toHaveProperty('type');
        expect(responseData).toHaveProperty('tags');
        expect(responseData).toHaveProperty('created_at');
        expect(responseData).toHaveProperty('updated_at');
        
        expect(typeof responseData.id).toBe('number');
        expect(responseData.url).toContain('zendesk.com');
        expect(Array.isArray(responseData.tags)).toBe(true);
        
        console.log('Ticket status:', responseData.status);
        console.log('Ticket priority:', responseData.priority);
        console.log('Ticket tags:', responseData.tags);
        
        console.log('Zendesk ticket created successfully');
        console.log('Ticket ID:', responseData.id);
        console.log('Ticket URL:', responseData.url);
        
        await page.waitForTimeout(1000);
        
        const successIndicators = [
            page.getByText('submitted successfully', { exact: false }),
            page.getByText('thank you', { exact: false }),
            page.getByText('received', { exact: false }),
            page.getByTestId('success-message'),
            page.getByTestId('report-success'),
            page.getByTestId('report-form-success'),
            page.locator('[data-step="success"]')
        ];
        
        await deleteZendeskTicket(reportSubmissionResponse?.id.toString() ?? '');

        let successFound = false;
        for (const indicator of successIndicators) {
            try {
                await expect(indicator).toBeVisible({ timeout: 3000 });
                successFound = true;
                console.log('Success indicator found:', await indicator.textContent());
                break;
            } catch (error) {
                console.log('Indicator not visible:', error);
            }
        }
        
        if (!successFound) {
            try {
                await expect(page.getByTestId('report-form')).not.toBeVisible({ timeout: 2000 });
                console.log('Form transitioned away - success assumed');
                successFound = true;
            } catch {
                console.log('Form still visible after submission');
            }
        }
        
        const currentUrl = page.url();
        console.log('Current URL after submission:', currentUrl);
        
        if (successFound) {
            console.log('Form submission completed successfully');
        } else {
            console.log('Success indicator not found, but response was successful');
        }
    });
});

function generateJPGBuffer(targetSizeInMB: number): Buffer {
    const targetSizeInBytes = targetSizeInMB * 1024 * 1024;
    
    const jpgHeader = Buffer.from([
        0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00,
        0x01, 0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB,
        0x00, 0x43, 0x00
    ]);

    const quantTable = Buffer.alloc(64, 0x10);
    
    const sofHeader = Buffer.from([
        0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01, 0x00, 0x01, 0x01, 0x01, 0x11, 0x00
    ]);

    const huffmanTables = Buffer.from([0xFF, 0xC4, 0x00, 0x1F, 0x00]);
    const huffmanData = Buffer.alloc(31, 0x00);
    
    const sosHeader = Buffer.from([
        0xFF, 0xDA, 0x00, 0x0C, 0x01, 0x01, 0x00, 0x00, 0x3F, 0x00
    ]);

    const headerSize = jpgHeader.length + quantTable.length + sofHeader.length + 
                      huffmanTables.length + huffmanData.length + sosHeader.length + 2;
    const paddingSize = Math.max(0, targetSizeInBytes - headerSize);
    const padding = Buffer.alloc(paddingSize, 0xFF);
    const eoiMarker = Buffer.from([0xFF, 0xD9]);

    return Buffer.concat([
        jpgHeader, quantTable, sofHeader, huffmanTables, huffmanData, sosHeader, padding, eoiMarker
    ]);
  }
});