import { test, expect } from '@playwright/test';
import { reliableGoto, loginAsGuest, cleanupBrowserState } from '../utils';

test.describe('Legal Links Tests', () => {
  // Add cleanup after each test to prevent state pollution
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test('Privacy Policy page contains correct text', async ({ page }) => {
    await reliableGoto(page, '/legal/privacy-policy');
    await page.waitForLoadState('domcontentloaded');
    
    await expect(page.locator('text=Welcome to VirtuosoHub! Your privacy and trust are important to us')).toBeVisible();
    
  });

  test('Terms and Conditions page contains correct text', async ({ page }) => {
    await reliableGoto(page, '/legal/terms-and-conditions');
    await page.waitForLoadState('domcontentloaded');
    
    await expect(page.locator('text=Welcome to VirtuosoHub. These Terms & Conditions')).toBeVisible();
  });

  test('Code of Conduct page separately contains correct text', async ({ page }) => {
    await reliableGoto(page, '/legal/code-of-conduct');
    await page.waitForLoadState('domcontentloaded');
    
    await expect(page.locator('text=This master Code of Conduct contains VirtuosoHub')).toBeVisible();
  });

  test('Copyright page contains correct text', async ({ page }) => {
    await reliableGoto(page, '/legal/copyright');
    await page.waitForLoadState('domcontentloaded');
    
    await expect(page.locator('text=Copyright © 2025 VirtuosoHub All rights reserved.')).toBeVisible();
  });

  test('License page contains correct text', async ({ page }) => {
    await reliableGoto(page, '/legal/license');
    await page.waitForLoadState('domcontentloaded');
    
    await expect(page.locator('text=Full License Texts')).toBeVisible();
  });

  test('All legal links are accessible on the profile page', async ({ page, context }) => {
    await reliableGoto(page, '/profile');
    await page.waitForLoadState('domcontentloaded');
    await loginAsGuest(page);
    await page.waitForLoadState('domcontentloaded');
    await page.getByTestId('sidebar-trigger').click();
    await expect(page.getByTestId('sidebar-profile-button')).toBeVisible();
    await page.getByTestId('sidebar-profile-button').click();
    await page.getByRole('menuitem', { name: /profile/i }).click();
    await page.waitForLoadState('domcontentloaded');

    await expect(page.getByTestId('profile-actions-privacy-link')).toBeVisible();
    
    const [newPage1] = await Promise.all([
      context.waitForEvent('page'),
      page.getByTestId('profile-actions-privacy-link').click()
    ]);
    
    await newPage1.waitForLoadState('domcontentloaded');
    await expect(newPage1).toHaveURL(/.*\/legal\/privacy-policy/);
    await expect(newPage1.locator('text=Welcome to VirtuosoHub! Your privacy and trust are important to us')).toBeVisible({ timeout: 15000 });
    await newPage1.close();
    await page.waitForTimeout(1000);

    await expect(page.getByTestId('profile-actions-terms-link')).toBeVisible();
    
    const [newPage2] = await Promise.all([
      context.waitForEvent('page'),
      page.getByTestId('profile-actions-terms-link').click()
    ]);
    
    await newPage2.waitForLoadState('domcontentloaded');
    await expect(newPage2).toHaveURL(/.*\/legal\/terms-and-conditions/);
    await expect(newPage2.locator('text=Welcome to VirtuosoHub. These Terms & Conditions')).toBeVisible({ timeout: 15000 });
    await newPage2.close();
    await page.waitForTimeout(1000);
    await expect(page.getByTestId('profile-actions-code-of-conduct-link')).toBeVisible();
  });

  test('Code of Conduct page contains correct text', async ({ page, context }) => {
    await reliableGoto(page, '/profile');
    await page.waitForLoadState('domcontentloaded');
    await loginAsGuest(page);
    await page.waitForLoadState('domcontentloaded');
    await page.getByTestId('sidebar-trigger').click();
    await expect(page.getByTestId('sidebar-profile-button')).toBeVisible();
    await page.getByTestId('sidebar-profile-button').click();
    await page.getByRole('menuitem', { name: /profile/i }).click();
    await page.waitForLoadState('domcontentloaded');

    const [newPage3] = await Promise.all([
      context.waitForEvent('page'),
      page.getByTestId('profile-actions-code-of-conduct-link').click()
    ]);

    await newPage3.waitForLoadState('domcontentloaded');
    await newPage3.waitForLoadState('networkidle', { timeout: 10000 });
    await expect(newPage3).toHaveURL(/.*\/legal\/code-of-conduct/);
    await expect(newPage3.locator('text=This master Code of Conduct contains VirtuosoHub')).toBeVisible({ timeout: 20000 });
    await newPage3.close();
    await page.waitForTimeout(1000);

    await expect(page.getByTestId('profile-actions-copyright-link')).toBeVisible();
    
    const copyrightPagePromise = context.waitForEvent('page');
    await page.getByTestId('profile-actions-copyright-link').click();
    const newPage4 = await copyrightPagePromise;
    
    await newPage4.waitForLoadState('domcontentloaded');
    await expect(newPage4).toHaveURL(/.*\/legal\/copyright/);
    await expect(newPage4.locator('text=Copyright © 2025 VirtuosoHub All rights reserved.').first()).toBeVisible({ timeout: 15000 });

    const licenseLink = newPage4.getByRole('link', { name: 'https://dev.virtuosohub.ai/legal/license' }).first();
    await expect(licenseLink).toBeVisible({ timeout: 15000 });
    const licensePagePromise = context.waitForEvent('page');
    await licenseLink.click();
    const licensePage = await licensePagePromise;

    await licensePage.waitForLoadState('domcontentloaded');
    await expect(licensePage).toHaveURL(/.*\/legal\/license/);
    await expect(licensePage.getByText('Full License Texts', { exact: false })).toBeVisible({ timeout: 15000 });

    await licensePage.close();
    await newPage4.close();
    await page.waitForTimeout(1000);
  });
});

