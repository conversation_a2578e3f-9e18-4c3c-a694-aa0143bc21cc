import { test, expect } from '@playwright/test';
import { reliableGoto, cleanupBrowserState } from '../utils';

test.describe('404 Page Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test('404 page', async ({ page }) => {
    await reliableGoto(page, '/page-that-does-not-exist');
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('not-found-button')).toBeVisible();
    await page.getByTestId('not-found-button').click();
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await expect(page).toHaveURL('/');
  });
});
