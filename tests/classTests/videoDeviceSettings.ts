import { <PERSON><PERSON><PERSON>, <PERSON>, expect } from '@playwright/test';

// Extend MediaStreamTrack with optional testing helpers
type ExtendedMediaTrack = MediaStreamTrack & {
  deviceId?: string;
  customLabel?: string;
};

export async function videoDeviceSettingsTest(page: Page, openBrowsers: Browser[]) {
// Using this function we will get the 
  const getCurrentVideoDeviceId = async (p: Page) => {
    await p.waitForTimeout(5000);
    return await p.evaluate(() => {
      const video = document.querySelector(
        '[data-testid="participant-view-local-video"]'
      ) as HTMLVideoElement | null;
      if (!video || !video.srcObject) return null;
      const track = (video.srcObject as MediaStream).getVideoTracks()[0] as ExtendedMediaTrack;
      return track.deviceId || track.getSettings().deviceId || track.label || null;
    });
  };

  // Reads the MediaStream.id for teacher's video tile
  const readTeacherStreamId = async (p: Page) => {
    // Allow some time for the remote page to render the video element
    await p.waitForTimeout(process.env.CI ? 10000 : 5000);
    return p.evaluate(() => {
      const container = Array.from(
        document.querySelectorAll('[data-testid^="participant-view-"]'),
      ).find((div) =>
        div
          .querySelector('[data-testid$="-name"]')
          ?.textContent?.includes('Test1'),
      ) as HTMLElement | undefined;

      if (!container) return null;
      const video = container.querySelector(
        'video[data-testid$="-video"]',
      ) as HTMLVideoElement | null;
      if (!video || !video.srcObject) return null;
      return (video.srcObject as MediaStream).id;
    });
  };

  // Capture initial labels for every remote browser
  const initialStreamIds: (string | null)[] = [];
  for (const br of openBrowsers) {
    const p = br.contexts()[0].pages()[0];
    // wait a little until remote stream arrives (max 10s)
    let streamId: string | null = null;
    const startCapture = Date.now();
    while (Date.now() - startCapture < (process.env.CI ? 20_000 : 10_000)) {
      streamId = await readTeacherStreamId(p);
      if (streamId) break;
      await p.waitForTimeout(400);
    }
    initialStreamIds.push(streamId);
  }
  console.log('Initial remote MediaStream IDs:', initialStreamIds);

  const initialCameraId = await getCurrentVideoDeviceId(page);
  console.log('Initial local camera ID:', initialCameraId);
  expect(initialCameraId).toBeTruthy();

  // --- open Settings → Device accordion
  console.log('Opening Settings sidebar to access Device accordion');
  await page.getByTestId('settings-sidebar').click();
  // Accordion trigger contains the text "Device" (English locale)
  await page.locator('button:has-text("Device")').click();

  // --- select the second emulated camera (camera-external-002)
  const cameraContainer = page.getByTestId(
    'video-device-container-camera-external-002'
  );
  await cameraContainer.scrollIntoViewIfNeeded();
  const cameraCheckbox = page.getByTestId('video-checkbox-camera-external-002');
  await cameraCheckbox.click();

  console.log('Selected alternative camera device, waiting for local track ID to change...');
  await expect
    .poll(
      async () =>
        page.evaluate(() => {
          const v = document.querySelector('[data-testid="participant-view-local-video"]') as HTMLVideoElement;
          return v?.srcObject ? (v.srcObject as MediaStream).getVideoTracks()[0]?.id : null;
        }),
      { timeout: process.env.CI ? 60_000 : 30_000, intervals: [1_000] }
    )
    .not.toBe(initialCameraId);

  // Confirm and log the new local camera/device id after switch
  const newCameraId = await getCurrentVideoDeviceId(page);
  console.log('Local camera ID after switch:', newCameraId);

  console.log('Verifying that remote browsers observe the camera switch (MediaStream.id)…');
  await page.waitForTimeout(process.env.CI ? 20_000 : 10_000);

  // max 2 min on CI, 45 s locally, poll every second
  const remoteTimeout  = process.env.CI ? 180_000 : 60_000;
  const remoteInterval = process.env.CI ? 1500   : 1000;

  for (let i = 0; i < openBrowsers.length; i++) {
    const remote = openBrowsers[i].contexts()[0].pages()[0];

    // Wait for the remote <video> element to be present
    await remote.waitForSelector(
      '[data-testid^="participant-view-"] video[data-testid$="-video"]',
      { timeout: remoteTimeout },
    );

    await expect
      .poll(
        () =>
          remote.evaluate(() => {
            const video = document.querySelector(
              '[data-testid^="participant-view-"] video[data-testid$="-video"]',
            ) as HTMLVideoElement | null;
            if (!video || !video.srcObject) return null;
            return (video.srcObject as MediaStream).id;
          }),
        { timeout: remoteTimeout, intervals: [remoteInterval] },
      )
      .not.toBe(initialStreamIds[i] ?? null);

    console.log(`Browser ${i}: MediaStream ID changed from "${initialStreamIds[i]}"`);
  }
}
