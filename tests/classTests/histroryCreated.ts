import { expect, Page } from '@playwright/test';

export async function historyCreated(page: Page) {

  await page.waitForTimeout(5000);

  async function attemptHistoryDialog() {
    // Reload and wait for history section to be ready
    await page.waitForTimeout(5000);
    await page.reload();
    await page.waitForTimeout(10000);
    await page.waitForLoadState('networkidle');
    await expect(page.getByTestId('classroom-active-join-button')).not.toBeVisible();

    // Open history dialog - target the first (most recent) entry
    await page.getByRole('heading', { name: /Class/ }).first().scrollIntoViewIfNeeded();
    await expect(page.getByRole('heading', { name: /Class/ }).first()).toBeVisible({ timeout: 10000 });
    await page.getByRole('button', { name: 'View More' }).first().scrollIntoViewIfNeeded();
    await page.getByRole('button', { name: 'View More' }).first().click();
    await expect(page.getByRole('dialog', { name: 'Class History' })).toBeVisible({ timeout: 10000 });
    await page.waitForTimeout(3000);

    // Check if video player exists OR unsupported message is shown
    const videoPlayer = page.getByTestId('history-dialog-video-player');
    const unsupportedMessage = page.getByText('There is no recording available');
    await expect(videoPlayer.or(unsupportedMessage)).toBeVisible({ timeout: 10000 });

    // If video player exists, check if it has a valid src
    const hasVideoPlayer = await videoPlayer.isVisible().catch(() => false);
    if (hasVideoPlayer) {
      const src = await videoPlayer.getAttribute('src');
      expect(src).toBeTruthy();
      expect(src).toContain('virtuosohub2.s3.eu-west-3.amazonaws.com');
      console.log('Video player has valid S3 URL');
    } else {
      console.log('Showing unsupported message (no video recording)');
    }
  }

  // Try three times
  let lastError;
  for (let attempt = 1; attempt <= 3; attempt++) {
    try {
      console.log(`History dialog attempt ${attempt}/3`);
      await attemptHistoryDialog();
      break; // Success, exit the loop
    } catch (error) {
      lastError = error;
      console.log(`Attempt ${attempt} failed:`, error);
      
      if (attempt === 3) {
        // Last attempt failed, throw the error
        throw lastError;
      }
      
      console.log(`Retrying... (${attempt + 1}/3)`);
    }
  }

  // Define expected participants and their roles
  const expectedParticipants = [
      { name: '<EMAIL>', role: 'Lead Teacher' },
      { name: '<EMAIL>', role: 'Student' },
      { name: '<EMAIL>', role: 'Teacher' },
      { name: '<EMAIL>', role: 'Guest' }
  ];

  // Check each participant
  for (const participant of expectedParticipants) {
      const nameElement = page.getByTestId('history-dialog-participant-name').filter({ hasText: participant.name });
      await expect(nameElement).toBeVisible();
      
      const roleElement = nameElement.locator('..').getByTestId('history-dialog-participant-role');
      await expect(roleElement).toContainText(participant.role);
  }

  console.log('All participants verified successfully');

  await page.keyboard.press('Escape');
}

