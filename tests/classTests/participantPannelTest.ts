import { expect, Page } from '@playwright/test';
import { deleteZendeskTicket } from '../utils';

interface ParticipantReportData {
  report_type: string;
  description: string;
  reported_user_id: string;
  reported_username: string;
}

interface ParticipantReportData {
  report_type: string;
  description: string;
  reported_user_id: string;
  reported_username: string;
}

export async function participantPannelTest(page: Page) {

        // Test: Check participants panel shows all users and search functionality works
        await expect(page.getByTestId('action-bar-participants-button')).toBeVisible();
        await page.getByTestId('action-bar-participants-button').click();
        await expect(page.locator('text=Teachers')).toBeVisible();
        await expect(page.locator('text=Students')).toBeVisible();
        await expect(page.locator('text=Guests')).toBeVisible();
        await page.getByTestId('action-bar-students-button').click();
        await expect(page.locator('[data-testid="participant-name"]').filter({ hasText: '<EMAIL>' })).toBeVisible();
        await expect(page.locator('[data-testid="participant-status"]').filter({ hasText: 'live' }).first()).toBeVisible();
        await page.getByTestId('action-bar-teachers-button').click();
        await expect(page.locator('[data-testid="participant-name"]').filter({ hasText: '<EMAIL>' })).toBeVisible();
        await expect(page.locator('[data-testid="participant-status"]').filter({ hasText: 'live' }).first()).toBeVisible();
        await page.getByTestId('action-bar-guests-button').click();
        await expect(page.locator('[data-testid="participant-name"]').filter({ hasText: '<EMAIL>' })).toBeVisible();
        await expect(page.locator('[data-testid="participant-status"]').filter({ hasText: 'live' }).first()).toBeVisible();
        await page.keyboard.press('Escape');

        // Check that the sidebar indicator is visible and has the correct count (4)
        await expect(page.getByTestId('sidebar-indicator-participants-count')).toBeVisible();
        await expect(page.getByTestId('sidebar-indicator-participants-count')).toHaveText('4');

        // Test: Check report button is visible and works
        await expect(page.getByTestId('action-bar-participants-button')).toBeVisible();
        await page.getByTestId('action-bar-participants-button').click();
        await page.getByTestId('action-bar-students-button').click();
        await expect(page.locator('[data-testid="participant-report-button"]').first()).toBeVisible();
        
        // Test: Test participant report button functionality
        let reportResponseData: { id: number } | null = null;
        let reportRequestData: ParticipantReportData | null = null;
        
        const firstParticipantReportButton = page.locator('[data-testid="participant-report-button"]').first();
        await expect(firstParticipantReportButton).toBeVisible();
        
        const [reportPage] = await Promise.all([
            page.context().waitForEvent('page'),
            firstParticipantReportButton.click()
        ]);
        
        await reportPage.waitForLoadState('domcontentloaded');
        
        await expect(reportPage.getByTestId('report-form-description-textarea')).toBeVisible();
        
        reportPage.on('request', async (request) => {
            if (request.url().includes('/api/v1/reports') && request.method() === 'POST') {
                try {
                    const postData = request.postData();
                    if (postData) {
                        const jsonMatch = postData.match(/\{.*\}/s);
                        if (jsonMatch) {
                            reportRequestData = JSON.parse(jsonMatch[0]);
                            console.log('Captured participant report request data:', reportRequestData);
                        }
                    }
                } catch (error) {
                    console.log('Error parsing participant report request data:', error);
                }
            }
        });

        reportPage.on('response', async (response) => {
            if (response.url().includes('/api/v1/reports') && response.request().method() === 'POST') {
                try {
                    reportResponseData = await response.json() as { id: number };
                    console.log('Captured Zendesk response:', reportResponseData);
                } catch (error) {
                    console.log('Error parsing response:', error);
                }
            }
        });

        await reportPage.getByTestId('report-form-description-textarea').fill('Test Participant Report Description');
        await reportPage.getByTestId('report-form-submit-button').click();
        
        await reportPage.waitForTimeout(3000);
        
        expect(reportRequestData).toBeTruthy();
        expect(reportRequestData).toHaveProperty('report_type');
        expect(reportRequestData).toHaveProperty('description');
        expect(reportRequestData).toHaveProperty('reported_user_id');
        expect(reportRequestData).toHaveProperty('reported_username');
        
        if (reportRequestData) {
            const data = reportRequestData as ParticipantReportData;
            expect(data.report_type).toBe('report_user');
            expect(data.description).toBe('Test Participant Report Description');
            expect(data.reported_user_id).toBeTruthy();
            expect(data.reported_username).toBeTruthy();
            
            console.log('Participant report request data validation successful');
            console.log('Report type:', data.report_type);
            console.log('Description:', data.description);
            console.log('Reported User ID:', data.reported_user_id);
            console.log('Reported Username:', data.reported_username);
        }

        if (reportResponseData && 'id' in reportResponseData) {
            const ticket = reportResponseData as { id: number };
            await deleteZendeskTicket(ticket.id.toString());
            console.log(`Deleted ticket ${ticket.id}`);
        } else {
            console.log('No ticket ID found in response, skipping deletion');
        }
        
        await reportPage.close();
}