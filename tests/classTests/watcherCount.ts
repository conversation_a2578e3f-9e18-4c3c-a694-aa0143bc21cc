import { Browser, expect, Page } from '@playwright/test';

export async function watcherCountTest(page: Page, openBrowsers: <PERSON><PERSON><PERSON>[]) {

    //Test watcher count
    await expect(page.getByTestId('watcher-count')).toBeVisible();
    await expect(page.getByTestId('watcher-count')).toHaveText('1');
    const test4Browser = openBrowsers[2]; 
    const test4Context = test4Browser.contexts()[0];
    const test4Page = test4Context.pages()[0];
    await test4Page.getByTestId('action-bar-exit-button').click();
    await expect(page.getByTestId('watcher-count')).toHaveCount(0);
    await page.keyboard.press('Escape');
}