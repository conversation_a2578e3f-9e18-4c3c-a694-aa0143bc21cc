import { <PERSON>, <PERSON>rowser, expect } from '@playwright/test';
import { connectToClass } from '../utils';

interface ParticipantTestResult {
  participantName: string;
  role: string;
  success: boolean;
  errors: string[];
}

interface ConnectedParticipant {
  page: Page;
  name: string;
  role: string;
}

export async function testAllParticipantViews(
  mainPage: Page,
  openBrowsers: Browser[],
  classroomId: string,
): Promise<ParticipantTestResult[]> {
  try {
    // STEP 1: Test local participant view (Test1 - main teacher)
    await mainPage.waitForLoadState('domcontentloaded');

    await expect(
      mainPage.getByTestId('participant-view-local-video'),
    ).toBeVisible({ timeout: 15000 });
    await expect(
      mainPage.getByTestId('participant-view-local-name'),
    ).toHaveText('You (Test1)');
    await expect(
      mainPage.getByTestId('participant-view-local-teacher-badge'),
    ).toBeVisible();
    await expect(
      mainPage.getByTestId('participant-view-local-mic-on-icon'),
    ).toBeVisible();

    // STEP 2: Validate all participants with role-specific rules
    if (!openBrowsers || openBrowsers.length < 3) {
      throw new Error(
        `Expected at least 3 open browsers, but got ${openBrowsers?.length || 0}`,
      );
    }

    const participants: ConnectedParticipant[] = [
      { page: mainPage, name: 'Test1', role: 'teacher' },
    ];

    const browserNames = ['Test2', 'Test3', 'Test4'];
    const browserRoles = ['student', 'teacher', 'guest'];

    for (let i = 0; i < 3; i++) {
      const browser = openBrowsers[i];
      const expectedName = browserNames[i];
      const expectedRole = browserRoles[i];

      if (!browser) {
        throw new Error(`${expectedName} (${expectedRole}) browser is missing`);
      }

      const context = browser.contexts()[0];
      const page = context?.pages()[0];

      if (!page) {
        throw new Error(`${expectedName} (${expectedRole}) page not found`);
      }

      participants.push({
        page: page,
        name: expectedName,
        role: expectedRole,
      });
    }

    const results: ParticipantTestResult[] = [];

    for (const participant of participants) {
      const result: ParticipantTestResult = {
        participantName: participant.name,
        role: participant.role,
        success: true,
        errors: [],
      };

      try {
        await participant.page.waitForLoadState('domcontentloaded');

        const exitButton = participant.page.locator(
          '[data-testid="action-bar-exit-button"]',
        );
        const isInClass = await exitButton.isVisible();

        if (!isInClass) {
          result.errors.push(`${participant.name} is not in the class`);
          result.success = false;
        }
      } catch (error) {
        result.errors.push(`${participant.name} validation failed: ${error}`);
        result.success = false;
        console.error(`${participant.name} test failed:`, error);
      }

      results.push(result);
    }

    // STEP 3: Test Test1 can see Test2 and Test3
    await testRemoteParticipantsVisibility(mainPage);

    // STEP 4: Test Test4 (guest) perspective
    const test4Page = participants.find((p) => p.name === 'Test4')?.page;

    if (!test4Page) {
      throw new Error('Test4 page not found for guest perspective test');
    }

    await testGuestParticipantView(test4Page);

    // STEP 5: Test camera/microphone toggle functionality
    const test2Page = participants.find((p) => p.name === 'Test2')?.page;
    const test3Page = participants.find((p) => p.name === 'Test3')?.page;

    if (!test2Page || !test3Page || !test4Page) {
      throw new Error(
        'Test2, Test3, or Test4 page not found for camera/microphone toggle test',
      );
    }

    await testCameraMicrophoneToggle(mainPage, test2Page, test3Page, test4Page);

    // STEP 6: Test page reload and state persistence
    await testPageReloadStatePersistence(mainPage, classroomId);

    // STEP 7: Test Test4 page reload
    if (!test4Page) {
      throw new Error('Test4 page not found for reload test');
    }

    await testTest4PageReloadStatePersistence(test4Page, classroomId);

    return results;
  } catch (error) {
    console.error('Comprehensive participant view test failed:', error);
    throw error;
  }
}

/**
 * Test specific remote participants (Test2 and Test3) visibility and states
 * @param page - The page to test on
 * @throws Error if remote participants are not properly visible
 */
export async function testRemoteParticipantsVisibility(
  page: Page,
): Promise<void> {
  // Test2 participant
  const test2NameElement = page
    .locator('[data-testid*="participant-view"][data-testid*="name"]')
    .filter({ hasText: 'Test2' });
  await expect(test2NameElement).toBeVisible();

  const test2ParentContainer = test2NameElement
    .locator('..')
    .locator('..')
    .locator('..');
  const test2ContainerTestId =
    await test2ParentContainer.getAttribute('data-testid');

  if (test2ContainerTestId) {
    const test2ParticipantId = test2ContainerTestId
      .replace('participant-view-', '')
      .replace('-container', '');

    await expect(
      page.getByTestId(`participant-view-${test2ParticipantId}-video`),
    ).toBeVisible();
    await expect(
      page.getByTestId(`participant-view-${test2ParticipantId}-media-wrapper`),
    ).toBeVisible();
    await expect(
      page.getByTestId(`participant-view-${test2ParticipantId}-info-overlay`),
    ).toBeVisible();

    const test2MicOn = page.getByTestId(
      `participant-view-${test2ParticipantId}-mic-on-icon`,
    );
    const test2MicOff = page.getByTestId(
      `participant-view-${test2ParticipantId}-mic-off-icon`,
    );

    const test2MicOnVisible = await test2MicOn.isVisible().catch(() => false);
    const test2MicOffVisible = await test2MicOff.isVisible().catch(() => false);

    if (!test2MicOnVisible && !test2MicOffVisible) {
      console.warn(
        'Test2 microphone state unclear - neither on nor off icon visible',
      );
    }

    await expect(test2MicOn.or(test2MicOff)).toBeVisible();

    await expect(
      page.getByTestId(
        `participant-view-${test2ParticipantId}-sound-control-wrapper`,
      ),
    ).toBeVisible();
    await expect(
      page.getByTestId(
        `participant-view-${test2ParticipantId}-sound-control-button`,
      ),
    ).toBeVisible();
    await expect(
      page.getByTestId(
        `participant-view-${test2ParticipantId}-sound-control-icon`,
      ),
    ).toBeVisible();
  }

  // Test3 participant
  const test3NameElement = page
    .locator('[data-testid*="participant-view"][data-testid*="name"]')
    .filter({ hasText: 'Test3' });
  await expect(test3NameElement).toBeVisible();
  const test3ParentContainer = test3NameElement
    .locator('..')
    .locator('..')
    .locator('..');
  const test3ContainerTestId =
    await test3ParentContainer.getAttribute('data-testid');

  if (test3ContainerTestId) {
    const test3ParticipantId = test3ContainerTestId
      .replace('participant-view-', '')
      .replace('-container', '');

    await expect(
      page.getByTestId(`participant-view-${test3ParticipantId}-video`),
    ).toBeVisible();
    await expect(
      page.getByTestId(`participant-view-${test3ParticipantId}-media-wrapper`),
    ).toBeVisible();
    await expect(
      page.getByTestId(`participant-view-${test3ParticipantId}-info-overlay`),
    ).toBeVisible();

    const test3MicOn = page.getByTestId(
      `participant-view-${test3ParticipantId}-mic-on-icon`,
    );
    const test3MicOff = page.getByTestId(
      `participant-view-${test3ParticipantId}-mic-off-icon`,
    );

    const test3MicOnVisible = await test3MicOn.isVisible().catch(() => false);
    const test3MicOffVisible = await test3MicOff.isVisible().catch(() => false);

    if (!test3MicOnVisible && !test3MicOffVisible) {
      console.warn(
        'Test3 microphone state unclear - neither on nor off icon visible',
      );
    }

    await expect(test3MicOn.or(test3MicOff)).toBeVisible();

    await expect(
      page.getByTestId(
        `participant-view-${test3ParticipantId}-sound-control-wrapper`,
      ),
    ).toBeVisible();
    await expect(
      page.getByTestId(
        `participant-view-${test3ParticipantId}-sound-control-button`,
      ),
    ).toBeVisible();
    await expect(
      page.getByTestId(
        `participant-view-${test3ParticipantId}-sound-control-icon`,
      ),
    ).toBeVisible();
  }
}

export async function testGuestParticipantView(test4Page: Page): Promise<void> {
  await test4Page.waitForLoadState('domcontentloaded');

  const continueButton = test4Page
    .locator('button')
    .filter({ hasText: /continue/i });
  const isContinueVisible = await continueButton.isVisible().catch(() => false);

  if (isContinueVisible) {
    await continueButton.click();
    await test4Page.waitForLoadState('networkidle');
  }

  await test4Page.waitForTimeout(15000);

  const exitButton = test4Page.locator(
    '[data-testid="action-bar-exit-button"]',
  );
  const isInClass = await exitButton.isVisible();

  if (!isInClass) {
    throw new Error('Test4 is not in the class - exit button not visible');
  }

  const expectedParticipants = ['Test1', 'Test2', 'Test3'];
  const foundParticipants: string[] = [];

  for (const participantName of expectedParticipants) {
    try {
      let nameElement;
      let isNameVisible = false;

      // Strategy 1: Full participant view
      nameElement = test4Page
        .locator('[data-testid*="participant-view"][data-testid*="name"]')
        .filter({ hasText: participantName });
      isNameVisible = await nameElement.isVisible().catch(() => false);

      // Strategy 2: Simple paragraph view
      if (!isNameVisible) {
        nameElement = test4Page
          .locator('p')
          .filter({ hasText: participantName });
        isNameVisible = await nameElement.isVisible().catch(() => false);
      }

      // Strategy 3: Any text containing participant name
      if (!isNameVisible) {
        nameElement = test4Page.getByText(participantName, { exact: false });
        isNameVisible = await nameElement.isVisible().catch(() => false);
      }

      if (isNameVisible) {
        foundParticipants.push(participantName);

        try {
          const participantContainer = nameElement
            .locator('..')
            .locator('..')
            .locator('..');
          const containerTestId = await participantContainer
            .getAttribute('data-testid')
            .catch(() => null);

          if (containerTestId && containerTestId.includes('participant-view')) {
            const participantId = containerTestId
              .replace('participant-view-', '')
              .replace('-container', '');

            const videoElement = test4Page.getByTestId(
              `participant-view-${participantId}-video`,
            );
            const isVideoVisible = await videoElement
              .isVisible()
              .catch(() => false);

            if (!isVideoVisible) {
              console.warn(`Test4 cannot see ${participantName}'s video`);
            }

            const micOn = test4Page.getByTestId(
              `participant-view-${participantId}-mic-on-icon`,
            );
            const micOff = test4Page.getByTestId(
              `participant-view-${participantId}-mic-off-icon`,
            );

            const micOnVisible = await micOn.isVisible().catch(() => false);
            const micOffVisible = await micOff.isVisible().catch(() => false);

            if (!micOnVisible && !micOffVisible) {
              console.warn(
                `Test4 cannot see ${participantName}'s microphone state`,
              );
            }
          }
        } catch (error) {
          console.error(
            `Error testing ${participantName} visibility from Test4: ${error}`,
          );
        }
      } else {
        console.error(`Test4 cannot see ${participantName}`);
      }
    } catch (error) {
      console.error(
        `Error testing ${participantName} visibility from Test4: ${error}`,
      );
    }
  }

  const missingParticipants = expectedParticipants.filter(
    (name) => !foundParticipants.includes(name),
  );

  if (missingParticipants.length > 0) {
    await test4Page.screenshot({
      path: 'tests/debug-test4-missing-participants.png',
    });
    throw new Error(
      `Test4 (guest) cannot see participants: ${missingParticipants.join(', ')}`,
    );
  }

  const test4SelfName = test4Page
    .locator('[data-testid*="participant-view"][data-testid*="name"]')
    .filter({ hasText: /Test4|You \(Test4\)/ });

  const test4SelfVisible = await test4SelfName.isVisible().catch(() => false);

  if (test4SelfVisible) {
    console.warn(
      'Test4 appears to see themselves (this might be expected in some UI designs)',
    );
  }
}

export async function testCameraMicrophoneToggle(
  test1Page: Page,
  test2Page: Page,
  test3Page: Page,
  test4Page: Page,
): Promise<void> {
  // PART 1: Turn off Test2's camera
  await test2Page.waitForLoadState('domcontentloaded');

  const test2CameraButton = test2Page.getByTestId('action-bar-camera-button');
  await expect(test2CameraButton).toBeVisible();
  await test2CameraButton.click();
  await test2Page.waitForFunction(
    () => {
      const avatarSelectors = [
        '[data-testid*="local"][data-testid*="avatar"]',
        '[data-testid*="local"][data-testid*="placeholder"]',
        '[data-testid*="local"][data-testid*="no-video"]',
      ];

      let avatarVisible = false;
      for (const selector of avatarSelectors) {
        const element = document.querySelector(selector);
        if (element && (element as HTMLElement).offsetParent !== null) {
          avatarVisible = true;
          break;
        }
      }

      const video = document.querySelector(
        '[data-testid="participant-view-local-video"]',
      ) as HTMLVideoElement;
      let videoStreamInactive = true;
      if (video && video.srcObject && video.srcObject instanceof MediaStream) {
        videoStreamInactive =
          (video.srcObject as MediaStream).getVideoTracks().length === 0;
      }

      return avatarVisible || videoStreamInactive;
    },
    { timeout: 15000 },
  );

  // PART 2: Turn off Test3's microphone
  await test3Page.waitForLoadState('domcontentloaded');

  const test3MicButton = test3Page.getByTestId('action-bar-mic-button');
  await expect(test3MicButton).toBeVisible();
  await test3MicButton.click();
  await test3Page.waitForFunction(
    () => {
      const micOffIcon = document.querySelector(
        '[data-testid="participant-view-local-mic-off-icon"]',
      );
      const micOnIcon = document.querySelector(
        '[data-testid="participant-view-local-mic-on-icon"]',
      );

      const micOffVisible =
        micOffIcon && (micOffIcon as HTMLElement).offsetParent !== null;
      const micOnVisible =
        micOnIcon && (micOnIcon as HTMLElement).offsetParent !== null;

      const actionBarMicButton = document.querySelector(
        '[data-testid="action-bar-mic-button"]',
      );
      let buttonIndicatesMuted = false;

      if (actionBarMicButton) {
        const ariaPressed = actionBarMicButton.getAttribute('aria-pressed');
        const buttonClass = actionBarMicButton.getAttribute('class') || '';
        buttonIndicatesMuted =
          ariaPressed === 'false' ||
          buttonClass.includes('muted') ||
          buttonClass.includes('off');
      }

      return (micOffVisible && !micOnVisible) || buttonIndicatesMuted;
    },
    { timeout: 15000 },
  );

  const test3LocalMicOn = test3Page.getByTestId(
    'participant-view-local-mic-on-icon',
  );
  const test3LocalMicOff = test3Page.getByTestId(
    'participant-view-local-mic-off-icon',
  );

  const localMicOnVisible = await test3LocalMicOn
    .isVisible()
    .catch(() => false);
  const localMicOffVisible = await test3LocalMicOff
    .isVisible()
    .catch(() => false);

  if (localMicOnVisible && !localMicOffVisible) {
    console.warn(
      'Test3 local microphone still shows as ON (this should be rare after generous timeout)',
    );
  } else if (!localMicOffVisible && !localMicOnVisible) {
    console.warn(
      'Test3 local microphone state unclear (this should be rare after generous timeout)',
    );
    await test3Page.screenshot({
      path: 'tests/debug-test3-local-mic-unclear.png',
    });
  }

  // PART 3: Validate changes from Test1's perspective
  await test1Page.waitForLoadState('domcontentloaded');

  await test1Page.waitForTimeout(8000);

  const test2NameElement = test1Page
    .locator('[data-testid*="participant-view"][data-testid*="name"]')
    .filter({ hasText: 'Test2' });

  const isTest2NameVisible = await test2NameElement.isVisible();
  if (!isTest2NameVisible) {
    throw new Error("Cannot find Test2 participant from Test1's view");
  }

  const test2Container = test2NameElement
    .locator('..')
    .locator('..')
    .locator('..');
  const test2ContainerTestId = await test2Container.getAttribute('data-testid');

  if (test2ContainerTestId) {
    const test2ParticipantId = test2ContainerTestId
      .replace('participant-view-', '')
      .replace('-container', '');

    const avatarSelectors = [
      `[data-testid*="${test2ParticipantId}"][data-testid*="avatar"]`,
      `[data-testid*="${test2ParticipantId}"][data-testid*="placeholder"]`,
      `[data-testid*="${test2ParticipantId}"][data-testid*="no-video"]`,
    ];

    let avatarFound = false;
    for (const selector of avatarSelectors) {
      const avatarElement = test1Page.locator(selector);
      if (await avatarElement.isVisible().catch(() => false)) {
        avatarFound = true;
        break;
      }
    }

    const videoStreamActive = await test1Page.evaluate((videoSelector) => {
      const video = document.querySelector(videoSelector) as HTMLVideoElement;
      if (video && video.srcObject && video.srcObject instanceof MediaStream) {
        return (video.srcObject as MediaStream).getVideoTracks().length > 0;
      }
      return false;
    }, `[data-testid="${test2ParticipantId}-video"]`);

    if (videoStreamActive && !avatarFound) {
      console.warn(
        'Test2 video still appears to be active - camera toggle might not have worked',
      );
    }
  }

  const test3NameElement = test1Page
    .locator('[data-testid*="participant-view"][data-testid*="name"]')
    .filter({ hasText: 'Test3' });

  const isTest3NameVisible = await test3NameElement.isVisible();
  if (!isTest3NameVisible) {
    throw new Error("Cannot find Test3 participant from Test1's view");
  }

  const test3Container = test3NameElement
    .locator('..')
    .locator('..')
    .locator('..');
  const test3ContainerTestId = await test3Container.getAttribute('data-testid');

  if (test3ContainerTestId) {
    const test3ParticipantId = test3ContainerTestId
      .replace('participant-view-', '')
      .replace('-container', '');

    const test3MicOn = test1Page.getByTestId(
      `participant-view-${test3ParticipantId}-mic-on-icon`,
    );
    const test3MicOff = test1Page.getByTestId(
      `participant-view-${test3ParticipantId}-mic-off-icon`,
    );

    const micOnVisible = await test3MicOn.isVisible().catch(() => false);
    const micOffVisible = await test3MicOff.isVisible().catch(() => false);

    if (micOnVisible && !micOffVisible) {
      console.warn(
        'Test3 microphone still shows as ON - microphone toggle might not have worked',
      );
    } else if (!micOffVisible && !micOnVisible) {
      console.warn("Test3 microphone state unclear from Test1's view");
      await test1Page.screenshot({
        path: 'tests/debug-test3-mic-unclear-from-test1.png',
      });
    }
  }

  // PART 4: Validate changes from Test4 (guest) perspective
  await test4Page.waitForLoadState('domcontentloaded');

  await test4Page.waitForTimeout(8000);

  const test4Test2NameElement = test4Page
    .locator('[data-testid*="participant-view"][data-testid*="name"]')
    .filter({ hasText: 'Test2' });

  const isTest4Test2NameVisible = await test4Test2NameElement.isVisible();
  if (!isTest4Test2NameVisible) {
    console.warn(
      'Test4 cannot find Test2 participant - skipping Test2 camera validation',
    );
  } else {
    const test4Test2Container = test4Test2NameElement
      .locator('..')
      .locator('..')
      .locator('..');
    const test4Test2ContainerTestId =
      await test4Test2Container.getAttribute('data-testid');

    if (test4Test2ContainerTestId) {
      const test4Test2ParticipantId = test4Test2ContainerTestId
        .replace('participant-view-', '')
        .replace('-container', '');

      const test4AvatarSelectors = [
        `[data-testid*="${test4Test2ParticipantId}"][data-testid*="avatar"]`,
        `[data-testid*="${test4Test2ParticipantId}"][data-testid*="placeholder"]`,
        `[data-testid*="${test4Test2ParticipantId}"][data-testid*="no-video"]`,
      ];

      let test4AvatarFound = false;
      for (const selector of test4AvatarSelectors) {
        const avatarElement = test4Page.locator(selector);
        if (await avatarElement.isVisible().catch(() => false)) {
          test4AvatarFound = true;
          break;
        }
      }

      const test4VideoStreamActive = await test4Page.evaluate(
        (videoSelector) => {
          const video = document.querySelector(
            videoSelector,
          ) as HTMLVideoElement;
          if (
            video &&
            video.srcObject &&
            video.srcObject instanceof MediaStream
          ) {
            return (video.srcObject as MediaStream).getVideoTracks().length > 0;
          }
          return false;
        },
        `[data-testid="${test4Test2ParticipantId}-video"]`,
      );

      if (test4VideoStreamActive && !test4AvatarFound) {
        console.warn('Test4 still sees Test2 video as active');
      }
    }
  }

  const test4Test3NameElement = test4Page
    .locator('[data-testid*="participant-view"][data-testid*="name"]')
    .filter({ hasText: 'Test3' });

  const isTest4Test3NameVisible = await test4Test3NameElement.isVisible();
  if (!isTest4Test3NameVisible) {
    console.warn(
      'Test4 cannot find Test3 participant - skipping Test3 microphone validation',
    );
  } else {
    // Get Test3's participant ID from Test4's perspective
    const test4Test3Container = test4Test3NameElement
      .locator('..')
      .locator('..')
      .locator('..');
    const test4Test3ContainerTestId =
      await test4Test3Container.getAttribute('data-testid');

    if (test4Test3ContainerTestId) {
      const test4Test3ParticipantId = test4Test3ContainerTestId
        .replace('participant-view-', '')
        .replace('-container', '');

      const test4Test3MicOn = test4Page.getByTestId(
        `participant-view-${test4Test3ParticipantId}-mic-on-icon`,
      );
      const test4Test3MicOff = test4Page.getByTestId(
        `participant-view-${test4Test3ParticipantId}-mic-off-icon`,
      );

      const test4MicOnVisible = await test4Test3MicOn
        .isVisible()
        .catch(() => false);
      const test4MicOffVisible = await test4Test3MicOff
        .isVisible()
        .catch(() => false);

      if (test4MicOnVisible && !test4MicOffVisible) {
        console.warn('Test4 still sees Test3 microphone as ON');
      } else if (!test4MicOffVisible && !test4MicOnVisible) {
        console.warn('Test4 sees unclear Test3 microphone state');
        await test4Page.screenshot({
          path: 'tests/debug-test4-test3-mic-unclear.png',
        });
      }
    }
  }
}

export async function testPageReloadStatePersistence(
  test1Page: Page,
  classroomId: string,
): Promise<void> {
  // PART 1: Reload Test1's page
  await test1Page.reload();
  await test1Page.waitForLoadState('domcontentloaded');

  // PART 2: Click Continue button

  await connectToClass(test1Page, true, 'teacher', classroomId);

  // PART 3: Verify Test1's local view is correct

  await expect(
    test1Page.getByTestId('participant-view-local-video'),
  ).toBeVisible();
  await expect(test1Page.getByTestId('participant-view-local-name')).toHaveText(
    'You (Test1)',
  );
  await expect(
    test1Page.getByTestId('participant-view-local-teacher-badge'),
  ).toBeVisible();

  const test1LocalMicOn = test1Page.getByTestId(
    'participant-view-local-mic-on-icon',
  );
  const test1LocalMicOff = test1Page.getByTestId(
    'participant-view-local-mic-off-icon',
  );

  const test1MicOnVisible = await test1LocalMicOn
    .isVisible()
    .catch(() => false);
  const test1MicOffVisible = await test1LocalMicOff
    .isVisible()
    .catch(() => false);

  if (!test1MicOnVisible || test1MicOffVisible) {
    console.warn('Test1 local microphone state unclear after reload');
    await test1Page.screenshot({
      path: 'tests/debug-test1-mic-unclear-after-reload.png',
    });
  }

  // PART 4: Verify Test2's camera state persists

  await test1Page.waitForTimeout(12000);

  const test2NameElement = test1Page
    .locator('[data-testid*="participant-view"][data-testid*="name"]')
    .filter({ hasText: 'Test2' });

  await expect(test2NameElement).toBeVisible({ timeout: 15000 });

  const test2Container = test2NameElement
    .locator('..')
    .locator('..')
    .locator('..');
  const test2ContainerTestId = await test2Container.getAttribute('data-testid');

  if (test2ContainerTestId) {
    const test2ParticipantId = test2ContainerTestId
      .replace('participant-view-', '')
      .replace('-container', '');

    const avatarSelectors = [
      `[data-testid*="${test2ParticipantId}"][data-testid*="avatar"]`,
      `[data-testid*="${test2ParticipantId}"][data-testid*="placeholder"]`,
      `[data-testid*="${test2ParticipantId}"][data-testid*="no-video"]`,
    ];

    let avatarFoundAfterReload = false;
    for (const selector of avatarSelectors) {
      const avatarElement = test1Page.locator(selector);
      if (await avatarElement.isVisible().catch(() => false)) {
        avatarFoundAfterReload = true;
        break;
      }
    }

    const videoStreamActiveAfterReload = await test1Page.evaluate(
      (videoSelector) => {
        const video = document.querySelector(videoSelector) as HTMLVideoElement;
        if (
          video &&
          video.srcObject &&
          video.srcObject instanceof MediaStream
        ) {
          return (video.srcObject as MediaStream).getVideoTracks().length > 0;
        }
        return false;
      },
      `[data-testid="${test2ParticipantId}-video"]`,
    );

    if (videoStreamActiveAfterReload && !avatarFoundAfterReload) {
      console.warn(
        'Test2 camera state may not have persisted - appears active after reload',
      );
    }
  }

  // PART 5: Verify Test3's microphone state persists

  const test3NameElement = test1Page
    .locator('[data-testid*="participant-view"][data-testid*="name"]')
    .filter({ hasText: 'Test3' });

  await expect(test3NameElement).toBeVisible({ timeout: 15000 });

  const test3Container = test3NameElement
    .locator('..')
    .locator('..')
    .locator('..');
  const test3ContainerTestId = await test3Container.getAttribute('data-testid');

  if (test3ContainerTestId) {
    const test3ParticipantId = test3ContainerTestId
      .replace('participant-view-', '')
      .replace('-container', '');

    const test3MicOn = test1Page.getByTestId(
      `participant-view-${test3ParticipantId}-mic-on-icon`,
    );
    const test3MicOff = test1Page.getByTestId(
      `participant-view-${test3ParticipantId}-mic-off-icon`,
    );

    const micOnVisibleAfterReload = await test3MicOn
      .isVisible()
      .catch(() => false);
    const micOffVisibleAfterReload = await test3MicOff
      .isVisible()
      .catch(() => false);

    if (micOnVisibleAfterReload && !micOffVisibleAfterReload) {
      console.warn(
        'Test3 microphone state may not have persisted - appears on after reload',
      );
    } else if (!micOffVisibleAfterReload && !micOnVisibleAfterReload) {
      console.warn('Test3 microphone state unclear after reload');
    }
  }
}

export async function testTest4PageReloadStatePersistence(
  test4Page: Page,
  classroomId: string,
): Promise<void> {
  // PART 1: Reload Test4's page
  await test4Page.reload();
  await test4Page.waitForLoadState('domcontentloaded');

  await test4Page.waitForLoadState('networkidle');
  await test4Page.waitForTimeout(15000);

  // PART 2: Verify Test4 is in the class

  await connectToClass(test4Page, true, 'guest', classroomId);

  // PART 3: Verify Test4 can see Test1

  const test1NameElement = test4Page
    .locator('[data-testid*="participant-view"][data-testid*="name"]')
    .filter({ hasText: 'Test1' });

  await expect(test1NameElement).toBeVisible({ timeout: 15000 });

  const test1Container = test1NameElement
    .locator('..')
    .locator('..')
    .locator('..');
  const test1ContainerTestId = await test1Container.getAttribute('data-testid');

  if (test1ContainerTestId) {
    const test1ParticipantId = test1ContainerTestId
      .replace('participant-view-', '')
      .replace('-container', '');

    const test1Video = test4Page.getByTestId(
      `participant-view-${test1ParticipantId}-video`,
    );
    await expect(test1Video).toBeVisible();

    const test1MicOn = test4Page.getByTestId(
      `participant-view-${test1ParticipantId}-mic-on-icon`,
    );
    const test1MicOff = test4Page.getByTestId(
      `participant-view-${test1ParticipantId}-mic-off-icon`,
    );

    const test1MicOnVisible = await test1MicOn.isVisible().catch(() => false);
    const test1MicOffVisible = await test1MicOff.isVisible().catch(() => false);

    if (!test1MicOnVisible || test1MicOffVisible) {
      console.warn(
        "Test1 microphone state unclear from Test4's view after reload",
      );
    }
  }

  // PART 4: Verify Test4 can see Test2

  const test2NameElement = test4Page
    .locator('[data-testid*="participant-view"][data-testid*="name"]')
    .filter({ hasText: 'Test2' });

  await expect(test2NameElement).toBeVisible({ timeout: 15000 });

  const test2Container = test2NameElement
    .locator('..')
    .locator('..')
    .locator('..');
  const test2ContainerTestId = await test2Container.getAttribute('data-testid');

  if (test2ContainerTestId) {
    const test2ParticipantId = test2ContainerTestId
      .replace('participant-view-', '')
      .replace('-container', '');

    const test2AvatarSelectors = [
      `[data-testid*="${test2ParticipantId}"][data-testid*="avatar"]`,
      `[data-testid*="${test2ParticipantId}"][data-testid*="placeholder"]`,
      `[data-testid*="${test2ParticipantId}"][data-testid*="no-video"]`,
    ];

    let test2AvatarFound = false;
    for (const selector of test2AvatarSelectors) {
      const avatarElement = test4Page.locator(selector);
      if (await avatarElement.isVisible().catch(() => false)) {
        test2AvatarFound = true;
        break;
      }
    }

    const test2VideoStreamActive = await test4Page.evaluate((videoSelector) => {
      const video = document.querySelector(videoSelector) as HTMLVideoElement;
      if (video && video.srcObject && video.srcObject instanceof MediaStream) {
        return (video.srcObject as MediaStream).getVideoTracks().length > 0;
      }
      return false;
    }, `[data-testid="${test2ParticipantId}-video"]`);

    if (test2VideoStreamActive && !test2AvatarFound) {
      console.warn(
        "Test2 video appears active from Test4's view - camera state unclear",
      );
      await test4Page.screenshot({
        path: 'tests/debug-test4-test2-camera-unclear.png',
      });
    }

    const test2MicOn = test4Page.getByTestId(
      `participant-view-${test2ParticipantId}-mic-on-icon`,
    );
    const test2MicOff = test4Page.getByTestId(
      `participant-view-${test2ParticipantId}-mic-off-icon`,
    );

    const test2MicOnVisible = await test2MicOn.isVisible().catch(() => false);
    const test2MicOffVisible = await test2MicOff.isVisible().catch(() => false);

    if (test2MicOffVisible && !test2MicOnVisible) {
      console.warn(
        "Test2 appears to have microphone OFF from Test4's view (expected ON)",
      );
    } else if (!test2MicOnVisible && !test2MicOffVisible) {
      console.warn(
        "Test2 microphone state unclear from Test4's view after reload",
      );
    }
  }

  // PART 5: Verify Test4 can see Test3

  const test3NameElement = test4Page
    .locator('[data-testid*="participant-view"][data-testid*="name"]')
    .filter({ hasText: 'Test3' });

  await expect(test3NameElement).toBeVisible({ timeout: 15000 });

  const test3Container = test3NameElement
    .locator('..')
    .locator('..')
    .locator('..');
  const test3ContainerTestId = await test3Container.getAttribute('data-testid');

  if (test3ContainerTestId) {
    const test3ParticipantId = test3ContainerTestId
      .replace('participant-view-', '')
      .replace('-container', '');

    const test3Video = test4Page.getByTestId(
      `participant-view-${test3ParticipantId}-video`,
    );
    await expect(test3Video).toBeVisible();

    const test3MicOn = test4Page.getByTestId(
      `participant-view-${test3ParticipantId}-mic-on-icon`,
    );
    const test3MicOff = test4Page.getByTestId(
      `participant-view-${test3ParticipantId}-mic-off-icon`,
    );

    const test3MicOnVisible = await test3MicOn.isVisible().catch(() => false);
    const test3MicOffVisible = await test3MicOff.isVisible().catch(() => false);

    if (test3MicOnVisible && !test3MicOffVisible) {
      console.warn(
        "Test3 appears to have microphone ON from Test4's view (expected OFF)",
      );
    } else if (!test3MicOffVisible && !test3MicOnVisible) {
      console.warn(
        "Test3 microphone state unclear from Test4's view after reload",
      );
    }
  }
}
