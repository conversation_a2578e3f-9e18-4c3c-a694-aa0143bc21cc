import { Browser, expect, Page } from '@playwright/test';

export async function guestRedirectedFromClassWithNoParties(page: Page, openBrowsers: Browser[], classroomId: string) {

    page.setDefaultTimeout(30000);

    // Have test1 (main user/teacher) exit the class 
    await page.getByTestId('action-bar-exit-button').click();
    await page.waitForURL(new RegExp(`/classrooms/${classroomId}`), { timeout: 5000 });

    // Have test2 (student) exit the class
    const test2Browser = openBrowsers[0]; 
    const test2Context = test2Browser.contexts()[0];
    const test2Page = test2Context.pages()[0];
    await test2Page.getByTestId('action-bar-exit-button').click();
    await page.waitForURL(new RegExp(`/classrooms/${classroomId}`), { timeout: 5000 });

    // Have test3 (teacher) exit the class
    const test3Browser = openBrowsers[1]; 
    const test3Context = test3Browser.contexts()[0];
    const test3Page = test3Context.pages()[0];
    await test3Page.getByTestId('action-bar-exit-button').click();
    await page.waitForURL(new RegExp(`/classrooms/${classroomId}`), { timeout: 5000 });

    // Check if test4 (guest) was automatically redirected to the classroom page
    const test4Browser = openBrowsers[2]; 
    const test4Context = test4Browser.contexts()[0];
    const test4Page = test4Context.pages()[0];

    // Wait for potential redirect
    await test4Page.waitForLoadState('domcontentloaded');

    // Check that test4 was redirected to the classroom page
    await expect(test4Page).toHaveURL(new RegExp(`/classrooms/${classroomId}`));
}