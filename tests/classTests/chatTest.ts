import { Browser, expect, Page } from '@playwright/test';

export async function chatTest(page: Page, openBrowsers: <PERSON><PERSON><PERSON>[]) {

    // Test: Check chat messages are displayed correctly
    const test2Browser = openBrowsers[0]; // Student
    const test2Context = test2Browser.contexts()[0];
    const test2Page = test2Context.pages()[0];
    const guestBrowser = openBrowsers[2]; // Guest
    const guestContext = guestBrowser.contexts()[0];
    const guestPage = guestContext.pages()[0];
    await test2Page.getByTestId('action-bar-chat-button').click();
    await expect(test2Page.getByTestId('chat-container')).toBeVisible();
    await expect(test2Page.getByTestId('chat-message-input')).toBeVisible();
    await test2Page.getByTestId('chat-message-input').fill('Hello World!');
    await test2Page.getByTestId('chat-send-button').click();
    await expect(test2Page.locator('[data-testid^="chat-message-text-"]').first().filter({ hasText: 'Hello World!' })).toBeVisible();
    await test2Page.keyboard.press('Escape');
    await guestPage.getByTestId('action-bar-chat-button').click();
    await expect(guestPage.getByTestId('chat-container')).toBeVisible();
    await expect(guestPage.locator('[data-testid^="chat-message-text-"]').first().filter({ hasText: 'Hello World!' })).toBeVisible();
    

    
    //  // Try to interact with chat input and verify it doesn't work
    //    await guestPage.getByTestId('chat-message-input').fill('This should not work!');
    //    const inputValue = await guestPage.getByTestId('chat-message-input').inputValue();
    //    await expect(inputValue === '' || inputValue === 'Type your message...').toBeTruthy();

    
    await guestPage.keyboard.press('Escape');
    await expect(page.getByTestId('sidebar-indicator-chat-messages')).toBeVisible();
    await expect(page.getByTestId('sidebar-indicator-chat-messages')).toHaveText(/[1-9]/);
    await page.getByTestId('action-bar-chat-button').click();
    await expect(page.getByTestId('chat-container')).toBeVisible();
    await expect(page.locator('[data-testid^="chat-message-text-"]').first().filter({ hasText: 'Hello World!' })).toBeVisible();
    await expect(page.locator('[data-testid^="chat-message-author-"]').first().filter({ hasText: '<EMAIL>' })).toBeVisible();
    await page.keyboard.press('Escape');
    await expect(page.getByTestId('sidebar-indicator-chat-messages')).toHaveCount(0);
}