import { Browser, expect, Page } from '@playwright/test';
import {
  connectToClass,
  muteUnmute,
  isPageReceivingAudio,
  waitForAudioChange,
  detectIncomingAudio,
  detectMeaningfulAudio,
} from '../utils';

// Define types for audio call logging
interface AudioCallLogs {
  oscillatorCreated: number[];
  gainNodeCreated: number[];
  frequencies: number[];
  gainValues: number[];
  oscillatorTypes: string[];
  startTimes: number[];
  stopTimes: number[];
}

// Extend Window interface to include our custom property
declare global {
  interface Window {
    audioCallLogs: AudioCallLogs;
  }
}

export async function metronomeTest(page: Page) {
  await page.getByTestId('action-bar-metronome-button').click();
  await page.waitForTimeout(1000);

  await expect(page.getByTestId('metronome-bpm-display')).toHaveText('120');

  await page.getByTestId('metronome-toggle-button').click();

  // Wait for audio context to initialize and start generating sounds
  await page.waitForTimeout(2000);

  await expect(page.getByTestId('metronome-toggle-button')).toContainText(
    'Stop',
  );

  // The metronome should show visual beat indicators when playing
  await expect(page.getByTestId('metronome-beat-indicators')).toBeVisible();

  // Test BPM change - this will stop the metronome
  await page.getByTestId('metronome-increase-bpm-button').click();
  await expect(page.getByTestId('metronome-bpm-display')).toHaveText('121');

  await expect(page.getByTestId('metronome-toggle-button')).toContainText(
    'Start',
  );

  await page.getByTestId('metronome-toggle-button').click();
  await page.waitForTimeout(1000);
  await expect(page.getByTestId('metronome-toggle-button')).toContainText(
    'Stop',
  );

  // Change time signature - this will also stop the metronome
  await page.getByTestId('metronome-time-signature-select').click();
  await page.getByTestId('metronome-time-signature-3/4').click();

  await expect(page.getByTestId('metronome-toggle-button')).toContainText(
    'Start',
  );

  // Check beat indicators (should now show 3 beats for 3/4 time)
  await expect(page.getByTestId('metronome-beat-indicators')).toBeVisible();
  await expect(page.getByTestId('metronome-beat-0')).toBeVisible();
  await expect(page.getByTestId('metronome-beat-1')).toBeVisible();
  await expect(page.getByTestId('metronome-beat-2')).toBeVisible();

  // Verify the 4th beat indicator is not present (3/4 time has only 3 beats)
  await expect(page.getByTestId('metronome-beat-3')).not.toBeVisible();

  await page.getByTestId('metronome-toggle-button').click();
  await page.waitForTimeout(1000);
  await expect(page.getByTestId('metronome-toggle-button')).toContainText(
    'Stop',
  );

  // Volume changes should NOT stop the metronome
  const volumeSlider = page.getByTestId('metronome-volume-slider');
  await volumeSlider.click();

  await page.waitForTimeout(500);

  await expect(page.getByTestId('metronome-toggle-button')).toContainText(
    'Stop',
  );

  // Test that BPM can be decreased - this will stop the metronome again
  await page.getByTestId('metronome-decrease-bpm-button').click();
  await expect(page.getByTestId('metronome-bpm-display')).toHaveText('120');

  await expect(page.getByTestId('metronome-toggle-button')).toContainText(
    'Start',
  );

  await page.getByTestId('metronome-time-signature-select').click();
  await page.getByTestId('metronome-time-signature-4/4').click();

  // Verify we now have 4 beat indicators
  await expect(page.getByTestId('metronome-beat-0')).toBeVisible();
  await expect(page.getByTestId('metronome-beat-1')).toBeVisible();
  await expect(page.getByTestId('metronome-beat-2')).toBeVisible();
  await expect(page.getByTestId('metronome-beat-3')).toBeVisible();

  await page.getByTestId('metronome-toggle-button').click();
  await page.waitForTimeout(1000);
  await expect(page.getByTestId('metronome-toggle-button')).toContainText(
    'Stop',
  );

  await page.getByTestId('metronome-toggle-button').click();
  await page.waitForTimeout(500);

  await expect(page.getByTestId('metronome-toggle-button')).toContainText(
    'Start',
  );

  await page.keyboard.press('Escape');
}

export async function metronomeAudioParameterTest(
  page: Page,
  classroomId: string,
) {
  // This test verifies that UI changes result in corresponding audio parameter changes

  // Set up audio context monitoring
  await page.addInitScript(() => {
    // Store references to audio calls for verification
    window.audioCallLogs = {
      oscillatorCreated: [],
      gainNodeCreated: [],
      frequencies: [],
      gainValues: [],
      oscillatorTypes: [],
      startTimes: [],
      stopTimes: [],
    };

    // Mock and monitor createOscillator calls
    const originalCreateOscillator = AudioContext.prototype.createOscillator;
    AudioContext.prototype.createOscillator = function () {
      const osc = originalCreateOscillator.call(this);
      const logs = window.audioCallLogs;

      logs.oscillatorCreated.push(Date.now());

      // Monitor frequency.value property changes directly
      const originalFrequency = osc.frequency;
      Object.defineProperty(originalFrequency, 'value', {
        get() {
          return this._value !== undefined ? this._value : 440;
        },
        set(value) {
          this._value = value;
          logs.frequencies.push(value);
        },
      });

      // Monitor type changes
      const originalType = osc.type;
      Object.defineProperty(osc, 'type', {
        get() {
          return this._type !== undefined ? this._type : originalType;
        },
        set(type) {
          this._type = type;
          logs.oscillatorTypes.push(type);
        },
      });

      // Monitor start/stop calls
      const originalStart = osc.start.bind(osc);
      const originalStop = osc.stop.bind(osc);

      osc.start = function (time) {
        logs.startTimes.push(time || 0);
        return originalStart(time);
      };

      osc.stop = function (time) {
        logs.stopTimes.push(time || 0);
        return originalStop(time);
      };

      return osc;
    };

    // Mock and monitor createGain calls
    const originalCreateGain = AudioContext.prototype.createGain;
    AudioContext.prototype.createGain = function () {
      const gain = originalCreateGain.call(this);
      const logs = window.audioCallLogs;

      logs.gainNodeCreated.push(Date.now());

      // Monitor gain value changes
      const originalSetValueAtTime = gain.gain.setValueAtTime.bind(gain.gain);
      gain.gain.setValueAtTime = function (value, time) {
        logs.gainValues.push(value);
        return originalSetValueAtTime(value, time);
      };

      return gain;
    };
  });

  // Reload the page to ensure monitoring is active before any AudioContext creation
  await page.reload();
  await page.waitForLoadState('domcontentloaded');
  await connectToClass(page, false, 'teacher', classroomId);

  await page.getByTestId('action-bar-metronome-button').click();

  // Wait for metronome to be fully initialized (check for "Start" button text)
  await expect(page.getByTestId('metronome-toggle-button')).toContainText(
    'Start',
  );

  // Clear initial logs after initialization
  await page.evaluate(() => {
    window.audioCallLogs = {
      oscillatorCreated: [],
      gainNodeCreated: [],
      frequencies: [],
      gainValues: [],
      oscillatorTypes: [],
      startTimes: [],
      stopTimes: [],
    };
  });

  // Test 1: Start metronome and verify audio generation
  await page.getByTestId('metronome-toggle-button').click();

  await expect(page.getByTestId('metronome-toggle-button')).toContainText(
    'Stop',
  );

  // Wait longer for several beats to be generated
  await page.waitForTimeout(3000);

  let audioLogs = await page.evaluate(() => window.audioCallLogs);
  expect(audioLogs.oscillatorCreated.length).toBeGreaterThan(0);
  expect(audioLogs.frequencies).toContain(880); // Downbeat frequency
  expect(audioLogs.frequencies).toContain(660); // Regular beat frequency
  expect(audioLogs.oscillatorTypes).toContain('sine');

  // Test 2: Change volume and verify gain changes
  const initialGainValues = [...audioLogs.gainValues];

  // Move volume slider to a different position using keyboard navigation
  const volumeSlider = page.getByTestId('metronome-volume-slider');
  await volumeSlider.focus();

  // Use arrow keys to increase volume (each press typically increases by step amount)
  await page.keyboard.press('ArrowRight');
  await page.keyboard.press('ArrowRight');
  await page.keyboard.press('ArrowRight');
  await page.keyboard.press('ArrowRight');
  await page.keyboard.press('ArrowRight');

  await page.waitForTimeout(2000);

  audioLogs = await page.evaluate(() => window.audioCallLogs);
  const newGainValues = audioLogs.gainValues.slice(initialGainValues.length);
  expect(newGainValues.length).toBeGreaterThan(0);

  await page.getByTestId('metronome-toggle-button').click();
  await page.waitForTimeout(500);

  // Test 3: Change BPM and verify timing changes
  await page.evaluate(() => {
    window.audioCallLogs.startTimes = [];
  });

  await page.getByTestId('metronome-increase-bpm-button').click();
  await page.getByTestId('metronome-increase-bpm-button').click();
  await expect(page.getByTestId('metronome-bpm-display')).toHaveText('122');

  await page.getByTestId('metronome-toggle-button').click();
  await page.waitForTimeout(3000);

  audioLogs = await page.evaluate(() => window.audioCallLogs);
  const startTimes = audioLogs.startTimes;
  expect(startTimes.length).toBeGreaterThan(2);

  // Verify timing intervals are consistent with 122 BPM (approximately 0.492 seconds per beat)
  for (let i = 1; i < startTimes.length && i < 4; i++) {
    const interval = startTimes[i] - startTimes[i - 1];
    expect(interval).toBeGreaterThan(0.4);
    expect(interval).toBeLessThan(0.6);
  }

  await page.getByTestId('metronome-toggle-button').click();
  await page.waitForTimeout(500);

  // Test 4: Change time signature and verify beat pattern
  await page.evaluate(() => {
    window.audioCallLogs.frequencies = [];
  });

  await page.getByTestId('metronome-time-signature-select').click();
  await page.getByTestId('metronome-time-signature-3/4').click();

  await page.getByTestId('metronome-toggle-button').click();
  await page.waitForTimeout(4000); // Wait for at least one complete 3/4 measure

  audioLogs = await page.evaluate(() => window.audioCallLogs);
  const frequencies = audioLogs.frequencies;

  // In 3/4 time, we should see the pattern: 880Hz (downbeat), 660Hz, 660Hz, 880Hz (next downbeat), etc.
  let downbeatCount = 0;
  let regularBeatCount = 0;

  for (const freq of frequencies) {
    if (freq === 880) downbeatCount++;
    if (freq === 660) regularBeatCount++;
  }

  expect(downbeatCount).toBeGreaterThan(0);
  expect(regularBeatCount).toBeGreaterThan(0);
  // In 3/4 time, we should have 2 regular beats for every downbeat
  expect(regularBeatCount).toBeGreaterThanOrEqual(downbeatCount);

  // Test 5: Volume changes don't break audio generation
  await page.evaluate(() => {
    window.audioCallLogs.gainValues = [];
    window.audioCallLogs.oscillatorCreated = [];
  });

  await page.getByTestId('metronome-toggle-button').click();
  await expect(page.getByTestId('metronome-toggle-button')).toContainText(
    'Start',
  );
  await page.waitForTimeout(1000);

  await page.getByTestId('metronome-toggle-button').click();
  await expect(page.getByTestId('metronome-toggle-button')).toContainText(
    'Stop',
    { timeout: 10000 },
  );

  // Test minimum volume - verify audio is still generated
  const volumeSliderMin = page.getByTestId('metronome-volume-slider');
  await volumeSliderMin.focus();
  await page.keyboard.press('Home');
  await page.waitForTimeout(1500);

  let testLogs = await page.evaluate(() => window.audioCallLogs);
  const lowVolumeOscillators = testLogs.oscillatorCreated.length;

  await page.evaluate(() => {
    window.audioCallLogs.oscillatorCreated = [];
  });

  // Test maximum volume - verify audio is still generated
  await page.keyboard.press('End');
  await page.waitForTimeout(1500);

  testLogs = await page.evaluate(() => window.audioCallLogs);
  const highVolumeOscillators = testLogs.oscillatorCreated.length;

  // Verify audio generation continues at both volume levels
  expect(lowVolumeOscillators).toBeGreaterThan(0);
  expect(highVolumeOscillators).toBeGreaterThan(0);

  await page.getByTestId('metronome-toggle-button').click();
  await page.keyboard.press('Escape');
}

export async function metronomeOnRemoteParticipants(
  page: Page,
  openBrowsers: Browser[],
) {
  const test15Browser = openBrowsers[0];
  const test15Context = test15Browser.contexts()[0];
  const test15Page = test15Context.pages()[0];
  const test16Browser = openBrowsers[1];
  const test16Context = test16Browser.contexts()[0];
  const test16Page = test16Context.pages()[0];
  const guest1Browser = openBrowsers[2];
  const guest1Context = guest1Browser.contexts()[0];
  const guest1Page = guest1Context.pages()[0];

  await muteUnmute(page);
  await muteUnmute(test15Page);
  await muteUnmute(test16Page);

  // Verify no audio is being received initially on remote participants
  const initialAudioPage = await isPageReceivingAudio(page);
  const initialAudioTest16 = await isPageReceivingAudio(test16Page);
  const initialAudioGuest1 = await isPageReceivingAudio(guest1Page);

  expect(initialAudioPage).toBe(false);
  expect(initialAudioTest16).toBe(false);
  expect(initialAudioGuest1).toBe(false);

  await expect(
    test15Page.getByTestId('action-bar-metronome-button'),
  ).toBeVisible();
  await test15Page.getByTestId('action-bar-metronome-button').click();
  await expect(test15Page.getByTestId('metronome-toggle-button')).toBeVisible();
  await test15Page.getByTestId('metronome-toggle-button').click();

  // Wait for metronome to start and verify it's playing
  await expect(test15Page.getByTestId('metronome-toggle-button')).toContainText(
    'Stop',
  );

  try {
    await waitForAudioChange(page, true, 8000);
    await waitForAudioChange(test16Page, true, 8000);
    await waitForAudioChange(guest1Page, true, 8000);
  } catch (error) {
    console.warn(
      'Some participants may not have received metronome audio within timeout:',
      error,
    );
  }

  const metronomeAudioPage = await detectIncomingAudio(page, {
    monitorDuration: 5000,
    threshold: 0.005, // Lower threshold for metronome audio
    minimumActivePercentage: 0.2,
  });

  const metronomeAudioTest16 = await detectIncomingAudio(test16Page, {
    monitorDuration: 5000,
    threshold: 0.005,
    minimumActivePercentage: 0.2,
  });

  const metronomeAudioGuest1 = await detectIncomingAudio(guest1Page, {
    monitorDuration: 5000,
    threshold: 0.005,
    minimumActivePercentage: 0.2,
  });

  expect(metronomeAudioPage.isReceivingAudio).toBe(true);
  expect(metronomeAudioTest16.isReceivingAudio).toBe(true);
  expect(metronomeAudioGuest1.isReceivingAudio).toBe(true);

  console.log('Audio levels:');
  console.log(
    `Page: avg=${metronomeAudioPage.averageLevel.toFixed(4)}, peak=${metronomeAudioPage.peakLevel.toFixed(4)}`,
  );
  console.log(
    `Test16: avg=${metronomeAudioTest16.averageLevel.toFixed(4)}, peak=${metronomeAudioTest16.peakLevel.toFixed(4)}`,
  );
  console.log(
    `Guest1: avg=${metronomeAudioGuest1.averageLevel.toFixed(4)}, peak=${metronomeAudioGuest1.peakLevel.toFixed(4)}`,
  );

  await test15Page.getByTestId('metronome-toggle-button').click();
  await expect(test15Page.getByTestId('metronome-toggle-button')).toContainText(
    'Start',
  );

  await page.waitForTimeout(5000);

  const finalAudioPageDetails = await detectMeaningfulAudio(page, {
    monitorDuration: 4000,
    threshold: 0.003, // Lower threshold for better detection
    minimumActivePercentage: 0.15, // Require less active percentage
    sampleInterval: 150,
  });
  console.log('Enhanced audio details on page:', {
    isReceiving: finalAudioPageDetails.isReceivingAudio,
    avgLevel: finalAudioPageDetails.averageLevel.toFixed(6),
    peakLevel: finalAudioPageDetails.peakLevel.toFixed(6),
  });

  const finalAudioTest16Details = await detectMeaningfulAudio(test16Page, {
    monitorDuration: 4000,
    threshold: 0.003,
    minimumActivePercentage: 0.15,
    sampleInterval: 150,
  });
  console.log('Enhanced audio details on test16Page:', {
    isReceiving: finalAudioTest16Details.isReceivingAudio,
    avgLevel: finalAudioTest16Details.averageLevel.toFixed(6),
    peakLevel: finalAudioTest16Details.peakLevel.toFixed(6),
  });

  const finalAudioGuest1Details = await detectMeaningfulAudio(guest1Page, {
    monitorDuration: 4000,
    threshold: 0.003,
    minimumActivePercentage: 0.15,
    sampleInterval: 150,
  });
  console.log('Enhanced audio details on guest1Page:', {
    isReceiving: finalAudioGuest1Details.isReceivingAudio,
    avgLevel: finalAudioGuest1Details.averageLevel.toFixed(6),
    peakLevel: finalAudioGuest1Details.peakLevel.toFixed(6),
  });

  const hasAudioPage = finalAudioPageDetails.isReceivingAudio;
  const hasAudioTest16 = finalAudioTest16Details.isReceivingAudio;
  const hasAudioGuest1 = finalAudioGuest1Details.isReceivingAudio;

  expect(hasAudioPage).toBe(false);
  expect(hasAudioTest16).toBe(false);
  expect(hasAudioGuest1).toBe(false);

  //Toggle settings and verify audio is still being received
  await expect(test15Page.getByTestId('metronome-mix-switch')).toBeVisible();
  await test15Page.getByTestId('metronome-mix-switch').click();
  await test15Page.getByTestId('metronome-toggle-button').click();
  await page.waitForTimeout(5000);

  const finalAudioPageDetails2 = await detectMeaningfulAudio(page, {
    monitorDuration: 4000,
    threshold: 0.003,
    minimumActivePercentage: 0.15,
    sampleInterval: 150,
  });
  console.log('Enhanced audio details on page:', {
    isReceiving: finalAudioPageDetails2,
  });

  const finalAudioTest16Details2 = await detectMeaningfulAudio(test16Page, {
    monitorDuration: 4000,
    threshold: 0.003,
    minimumActivePercentage: 0.15,
    sampleInterval: 150,
  });
  console.log('Enhanced audio details on test16Page:', {
    isReceiving: finalAudioTest16Details2.isReceivingAudio,
    avgLevel: finalAudioTest16Details2.averageLevel.toFixed(6),
    peakLevel: finalAudioTest16Details2.peakLevel.toFixed(6),
  });

  const finalAudioGuest1Details2 = await detectMeaningfulAudio(guest1Page, {
    monitorDuration: 4000,
    threshold: 0.003,
    minimumActivePercentage: 0.15,
    sampleInterval: 150,
  });
  console.log('Enhanced audio details on guest1Page:', {
    isReceiving: finalAudioGuest1Details2.isReceivingAudio,
    avgLevel: finalAudioGuest1Details2.averageLevel.toFixed(6),
    peakLevel: finalAudioGuest1Details2.peakLevel.toFixed(6),
  });

  const hasAudioPage2 = finalAudioPageDetails2.isReceivingAudio;
  const hasAudioTest162 = finalAudioTest16Details2.isReceivingAudio;
  const hasAudioGuest12 = finalAudioGuest1Details2.isReceivingAudio;

  expect(hasAudioPage2).toBe(false);
  expect(hasAudioTest162).toBe(false);
  expect(hasAudioGuest12).toBe(false);

  await test15Page.keyboard.press('Escape');
}
