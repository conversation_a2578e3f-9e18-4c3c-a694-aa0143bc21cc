import { expect, <PERSON>, <PERSON><PERSON>er } from '@playwright/test';

interface ParticipantSids {
  test1: string;
  test2: string;
  test3: string;
}

async function getParticipantSids(guestPage: Page): Promise<ParticipantSids> {
  console.log('Collecting participant SIDs from guest page by usernames...');
  
  await guestPage.waitForTimeout(3000);
  
  const participantContainers = guestPage.locator('[data-testid*="participant-view-"][data-testid*="-container"]');
  await expect(participantContainers.first()).toBeVisible({ timeout: 15000 });
  
  const count = await participantContainers.count();
  console.log(`Found ${count} participant containers`);
  
  if (count < 3) {
    throw new Error(`Expected at least 3 participants (test1, test2, test3), but found ${count}`);
  }

  async function findParticipantSidByUsername(username: string): Promise<string> {
    console.log(`Looking for participant with username: ${username}`);
    
    const nameElement = guestPage.locator('[data-testid*="participant-view-"][data-testid*="-name"]')
      .filter({ hasText: username });
    
    await expect(nameElement).toBeVisible({ timeout: 10000 });
    
    const container = nameElement.locator('xpath=ancestor::*[contains(@data-testid, "participant-view-") and contains(@data-testid, "-container")]').first();
    
    const containerTestId = await container.getAttribute('data-testid');
    if (!containerTestId) {
      throw new Error(`Could not get data-testid from ${username} participant container`);
    }
    
    const sidMatch = containerTestId.match(/participant-view-(.+?)-container/);
    if (!sidMatch || sidMatch.length < 2) {
      throw new Error(`Could not extract SID from container data-testid: ${containerTestId}`);
    }
    
    const sid = sidMatch[1];
    console.log(`Found ${username} SID: ${sid}`);
    return sid;
  }
  
  const test1Sid = await findParticipantSidByUsername('<EMAIL>');
  const test2Sid = await findParticipantSidByUsername('<EMAIL>');
  const test3Sid = await findParticipantSidByUsername('<EMAIL>');
  
  const sids: ParticipantSids = {
    test1: test1Sid,
    test2: test2Sid,
    test3: test3Sid
  };
  
  console.log('Collected participant SIDs:', sids);
  return sids;
}

async function checkParticipantVideoOff(page: Page, participantId: string): Promise<boolean> {
  console.log(`Checking if participant ${participantId} video is off...`);
  
  const avatarSelectors = [
    `[data-testid*="${participantId}"][data-testid*="avatar"]`,
    `[data-testid*="${participantId}"][data-testid*="placeholder"]`,
    `[data-testid*="${participantId}"][data-testid*="no-video"]`,
  ];

  let avatarFound = false;
  for (const selector of avatarSelectors) {
    const avatarElement = page.locator(selector);
    if (await avatarElement.isVisible().catch(() => false)) {
      avatarFound = true;
      console.log(`Found avatar with selector: ${selector}`);
      break;
    }
  }

  const videoElement = page.getByTestId(`participant-view-${participantId}-video`);
  const videoVisible = await videoElement.isVisible().catch(() => false);
  
  let videoStreamActive = false;
  if (videoVisible) {
    videoStreamActive = await page.evaluate((videoSelector) => {
      const video = document.querySelector(videoSelector) as HTMLVideoElement;
      if (video && video.srcObject && video.srcObject instanceof MediaStream) {
        const tracks = (video.srcObject as MediaStream).getVideoTracks();
        return tracks.length > 0 && tracks[0].enabled;
      }
      return false;
    }, `[data-testid="participant-view-${participantId}-video"]`);
  }

  const isVideoOff = avatarFound || !videoVisible || (videoVisible && !videoStreamActive);
  
  console.log(`Participant ${participantId} - avatarFound: ${avatarFound}, videoVisible: ${videoVisible}, videoStreamActive: ${videoStreamActive}`);
  console.log(`Participant ${participantId} video is ${isVideoOff ? 'OFF' : 'ON'}`);
  return isVideoOff;
}

async function checkParticipantMicrophoneOff(page: Page, participantId: string): Promise<boolean> {
  console.log(`Checking if participant ${participantId} microphone is off...`);
  
  const micOn = page.getByTestId(`participant-view-${participantId}-mic-on-icon`);
  const micOff = page.getByTestId(`participant-view-${participantId}-mic-off-icon`);

  const micOnVisible = await micOn.isVisible().catch(() => false);
  const micOffVisible = await micOff.isVisible().catch(() => false);

  if (!micOnVisible && !micOffVisible) {
    console.warn(`Participant ${participantId} microphone state unclear - neither on nor off icon visible`);
    return false;
  }

  const isMicOff = micOffVisible && !micOnVisible;
  console.log(`Participant ${participantId} microphone is ${isMicOff ? 'OFF' : 'ON'}`);
  return isMicOff;
}

export async function previewButtonsTest(page: Page, openBrowsers: Browser[]) {
  await page.reload();
  await page.waitForLoadState('domcontentloaded');

  await expect(page.getByTestId('welcome-dialog-party-camera-button')).toBeVisible();
  await page.getByTestId('welcome-dialog-party-camera-button').click();
  await expect(page.getByTestId('welcome-dialog-party-microphone-button')).toBeVisible();
  await page.getByTestId('welcome-dialog-party-microphone-button').click();

  await page.getByTestId('welcome-dialog-continue-button').click();

  await expect(page.getByTestId('action-bar-exit-button')).toBeVisible({ timeout: 15000 });

  await expect(page.getByTestId('participant-view-local-avatar')).toBeVisible();
  await expect(page.getByTestId('participant-view-local-mic-off-icon')).toBeVisible();

  console.log('=== STEP 2: Getting participant IDs ===');
  const guestBrowser = openBrowsers[2];
  const guestContext = guestBrowser.contexts()[0];
  const guestPage = guestContext.pages()[0];
  
  const participantSids = await getParticipantSids(guestPage);
  console.log('Participant SIDs collected:', participantSids);

  console.log('=== STEP 3: Checking guest browser participant status ===');
  
  const test1VideoOff = await checkParticipantVideoOff(guestPage, participantSids.test1);
  const test1MicOff = await checkParticipantMicrophoneOff(guestPage, participantSids.test1);
  
  console.log(`Guest browser - Test1 video off: ${test1VideoOff}, microphone off: ${test1MicOff}`);
  
  expect(test1VideoOff).toBe(true);
  expect(test1MicOff).toBe(true);

  console.log('=== STEP 4: Checking page2 participant status ===');
  
  const page2Browser = openBrowsers[1];
  const page2Context = page2Browser.contexts()[0];
  const page2Page = page2Context.pages()[0];
  
  const test1VideoOffFromPage2 = await checkParticipantVideoOff(page2Page, participantSids.test1);
  const test1MicOffFromPage2 = await checkParticipantMicrophoneOff(page2Page, participantSids.test1);
  
  console.log(`Page2 - Test1 video off: ${test1VideoOffFromPage2}, microphone off: ${test1MicOffFromPage2}`);
  
  expect(test1VideoOffFromPage2).toBe(true);
  expect(test1MicOffFromPage2).toBe(true);

  console.log('All preview button tests completed successfully!');
}
