import { expect, Page} from '@playwright/test';


export async function imslpTest(page: Page) {

        //Test imslp search
        await page.getByTestId('action-bar-materials-button').click();
        await expect(page.getByTestId('materials-search-scores-button')).toBeVisible();
        await page.getByTestId('materials-search-scores-button').click();
        await expect(page.getByTestId('imslp-search-container')).toBeVisible();
        await page.getByTestId('imslp-search-input').fill('Mozart');
        await page.getByTestId('imslp-search-input').press('Enter');
        await expect(page.getByText('5 Country Dances')).toBeVisible({ timeout: 10000 });
        await page.getByText('5 Country Dances').click({ timeout: 5000 });
        await expect(page.getByText('Classical')).toBeVisible();
        await expect(page.getByText('Nottebohm')).toBeVisible({ timeout: 5000 });
        await page.getByText('Nottebohm').click();
        await expect(page.getByText('Complete Score').first()).toBeVisible({ timeout: 5000 });
        await page.getByText('Complete Score').first().click();
        await expect(page.locator('canvas[data-page-number="1"]')).toBeVisible({ timeout: 10000 });
        await page.keyboard.press('Escape');
        await page.getByTestId('action-bar-materials-button').click();
        await expect(page.locator('canvas[data-page-number="1"]')).toBeVisible({ timeout: 10000 });
        await page.keyboard.press('Escape');
}