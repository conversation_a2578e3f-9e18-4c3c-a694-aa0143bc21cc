import { expect, Page } from '@playwright/test';
import { connectToClass } from '../utils';

export async function exitAndRejoinClass(page: Page, classroomId: string) {
  // Exit the class and check that active class is visible and rejoin the class test
  await page.getByTestId('action-bar-exit-button').click();
  await page.reload();
  await page.waitForLoadState('domcontentloaded');
  await expect(page.getByTestId('classroom-active-join-button')).toBeVisible({
    timeout: 5000,
  });
  await page.getByTestId('classroom-active-join-button').click();
  await page.waitForLoadState('domcontentloaded');
  await connectToClass(page, true, 'teacher', classroomId);
  await expect(
    page.getByTestId('sidebar-indicator-participants-count'),
  ).toBeVisible({ timeout: 15000 });
  await expect(
    page.getByTestId('sidebar-indicator-participants-count'),
  ).toHaveText('3');
}
