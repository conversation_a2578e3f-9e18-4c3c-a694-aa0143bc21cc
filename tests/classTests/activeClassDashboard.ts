import { expect, Page } from "@playwright/test";
import { reliableGoto, connectToClass } from "../utils";
import { testRemoteParticipantsVisibility } from "./index";

export const activeClassDashboardTest = async (page: Page, role: 'student' | 'teacher' | 'guest', classroomId: string) => {
    await reliableGoto(page, '/classrooms');
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId(`classroom-card-${classroomId}-join-button`)).toBeVisible({ timeout: 10000 });
    await page.getByTestId(`classroom-card-${classroomId}-join-button`).click();
    await connectToClass(page, true, role, classroomId);
    await testRemoteParticipantsVisibility(page);
}