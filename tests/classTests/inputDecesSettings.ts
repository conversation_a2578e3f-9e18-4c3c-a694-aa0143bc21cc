// import { <PERSON><PERSON><PERSON>, <PERSON>, expect } from '@playwright/test';

// export async function audioDeviceSettingsTest(page: Page, openBrowsers: Browser[]) {
//   const initialRemoteTrackCounts: number[] = [];
//   const initialRemoteTrackIds: string[][] = [];
//   for (const br of openBrowsers) {
//     const remotePage = br.contexts()[0].pages()[0];
//     await remotePage.waitForTimeout(10000);
//     const getTrackInfo = async () =>
//       await remotePage.evaluate(() => {
//         const mixer = (window as any).remoteAudioMixer;
//         if (!mixer || !mixer.participants) return { count: 0, ids: [] as string[] };
//         const ids: string[] = [];
//         (mixer.participants as Map<string, any>).forEach((p: any) => {
//           const stream = p.getStream ? p.getStream() : null;
//           if (stream) {
//             stream.getAudioTracks().forEach((t: any) => {
//               const id = t.deviceId || t.getSettings().deviceId || t.label || t.id;
//               ids.push(id);
//             });
//           }
//         });
//         return { count: ids.length, ids };
//       });

//     // Wait until **three** audio tracks arrive (max 15 s)
//     let initialCount = 0;
//     let initialIds: string[] = [];
//     const start = Date.now();
//     while (Date.now() - start < 15_000) {
//       const info = await getTrackInfo();
//       initialCount = info.count;
//       initialIds = info.ids;
//       if (initialCount >= 3) break;
//       await remotePage.waitForTimeout(400);
//     }
//     initialRemoteTrackCounts.push(initialCount);
//     initialRemoteTrackIds.push(initialIds);

//     // Validate exactly three unique tracks are present initially
//     expect(initialCount).toBe(3);
//     expect(new Set(initialIds).size).toBe(3);
//   }
//   console.log('Initial remote audio track info:', initialRemoteTrackCounts, initialRemoteTrackIds);

//   // ── 2. Open Settings → Device accordion ─────────────────────────────────
//   await page.getByTestId('settings-sidebar').click();
//   await page.locator('button:has-text("Device")').click();

//   // ── 3. Select the SECOND emulated microphone (mic-headset-002) ──────────
//   const micContainer = page.getByTestId(
//     'audio-input-device-container-mic-headset-002',
//   );
//   await micContainer.scrollIntoViewIfNeeded();
//   const micCheckbox = page.getByTestId('audio-input-checkbox-mic-headset-002');
//   await micCheckbox.click();

//   // Give UI some time to apply device change
//   await page.waitForTimeout(10_000);

//   // ── 4. Give some time for track propagation ─────────────────────────────
//   await page.waitForTimeout(3_000);

//   // ── 5. Validate every remote browser now has BOTH tracks ────────────────
//   const timeoutMs = 15_000;
//   const intervalMs = 500;

//   for (let i = 0; i < openBrowsers.length; i++) {
//     const br = openBrowsers[i];
//     const remotePage = br.contexts()[0].pages()[0];
//     const initialCount = initialRemoteTrackCounts[i];
//     const initialIds = initialRemoteTrackIds[i];

//     await remotePage.waitForTimeout(10_000);
//     const getTrackInfo = async () =>
//       await remotePage.evaluate(() => {
//         const mixer = (window as any).remoteAudioMixer;
//         if (!mixer || !mixer.participants) return { count: 0, ids: [] as string[] };
//         const ids: string[] = [];
//         (mixer.participants as Map<string, any>).forEach((p: any) => {
//           const stream = p.getStream ? p.getStream() : null;
//           if (stream) {
//             stream.getAudioTracks().forEach((t: any) => {
//               const id = t.deviceId || t.getSettings().deviceId || t.label || t.id;
//               ids.push(id);
//             });
//           }
//         });
//         return { count: ids.length, ids };
//       });

//     let currentCount = initialCount;
//     let currentIds: string[] = [];
//     const start = Date.now();
//     while (Date.now() - start < timeoutMs) {
//       const info = await getTrackInfo();
//       currentCount = info.count;
//       currentIds = info.ids;
//       if (currentCount >= initialCount + 1) break;
//       await remotePage.waitForTimeout(intervalMs);
//     }

//     console.log(`Browser ${i} audio track count before/after:`, initialCount, '→', currentCount);
//     console.log(`Browser ${i} audio track ids before/after:`, initialIds, '→', currentIds);

//     // Expect at least one additional audio track received
//     expect(currentCount).toBeGreaterThanOrEqual(initialCount);

//   }
// }
