import { test, expect } from '@playwright/test';
import { loginAsGuest, signUp, createClassroom, renameClassroom, reliableGoto, cleanupBrowserState } from '../utils';

test.describe('Guest Conversion Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test("Guest Conversion", async ({ page, browserName }) => {
    test.setTimeout(process.env.CI ? 240000 : 120000);
    test.skip(browserName === 'webkit', 'Skipping test for WebKit browser');
 await loginAsGuest(page);
 const classroomId = await createClassroom(page);
 await renameClassroom(page, 'New Name', 'New Description', classroomId);
 await page.waitForTimeout(2000);

  await expect(page.getByTestId('anonymous-warning-link')).toBeVisible();
  await page.getByTestId('anonymous-warning-link').click();
  await signUp(page, '1234abcdABCD!', true, false);
  await reliableG<PERSON>(page, `/classrooms/`);
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await expect(page.getByText('New Name')).toBeVisible();
  });
});