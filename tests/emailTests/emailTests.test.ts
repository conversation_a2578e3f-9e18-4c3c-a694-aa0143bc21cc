import { test, expect } from '@playwright/test';
import { reliable<PERSON><PERSON>, MailSlurpHelper, signIn<PERSON>heck, signUp, cleanupBrowserState } from '../utils';
import { MAILPIT_URL } from '../../src/shared/constants/server';

test.describe('Email Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test('Password change double email test', async ({ page }) => {
    test.setTimeout(process.env.CI ? 240000 : 120000);
  const mailslurpHelper = new MailSlurpHelper({
    baseUrl: MAILPIT_URL,
  });

  const initialPassword = '1Aa@********';
  const password1 = 'NewPassword1@';
  const password2 = 'NewPassword2@';

  const { tempEmail, inboxId } = await signUp(
    page,
    initialPassword,
    false,
    true,
  );
  console.log('Created account with email:', tempEmail);

  console.log('=== STEP 1: Send first password reset email ===');
  await reliableGoto(page, '/');
  await expect(page.getByTestId('sign-in-button')).toBeVisible();
  await page.getByTestId('sign-in-button').click();
  await expect(
    page.getByTestId('sign-in-forgot-password-button'),
  ).toBeVisible();
  await page.getByTestId('sign-in-forgot-password-button').click();
  await expect(
    page.getByTestId('sign-in-forgot-password-email-input'),
  ).toBeVisible();
  await page
    .getByTestId('sign-in-forgot-password-email-input')
    .fill(tempEmail!);
  await page.getByTestId('sign-in-forgot-password-submit-button').click();

  await page.waitForTimeout(5000);
  const passwordResetResult1 = await mailslurpHelper.getPasswordResetEmail(
    inboxId,
    60000,
  );
  expect(passwordResetResult1.success).toBe(true);
  expect(passwordResetResult1.verificationLink).toBeTruthy();
  const resetLink1 = passwordResetResult1.verificationLink!;
  console.log('First reset link obtained:', resetLink1);

  console.log('=== STEP 2: Send second password reset email ===');
  await reliableGoto(page, '/');
  await expect(page.getByTestId('sign-in-button')).toBeVisible();
  await page.getByTestId('sign-in-button').click();
  await expect(
    page.getByTestId('sign-in-forgot-password-button'),
  ).toBeVisible();
  await page.getByTestId('sign-in-forgot-password-button').click();
  await expect(
    page.getByTestId('sign-in-forgot-password-email-input'),
  ).toBeVisible();
  await page
    .getByTestId('sign-in-forgot-password-email-input')
    .fill(tempEmail!);
  await page.keyboard.press('Enter');

  await page.waitForTimeout(3000);
  const passwordResetResult2 = await mailslurpHelper.getPasswordResetEmail(
    inboxId,
    60000,
  );
  expect(passwordResetResult2.success).toBe(true);
  expect(passwordResetResult2.verificationLink).toBeTruthy();
  const resetLink2 = passwordResetResult2.verificationLink!;
  console.log('Second reset link obtained:', resetLink2);

  expect(resetLink1).not.toBe(resetLink2);

  console.log('=== STEP 3: Follow second link and change password ===');
  await page.goto(resetLink2);
  await page.waitForLoadState('domcontentloaded');

  await expect(page.getByTestId('sign-up-password-input')).toBeVisible();
  await expect(
    page.getByTestId('sign-up-confirm-password-input'),
  ).toBeVisible();

  await page.getByTestId('sign-up-password-input').fill(password2);
  await page.getByTestId('sign-up-confirm-password-input').fill(password2);
  await page.getByTestId('sign-up-reset-password-submit-button').click();

  await page.waitForTimeout(5000);
  console.log('Password changed using second reset link');

  console.log('=== STEP 4: Test login with new password (password2) ===');
  await signInCheck(page, tempEmail!, password2, true);
  console.log('Login with password2 successful');

  console.log('=== STEP 5: Try to use first reset link (should fail) ===');
  await expect(page.getByTestId('sidebar-trigger')).toBeVisible();
  await page.getByTestId('sidebar-trigger').click();
  await expect(page.getByTestId('sidebar-profile-button')).toBeVisible();
  await page.getByTestId('sidebar-profile-button').click();
  await expect(page.getByTestId('sidebar-profile-logout')).toBeVisible();
  await page.getByTestId('sidebar-profile-logout').click();
  await page.waitForTimeout(2000);

  await page.goto(resetLink1);
  await page.waitForLoadState('domcontentloaded');

  try {
    await expect(page.getByTestId('sign-up-password-input')).not.toBeVisible({
      timeout: 5000,
    });
    console.log('First reset link correctly invalidated');
  } catch {
    await page.getByTestId('sign-up-password-input').fill(password1);
    await page.getByTestId('sign-up-confirm-password-input').fill(password1);
    await page.getByTestId('sign-up-reset-password-submit-button').click();
    await page.waitForTimeout(2000);
    console.log('First reset link failed as expected');
  }

  console.log('=== STEP 6: Test login attempts with different passwords ===');

  await signInCheck(page, tempEmail!, initialPassword, false);
  console.log('Login with initial password failed as expected');

  await signInCheck(page, tempEmail!, password1, false);
  console.log('Login with password1 failed as expected');

  await signInCheck(page, tempEmail!, password2, true);
  console.log('Login with password2 successful as expected');

  console.log('=== Two-email password reset test completed successfully ===');
});

test('Sign up double email verification test', async ({ page }) => {
    test.setTimeout(process.env.CI ? 240000 : 120000);
  const mailslurpHelper = new MailSlurpHelper({
    baseUrl: MAILPIT_URL,
  });

  let inboxId: string | null = null;
  let tempEmail: string | null = null;

  try {
    console.log('Creating temporary email inbox...');
    const inbox = await mailslurpHelper.createInbox();
    inboxId = inbox.id;
    tempEmail = inbox.emailAddress;
    const password = '1234abcdABCD!';
    console.log(`Created inbox: ${tempEmail}`);

    await reliableGoto(page, '/');
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await page.getByTestId('sign-in-button').click();
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('dialog-sign-up-button')).toBeVisible();
    await page.getByTestId('dialog-sign-up-button').click();
    await page.waitForLoadState('domcontentloaded');

    console.log('=== STEP 1-3: Complete sign up form ===');

    await expect(page.getByTestId('sign-up-full-name-input')).toBeVisible();
    await expect(page.getByTestId('sign-up-email-input')).toBeVisible();
    await page.getByTestId('sign-up-email-input').click();
    await page.getByTestId('sign-up-email-input').fill(tempEmail!);
    await page.getByTestId('sign-up-full-name-input').click();
    await page.getByTestId('sign-up-full-name-input').fill('Test User');
    await expect(page.getByTestId('sign-up-password-input')).toBeVisible();
    await page.getByTestId('sign-up-password-input').click();
    await page.getByTestId('sign-up-password-input').fill(password);
    await expect(
      page.getByTestId('sign-up-confirm-password-input'),
    ).toBeVisible();
    await page.getByTestId('sign-up-confirm-password-input').click();
    await page.getByTestId('sign-up-confirm-password-input').fill(password);
    await page.getByTestId('sign-up-step-1-next-button').click();

    await expect(page.getByTestId('sign-up-terms-checkbox')).toBeVisible();
    await page.getByTestId('sign-up-step-2-next-button').click();
    await expect(page.getByTestId('sign-up-terms-error')).toBeVisible();
    await page.getByTestId('sign-up-terms-checkbox').click();
    await expect(page.getByTestId('sign-up-terms-error')).not.toBeVisible();
    await page.getByTestId('sign-up-step-2-next-button').click();

    await expect(page.getByTestId('sign-up-step-3-next-button')).toBeVisible();
    await page.getByTestId('sign-up-step-3-next-button').click();

    console.log('=== STEP 4: Submit and wait for first email ===');
    await expect(page.getByTestId('sign-up-step-4-next-button')).toBeVisible();
    await expect(page.getByText(tempEmail!)).toBeVisible();
    await page.getByTestId('sign-up-step-4-next-button').click();
    await page.waitForTimeout(2000);

    console.log('Waiting for first verification email...');
    const emailResult1 = await mailslurpHelper.getEmailVerification(
      inboxId,
      60000,
    );
    expect(emailResult1.success).toBe(true);
    expect(emailResult1.verificationLink).toBeTruthy();
    const link1 = emailResult1.verificationLink!;
    console.log('First verification link received:', link1);

    console.log('=== STEP 5: Wait for resend button and click it ===');
    let resendButtonEnabled = false;
    for (let i = 0; i < 30; i++) {
      try {
        const resendButton = page.getByTestId('resend-button');
        if (
          (await resendButton.isVisible()) &&
          (await resendButton.isEnabled())
        ) {
          console.log(`Resend button enabled after ${i} seconds`);
          await resendButton.click();
          resendButtonEnabled = true;
          break;
        }
      } catch (e) {
        console.log('Error during double email test:', e);
      }
      await page.waitForTimeout(1000);
    }

    expect(resendButtonEnabled).toBe(true);
    console.log('Resend button clicked successfully');

    console.log('Waiting for second verification email...');
    await page.waitForTimeout(2000);
    const emailResult2 = await mailslurpHelper.getEmailVerification(
      inboxId,
      60000,
    );
    expect(emailResult2.success).toBe(true);
    expect(emailResult2.verificationLink).toBeTruthy();
    const link2 = emailResult2.verificationLink!;
    console.log('Second verification link received:', link2);

    expect(link1).not.toBe(link2);
    console.log('Verification links are different');

    console.log('=== STEP 6: Follow second link and verify account ===');
    await page.goto(link2);
    await page.waitForLoadState('domcontentloaded');
    await expect(page).toHaveURL(
      /.*verify.*|.*success.*|.*login.*|.*dashboard.*/,
    );
    console.log('Second link verification completed successfully!');

    console.log('=== STEP 7: Test successful login ===');
    await reliableGoto(page, '/');
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await page.getByTestId('sign-in-button').click();
    await expect(page.getByTestId('sign-in-email-input')).toBeVisible();
    await page.getByTestId('sign-in-email-input').fill(tempEmail!);
    await page.getByTestId('sign-in-password-input').fill(password);
    await page.getByTestId('dialog-sign-in-button').click();
    await expect(page.getByTestId('create-classroom-button')).toBeVisible({
      timeout: 5000,
    });
    console.log('Login successful with second link');

    console.log('=== STEP 8: Test first link is expired ===');
    await page.goto(link1);
    await page.waitForLoadState('domcontentloaded');

    const expiredTexts = [
      'link expired',
      'expired',
      'invalid',
      'invalid or expired',
      'link has expired',
      'verification token',
      'no longer valid',
    ];

    let foundExpiredMessage = false;
    for (const text of expiredTexts) {
      try {
        await expect(page.getByText(new RegExp(text, 'i'))).toBeVisible({
          timeout: 2000,
        });
        console.log(`Found expired link message: "${text}"`);
        foundExpiredMessage = true;
        break;
      } catch (e) {
        console.log('Error during double email test:', e);
      }
    }

    expect(foundExpiredMessage).toBe(true);
    console.log('First link correctly shows as expired');

    console.log(
      '=== Double email verification test completed successfully ===',
    );
  } catch (error) {
    console.error('Error during double email test:', error);
    throw error;
  }
});

test('Sign up double email verification test on login dialog', async ({
  page,
}) => {
    test.setTimeout(process.env.CI ? 240000 : 120000);
  const mailslurpHelper = new MailSlurpHelper({
    baseUrl: MAILPIT_URL,
  });

  let inboxId: string | null = null;
  let tempEmail: string | null = null;

  try {
    console.log('Creating temporary email inbox...');
    const inbox = await mailslurpHelper.createInbox();
    inboxId = inbox.id;
    tempEmail = inbox.emailAddress;
    const password = '1234abcdABCD!';
    console.log(`Created inbox: ${tempEmail}`);

    await reliableGoto(page, '/');
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await page.getByTestId('sign-in-button').click();
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('dialog-sign-up-button')).toBeVisible();
    await page.getByTestId('dialog-sign-up-button').click();
    await page.waitForLoadState('domcontentloaded');

    console.log('=== STEP 1-3: Complete sign up form ===');

    await expect(page.getByTestId('sign-up-full-name-input')).toBeVisible();
    await expect(page.getByTestId('sign-up-email-input')).toBeVisible();
    await page.getByTestId('sign-up-email-input').click();
    await page.getByTestId('sign-up-email-input').fill(tempEmail!);
    await page.getByTestId('sign-up-full-name-input').click();
    await page.getByTestId('sign-up-full-name-input').fill('Test User');
    await expect(page.getByTestId('sign-up-password-input')).toBeVisible();
    await page.getByTestId('sign-up-password-input').click();
    await page.getByTestId('sign-up-password-input').fill(password);
    await expect(
      page.getByTestId('sign-up-confirm-password-input'),
    ).toBeVisible();
    await page.getByTestId('sign-up-confirm-password-input').click();
    await page.getByTestId('sign-up-confirm-password-input').fill(password);
    await page.getByTestId('sign-up-step-1-next-button').click();

    await expect(page.getByTestId('sign-up-terms-checkbox')).toBeVisible();
    await page.getByTestId('sign-up-step-2-next-button').click();
    await expect(page.getByTestId('sign-up-terms-error')).toBeVisible();
    await page.getByTestId('sign-up-terms-checkbox').click();
    await expect(page.getByTestId('sign-up-terms-error')).not.toBeVisible();
    await page.getByTestId('sign-up-step-2-next-button').click();

    await expect(page.getByTestId('sign-up-step-3-next-button')).toBeVisible();
    await page.getByTestId('sign-up-step-3-next-button').click();

    console.log('=== STEP 4: Submit and wait for first email ===');
    await expect(page.getByTestId('sign-up-step-4-next-button')).toBeVisible();
    await expect(page.getByText(tempEmail!)).toBeVisible();
    await page.getByTestId('sign-up-step-4-next-button').click();
    await page.waitForTimeout(2000);

    console.log('Waiting for first verification email...');
    const emailResult1 = await mailslurpHelper.getEmailVerification(
      inboxId,
      60000,
    );
    expect(emailResult1.success).toBe(true);
    expect(emailResult1.verificationLink).toBeTruthy();
    const link1 = emailResult1.verificationLink!;
    console.log('First verification link received:', link1);

    console.log('=== STEP 5: Try to login and use resend button ===');
    await reliableGoto(page, '/');
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await page.getByTestId('sign-in-button').click();
    await expect(page.getByTestId('sign-in-email-input')).toBeVisible();
    await page.getByTestId('sign-in-email-input').fill(tempEmail!);
    await page.getByTestId('sign-in-password-input').fill(password);
    await page.getByTestId('dialog-sign-in-button').click();

    // Login should fail because email is not verified, and resend button should appear
    await page.waitForTimeout(2000);

    // Wait up to 30 seconds for resend button to be enabled
    let resendButtonEnabled = false;
    for (let i = 0; i < 30; i++) {
      try {
        const resendButton = page.getByTestId('resend-button');
        if (
          (await resendButton.isVisible()) &&
          (await resendButton.isEnabled())
        ) {
          console.log(`Resend button enabled after ${i} seconds`);
          await resendButton.click();
          resendButtonEnabled = true;
          break;
        }
      } catch (e) {
        console.log('Error during double email test:', e);
      }
      await page.waitForTimeout(1000);
    }

    expect(resendButtonEnabled).toBe(true);
    console.log('Resend button clicked successfully');

    console.log('Waiting for second verification email...');
    await page.waitForTimeout(2000);
    const emailResult2 = await mailslurpHelper.getEmailVerification(
      inboxId,
      60000,
    );
    expect(emailResult2.success).toBe(true);
    expect(emailResult2.verificationLink).toBeTruthy();
    const link2 = emailResult2.verificationLink!;
    console.log('Second verification link received:', link2);

    // Verify links are different
    expect(link1).not.toBe(link2);
    console.log('Verification links are different');

    console.log('=== STEP 6: Follow second link and verify account ===');
    await page.goto(link2);
    await page.waitForLoadState('domcontentloaded');
    await expect(page).toHaveURL(
      /.*verify.*|.*success.*|.*login.*|.*dashboard.*/,
    );
    console.log('Second link verification completed successfully!');

    console.log('=== STEP 7: Test successful login ===');
    await reliableGoto(page, '/');
    await page.waitForTimeout(5000);
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await page.getByTestId('sign-in-button').click();
    await expect(page.getByTestId('sign-in-email-input')).toBeVisible();
    await page.getByTestId('sign-in-email-input').fill(tempEmail!);
    await page.getByTestId('sign-in-password-input').fill(password);
    await page.getByTestId('dialog-sign-in-button').click();
    await expect(page.getByTestId('create-classroom-button')).toBeVisible({
      timeout: 5000,
    });
    console.log('Login successful with second link');

    console.log('=== STEP 8: Test first link is expired ===');
    await page.goto(link1);
    await page.waitForLoadState('domcontentloaded');

    const expiredTexts = [
      'link expired',
      'expired',
      'invalid',
      'invalid or expired',
      'link has expired',
      'verification token',
      'no longer valid',
    ];

    let foundExpiredMessage = false;
    for (const text of expiredTexts) {
      try {
        await expect(page.getByText(new RegExp(text, 'i'))).toBeVisible({
          timeout: 2000,
        });
        console.log(`Found expired link message: "${text}"`);
        foundExpiredMessage = true;
        break;
      } catch (e) {
        console.log('Error during double email test:', e);
      }
    }

    expect(foundExpiredMessage).toBe(true);
    console.log('First link correctly shows as expired');

    console.log(
      '=== Double email verification test completed successfully ===',
    );
  } catch (error) {
    console.error('Error during double email test:', error);
    throw error;
  }
});
});
