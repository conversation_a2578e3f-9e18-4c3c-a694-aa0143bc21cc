import { test, expect } from "@playwright/test";
import { reliableGoto, loginAsGuest, signUp, cleanupBrowserState } from "../utils";

test.describe('Delete Account Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test('delete account with guest account', async ({ page }) => {
    test.setTimeout(process.env.CI ? 240000 : 120000);
    console.log('=== STEP 1: Login as guest and navigate to profile ===');
    await reliableGoto(page, '/');
    await loginAsGuest(page);
    await expect(page.getByTestId("sidebar-trigger")).toBeVisible();
    await page.getByTestId("sidebar-trigger").click();
    await expect(page.getByTestId("sidebar-profile-button")).toBeVisible();
    await page.getByTestId("sidebar-profile-button").click();
    await expect(page.getByTestId("sidebar-profile-dropdown-profile-link")).toBeVisible();
    await page.getByTestId("sidebar-profile-dropdown-profile-link").click();
    await page.waitForLoadState('domcontentloaded');
    await page.waitForLoadState('networkidle');
    console.log('Successfully navigated to profile page');

    console.log('=== STEP 2: Open delete account dialog ===');
    await expect(page.getByTestId("delete-account-trigger-button")).toBeVisible();
    await page.getByTestId("delete-account-trigger-button").click();
    await expect(page.getByTestId("delete-account-dialog")).toBeVisible();
    console.log('Delete account dialog opened successfully');

    console.log('=== STEP 3: Test terms and conditions link ===');
    await expect(page.getByTestId("delete-account-terms-link")).toBeVisible();
    

    const [newPage] = await Promise.all([
        page.waitForEvent('popup'),
        page.getByTestId("delete-account-terms-link").click()
    ]);
    
    await newPage.waitForLoadState('domcontentloaded');
    expect(newPage.url()).toContain('/legal/terms-and-conditions');
    console.log('Terms and conditions link opened successfully');
    await newPage.close();

    console.log('=== STEP 4: Test form validation - null submission ===');

    await expect(page.getByTestId("delete-account-confirm-button")).toBeVisible();
    console.log('Confirm button correctly disabled when form is empty');

    console.log('=== STEP 5: Test form validation - invalid display name ===');
    await expect(page.getByTestId("delete-account-displayname-input")).toBeVisible();
    await page.getByTestId("delete-account-displayname-input").fill("WrongName");
    

    if (await page.getByTestId("delete-account-confirm-button").isEnabled()) {
        await page.getByTestId("delete-account-confirm-button").click();
        await page.waitForTimeout(1000);

        await expect(page.getByTestId("notification-close-button")).toBeVisible();
        await page.getByTestId("notification-close-button").click();
        console.log('Invalid display name correctly rejected');
    } else {
        console.log('Confirm button correctly disabled for invalid display name');
    }

    console.log('=== STEP 6: Test form validation - valid display name ===');

    await page.getByTestId("delete-account-displayname-input").clear();
    await page.getByTestId("delete-account-displayname-input").fill("Test1");
    

    await expect(page.getByTestId("delete-account-confirm-button")).toBeEnabled();
    await page.getByTestId("delete-account-confirm-button").click();
    

    await page.waitForTimeout(3000);
    console.log('Account deletion submitted with valid display name');

    console.log('=== STEP 7: Verify account is deleted - /classrooms should be unreachable ===');

    await reliableGoto(page, '/classrooms');
    await page.waitForTimeout(3000);
    await page.waitForLoadState('domcontentloaded');
    

    expect(page.url()).not.toContain('/classrooms');
    console.log('Successfully verified that /classrooms is unreachable after account deletion');
    

    const currentUrl = page.url();
    const isOnHomeOrLogin = currentUrl.includes('/') && !currentUrl.includes('/classrooms') && !currentUrl.includes('/profile');
    expect(isOnHomeOrLogin).toBe(true);
    console.log('User correctly redirected away from protected routes');

    console.log('=== Delete account test completed successfully ===');
});

test('delete account with full access account', async ({ page }) => {
    test.setTimeout(process.env.CI ? 240000 : 120000);
    console.log('=== STEP 1: Create full access account and navigate to profile ===');
    await reliableGoto(page, '/');
    await signUp(page, '1234567890Aa$', true, true);
    await expect(page.getByTestId("sidebar-trigger")).toBeVisible();
    await page.getByTestId("sidebar-trigger").click();
    await expect(page.getByTestId("sidebar-profile-button")).toBeVisible();
    await page.getByTestId("sidebar-profile-button").click();
    await expect(page.getByTestId("sidebar-profile-dropdown-profile-link")).toBeVisible();
    await page.getByTestId("sidebar-profile-dropdown-profile-link").click();
    await page.waitForLoadState('domcontentloaded');
    await page.waitForLoadState('networkidle');
    console.log('Successfully navigated to profile page');

    console.log('=== STEP 2: Open delete account dialog ===');
    await expect(page.getByTestId("delete-account-trigger-button")).toBeVisible();
    await page.getByTestId("delete-account-trigger-button").click();
    await expect(page.getByTestId("delete-account-dialog")).toBeVisible();
    console.log('Delete account dialog opened successfully');

    console.log('=== STEP 3: Test terms and conditions link ===');
    await expect(page.getByTestId("delete-account-terms-link")).toBeVisible();
    

    const [newPage] = await Promise.all([
        page.waitForEvent('popup'),
        page.getByTestId("delete-account-terms-link").click()
    ]);
    
    await newPage.waitForLoadState('domcontentloaded');
    expect(newPage.url()).toContain('/legal/terms-and-conditions');
    console.log('Terms and conditions link opened successfully');
    await newPage.close();

    console.log('=== STEP 4: Test form validation - null submission ===');

    await expect(page.getByTestId("delete-account-confirm-button")).toBeVisible();
    console.log('Confirm button correctly disabled when form is empty');

    console.log('=== STEP 5: Test form validation - invalid password ===');
    await expect(page.getByTestId("delete-account-password-input")).toBeVisible();
    await page.getByTestId("delete-account-password-input").fill("WrongPassword123!");
    

    await expect(page.getByTestId("delete-account-confirm-button")).toBeEnabled();
    await page.getByTestId("delete-account-confirm-button").click();
    await page.waitForTimeout(2000);
    

    await expect(page.getByTestId("notification-close-button")).toBeVisible();
    await page.getByTestId("notification-close-button").click();
    await expect(page.getByTestId("delete-account-dialog")).toBeVisible();
    console.log('Invalid password correctly rejected');

    console.log('=== STEP 6: Test form validation - valid password ===');

    await page.getByTestId("delete-account-password-input").clear();
    await page.getByTestId("delete-account-password-input").fill("1234567890Aa$");
    

    await expect(page.getByTestId("delete-account-confirm-button")).toBeEnabled();
    await page.getByTestId("delete-account-confirm-button").click();
    

    await page.waitForTimeout(3000);
    console.log('Account deletion submitted with valid password');

    console.log('=== STEP 7: Verify account is deleted - /classrooms should be unreachable ===');

    await reliableGoto(page, '/classrooms');
    await page.waitForTimeout(3000);
    await page.waitForLoadState('domcontentloaded');
    

    expect(page.url()).not.toContain('/classrooms');
    console.log('Successfully verified that /classrooms is unreachable after account deletion');
    

    const currentUrl = page.url();
    const isOnHomeOrLogin = currentUrl.includes('/') && !currentUrl.includes('/classrooms') && !currentUrl.includes('/profile');
    expect(isOnHomeOrLogin).toBe(true);
    console.log('User correctly redirected away from protected routes');

    console.log('=== STEP 8: Verify account is deleted - login should fail ===');
    // await signInCheck(page, tempEmail!, '1234567890Aa$', false);
    console.log('Successfully verified that login fails after account deletion');

    console.log('=== Delete account test completed successfully ===');
  });
});