import { test, expect } from '@playwright/test';
import { reliableGoto, InputValidationTester, MailSlurpHelper, updateUserSettings, signInCheck, cleanupBrowserState } from '../utils';
import { MAILPIT_URL } from '../../src/shared/constants/server';

test.describe('Sign Up Email Tests', () => {
  test.afterEach(async ({ page, context }) => {
    await cleanupBrowserState(page, context);
  });

  test("Complete Sign up with email verification and password reset", async ({ page, browserName }) => {
    test.setTimeout(process.env.CI ? 240000 : 120000);
    test.skip(browserName === 'webkit');

  // This test checks usual sign up flow and password reset flow
  const password = '1234abcdABCD!';
  const passwordModified = '1234abcdABCD!1';
  const passwordForSecondSignUp = '1234abcdABCD!2';
  const mailslurpHelper = new MailSlurpHelper({
    baseUrl: MAILPIT_URL,
  });

  let inboxId: string | null = null;
  let tempEmail: string | null = null;

  try {
    console.log('Creating temporary email inbox...');
    const inbox = await mailslurpHelper.createInbox();
    inboxId = inbox.id;
    tempEmail = inbox.emailAddress;
    console.log(`Created inbox: ${tempEmail}`);

    console.log('=== STEP 1: ACCOUNT CREATION & EMAIL VERIFICATION ===');

    await reliableGoto(page, '/');
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await page.getByTestId('sign-in-button').click();
    await expect(page.getByTestId('dialog-sign-up-button')).toBeVisible();
    await page.getByTestId('dialog-sign-up-button').click();
    await expect(page.getByTestId('sign-up-full-name-input')).toBeVisible();

    await page.getByTestId('sign-up-locale-trigger').click();
    await page.getByTestId('sign-up-locale-option-ru').click();

    // Test display name validation
    const displayNameTester = new InputValidationTester(page, {
      inputTestId: 'sign-up-full-name-input',
      submitButtonTestId: 'sign-up-step-1-next-button',
      errorElementTestId: 'sign-up-full-name-error',
      validationMode: 'submit',
      disableButtonOnInvalid: false,
      useI18nKeyMatching: false,
      language: 'en',
    });

    await displayNameTester.testConstraints([
      {
        name: 'empty full name',
        value: '',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: true,
        shouldPreventSubmission: true,
        errorMessageKey: 'Display Name is required',
      },
      {
        name: 'full name too short',
        value: 'ab',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: true,
        shouldPreventSubmission: true,
        errorMessageKey: 'Display Name must be at least 3 characters',
      },
      {
        name: 'full name too long',
        value: 'VeryLongFullNameThatExceedsLimit',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: true,
        shouldPreventSubmission: true,
        errorMessageKey: 'Display Name must be at most 20 characters',
      },
      {
        name: 'valid full name',
        value: 'John Test User',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: false,
        shouldPreventSubmission: false,
      },
    ]);

    // Test email validation
    await expect(page.getByTestId('sign-up-email-input')).toBeVisible();
    await page.getByTestId('sign-up-email-input').click();

    const emailTester = new InputValidationTester(page, {
      inputTestId: 'sign-up-email-input',
      submitButtonTestId: 'sign-up-step-1-next-button',
      errorElementTestId: 'sign-up-email-error',
      validationMode: 'submit',
      disableButtonOnInvalid: false,
      useI18nKeyMatching: false,
      language: 'en',
    });

    await emailTester.testConstraints([
      {
        name: 'empty email',
        value: '',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: true,
        shouldPreventSubmission: true,
        errorMessageKey: 'globalFields.email.validation.required',
      },
      {
        name: 'email too short',
        value: 'ab',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: true,
        shouldPreventSubmission: true,
        errorMessageKey: 'globalFields.email.validation.minLength',
      },
      {
        name: 'email too long',
        value: '<EMAIL>',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: true,
        shouldPreventSubmission: true,
        errorMessageKey: 'globalFields.email.validation.maxLength',
      },
      {
        name: 'invalid email format',
        value: 'invalid-email-format',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: true,
        shouldPreventSubmission: true,
        errorMessageKey: 'globalFields.email.validation.invalid',
      },
      {
        name: 'valid temp email',
        value: tempEmail!,
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: false,
        shouldPreventSubmission: false,
      },
    ]);

    // Test password validation
    await expect(page.getByTestId('sign-up-password-input')).toBeVisible();
    await page.getByTestId('sign-up-password-input').click();

    const passwordTester = new InputValidationTester(page, {
      inputTestId: 'sign-up-password-input',
      submitButtonTestId: 'sign-up-step-1-next-button',
      errorElementTestId: 'sign-up-password-error',
      validationMode: 'submit',
      disableButtonOnInvalid: false,
      useI18nKeyMatching: false,
      language: 'en',
    });

    await passwordTester.testConstraints([
      {
        name: 'empty password',
        value: '',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: true,
        shouldPreventSubmission: true,
        errorMessageKey: 'Password is required',
      },
      {
        name: 'password too short',
        value: '123456',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: true,
        shouldPreventSubmission: true,
        errorMessageKey: 'Password must be at least 8 characters long',
      },
      {
        name: 'password too long',
        value:
          '12345678901234567890123456789012345678901234567890123456789012345678901234567890',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: true,
        shouldPreventSubmission: true,
        errorMessageKey: 'Password must be at most 50 characters',
      },
      {
        name: 'password too weak',
        value: '12345678',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: true,
        shouldPreventSubmission: true,
        errorMessageKey:
          'Password must contain at least one uppercase letter, one lowercase letter, and one number',
      },
      {
        name: 'valid password',
        value: password,
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: false,
        shouldPreventSubmission: false,
      },
    ]);

    // Test confirm password validation
    await expect(
      page.getByTestId('sign-up-confirm-password-input'),
    ).toBeVisible();
    await page.getByTestId('sign-up-confirm-password-input').click();

    const confirmPasswordTester = new InputValidationTester(page, {
      inputTestId: 'sign-up-confirm-password-input',
      submitButtonTestId: 'sign-up-step-1-next-button',
      errorElementTestId: 'sign-up-confirm-password-error',
      validationMode: 'submit',
      disableButtonOnInvalid: false,
      useI18nKeyMatching: false,
      language: 'en',
    });

    await confirmPasswordTester.testConstraints([
      {
        name: 'empty confirm password',
        value: '',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: true,
        shouldPreventSubmission: true,
        errorMessageKey: 'Confirm Password is required',
      },
      {
        name: 'confirm password does not match',
        value: '1',
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: true,
        shouldPreventSubmission: true,
        errorMessageKey: 'Passwords do not match',
      },
      {
        name: 'confirm password matches',
        value: password,
        validationTrigger: 'submit',
        shouldShowErrorAfterSubmit: false,
        shouldPreventSubmission: false,
      },
    ]);

    // Complete signup form
    await expect(page.getByTestId('sign-up-terms-checkbox')).toBeVisible();
    await page.getByTestId('sign-up-step-2-next-button').click();

    await expect(page.getByTestId('sign-up-terms-error')).toBeVisible();
    await page.getByTestId('sign-up-terms-checkbox').click();
    await expect(page.getByTestId('sign-up-terms-error')).not.toBeVisible();
    await page.getByTestId('sign-up-step-2-next-button').click();

    await expect(page.getByTestId('sign-up-step-3-next-button')).toBeVisible();
    await page.getByTestId('sign-up-step-3-next-button').click();

    await expect(page.getByTestId('sign-up-step-4-next-button')).toBeVisible();
    await expect(page.getByText(tempEmail!)).toBeVisible();

    console.log('Submitting signup form...');
    await page.getByTestId('sign-up-step-4-next-button').click();
    await page.waitForTimeout(2000);

    await signInCheck(page, tempEmail!, password, false);

    // Wait for verification email (small buffer to allow SMTP delivery)
    console.log('Waiting for verification email...');
    await page.waitForTimeout(10000);
    const emailResult = await mailslurpHelper.getEmailVerification(
      inboxId,
      60000,
    );

    expect(emailResult.success).toBe(true);
    expect(emailResult.verificationLink).toBeTruthy();

    console.log('Verification email received!');
    console.log('Verification link:', emailResult.verificationLink);

    if (emailResult.verificationLink) {
      console.log('Clicking verification link...');
      await page.goto(emailResult.verificationLink);

      await page.waitForLoadState('domcontentloaded');
      await expect(page).toHaveURL(
        /.*verify.*|.*success.*|.*login.*|.*dashboard.*/,
      );

      console.log('Email verification completed successfully!');
    }

    // Login with verified account
    if (page.url().includes('login') || page.url().includes('signin')) {
      console.log('Attempting to login with verified account...');

      const emailInput = page.getByTestId('sign-in-email-input');
      const passwordInput = page.getByTestId('sign-in-password-input');
      const loginButton = page.getByTestId('sign-in-button');

      if (await emailInput.isVisible()) {
        await emailInput.fill(tempEmail!);
        await passwordInput.fill(password);
        await loginButton.click();

        await expect(
          page.getByTestId('onboarding-dialog-dashboard-button'),
        ).toBeVisible();
        await page.getByTestId('onboarding-dialog-dashboard-button').click();
        await page.waitForURL('/classrooms', { timeout: 15000 });
        console.log('Login successful after email verification!');
      }
    }

    const currentUrl = page.url();
    const remoteUrl = new URL(currentUrl);

    const localOrigin =
      process.env.LOCAL_APP_ORIGIN || 'https://localhost:5173';
    const localBase = new URL(localOrigin);

    const localAbsoluteUrl = `${localBase.origin}${remoteUrl.pathname}${remoteUrl.search}${remoteUrl.hash}`;

    await reliableGoto(page, localAbsoluteUrl);
    await page.waitForTimeout(3000);
    await page.getByTestId('onboarding-dialog-dashboard-button').click();
    await page.waitForTimeout(3000);

    console.log(
      '=== STEP 1 COMPLETED: ACCOUNT CREATION & EMAIL VERIFICATION ===',
    );

    console.log('=== STEP 2: VERIFY PROFILE INFORMATION ===');

    // Navigate to profile page via sidebar to verify user information
    console.log('Opening sidebar and navigating to profile...');

    await page.waitForTimeout(1000);
    // Open sidebar
    const sidebarTrigger = page.getByTestId('sidebar-trigger');
    await expect(sidebarTrigger).toBeVisible({ timeout: 15000 });
    await sidebarTrigger.click();

    // Click on profile button in sidebar
    await page.getByTestId('sidebar-profile-button').click();
    await expect(
      page.getByTestId('sidebar-profile-dropdown-profile-link'),
    ).toBeVisible({ timeout: 15000 });
    await page.getByRole('menuitem', { name: /профиль/i }).click();

    // Wait for profile page to load
    await page.waitForLoadState('domcontentloaded');

    // Verify profile information matches what was entered during signup
    console.log('Verifying profile information...');

    // Check display name
    await expect(
      page.getByTestId('profile-basic-information-display-name'),
    ).toHaveText('John Test User');

    // Check email
    await expect(
      page.getByTestId('profile-basic-information-email'),
    ).toHaveText(tempEmail!);

    // Check locale/language preference
    await expect(
      page.getByTestId('profile-advanced-information-language-locale'),
    ).toHaveText('Русский');

    console.log('Profile information verified successfully!');

    await updateUserSettings(page, true, {
      displayName: 'John Test User',
      locale: 'en',
      speaking: 'en',
      translation: 'en',
    });

    await page.waitForTimeout(1000);
    await page.getByTestId('sidebar-profile-button').click();
    await expect(
      page.getByTestId('sidebar-profile-dropdown-profile-link'),
    ).toBeVisible({ timeout: 15000 });
    await page.getByTestId('sidebar-profile-logout').click();
    await expect(page.getByTestId('sign-in-button')).toBeVisible();

    console.log('=== STEP 2 COMPLETED: PROFILE INFORMATION VERIFICATION ===');

    console.log('=== STEP 3: PASSWORD RESET & LOGIN WITH NEW PASSWORD ===');

    await reliableGoto(page, '/');
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await page.getByTestId('sign-in-button').click();
    await expect(
      page.getByTestId('sign-in-forgot-password-button'),
    ).toBeVisible();
    await page.getByTestId('sign-in-forgot-password-button').click();
    await expect(
      page.getByTestId('sign-in-forgot-password-email-input'),
    ).toBeVisible();
    await page
      .getByTestId('sign-in-forgot-password-email-input')
      .fill(tempEmail!);
    await page.getByTestId('sign-in-forgot-password-submit-button').click();

    console.log('Waiting for password reset email...');
    await page.waitForTimeout(5000);
    const passwordResetResult = await mailslurpHelper.getPasswordResetEmail(
      inboxId,
      60000,
    );

    expect(passwordResetResult.success).toBe(true);
    expect(passwordResetResult.verificationLink).toBeTruthy();

    console.log('Password reset email received!');
    console.log('Password reset link:', passwordResetResult.verificationLink);

    const resetLink = passwordResetResult.verificationLink;
    if (resetLink) {
      console.log('Clicking password reset link...');
      await page.goto(resetLink);

      await page.waitForLoadState('domcontentloaded');

      await expect(page.getByTestId('sign-up-password-input')).toBeVisible();
      await page.getByTestId('sign-up-password-input').click();

      const resetPasswordTester = new InputValidationTester(page, {
        inputTestId: 'sign-up-password-input',
        submitButtonTestId: 'sign-up-reset-password-submit-button',
        errorElementTestId: 'sign-up-password-error',
        validationMode: 'submit',
        disableButtonOnInvalid: false,
        useI18nKeyMatching: false,
        language: 'en',
      });

      await resetPasswordTester.testConstraints([
        {
          name: 'empty password',
          value: '',
          validationTrigger: 'submit',
          shouldShowErrorAfterSubmit: true,
          shouldPreventSubmission: true,
          errorMessageKey: 'Password is required',
        },
        {
          name: 'password too short',
          value: '123456',
          validationTrigger: 'submit',
          shouldShowErrorAfterSubmit: true,
          shouldPreventSubmission: true,
          errorMessageKey: 'Password must be at least 8 characters long',
        },
        {
          name: 'password too long',
          value:
            '12345678901234567890123456789012345678901234567890123456789012345678901234567890',
          validationTrigger: 'submit',
          shouldShowErrorAfterSubmit: true,
          shouldPreventSubmission: true,
          errorMessageKey: 'Password must be at most 50 characters',
        },
        {
          name: 'password too weak',
          value: '12345678',
          validationTrigger: 'submit',
          shouldShowErrorAfterSubmit: true,
          shouldPreventSubmission: true,
          errorMessageKey:
            'Password must contain at least one uppercase letter, one lowercase letter, and one number',
        },
      ]);

      const resetConfirmPasswordTester = new InputValidationTester(page, {
        inputTestId: 'sign-up-confirm-password-input',
        submitButtonTestId: 'sign-up-reset-password-submit-button',
        errorElementTestId: 'sign-up-confirm-password-error',
        validationMode: 'submit',
        disableButtonOnInvalid: false,
        useI18nKeyMatching: false,
        language: 'en',
      });

      await resetConfirmPasswordTester.testConstraints([
        {
          name: 'empty confirm password',
          value: '',
          validationTrigger: 'submit',
          shouldShowErrorAfterSubmit: true,
          shouldPreventSubmission: true,
          errorMessageKey: 'Confirm Password is required',
        },
        {
          name: 'confirm password does not match',
          value: '1',
          validationTrigger: 'submit',
          shouldShowErrorAfterSubmit: true,
          shouldPreventSubmission: true,
          errorMessageKey: 'Passwords do not match',
        },
        {
          name: 'confirm password matches',
          value: passwordModified,
          validationTrigger: 'submit',
          shouldShowErrorAfterSubmit: false,
          shouldPreventSubmission: false,
        },
      ]);

      await page.getByTestId('sign-up-password-input').fill(passwordModified);
      await page
        .getByTestId('sign-up-confirm-password-input')
        .fill(passwordModified);
      await page.waitForTimeout(1000);
      await page.getByTestId('sign-up-reset-password-submit-button').click();
      await page.waitForTimeout(1000);

      await page.waitForLoadState('domcontentloaded');
      console.log('Password reset completed successfully!');

      console.log('Attempting to login with old password...');
      await signInCheck(page, tempEmail!, password, false);

      console.log('Attempting to login with new password...');
      await signInCheck(page, tempEmail!, passwordModified, true);

      await expect(page.getByTestId('create-classroom-button')).toBeVisible({
        timeout: 15000,
      });
      console.log('Login successful with new password!');
    }

    console.log(
      '=== STEP 3 COMPLETED: PASSWORD RESET & LOGIN WITH NEW PASSWORD ===',
    );

    // ========================================
    // STEP 4: SECOND REGISTRATION WITH SAME EMAIL & LOGIN ATTEMPT
    // ========================================
    console.log(
      '=== STEP 4: SECOND REGISTRATION WITH SAME EMAIL & LOGIN ATTEMPT ===',
    );

    // Navigate to home page and start second registration

    await page.context().clearCookies();
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    await reliableGoto(page, '/');
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await page.getByTestId('sign-in-button').click();
    await expect(page.getByTestId('dialog-sign-up-button')).toBeVisible();
    await page.getByTestId('dialog-sign-up-button').click();
    await expect(page.getByTestId('sign-up-full-name-input')).toBeVisible();

    // Fill in the signup form with same email but different password
    await page.getByTestId('sign-up-full-name-input').fill('John Test User 2');
    await page.getByTestId('sign-up-email-input').fill(tempEmail!);
    await page
      .getByTestId('sign-up-password-input')
      .fill(passwordForSecondSignUp);
    await page
      .getByTestId('sign-up-confirm-password-input')
      .fill(passwordForSecondSignUp);
    await page.getByTestId('sign-up-step-1-next-button').click();

    // Accept terms and continue
    await expect(page.getByTestId('sign-up-terms-checkbox')).toBeVisible();
    await page.getByTestId('sign-up-terms-checkbox').click();
    await page.getByTestId('sign-up-step-2-next-button').click();

    await expect(page.getByTestId('sign-up-step-3-next-button')).toBeVisible();
    await page.getByTestId('sign-up-step-3-next-button').click();

    await expect(page.getByTestId('sign-up-step-4-next-button')).toBeVisible();
    await expect(page.getByText(tempEmail!)).toBeVisible();

    console.log('Submitting second signup form with same email...');
    await page.getByTestId('sign-up-step-4-next-button').click();

    // Wait for account alert email (should not be verification email)
    console.log('Waiting for account alert email...');
    await page.waitForTimeout(5000);
    const accountAlertResult = await mailslurpHelper.getAccountAlertEmail(
      inboxId,
      60000, // Wait up to 60 seconds
    );

    expect(accountAlertResult.success).toBe(true);
    expect(accountAlertResult.emailContent).toBeTruthy();

    console.log('Account alert email received!');

    // Verify the email content contains expected text
    expect(accountAlertResult.emailContent).toContain(
      'VirtuosoHub Account Alert',
    );

    // Try to login immediately after second registration (should fail)
    console.log(
      'Attempting to login immediately after second registration (should fail)...',
    );

    // Navigate to login page
    await reliableGoto(page, '/');
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await page.getByTestId('sign-in-button').click();

    // Fill login form with second password
    await expect(page.getByTestId('sign-in-email-input')).toBeVisible();
    await page.getByTestId('sign-in-email-input').fill(tempEmail!);
    await page
      .getByTestId('sign-in-password-input')
      .fill(passwordForSecondSignUp);
    await page.getByTestId('dialog-sign-in-button').click();

    // Wait for error message (should not be able to login immediately)
    await expect(
      page.getByText(
        /invalid email or password|account not verified|please wait/i,
      ),
    ).toBeVisible({ timeout: 10000 });
    console.log(
      'Login correctly failed immediately after second registration!',
    );

    console.log(
      '=== STEP 4 COMPLETED: SECOND REGISTRATION WITH SAME EMAIL & LOGIN ATTEMPT ===',
    );
    console.log(
      'Complete signup with email verification, profile verification, password reset, and duplicate email registration test completed successfully!',
    );
  } catch (error) {
    console.error('Test failed:', error);

    if (inboxId) {
      try {
        const emails = await mailslurpHelper.getAllEmails(inboxId);
        console.log('All emails in inbox:', emails);
      } catch (emailError) {
        console.error('Failed to get emails for debugging:', emailError);
      }
    }

    throw error;
  } finally {
    if (inboxId) {
      console.log('Cleaning up temporary inbox...');
      await mailslurpHelper.deleteInbox(inboxId);
    }
  }
  });
});