import { createClassroom, createInvites, loginWithFullAccess } from "../utils";
import { expect, Page, Browser } from "@playwright/test";

export const invites = async (page: Page, browsers: Browser[]): Promise<void> => {
    await loginWithFullAccess(page, 20);
    const classroomId = await createClassroom(page);

    await expect(page.getByTestId('invite-trigger-student')).toBeVisible();
    await page.getByTestId('invite-trigger-student').click();
    await page.waitForTimeout(1000);
    const inviteLinks = await createInvites(page);

    // Define roles for invite testing
    const roles = ['teacher', 'student', 'guest'] as const;
    const newPages: Page[] = [];

        for (let i = 0; i < Math.min(roles.length, browsers.length); i++) {
            const browser = browsers[i];
            const role = roles[i];
            const inviteLink = inviteLinks[role];

            if (!inviteLink) {
                console.warn(`No invite link found for role: ${role}`);
                continue;
            }

            // Create new context and page for this role
            const context = await browser.newContext();
            const newPage = await context.newPage();
            newPages.push(newPage);

            // Navigate to the invite link
            await newPage.goto(inviteLink);
            await newPage.waitForLoadState('domcontentloaded');
            await expect(newPage.getByTestId('sign-in-button')).toBeVisible();
            await expect(newPage).toHaveURL('/?login=true');
            
            const email = `Test2${i + 1}@gmail.com`;
            const password = `Test2${i + 1}@gmail.com`;
            
            await expect(newPage.getByTestId('sign-in-email-input')).toBeVisible();
            await newPage.getByTestId('sign-in-email-input').click();
            await newPage.getByTestId('sign-in-email-input').fill(email);
            await newPage.getByTestId('sign-in-password-input').click();
            await newPage.getByTestId('sign-in-password-input').fill(password);
            await newPage.getByTestId('dialog-sign-in-button').click();
            await expect(newPage).toHaveURL(`/classrooms/${classroomId}`, { timeout: 10000 });
        }

        // Check the participants count on the original page
        await page.reload();
        await page.waitForLoadState('networkidle');
        await page.getByTestId('participants-summary-teacher').scrollIntoViewIfNeeded();
        await expect(page.getByTestId('participants-summary-teacher')).toContainText('2');
        await page.getByTestId('participants-summary-student').scrollIntoViewIfNeeded();
        await expect(page.getByTestId('participants-summary-student')).toContainText('1');
        await page.getByTestId('participants-summary-guest').scrollIntoViewIfNeeded();
        await expect(page.getByTestId('participants-summary-guest')).toContainText('1');
    
}