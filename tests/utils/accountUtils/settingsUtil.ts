import { expect, Page } from '@playwright/test';

export interface SettingsOptions {
  email?: string;
  displayName?: string;
  locale?: string; // e.g., 'en', 'ru', 'de'
  speaking?: string; // e.g., 'en', 'ru', 'de'
  translation?: string; // e.g., 'en', 'ru', 'de'
}

export async function openSettingsDialog(page: Page): Promise<void> {
  await page.waitForLoadState('domcontentloaded');

  const sidebarTrigger = page.getByTestId('sidebar-trigger');
  await expect(sidebarTrigger).toBeVisible({ timeout: 15000 });
  await sidebarTrigger.click();

  const profileButton = page.getByTestId('sidebar-profile-button');
  await expect(profileButton).toBeVisible({ timeout: 15000 });
  await profileButton.click();

  const settingsItem = page.getByTestId('sidebar-profile-settings');
  await expect(settingsItem).toBeVisible({ timeout: 15000 });
  await settingsItem.click();

  await expect(page.getByTestId('settings-dialog')).toBeVisible({ timeout: 15000 });
}

async function setLocaleByKey(page: Page, key: string): Promise<void> {
  await page.getByTestId('settings-locale-trigger').click();
  await page.getByTestId(`settings-locale-option-${key}`).click();
}

async function setSpeakingByKey(page: Page, key: string): Promise<void> {
  await page.getByTestId('settings-speaking-trigger').click();
  await page.getByTestId(`settings-speaking-option-${key}`).click();
}

async function setTranslationByKey(page: Page, key: string): Promise<void> {
  await page.getByTestId('settings-translation-trigger').click();
  await page.getByTestId(`settings-translation-option-${key}`).click();
}

export async function updateUserSettings(page: Page, shouldOpenDialog: boolean, options: SettingsOptions): Promise<void> {
  if (shouldOpenDialog) {
    await openSettingsDialog(page);
  }

  // Email (if visible for non-guest users)
  if (typeof options.email === 'string') {
    const emailInput = page.getByTestId('settings-email-input');
    if (await emailInput.isVisible()) {
      await emailInput.click();
      await emailInput.fill(options.email);
    }
  }

  // Display name
  if (typeof options.displayName === 'string') {
    const displayNameInput = page.getByTestId('settings-display-name-input');
    await displayNameInput.click();
    await displayNameInput.fill(options.displayName);
  }

  // Locale
  if (typeof options.locale === 'string') {
    await setLocaleByKey(page, options.locale);
  }

  // Speaking language
  if (typeof options.speaking === 'string') {
    await setSpeakingByKey(page, options.speaking);
  }

  // Translation language
  if (typeof options.translation === 'string') {
    await setTranslationByKey(page, options.translation);
  }

  // Save and wait for dialog to close
  const saveButton = page.getByTestId('settings-save-button');
  await expect(saveButton).toBeVisible({ timeout: 15000 });
  await saveButton.click();
  await expect(page.getByTestId('settings-dialog')).toBeHidden({ timeout: 15000 });
}

export async function resetUserSettings(
  page: Page,
  overrides: Partial<SettingsOptions> = {},
): Promise<void> {
  const {
    email,
    displayName = 'Test',
    locale = 'en',
    speaking = 'en',
    translation = 'en',
  } = overrides;

  await updateUserSettings(page, true, { email, displayName, locale, speaking, translation });
}


