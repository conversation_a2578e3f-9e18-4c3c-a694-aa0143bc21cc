import { expect } from "@playwright/test";
import { Page } from "@playwright/test";
import { reliableGoto } from "../otherUtils/smartRetry";
import { MailSlurpHelper } from "../externalUtils/mailHelper";
import { MAILPIT_URL } from "../../../src/shared/constants/server";

export async function loginWithFullAccess(page: Page, testAccountNumber: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 11 | 15 | 16 | 17 | 20 | 21 | 22 | 23 | 24 | 26 | 29 | 30 = 1) {
    await reliableGoto(page, '/');

    // Construct email and password based on test account number
    const email = `Test${testAccountNumber}@gmail.com`;
    const password = `Test${testAccountNumber}@gmail.com`;

    //Login with the test user
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await page.getByTestId('sign-in-button').click();
    await expect(page.getByTestId('sign-in-email-input')).toBeVisible();
    await page.getByTestId('sign-in-email-input').click();
    await page.getByTestId('sign-in-email-input').fill(email);
    await page.getByTestId('sign-in-password-input').click();
    await page.getByTestId('sign-in-password-input').fill(password);
    await page.getByTestId('dialog-sign-in-button').click();
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('create-classroom-button')).toBeVisible();
    await expect(page).toHaveURL('/classrooms');
}


export async function loginAsGuest(page: Page) {
    await reliableGoto(page, '/');

    //Login as guest
    await expect(page.getByTestId('guest-login-button')).toBeVisible();
    await page.getByTestId('guest-login-button').click();
    await expect(page.getByTestId('guest-display-name-input')).toBeVisible();
    await page.getByTestId('guest-display-name-input').click();
    await page.getByTestId('guest-display-name-input').fill('Test1');
    await page.getByTestId('guest-terms-checkbox').click();
    await page.getByTestId('guest-submit-button').click();
    await expect(page.getByTestId('create-classroom-button')).toBeVisible();
    await expect(page).toHaveURL('/classrooms');
}

export async function signInCheck(page: Page, email: string, password: string, success: boolean) {
    await reliableGoto(page, '/');
    await expect(page.getByTestId("sign-in-button")).toBeVisible();
    await page.getByTestId("sign-in-button").click();
    await expect(page.getByTestId("sign-in-email-input")).toBeVisible();
    await page.getByTestId("sign-in-email-input").fill(email);
    await page.getByTestId("sign-in-password-input").fill(password);
    await page.getByTestId("dialog-sign-in-button").click();
    if (success === true) {
        await expect(page.getByTestId("create-classroom-button")).toBeVisible({ timeout: 5000 });
    } else {
        await expect(page.getByTestId("create-classroom-button")).not.toBeVisible({ timeout: 5000 });
    }
}

export async function signUp(page: Page, password: string, loginAfter: boolean = false, openSignUp: boolean = false) {
     // This test checks usual sign up flow and password reset flow
     const mailslurpHelper = new MailSlurpHelper({
         baseUrl: MAILPIT_URL,
     });
    
     let inboxId: string | null = null;
     let tempEmail: string | null = null;
 
     try {
         console.log('Creating temporary email inbox...');
         const inbox = await mailslurpHelper.createInbox();
         inboxId = inbox.id;
         tempEmail = inbox.emailAddress;
         console.log(`Created inbox: ${tempEmail}`);
        
        if (openSignUp) {
         await reliableGoto(page, '/');
         await page.waitForLoadState('domcontentloaded');
         await expect(page.getByTestId("sign-in-button")).toBeVisible();
         await page.getByTestId("sign-in-button").click();
         await page.waitForLoadState('domcontentloaded');
         await expect(page.getByTestId("dialog-sign-up-button")).toBeVisible();
         await page.getByTestId("dialog-sign-up-button").click();
         await page.waitForLoadState('domcontentloaded');
        }

         console.log('=== STEP 1: ACCOUNT CREATION & EMAIL VERIFICATION ===');
         
         await expect(page.getByTestId("sign-up-full-name-input")).toBeVisible();
         await expect(page.getByTestId("sign-up-email-input")).toBeVisible();
         await page.getByTestId("sign-up-email-input").click();
         await page.getByTestId("sign-up-email-input").fill(tempEmail!);

         await page.getByTestId("sign-up-full-name-input").click();
         await page.getByTestId("sign-up-full-name-input").fill("Test User");
 
         await expect(page.getByTestId("sign-up-password-input")).toBeVisible();
         await page.getByTestId("sign-up-password-input").click();
         await page.getByTestId("sign-up-password-input").fill(password);
 
         await expect(page.getByTestId("sign-up-confirm-password-input")).toBeVisible();
         await page.getByTestId("sign-up-confirm-password-input").click();
         await page.getByTestId("sign-up-confirm-password-input").fill(password);
         await page.getByTestId("sign-up-step-1-next-button").click();
 
         await expect(page.getByTestId("sign-up-terms-checkbox")).toBeVisible();
         await page.getByTestId("sign-up-step-2-next-button").click();
 
         await expect(page.getByTestId("sign-up-terms-error")).toBeVisible();
         await page.getByTestId("sign-up-terms-checkbox").click();
         await expect(page.getByTestId("sign-up-terms-error")).not.toBeVisible();
         await page.getByTestId("sign-up-step-2-next-button").click();
 
         await expect(page.getByTestId("sign-up-step-3-next-button")).toBeVisible();
         await page.getByTestId("sign-up-step-3-next-button").click();
 
         await expect(page.getByTestId("sign-up-step-4-next-button")).toBeVisible();
         await expect(page.getByText(tempEmail!)).toBeVisible();
 
         console.log('Submitting signup form...');
         await page.getByTestId("sign-up-step-4-next-button").click();
         await page.waitForTimeout(2000);
         console.log('Waiting for verification email...');
         const emailResult = await mailslurpHelper.getEmailVerification(
             inboxId,
             60000,
         );
         await page.waitForTimeout(5000);
         expect(emailResult.success).toBe(true);
         expect(emailResult.verificationLink).toBeTruthy();
         
         console.log('Verification email received!');
         console.log('Verification link:', emailResult.verificationLink);
 
         if (emailResult.verificationLink) {
             console.log('Clicking verification link...');
             await page.goto(emailResult.verificationLink);
             
             await page.waitForLoadState("domcontentloaded");
             await expect(page).toHaveURL(/.*verify.*|.*success.*|.*login.*|.*dashboard.*/);
             
             console.log('Email verification completed successfully!');
         }

         if (page.url().includes('login') || page.url().includes('signin')) {
             console.log('Attempting to login with verified account...');
             
             const emailInput = page.getByTestId("sign-in-email-input");
             const passwordInput = page.getByTestId("sign-in-password-input");
             const loginButton = page.getByTestId("sign-in-button");
             
             if (await emailInput.isVisible()) {
                 await emailInput.fill(tempEmail!);
                 await passwordInput.fill(password);
                 await loginButton.click();
                 
                 await expect(page.getByTestId("onboarding-dialog-dashboard-button")).toBeVisible();
                 await page.getByTestId("onboarding-dialog-dashboard-button").click();
                 await page.waitForURL('/classrooms', { timeout: 15000 });
                 console.log('Login successful after email verification!');
             }
         }
         if (loginAfter) {
    await reliableGoto(page, '/');
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('sign-in-button')).toBeVisible();
    await page.getByTestId('sign-in-button').click();
    await expect(page.getByTestId('sign-in-email-input')).toBeVisible();
    await page.getByTestId('sign-in-email-input').click();
    await page.getByTestId('sign-in-email-input').fill(tempEmail!);
    await page.getByTestId('sign-in-password-input').click();
    await page.getByTestId('sign-in-password-input').fill(password);
    await page.getByTestId('dialog-sign-in-button').click();
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('create-classroom-button')).toBeVisible();
    await expect(page).toHaveURL('/classrooms');
             }
    } catch (error) {
        console.error('Error during signup:', error);
        throw error;
    }
    return { tempEmail, inboxId };
}

export async function logout(page: Page) {
    await page.waitForLoadState('domcontentloaded');

    const sidebarTrigger = page.getByTestId('sidebar-trigger');
    await expect(sidebarTrigger).toBeVisible({ timeout: 15000 });
    await sidebarTrigger.click();
  
    const profileButton = page.getByTestId('sidebar-profile-button');
    await expect(profileButton).toBeVisible({ timeout: 15000 });
    await profileButton.click();
  
    const logoutButton = page.getByTestId('sidebar-profile-logout');
    await expect(logoutButton).toBeVisible({ timeout: 15000 });
    await logoutButton.click();
  
    await expect(page.getByTestId('sign-in-button')).toBeVisible({ timeout: 15000 });
}