import { Page, expect } from '@playwright/test';
import { reliableGoto } from '../otherUtils/smartRetry';
import { MailSlurpHelper } from '../externalUtils/mailHelper';
import { MAILPIT_URL } from '../../../src/shared/constants/server';

export interface CreatedAccount {
  email: string;
  password: string;
  inboxId: string;
}

export async function createAndVerifyAccount(
  page: Page,
  options?: {
    fullName?: string;
    password?: string;
  },
): Promise<CreatedAccount> {
  const fullName = options?.fullName ?? 'Test User';
  const password = options?.password ?? '1234abcdABCD!';

  const mailslurpHelper = new MailSlurpHelper({
    baseUrl: MAILPIT_URL,
  });

  // Create a temporary inbox (synthetic address @test.test)
  const inbox = await mailslurpHelper.createInbox();
  const inboxId = inbox.id;
  const email = inbox.emailAddress;

  // Open sign-up
  await reliableGoto(page, '/');
  await expect(page.getByTestId('sign-in-button')).toBeVisible();
  await page.getByTestId('sign-in-button').click();
  await expect(page.getByTestId('dialog-sign-up-button')).toBeVisible();
  await page.getByTestId('dialog-sign-up-button').click();

  // Fill step 1
  await expect(page.getByTestId('sign-up-full-name-input')).toBeVisible();
  await page.getByTestId('sign-up-full-name-input').fill(fullName);
  await page.getByTestId('sign-up-email-input').fill(email);
  await page.getByTestId('sign-up-password-input').fill(password);
  await page.getByTestId('sign-up-confirm-password-input').fill(password);
  await page.getByTestId('sign-up-step-1-next-button').click();

  // Step 2: terms
  await expect(page.getByTestId('sign-up-terms-checkbox')).toBeVisible();
  await page.getByTestId('sign-up-terms-checkbox').click();
  await page.getByTestId('sign-up-step-2-next-button').click();

  // Step 3
  await expect(page.getByTestId('sign-up-step-3-next-button')).toBeVisible();
  await page.getByTestId('sign-up-step-3-next-button').click();

  // Step 4: submit
  await expect(page.getByTestId('sign-up-step-4-next-button')).toBeVisible();
  await expect(page.getByText(email)).toBeVisible();
  await page.getByTestId('sign-up-step-4-next-button').click();

  // Ensure not logged-in yet
  await expect(page.getByTestId('sign-in-button')).toBeVisible();

  // Wait for verification email and click link
  const emailResult = await mailslurpHelper.getEmailVerification(inboxId, 60000);
  expect(emailResult.success).toBe(true);
  expect(emailResult.verificationLink).toBeTruthy();

  if (emailResult.verificationLink) {
    await page.goto(emailResult.verificationLink);
    await page.waitForLoadState('domcontentloaded');
    await expect(page).toHaveURL(/verify|success|login|dashboard/i);

    // If redirected to login, perform a quick login to land on dashboard
    if (page.url().includes('login') || page.url().includes('signin')) {
      const emailInput = page.getByTestId('sign-in-email-input');
      const passwordInput = page.getByTestId('sign-in-password-input');
      if (await emailInput.isVisible()) {
        await emailInput.fill(email);
        await passwordInput.fill(password);
        await page.getByTestId('sign-in-button').click();
        // Onboarding may appear; try to proceed to dashboard
        const onboardingBtn = page.getByTestId('onboarding-dialog-dashboard-button');
        if (await onboardingBtn.isVisible({ timeout: 5000 }).catch(() => false)) {
          await onboardingBtn.click();
        }
        await page.waitForURL('/classrooms', { timeout: 15000 }).catch(() => {});
      }
    }
  }

  return { email, password, inboxId };
}


