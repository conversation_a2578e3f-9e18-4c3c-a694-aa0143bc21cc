import { ZENDESK_RPOXY_URL } from '../../../src/shared/constants/server';

export async function deleteZendeskTicket(ticketId: string): Promise<void> {
  const response = await fetch(`${ZENDESK_RPOXY_URL}/api/v1/tickets/${ticketId}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    let errorMessage = `Failed to delete ticket ${ticketId}. Status: ${response.status}`;
    
    try {
      const errorData = await response.json();
      errorMessage = errorData.error || errorData.message || errorMessage;
    } catch (parseError) {
      console.warn('Could not parse error response:', parseError);
    }
    
    throw new Error(errorMessage);
  }
}

