import { MAILPIT_URL } from "../../../src/shared/constants/server";

export interface EmailVerificationResult {
    verificationLink: string | null;
    emailContent: string;
    success: boolean;
}

type MailpitMessageSummary = {
    ID: string;
    Subject?: string;
    To?: Array<{ Address?: string; Name?: string }> | string;
    Created?: string;
};

export class MailSlurpHelper {
    private readonly baseUrl: string = MAILPIT_URL;
    private readonly basicAuthHeader: string | null = null;

    /**
     * Backward compatible constructor: accepts legacy API key (ignored) or an optional config.
     * If config is omitted, reads MAILPIT_BASE_URL/MAILPIT_USER/MAILPIT_PASS from env.
     */
    constructor(_ignoredApiKeyOrConfig?: string | { baseUrl?: string; user?: string; pass?: string }) {
        const cfg = typeof _ignoredApiKeyOrConfig === 'object' && _ignoredApiKeyOrConfig !== null
            ? _ignoredApiKeyOrConfig
            : {};

        // Prefer provided config → env → sensible local default
        this.baseUrl = cfg.baseUrl || process.env.MAILPIT_BASE_URL || MAILPIT_URL;
        this.basicAuthHeader = null;
        this.baseUrl = cfg.baseUrl || process.env.MAILPIT_BASE_URL || MAILPIT_URL;
        this.basicAuthHeader = null;
    }

    private getString(obj: Record<string, unknown>, key: string): string {
        const value = obj[key];
        return typeof value === 'string' ? value : '';
    }

    private getStringArray(obj: Record<string, unknown>, key: string): string[] {
        const value = obj[key];
        return Array.isArray(value) && value.every((v) => typeof v === 'string') ? (value as string[]) : [];
    }

    /**
     * Creates a new temporary email "inbox" (simulated via unique recipient)
     * We return an email address to which your app should send mail. ID = emailAddress.
     */
    async createInbox(): Promise<{ id: string; emailAddress: string }> {
        const unique = `${Date.now()}-${Math.random().toString(36).slice(2, 10)}`;
        const emailAddress = `test+${unique}@test.test`;
        return { id: emailAddress, emailAddress };
    }

    /**
     * Poll Mailpit for the latest message addressed to inboxId (email address)
     */
    async waitForEmail(inboxId: string, timeoutMs: number = 30000): Promise<{ id: string; subject: string; html: string; text: string } | null> {
        const deadline = Date.now() + timeoutMs;
        while (Date.now() < deadline) {
            const message = await this.findLatestMessageForRecipient(inboxId);
            if (message) {
                let html = await this.fetchMessageHtml(message.ID);
                let text = await this.fetchMessageText(message.ID);
                if (!html && !text) {
                    // Fallback to JSON body if HTML/Text endpoints are empty
                    try {
                        const res = await fetch(`${this.baseUrl}/api/v1/message/${message.ID}`, { headers: this.buildHeaders() });
                        if (res.ok) {
                            const payload = (await res.json()) as unknown;
                            if (payload && typeof payload === 'object') {
                                const obj = payload as Record<string, unknown>;
                                const htmlBody = this.getString(obj, 'HTML');
                                const textBody = this.getString(obj, 'Text');
                                const links = this.getStringArray(obj, 'Links');
                                const combined = [htmlBody, textBody, links.join('\n')].filter(Boolean).join('\n');
                                html = html || combined;
                                text = text || combined;
                            }
                        }
                    } catch (_e) {
                        // ignore and continue with empty html/text
                    }
                }
                return { id: message.ID, subject: message.Subject || '', html, text };
            }
            await new Promise((r) => setTimeout(r, 1000));
        }
        return null;
    }

    private buildHeaders(): Record<string, string> {
        const headers: Record<string, string> = {};
        if (this.basicAuthHeader) headers['Authorization'] = this.basicAuthHeader;
        return headers;
    }

    private async listMessages(limit: number = 100): Promise<MailpitMessageSummary[]> {
        const res = await fetch(`${this.baseUrl}/api/v1/messages?limit=${limit}`, {
            headers: this.buildHeaders(),
        });
        if (!res.ok) throw new Error(`Mailpit list messages failed: ${res.status}`);
        const data = (await res.json()) as { messages?: MailpitMessageSummary[] };
        return data.messages || [];
    }

    private recipientMatches(toField: MailpitMessageSummary['To'], targetEmail: string): boolean {
        if (!toField) return false;
        const target = targetEmail.toLowerCase();
        if (typeof toField === 'string') {
            return toField.toLowerCase().includes(target);
        }
        if (Array.isArray(toField)) {
            for (const entry of toField as unknown[]) {
                if (typeof entry === 'string') {
                    const s = entry.toLowerCase();
                    if (s.includes(target)) return true;
                    const m = s.match(/<([^>]+)>/);
                    if (m && m[1] && m[1].toLowerCase() === target) return true;
                } else if (entry && typeof entry === 'object') {
                    const addr = ((entry as { Address?: string }).Address || '').toLowerCase();
                    if (addr === target) return true;
                }
            }
            return false;
        }
        return false;
    }

    private async findLatestMessageForRecipient(emailAddress: string): Promise<MailpitMessageSummary | null> {
        // Try search endpoint first (faster & filtered), then fall back to listing
        try {
            const q = encodeURIComponent(`to:${emailAddress}`);
            const res = await fetch(`${this.baseUrl}/api/v1/search?query=${q}`, { headers: this.buildHeaders() });
            if (res.ok) {
                const data = (await res.json()) as { messages?: MailpitMessageSummary[] };
                const matches = (data.messages || []).filter((m) => this.recipientMatches(m.To, emailAddress));
                if (matches.length > 0) return matches[0];
            }
        } catch {
            // ignore and fall back
        }
        const messages = await this.listMessages(200);
        for (const msg of messages) {
            if (this.recipientMatches(msg.To, emailAddress)) return msg;
        }
        return null;
    }

    private async fetchMessageHtml(id: string): Promise<string> {
        const res = await fetch(`${this.baseUrl}/api/v1/message/${id}/html`, { headers: this.buildHeaders() });
        if (!res.ok) return '';
        return await res.text();
    }

    private async fetchMessageText(id: string): Promise<string> {
        const res = await fetch(`${this.baseUrl}/api/v1/message/${id}/text`, { headers: this.buildHeaders() });
        if (!res.ok) return '';
        return await res.text();
    }

    /**
     * Replaces production URLs with localhost for testing
     */
    private replaceProductionUrl(url: string): string {
        return url.replace(/https:\/\/dev\.virtuosohub\.ai/g, 'https://localhost:5173');
    }

    /**
     * Extract verification link from email content
     */
    extractVerificationLink(emailBody: string): string | null {
        const decodeEntities = (s: string) =>
            s
                .replace(/&amp;/g, '&')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&quot;/g, '"')
                .replace(/&#39;/g, "'");

        // Collect href values (supports both quotes and newline-safe using [\s\S])
        const hrefMatches = Array.from(emailBody.matchAll(/href=(?:"|')([\s\S]*?)(?:"|')/gi)).map((m) => decodeEntities(m[1]));

        // Collect absolute URLs present anywhere in the body
        const urlMatches = Array.from(emailBody.matchAll(/https?:\/\/[^\s"'<>]+/gi)).map((m) => decodeEntities(m[0]));

        const candidates = [...hrefMatches, ...urlMatches];
        if (candidates.length === 0) return null;

        const matchesKeywords = (u: string) =>
            /verify-email|verify.*email|email.*verify|confirm.*email|email.*confirm|activate.*account|verify/i.test(u);

        // Prefer verification-intent links
        const preferred = candidates.find(matchesKeywords);
        if (preferred) return this.replaceProductionUrl(preferred);

        // Fallback: any link containing 'verify'
        const verifyOnly = candidates.find((u) => /verify/i.test(u));
        if (verifyOnly) return this.replaceProductionUrl(verifyOnly);

        return null;
    }

    /**
     * Extract password reset link from email content
     */
    extractPasswordResetLink(emailBody: string): string | null {
        const decodeEntities = (s: string) =>
            s
                .replace(/&amp;/g, '&')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&quot;/g, '"')
                .replace(/&#39;/g, "'");

        // Gather candidates (hrefs + absolute URLs)
        const hrefMatches = Array.from(emailBody.matchAll(/href=(?:"|')([\s\S]*?)(?:"|')/gi)).map((m) => decodeEntities(m[1]));
        const urlMatches = Array.from(emailBody.matchAll(/https?:\/\/[^\s"'<>]+/gi)).map((m) => decodeEntities(m[0]));
        const candidates = [...hrefMatches, ...urlMatches];
        if (candidates.length === 0) return null;

        // Prefer reset/forgot/change password links or query param reset-password=true
        const matchesKeywords = (u: string) =>
            /reset[^\s]*password|password[^\s]*reset|forgot[^\s]*password|password[^\s]*forgot|password[^\s]*change|change[^\s]*password|reset-password=true/i.test(u);

        const preferred = candidates.find(matchesKeywords);
        if (preferred) return this.replaceProductionUrl(preferred);

        return null;
    }


    async getEmailVerification(inboxId: string, timeoutMs: number = 30000): Promise<EmailVerificationResult> {
        try {
            const email = await this.waitForEmail(inboxId, timeoutMs);
            if (!email) {
                return { verificationLink: null, emailContent: '', success: false };
                console.log('No email found');
            }
            const emailContent = email.html || email.text || '';
            const verificationLink = this.extractVerificationLink(emailContent);
            return { verificationLink, emailContent, success: !!verificationLink };
        } catch (error) {
            console.error('Failed to get email verification:', error);
            return { verificationLink: null, emailContent: '', success: false };
        }
    }

    /**
     * Get password reset email & extract link
     */
    async getPasswordResetEmail(inboxId: string, timeoutMs: number = 30000): Promise<EmailVerificationResult> {
        try {
            const email = await this.waitForEmail(inboxId, timeoutMs);
            if (email) {
                const emailContent = email.html || email.text || '';
                const resetLink = this.extractPasswordResetLink(emailContent);
                if (resetLink) {
                    return { verificationLink: resetLink, emailContent, success: true };
                }
            }

            // Fallback: scan recent messages for this recipient and try to extract a reset link
            const messages = await this.listMessages(200);
            for (const msg of messages) {
                if (!this.recipientMatches(msg.To, inboxId)) continue;
                let html = await this.fetchMessageHtml(msg.ID);
                let text = await this.fetchMessageText(msg.ID);
                if (!html && !text) {
                    try {
                        const res = await fetch(`${this.baseUrl}/api/v1/message/${msg.ID}`, { headers: this.buildHeaders() });
                        if (res.ok) {
                            const payload = (await res.json()) as unknown;
                            if (payload && typeof payload === 'object') {
                                const obj = payload as Record<string, unknown>;
                                const htmlBody = this.getString(obj, 'HTML');
                                const textBody = this.getString(obj, 'Text');
                                const links = this.getStringArray(obj, 'Links');
                                const combined = [htmlBody, textBody, links.join('\n')].filter(Boolean).join('\n');
                                html = html || combined;
                                text = text || combined;
                            }
                        }
                    } catch (_e) {
                        // ignore and continue with empty html/text
                    }
                }
                const content = html || text || '';
                const link = this.extractPasswordResetLink(content);
                if (link) {
                    return { verificationLink: link, emailContent: content, success: true };
                }
            }

            return { verificationLink: null, emailContent: '', success: false };
        } catch (error) {
            console.error('Failed to get password reset email:', error);
            return { verificationLink: null, emailContent: '', success: false };
        }
    }

    /**
     * Delete inbox (noop for Mailpit because inboxes are synthetic)
     */
    async deleteInbox(_inboxId: string) {
        // No action needed; keeping method for compatibility
        return;
    }

    /**
     * Get all emails for a recipient (summary)
     */
    async getAllEmails(inboxId: string) {
        const messages = await this.listMessages(200);
        return messages.filter((m) => this.recipientMatches(m.To, inboxId));
    }

    /**
     * Get account alert email result (duplicate registration attempts)
     */
    async getAccountAlertEmail(inboxId: string, timeoutMs: number = 30000): Promise<EmailVerificationResult> {
        try {
            const email = await this.waitForEmail(inboxId, timeoutMs);
            if (email) {
                const emailContent = email.html || email.text || '';
                const subject = email.subject || '';
                const isAlert =
                    /Account Alert|Account registration attempt|VirtuosoHub Account Alert/i.test(subject) ||
                    /VirtuosoHub Account Alert|Someone tried to create an account|account already exists/i.test(emailContent);
                if (isAlert) {
                    return { verificationLink: null, emailContent, success: true };
                }
            }

            // Fallback: scan recent messages for this recipient and detect alert by subject/content
            const messages = await this.listMessages(200);
            for (const msg of messages) {
                if (!this.recipientMatches(msg.To, inboxId)) continue;
                const subj = msg.Subject || '';
                if (/Account Alert|Account registration attempt|VirtuosoHub Account Alert/i.test(subj)) {
                    // Fast-path: subject indicates alert
                    return { verificationLink: null, emailContent: subj, success: true };
                }
                let html = await this.fetchMessageHtml(msg.ID);
                let text = await this.fetchMessageText(msg.ID);
                if (!html && !text) {
                    try {
                        const res = await fetch(`${this.baseUrl}/api/v1/message/${msg.ID}`, { headers: this.buildHeaders() });
                        if (res.ok) {
                            const payload = (await res.json()) as unknown;
                            if (payload && typeof payload === 'object') {
                                const obj = payload as Record<string, unknown>;
                                const htmlBody = this.getString(obj, 'HTML');
                                const textBody = this.getString(obj, 'Text');
                                const links = this.getStringArray(obj, 'Links');
                                const combined = [htmlBody, textBody, links.join('\n')].filter(Boolean).join('\n');
                                html = html || combined;
                                text = text || combined;
                            }
                        }
                    } catch (_e) {
                        // ignore and continue with empty html/text
                    }
                }
                const content = html || text || '';
                if (/VirtuosoHub Account Alert|Someone tried to create an account|account already exists/i.test(content)) {
                    return { verificationLink: null, emailContent: content, success: true };
                }
            }

            return { verificationLink: null, emailContent: '', success: false };
        } catch (error) {
            console.error('Failed to get account alert email:', error);
            return { verificationLink: null, emailContent: '', success: false };
        }
    }
}