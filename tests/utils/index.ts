// Main utils index - exports all utility functions organized by category

// Account utilities
export { 
  createAndVerifyAccount, 
  type CreatedAccount,
  loginWithFullAccess, 
  loginAsGuest, 
  signInCheck, 
  signUp, 
  logout,
  openSettingsDialog, 
  updateUserSettings, 
  resetUserSettings, 
  type SettingsOptions 
} from './accountUtils';

// Classroom utilities  
export { 
  openClassroomSettingsDialog, 
  navigateToCategory, 
  enableAllSwitchesInCurrentCategory, 
  disableAllSwitchesInCurrentCategory, 
  setClassroomGeneralInfo, 
  enableAccessParticipationSwitches, 
  disableAccessParticipationSwitches, 
  enableRecordingSharingSwitches, 
  disableRecordingSharingSwitches, 
  enableTranslationSwitches, 
  disableTranslationSwitches, 
  enableMaterialsSwitches, 
  disableMaterialsSwitches, 
  enableInClassInteractionSwitches, 
  disableInClassInteractionSwitches, 
  enableAllClassroomSwitches, 
  disableAllClassroomSwitches, 
  closeClassroomSettingsDialog, 
  configureClassroomSettings, 
  disableAllAdminSettings, 
  type ClassroomSettingsOptions,
  connectToClass, 
  connectToClassUtility,
  createClassroom, 
  deleteClassroom, 
  renameClassroom 
} from './classroomUtils';

// Class utilities
export { 
  setupAdvancedEmulatedDevices, 
  blockAdvancedMediaPermissions, 
  grantAdvancedMediaPermissions, 
  EMULATED_DEVICES, 
  getEmulatedDevice, 
  waitForDevicesReady, 
  forceDeviceContextRefresh, 
  forceReactContextPopulation, 
  debugReactDeviceState, 
  debugDeviceState, 
  verifyPermissionsBlocked,
  audioDeviceTestBefore, 
  audioDeviceTestAfter,
  createClass,
  newClass,
  createClassSetup,
  createInvites, 
  inviteToClass, 
  createInviteWithCustomSettings,
  detectIncomingAudio, 
  detectMeaningfulAudio, 
  isPageReceivingAudio, 
  getCurrentAudioLevel, 
  waitForAudioChange, 
  type AudioDetectionResult, 
  type AudioDetectionOptions,
  muteUnmute 
} from './classUtils';

// External service utilities
export { 
  MailSlurpHelper, 
  type EmailVerificationResult,
  deleteZendeskTicket 
} from './externalUtils';

// Other utilities
export { 
  BrowserCleanup, 
  cleanupBrowserState, 
  fullBrowserCleanup,
  openBrowsersForInvites, 
  closeBrowsers, 
  createBrowserConfigs, 
  type BrowserConfig,
  InputValidationTester, 
  EmailConstraints, 
  PasswordConstraints, 
  DisplayNameConstraints, 
  FullNameConstraints, 
  ClassroomNameConstraints, 
  ClassroomDescriptionConstraints, 
  type ValidationTestCase, 
  type InputValidationConfig,
  logBrowserConsole, 
  logBrowserErrors,
  smartRetry, 
  reliableGoto,
  SimpleTestWrapper, 
  ClassTestWrapper, 
  createSimpleWrapper, 
  createClassTestWrapper, 
  type InviteLinks 
} from './otherUtils';
