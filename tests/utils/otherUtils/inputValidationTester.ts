import { Page, expect, Locator, test } from '@playwright/test';
import fs from 'fs';
import path from 'path';

export interface ValidationTestCase {
  name: string;
  value: string;
  shouldDisableButton?: boolean;
  shouldShowError?: boolean;
  errorMessageKey?: string; 
  shouldPreventSubmission?: boolean;
  validationTrigger?: 'realtime' | 'submit' | 'both';
  shouldShowErrorAfterSubmit?: boolean;
}

export interface InputValidationConfig {
  inputTestId: string;
  submitButtonTestId: string;
  errorElementTestId?: string;
  errorElementI18nKey?: string; // i18n key for error message
  triggerValidation?: 'tab' | 'blur' | 'enter' | 'none';
  clearBetweenTests?: boolean;
  language?: string;
  validationMode?: 'realtime' | 'submit' | 'mixed';
  disableButtonOnInvalid?: boolean;
  useI18nKeyMatching?: boolean;
}

export class InputValidationTester {
  constructor(private page: Page, private config: InputValidationConfig) {}

  private async getTranslatedText(key: string): Promise<string> {
    const language = this.config.language || 'en';
    
    try {
      const translationPath = path.join(process.cwd(), `public/locales/${language}/translation.json`);
      const translations = JSON.parse(fs.readFileSync(translationPath, 'utf8'));
      
      const keys = key.split('.');
      let result = translations;
      
      for (const k of keys) {
        if (result && typeof result === 'object' && k in result) {
          result = result[k];
        } else {
          return key;
        }
      }
      
      return typeof result === 'string' ? result : key;
    } catch (error) {
      test.info().attach('translation-load-error', {
        body: `Could not load translation for ${key}: ${error}`,
        contentType: 'text/plain'
      });
      return key;
    }
  }

  async testConstraints(testCases: ValidationTestCase[]): Promise<void> {
    const input = this.page.getByTestId(this.config.inputTestId);
    const submitButton = this.page.getByTestId(this.config.submitButtonTestId);
    
    // Get error element by test-id or i18n key
    let errorElement: Locator | null = null;
    if (this.config.errorElementTestId) {
      errorElement = this.page.getByTestId(this.config.errorElementTestId);
    } else if (this.config.errorElementI18nKey) {
      // Find error element by i18n key using translated text
      const translatedText = await this.getTranslatedText(this.config.errorElementI18nKey);
      errorElement = this.page.getByText(translatedText);
    }

    for (const testCase of testCases) {
      await test.step(`Testing constraint: ${testCase.name}`, async () => {
      
      if (this.config.clearBetweenTests !== false) {
        // More robust input clearing to avoid flakiness
        await this.clearInputRobustly(input);
      }
      
      await input.fill(testCase.value);
      
      const validationTrigger = testCase.validationTrigger || this.config.validationMode || 'realtime';
      
      if (validationTrigger === 'realtime' || validationTrigger === 'both') {
        // Trigger real-time validation
        await this.triggerValidation();
        
        // Test button state for real-time validation (only if configured to disable)
        if (this.config.disableButtonOnInvalid !== false) {
          if (testCase.shouldDisableButton === true) {
            await this.waitForButtonState(submitButton, 'disabled');
          } else if (testCase.shouldDisableButton === false) {
            await this.waitForButtonState(submitButton, 'enabled');
          }
        } else {
          // Button stays enabled regardless of validation state
          await this.waitForButtonState(submitButton, 'enabled');
        }
        
        // Test error visibility for real-time validation
        if (errorElement && testCase.shouldShowError === true) {
          await expect(errorElement).toBeVisible();
          
          // Handle i18n validation
          if (testCase.errorMessageKey) {
            if (this.config.useI18nKeyMatching) {
              // Find error element by i18n key directly
              const errorByKey = this.page.locator(`[data-i18n-key="${testCase.errorMessageKey}"]`);
              await expect(errorByKey).toBeVisible();
            } else {
              // Fallback: Traditional text matching
              const expectedText = await this.getTranslatedText(testCase.errorMessageKey);
              await expect(errorElement).toHaveText(expectedText);
            }
          }
        } else if (errorElement && testCase.shouldShowError === false) {
          await expect(errorElement).not.toBeVisible();
        }
      }
      
      if (validationTrigger === 'submit' || validationTrigger === 'both') {
        // Test submit-triggered validation
        await this.testSubmitValidation(testCase, submitButton, errorElement, input);
      }
      
      // Test form submission prevention (legacy support)
      if (testCase.shouldPreventSubmission === true) {
        await this.robustClick(submitButton);
        // Verify we're still on the same page or form is still visible
        await expect(input).toBeVisible();
      }
      });
    }
  }

  private async triggerValidation(): Promise<void> {
    switch (this.config.triggerValidation || 'tab') {
      case 'tab':
        await this.page.keyboard.press('Tab');
        break;
      case 'blur':
        // Safari occasionally ignores synthetic blur; force via JS as fallback
        await this.page.evaluate(() => document.activeElement && (document.activeElement as HTMLElement).blur());
        break;
      case 'enter':
        await this.page.keyboard.press('Enter');
        break;
      case 'none':
        // Do nothing
        break;
    }
  }

  /**
   * Robustly clears an input field to avoid flaky test behavior
   */
  private async clearInputRobustly(input: Locator): Promise<void> {
    try {
      // Method 1: Try standard clear with wait
      await input.waitFor({ state: 'visible' });
      await input.focus();
      await input.clear();
      
      // Verify the input is actually cleared
      const value = await input.inputValue();
      if (value === '') {
        return; // Successfully cleared
      }
    } catch (_error) {
      // Fall through to alternative methods
    }

    try {
      // Method 2: Select all and delete
      await input.focus();
      await input.press('Control+a'); // Select all
      await input.press('Delete');
      
      // Verify cleared
      const value = await input.inputValue();
      if (value === '') {
        return;
      }
    } catch (_error) {
      // Fall through to final method
    }

    try {
      // Method 3: Triple-click and type empty string
      await input.focus();
      await input.click({ clickCount: 3 }); // Triple-click to select all
      await input.fill(''); // Fill with empty string
      
      // Final verification
      const value = await input.inputValue();
      if (value !== '') {
        throw new Error(`Failed to clear input field. Current value: "${value}"`);
      }
    } catch (error) {
      throw new Error(`All input clearing methods failed: ${error}`);
    }
  }

  private async testSubmitValidation(
    testCase: ValidationTestCase, 
    submitButton: Locator, 
    errorElement: Locator | null, 
    input: Locator
  ): Promise<void> {
    await test.step(`Testing submit validation for: ${testCase.name}`, async () => {
    // For submit validation, button should initially be enabled
    await this.waitForButtonState(submitButton, 'enabled');
    
    // Click submit button to trigger validation
    await this.robustClick(submitButton);
    
    // After submit attempt, check if error appears
    if (errorElement && testCase.shouldShowErrorAfterSubmit === true) {
      await expect(errorElement).toBeVisible();
      
      // Check error message content
      if (testCase.errorMessageKey) {
        if (this.config.useI18nKeyMatching) {
          // Find error element by i18n key directly
          const errorByKey = this.page.locator(`[data-i18n-key="${testCase.errorMessageKey}"]`);
          await expect(errorByKey).toBeVisible();
        } else {
          // Fallback: Traditional text matching
          const expectedText = await this.getTranslatedText(testCase.errorMessageKey);
          await expect(errorElement).toHaveText(expectedText);
        }
      }
    }
    
    // Check if form submission was prevented (should stay on same page)
    if (testCase.shouldPreventSubmission === true) {
      await expect(input).toBeVisible();
        // Test step name already indicates what we're checking, no need for console log
    }
    
    // Check if button becomes disabled after failed submit attempt
    if (testCase.shouldDisableButton === true) {
      await this.waitForButtonState(submitButton, 'disabled');
        // Test step name already indicates what we're checking, no need for console log
    }
    });
  }

  /**
   * Robustly waits for a button to be in the expected state to avoid flaky tests
   */
  private async waitForButtonState(button: Locator, expectedState: 'enabled' | 'disabled'): Promise<void> {
    const timeoutMs = process.env.CI ? 6000 : 3000;
    // `button.isDisabled()` returns boolean quickly without DOM querying overhead.
    await expect
      .poll(() => button.isDisabled(), { timeout: timeoutMs, intervals: [100] })
      .toBe(expectedState === 'disabled');
  }

  /**
   * Robustly clicks an element to avoid flaky test behavior
   */
  private async robustClick(element: Locator): Promise<void> {
    const maxAttempts = 2;
    const retryDelay = 150;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        // Method 1: Standard click with proper waiting
        await element.waitFor({ state: 'visible', timeout: 3000 });
        await element.waitFor({ state: 'attached', timeout: 1000 });
        
        // Ensure element is not covered by other elements
        await element.scrollIntoViewIfNeeded();
        
        // Wait a bit for any animations to complete
        await this.page.waitForTimeout(50);
        
        // Try standard click
        await element.click({ timeout: 2000 });
        return; // Success
        
      } catch (error) {
        if (attempt === maxAttempts) {
          // Final attempt: Try alternative click methods
          try {
            // Method 2: Force click (bypasses actionability checks)
            await element.click({ force: true, timeout: 1500 });
            return;
          } catch (forceError) {
            try {
              // Method 3: JavaScript click as last resort
              await element.evaluate((el) => {
                if (el instanceof HTMLElement) {
                  el.click();
                } else {
                  throw new Error('Element is not an HTMLElement');
                }
              });
              return;
            } catch (jsError) {
              throw new Error(`All click methods failed. Standard: ${error}, Force: ${forceError}, JS: ${jsError}`);
            }
          }
        }
        
        // Wait before retrying
        await this.page.waitForTimeout(retryDelay);
      }
    }
  }
}

// Predefined constraint builders with i18n support
export const EmailConstraints = {
  tooLong: (maxLength: number = 50): ValidationTestCase => ({
    name: `too long (${maxLength + 1} chars)`,
    value: 'a'.repeat(maxLength - 10) + '@gmail.com',
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.email.email.validation.maxLength'
  }),

  tooShort: (minLength: number = 3): ValidationTestCase => ({
    name: `too short (${minLength - 1} chars)`,
    value: 'a'.repeat(minLength - 1),
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.email.email.validation.minLength'
  }),

  // Submit-triggered validation versions
  tooLongSubmit: (maxLength: number = 50): ValidationTestCase => ({
    name: `too long (${maxLength + 1} chars) - submit validation`,
    value: 'a'.repeat(maxLength - 10) + '@gmail.com',
    validationTrigger: 'submit',
    shouldShowErrorAfterSubmit: true,
    shouldPreventSubmission: true,
    errorMessageKey: 'globalFields.email.email.validation.maxLength'
  }),

  tooShortSubmit: (minLength: number = 3): ValidationTestCase => ({
    name: `too short (${minLength - 1} chars) - submit validation`,
    value: 'a'.repeat(minLength - 1),
    validationTrigger: 'submit',
    shouldShowErrorAfterSubmit: true,
    shouldPreventSubmission: true,
    errorMessageKey: 'globalFields.email.email.validation.minLength'
  }),

  invalidFormat: (): ValidationTestCase[] => [
    {
      name: 'invalid format - no @',
      value: 'invalidemailformat',
      shouldDisableButton: true,
      shouldShowError: true,
      errorMessageKey: 'globalFields.email.email.validation.invalid'
    },
    {
      name: 'invalid format - no domain',
      value: 'test@',
      shouldDisableButton: true,
      shouldShowError: true,
      errorMessageKey: 'globalFields.email.email.validation.invalid'
    },
    {
      name: 'invalid format - no local part',
      value: '@domain.com',
      shouldDisableButton: true,
      shouldShowError: true,
      errorMessageKey: 'globalFields.email.email.validation.invalid'
    }
  ],

  empty: (): ValidationTestCase => ({
    name: 'empty field',
    value: '',
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.email.email.validation.required' // Assuming this exists
  }),

  valid: (): ValidationTestCase => ({
    name: 'valid email',
    value: '<EMAIL>',
    shouldDisableButton: false,
    shouldShowError: false
  })
};

export const PasswordConstraints = {
  empty: (): ValidationTestCase => ({
    name: 'empty field',
    value: '',
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.password.password.validation.required'
  }),
  tooShort: (minLength: number = 8): ValidationTestCase => ({
    name: `too short (${minLength - 1} chars)`,
    value: 'a'.repeat(minLength - 1),
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.password.password.validation.minLength'
  }),

  tooLong: (maxLength: number = 50): ValidationTestCase => ({
    name: `too long (${maxLength + 1} chars)`,
    value: 'a'.repeat(maxLength + 1),
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.password.password.validation.maxLength'
  }),

  valid: (): ValidationTestCase => ({
    name: 'valid password',
    value: 'ValidPassword123',
    shouldDisableButton: false,
    shouldShowError: false
  })
};

export const DisplayNameConstraints = {
  empty: (): ValidationTestCase => ({
    name: 'empty field',
    value: '',
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.displayName.displayName.validation.required'
  }),
  tooShort: (minLength: number = 3): ValidationTestCase => ({
    name: `too short (${minLength - 1} chars)`,
    value: 'a'.repeat(minLength - 1),
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.displayName.displayName.validation.minLength'
  }),

  tooLong: (maxLength: number = 20): ValidationTestCase => ({
    name: `too long (${maxLength + 1} chars)`,
    value: 'a'.repeat(maxLength + 1),
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.displayName.displayName.validation.maxLength'
  }),

  valid: (): ValidationTestCase => ({
    name: 'valid display name',
    value: 'ValidName',
    shouldDisableButton: false,
    shouldShowError: false
  })
};

export const FullNameConstraints = {
  empty: (): ValidationTestCase => ({
    name: 'empty field',
    value: '',
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.displayName.validation.required'
  }),
  tooShort: (minLength: number = 3): ValidationTestCase => ({
    name: `too short (${minLength - 1} chars)`,
    value: 'a'.repeat(minLength - 1),
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.displayName.validation.minLength'
  }),

  tooLong: (maxLength: number = 20): ValidationTestCase => ({
    name: `too long (${maxLength + 1} chars)`,
    value: 'a'.repeat(maxLength + 1),
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.displayName.validation.maxLength'
  }),

  valid: (): ValidationTestCase => ({
    name: 'valid full name',
    value: 'John Smith',
    shouldDisableButton: false,
    shouldShowError: false
  })
};

export const ClassroomNameConstraints = {
  empty: (): ValidationTestCase => ({
    name: 'empty field',
    value: '',
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.classroomName.validation.required'
  }),
  
  tooShort: (minLength: number = 3): ValidationTestCase => ({
    name: `too short (${minLength - 1} chars)`,
    value: 'a'.repeat(minLength - 1),
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.classroomName.validation.minLength'
  }),

  tooLong: (maxLength: number = 50): ValidationTestCase => ({
    name: `too long (${maxLength + 1} chars)`,
    value: 'a'.repeat(maxLength + 1),
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.classroomName.validation.maxLength'
  }),

  valid: (): ValidationTestCase => ({
    name: 'valid classroom name',
    value: 'Test Classroom',
    shouldDisableButton: false,
    shouldShowError: false
  })
};

export const ClassroomDescriptionConstraints = {
  empty: (): ValidationTestCase => ({
    name: 'empty field',
    value: '',
    shouldDisableButton: false,
    shouldShowError: false
  }),
  
  tooLong: (maxLength: number = 200): ValidationTestCase => ({
    name: `too long (${maxLength + 1} chars)`,
    value: 'a'.repeat(maxLength + 1),
    shouldDisableButton: true,
    shouldShowError: true,
    errorMessageKey: 'globalFields.classroomDescription.validation.maxLength'
  }),

  valid: (): ValidationTestCase => ({
    name: 'valid classroom description',
    value: 'This is a test classroom description that contains enough content to be meaningful but stays within the character limits.',
    shouldDisableButton: false,
    shouldShowError: false
  })
}; 