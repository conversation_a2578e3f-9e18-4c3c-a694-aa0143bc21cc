import { Page } from '@playwright/test';

const RETRYABLE_ERRORS = [
    'ERR_SOCKET_NOT_CONNECTED',
    'ERR_CONNECTION_REFUSED',
    'ERR_CONNECTION_RESET',
    'ERR_NETWORK_CHANGED',
    'ERR_INTERNET_DISCONNECTED',
    'TimeoutError',
    'net::ERR_',
    'Navigation timeout',
    'page.goto: Timeout'
];

interface RetryOptions {
    maxRetries?: number;
    retryDelay?: number;
    retryableErrors?: string[];
}

export async function smartRetry<T>(
    testFn: () => Promise<T>,
    options: RetryOptions = {}
): Promise<T> {
    const {
        maxRetries = 2,
        retryDelay = 2000,
        retryableErrors = RETRYABLE_ERRORS
    } = options;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            const result = await testFn();
            
            if (attempt > 0) {
                console.log(`Success on attempt ${attempt + 1}`);
            }
            
            return result;
        } catch (error) {
            const errorMessage = error.message || error.toString();
            
            const isRetryable = retryableErrors.some(pattern => 
                errorMessage.includes(pattern)
            );
            
            if (!isRetryable || attempt === maxRetries) {
                if (attempt > 0) {
                    console.log(`Failed after ${attempt + 1} attempts: ${errorMessage}`);
                }
                throw error;
            }
            
            console.log(`Retry ${attempt + 1}/${maxRetries} - Error: ${errorMessage}`);
            
            await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
    }
    
    throw new Error('Unexpected retry loop exit');
}

export async function reliableGoto(
    page: Page, 
    url: string, 
    options: RetryOptions & { waitUntil?: 'load' | 'domcontentloaded' | 'networkidle' | 'commit' } = {}
): Promise<void> {
    const { waitUntil = 'domcontentloaded', ...retryOptions } = options;
    
    await smartRetry(async () => {
        await page.goto(url, { 
            waitUntil,
            timeout: 30000 
        });
    }, retryOptions);
}
