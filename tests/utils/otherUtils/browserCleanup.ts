/* eslint-disable @typescript-eslint/no-explicit-any */
import { Page, BrowserContext } from '@playwright/test';

/**
 * Comprehensive browser cleanup utility to clear accumulated data between tests
 * This helps prevent state pollution and performance degradation in sequential test runs
 */
export class BrowserCleanup {
  /**
   * Clear all browser storage and state for a single page
   */
  static async clearPageState(page: Page): Promise<void> {
    try {
      // Clear all storage types
      await page.evaluate(() => {
        // Clear localStorage
        localStorage.clear();
        
        // Clear sessionStorage
        sessionStorage.clear();
        
        // Clear IndexedDB
        if ('indexedDB' in window) {
          indexedDB.databases?.().then(databases => {
            databases.forEach(db => {
              if (db.name) {
                indexedDB.deleteDatabase(db.name);
              }
            });
          });
        }
        
        // Clear WebSQL (if supported)
        if ('openDatabase' in window) {
          // WebSQL is deprecated but some apps might still use it
          try {
            const db = (window as any).openDatabase('', '', '', '');
            if (db) {
              db.transaction(tx => {
                tx.executeSql('DELETE FROM WebKitDatabaseInfoTable');
              });
            }
          } catch (_e) {
            // WebSQL not supported or already cleared
          }
        }
        
        // Clear Cache API
        if ('caches' in window) {
          caches.keys().then(cacheNames => {
            cacheNames.forEach(cacheName => {
              caches.delete(cacheName);
            });
          });
        }
        
        // Clear Service Worker registrations
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.getRegistrations().then(registrations => {
            registrations.forEach(registration => {
              registration.unregister();
            });
          });
        }
        
        // Clear WebRTC connections
        if ('RTCPeerConnection' in window) {
          // Close any existing peer connections
          const connections = (window as any).__webrtcConnections || [];
          connections.forEach((connection: any) => {
            if (connection && typeof connection.close === 'function') {
              connection.close();
            }
          });
          (window as any).__webrtcConnections = [];
        }
        
        // Clear MediaStream tracks
        if ('MediaStreamTrack' in window) {
          // Stop all media tracks
          navigator.mediaDevices?.getUserMedia({ video: true, audio: true })
            .then(stream => {
              stream.getTracks().forEach(track => track.stop());
            })
            .catch(() => {
              // No media access or already stopped
            });
        }
        
        // Clear any global event listeners that might persist
        const events = ['beforeunload', 'unload', 'pagehide', 'visibilitychange'];
        events.forEach(eventType => {
          const listeners = (window as any).__eventListeners?.[eventType] || [];
          listeners.forEach((listener: any) => {
            window.removeEventListener(eventType, listener);
          });
        });
      });
      
      console.log('Page state cleared successfully');
    } catch (error) {
      console.warn('Error clearing page state:', error);
    }
  }

  /**
   * Clear all browser context data and state
   */
  static async clearContextState(context: BrowserContext): Promise<void> {
    try {
      // Clear cookies
      await context.clearCookies();
      
      // Clear permissions
      await context.clearPermissions();
      
      // Clear HTTP authentication
      await context.setHTTPCredentials(null);
      
      // Clear geolocation
      await context.setGeolocation(null);
      
      // Clear extra HTTP headers
      await context.setExtraHTTPHeaders({});
      
      // Clear user agent (reset to default)
      // Note: This requires creating a new context, so we'll skip it
      
      console.log('Context state cleared successfully');
    } catch (error) {
      console.warn('Error clearing context state:', error);
    }
  }

  /**
   * Close all pages except the main one in a context
   */
  static async closeExtraPages(context: BrowserContext, keepPage?: Page): Promise<void> {
    try {
      const pages = context.pages();
      const pagesToClose = keepPage 
        ? pages.filter(page => page !== keepPage)
        : pages;
      
      for (const page of pagesToClose) {
        if (!page.isClosed()) {
          await page.close();
        }
      }
      
      console.log(`Closed ${pagesToClose.length} extra pages`);
    } catch (error) {
      console.warn('Error closing extra pages:', error);
    }
  }

  /**
   * Clear all network-related data and connections
   */
  static async clearNetworkState(page: Page): Promise<void> {
    try {
      await page.evaluate(() => {
        // Clear any pending fetch requests
        if ('AbortController' in window) {
          const controllers = (window as any).__abortControllers || [];
          controllers.forEach((controller: any) => {
            if (controller && typeof controller.abort === 'function') {
              controller.abort();
            }
          });
          (window as any).__abortControllers = [];
        }
        
        // Clear any WebSocket connections
        const sockets = (window as any).__webSockets || [];
        sockets.forEach((socket: any) => {
          if (socket && typeof socket.close === 'function') {
            socket.close();
          }
        });
        (window as any).__webSockets = [];
        
        // Clear any EventSource connections
        const eventSources = (window as any).__eventSources || [];
        eventSources.forEach((source: any) => {
          if (source && typeof source.close === 'function') {
            source.close();
          }
        });
        (window as any).__eventSources = [];
      });
      
      console.log('Network state cleared successfully');
    } catch (error) {
      console.warn('Error clearing network state:', error);
    }
  }

  /**
   * Clear all media-related state and resources
   */
  static async clearMediaState(page: Page): Promise<void> {
    try {
      await page.evaluate(() => {
        // Stop all audio/video elements
        const mediaElements = document.querySelectorAll('audio, video');
        mediaElements.forEach((element: any) => {
          if (element.pause) element.pause();
          if (element.src) element.src = '';
          if (element.srcObject) element.srcObject = null;
        });
        
        // Clear any audio context
        if ('AudioContext' in window || 'webkitAudioContext' in window) {
          const contexts = (window as any).__audioContexts || [];
          contexts.forEach((context: any) => {
            if (context && typeof context.close === 'function') {
              context.close();
            }
          });
          (window as any).__audioContexts = [];
        }
        
        // Clear any media recorder instances
        const recorders = (window as any).__mediaRecorders || [];
        recorders.forEach((recorder: any) => {
          if (recorder && typeof recorder.stop === 'function') {
            recorder.stop();
          }
        });
        (window as any).__mediaRecorders = [];
      });
      
      console.log('Media state cleared successfully');
    } catch (error) {
      console.warn('Error clearing media state:', error);
    }
  }

  /**
   * Clear all timers and intervals
   */
  static async clearTimers(page: Page): Promise<void> {
    try {
      await page.evaluate(() => {
        // Clear all timeouts and intervals
        const timeouts = (window as any).__timeouts || [];
        const intervals = (window as any).__intervals || [];
        
        timeouts.forEach((id: number) => clearTimeout(id));
        intervals.forEach((id: number) => clearInterval(id));
        
        (window as any).__timeouts = [];
        (window as any).__intervals = [];
        
        // Override setTimeout and setInterval to track them
        const originalSetTimeout = window.setTimeout;
        const originalSetInterval = window.setInterval;
        
        (window as any).setTimeout = function(callback: any, delay: any, ...args: any[]) {
          const id = originalSetTimeout(callback, delay, ...args);
          (window as any).__timeouts = (window as any).__timeouts || [];
          (window as any).__timeouts.push(id);
          return id;
        };
        
        (window as any).setInterval = function(callback: any, delay: any, ...args: any[]) {
          const id = originalSetInterval(callback, delay, ...args);
          (window as any).__intervals = (window as any).__intervals || [];
          (window as any).__intervals.push(id);
          return id;
        };
      });
      
      console.log('Timers cleared successfully');
    } catch (error) {
      console.warn('Error clearing timers:', error);
    }
  }

  /**
   * Comprehensive cleanup for a single page
   */
  static async cleanupPage(page: Page): Promise<void> {
    console.log('Starting comprehensive page cleanup...');
    
    await Promise.all([
      this.clearPageState(page),
      this.clearNetworkState(page),
      this.clearMediaState(page),
      this.clearTimers(page)
    ]);
    
    console.log('Page cleanup completed');
  }

  /**
   * Comprehensive cleanup for a browser context
   */
  static async cleanupContext(context: BrowserContext, keepPage?: Page): Promise<void> {
    console.log('Starting comprehensive context cleanup...');
    
    // Clear context-level data
    await this.clearContextState(context);
    
    // Close extra pages
    await this.closeExtraPages(context, keepPage);
    
    // Clean up remaining pages
    const pages = context.pages();
    for (const page of pages) {
      if (page !== keepPage && !page.isClosed()) {
        await this.cleanupPage(page);
      }
    }
    
    console.log('Context cleanup completed');
  }

  /**
   * Quick cleanup for test isolation - clears the most common issues
   */
  static async quickCleanup(page: Page, context?: BrowserContext): Promise<void> {
    console.log('Starting quick cleanup...');
    
    const cleanupReport = {
      storage: { localStorage: 0, sessionStorage: 0 },
      cookies: 0,
      media: { elements: 0, contexts: 0, recorders: 0 },
      network: { connections: 0, requests: 0 }
    };
    
    // Clear storage and count items
    const storageData = await page.evaluate(() => {
      const localCount = localStorage.length;
      const sessionCount = sessionStorage.length;
      localStorage.clear();
      sessionStorage.clear();
      return { localCount, sessionCount };
    });
    cleanupReport.storage.localStorage = storageData.localCount;
    cleanupReport.storage.sessionStorage = storageData.sessionCount;
    
    // Clear cookies if context provided
    if (context) {
      const cookies = await context.cookies();
      cleanupReport.cookies = cookies.length;
      await context.clearCookies();
    }
    
    // Clear media state and count items
    const mediaData = await page.evaluate(() => {
      const elements = document.querySelectorAll('audio, video').length;
      const contexts = (window as any).__audioContexts?.length || 0;
      const _recorders = (window as any).__mediaRecorders?.length || 0;
      
      // Stop all audio/video elements
      document.querySelectorAll('audio, video').forEach((element: any) => {
        if (element.pause) element.pause();
        if (element.src) element.src = '';
        if (element.srcObject) element.srcObject = null;
      });
      
      // Clear any audio context
      if ('AudioContext' in window || 'webkitAudioContext' in window) {
        const contexts = (window as any).__audioContexts || [];
        contexts.forEach((context: any) => {
          if (context && typeof context.close === 'function') {
            context.close();
          }
        });
        (window as any).__audioContexts = [];
      }
      
      // Clear any media recorder instances
      const mediaRecorders = (window as any).__mediaRecorders || [];
      mediaRecorders.forEach((recorder: any) => {
        if (recorder && typeof recorder.stop === 'function') {
          recorder.stop();
        }
      });
      (window as any).__mediaRecorders = [];
      
      return { elements, contexts, recorders: _recorders };
    });
    cleanupReport.media = mediaData;
    
    // Clear network state and count connections
    const networkData = await page.evaluate(() => {
      const connections = (window as any).__webrtcConnections?.length || 0;
      const requests = (window as any).__abortControllers?.length || 0;
      
      // Clear any pending fetch requests
      if ('AbortController' in window) {
        const controllers = (window as any).__abortControllers || [];
        controllers.forEach((controller: any) => {
          if (controller && typeof controller.abort === 'function') {
            controller.abort();
          }
        });
        (window as any).__abortControllers = [];
      }
      
      // Clear any WebRTC connections
      if ('RTCPeerConnection' in window) {
        const connections = (window as any).__webrtcConnections || [];
        connections.forEach((connection: any) => {
          if (connection && typeof connection.close === 'function') {
            connection.close();
          }
        });
        (window as any).__webrtcConnections = [];
      }
      
      return { connections, requests };
    });
    cleanupReport.network = networkData;
    
    // Log cleanup report
    console.log('Quick cleanup completed:');
    console.log(`   Storage: ${cleanupReport.storage.localStorage} localStorage, ${cleanupReport.storage.sessionStorage} sessionStorage items`);
    console.log(`   Cookies: ${cleanupReport.cookies} cleared`);
    console.log(`   Media: ${cleanupReport.media.elements} elements, ${cleanupReport.media.contexts} contexts, ${cleanupReport.media.recorders} recorders`);
    console.log(`   Network: ${cleanupReport.network.connections} WebRTC connections, ${cleanupReport.network.requests} pending requests`);
  }
}

/**
 * Convenience function for quick cleanup
 */
export async function cleanupBrowserState(page: Page, context?: BrowserContext): Promise<void> {
  await BrowserCleanup.quickCleanup(page, context);
}

/**
 * Convenience function for comprehensive cleanup
 */
export async function fullBrowserCleanup(page: Page, context?: BrowserContext): Promise<void> {
  if (context) {
    await BrowserCleanup.cleanupContext(context, page);
  } else {
    await BrowserCleanup.cleanupPage(page);
  }
}
