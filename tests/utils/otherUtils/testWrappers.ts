import { <PERSON>, <PERSON><PERSON><PERSON> } from '@playwright/test';

interface SimpleConfig {
    timeout: number;
    retries: number;
}

export class SimpleTestWrapper {
    private page: Page;
    private config: SimpleConfig;

    constructor(page: Page, config: SimpleConfig) {
        this.page = page;
        this.config = config;
    }

    async run<T, TArgs extends unknown[]>(
        testFunction: (...args: TArgs) => Promise<T>, 
        ...args: TArgs
    ): Promise<T> {
        let lastError: Error;
        let attempt = 0;
        console.log(`Running test ${testFunction.name} with ${this.config.retries} retries`);

        while (attempt <= this.config.retries) {
            try {
                const currentTimeout = this.config.timeout + (attempt * 30000);
                this.page.setDefaultTimeout(currentTimeout);
                
                if (attempt > 0) {
                    console.log(`${testFunction.name}: Attempt ${attempt + 1} with extended timeout: ${currentTimeout}ms`);
                } else {
                    console.log(`${testFunction.name}: First attempt with timeout: ${currentTimeout}ms`);
                }

                return await testFunction(...args);
            } catch (error) {
                lastError = error as Error;
                attempt++;
                
                if (attempt <= this.config.retries) {
                    console.log(`${testFunction.name}: Attempt ${attempt} failed, retrying with extended timeout...`);
                    await this.page.waitForTimeout(1000); // Brief pause between retries
                }
            }
        }

        throw lastError!;
    }
}

export function createSimpleWrapper(page: Page, config: SimpleConfig): SimpleTestWrapper {
    return new SimpleTestWrapper(page, config);
} 

interface ClassTestConfig {
    timeout: number;
    retries: number;
}

export interface InviteLinks {
    teacher: string;
    student: string;
    guest: string;
}

export class ClassTestWrapper {
    private config: ClassTestConfig;
    private inviteLinks: InviteLinks;
    private openBrowsers: Browser[];

    constructor(config: ClassTestConfig, inviteLinks: InviteLinks, openBrowsers: Browser[]) {
        this.config = config;
        this.inviteLinks = inviteLinks;
        this.openBrowsers = openBrowsers;
    }

    private async resetToClassState(currentPage: Page): Promise<void> {
        console.log('Checking and resetting participants to class state...');
        
        try {
            if (currentPage.isClosed()) {
                console.warn('Current page is closed, skipping state reset');
                return;
            }

            const isCurrentPageInClass = await this.isPageInClass(currentPage, 'Test1');
            if (!isCurrentPageInClass) {
                console.log('Resetting Test1 (main teacher) to class...');
                await currentPage.goto(this.inviteLinks.teacher, { waitUntil: 'domcontentloaded' });
                await this.waitForClassEntry(currentPage, 'Test1');
            } else {
                console.log('Test1 already in class, no reset needed');
            }

            const browserRoles = [
                { browser: this.openBrowsers[0], role: 'student', name: 'Test2', inviteLink: this.inviteLinks.student },
                { browser: this.openBrowsers[1], role: 'teacher', name: 'Test3', inviteLink: this.inviteLinks.teacher },
                { browser: this.openBrowsers[2], role: 'guest', name: 'Test4', inviteLink: this.inviteLinks.guest }
            ];

            for (const { browser, role, name, inviteLink } of browserRoles) {
                if (browser && !browser.isConnected()) {
                    console.warn(`${name} browser is disconnected, skipping reset`);
                    continue;
                }
                
                if (browser && browser.contexts().length > 0) {
                    const context = browser.contexts()[0];
                    const browserPage = context.pages()[0];
                    
                    if (browserPage && !browserPage.isClosed()) {
                        const isInClass = await this.isPageInClass(browserPage, name);
                        
                        if (!isInClass) {
                            console.log(`Resetting ${name} (${role}) to class...`);
                            await browserPage.goto(inviteLink, { waitUntil: 'domcontentloaded' });
                            await this.waitForClassEntry(browserPage, name);
                        } else {
                            console.log(`${name} already in class, no reset needed`);
                        }
                    } else {
                        console.warn(`${name} page is closed, skipping reset`);
                    }
                }
            }

            console.log('All participants checked/reset to class state successfully');
        } catch (error) {
            console.error('Error during state reset:', error);
        }
    }

    private async isPageInClass(page: Page, participantName: string): Promise<boolean> {
        try {
            const exitButton = page.locator('[data-testid="action-bar-exit-button"]');
            const isInClass = await exitButton.isVisible({ timeout: 3000 }).catch(() => false);
            
            if (isInClass) {
                console.log(`${participantName}: Already in class`);
                return true;
            } else {
                console.log(`${participantName}: Not in class, needs reset`);
                return false;
            }
        } catch (error) {
            console.warn(`${participantName}: Error checking class state:`, error);
            return false;
        }
    }

    private async waitForClassEntry(page: Page, participantName: string): Promise<void> {
        try {
            const continueButton = page.locator('[data-testid*="continue"]').or(page.locator('button').filter({ hasText: /continue/i }));
            const isContinueVisible = await continueButton.isVisible({ timeout: 8000 }).catch(() => false);
            
            if (isContinueVisible) {
                console.log(`${participantName}: Clicking continue button...`);
                await continueButton.click();
                await page.waitForLoadState('domcontentloaded');
            }

            console.log(`${participantName}: Waiting for class entry...`);
            await page.locator('[data-testid="action-bar-exit-button"]').waitFor({ 
                state: 'visible', 
                timeout: 15000 
            });
            
            console.log(`${participantName}: Successfully entered class`);
        } catch (error) {
            console.warn(`${participantName}: Failed to enter class during reset:`, error);
        }
    }

    private async setExtendedTimeoutForAllBrowsers(currentPage: Page, timeout: number): Promise<void> {
        if (!currentPage.isClosed()) {
            currentPage.setDefaultTimeout(timeout);
        }
        
        for (const browser of this.openBrowsers) {
            if (browser && browser.isConnected() && browser.contexts().length > 0) {
                const context = browser.contexts()[0];
                const browserPage = context.pages()[0];
                
                if (browserPage && !browserPage.isClosed()) {
                    browserPage.setDefaultTimeout(timeout);
                }
            }
        }
    }

    async run<T, TArgs extends unknown[]>(
        currentPage: Page,
        testFunction: (...args: TArgs) => Promise<T>, 
        ...args: TArgs
    ): Promise<T> {
        let lastError: Error;
        let attempt = 0;
        console.log(`Running test ${testFunction.name} with ${this.config.retries} retries`);

        while (attempt <= this.config.retries) {
            try {
                const currentTimeout = this.config.timeout + (attempt * 30000);
                await this.setExtendedTimeoutForAllBrowsers(currentPage, currentTimeout);
                
                if (attempt > 0) {
                    console.log(`${testFunction.name}: Attempt ${attempt + 1} with extended timeout: ${currentTimeout}ms`);
                } else {
                    console.log(`${testFunction.name}: First attempt with timeout: ${currentTimeout}ms`);
                }

                return await testFunction(...args);
            } catch (error) {
                lastError = error as Error;
                attempt++;
                
                if (attempt <= this.config.retries) {
                    console.log(`${testFunction.name}: Attempt ${attempt} failed, retrying with extended timeout...`);
                    
                    const needsStateReset = this.shouldResetState(lastError);
                    if (needsStateReset) {
                        console.log('Failure suggests state issues, performing state reset...');
                        await this.resetToClassState(currentPage);
                    } else {
                        console.log('Failure appears to be network/timing related, skipping state reset...');
                    }
                    
                    if (!currentPage.isClosed()) {
                        await currentPage.waitForTimeout(2000);
                    } else {
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                }
            }
        }

        throw lastError!;
    }

    private shouldResetState(error: Error): boolean {
        const errorMessage = error.message.toLowerCase();
        
        const stateRelatedErrors = [
            'element(s) not found',
            'not visible',
            'not attached',
            'page has been closed',
            'context has been closed',
            'target page, context or browser has been closed'
        ];
        
        const networkTimingErrors = [
            'timeout',
            'navigation timeout',
            'net::err_',
            'connection refused',
            'connection reset',
            'socket not connected'
        ];
        
        const isNetworkTiming = networkTimingErrors.some(pattern => errorMessage.includes(pattern));
        if (isNetworkTiming) {
            return false;
        }
        
        const isStateRelated = stateRelatedErrors.some(pattern => errorMessage.includes(pattern));
        if (isStateRelated) {
            return true;
        }
        return true;
    }
}

// Factory function
export function createClassTestWrapper(
    config: ClassTestConfig, 
    inviteLinks: InviteLinks, 
    openBrowsers: Browser[]
): ClassTestWrapper {
    return new ClassTestWrapper(config, inviteLinks, openBrowsers);
} 