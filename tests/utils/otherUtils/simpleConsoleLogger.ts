import { Page } from '@playwright/test';

export function logBrowserConsole(page: Page, prefix: string = '[BROWSER]') {
    page.on('console', msg => {
        const type = msg.type().toUpperCase();
        const text = msg.text();
        console.log(`${prefix} ${type}: ${text}`);
    });
}

export function logBrowserErrors(page: Page, prefix: string = '[BROWSER_ERROR]') {
    page.on('pageerror', error => {
        console.log(`${prefix}: ${error.message}`);
    });
} 