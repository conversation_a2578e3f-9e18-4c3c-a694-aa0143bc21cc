import { <PERSON><PERSON><PERSON>, BrowserContext, chromium } from '@playwright/test';

export async function openBrowsersForInvites(
    context: BrowserContext, 
    count: number = 3
): Promise<Browser[]> {
    const currentBrowserType = context.browser()?.browserType() || chromium;
    const openBrowsers: <PERSON><PERSON><PERSON>[] = [];

    for (let i = 0; i < count; i++) {
        const newBrowser = await currentBrowserType.launch();
        openBrowsers.push(newBrowser);
    }

    return openBrowsers;
}

export async function closeBrowsers(browsers: Browser[]): Promise<void> {
    for (const browser of browsers) {
        await browser.close();
    }
}

export interface BrowserConfig {
    type: typeof import('@playwright/test').chromium;
    device: null;
    name: string;
}

export function createBrowserConfigs(context: BrowserContext, count: number = 3): BrowserConfig[] {
    const currentBrowserType = context.browser()?.browserType() || chromium;
    
    return Array.from({ length: count }, (_, i) => ({
        type: currentBrowserType,
        device: null,
        name: `<PERSON><PERSON><PERSON> ${i + 1}`
    }));
} 