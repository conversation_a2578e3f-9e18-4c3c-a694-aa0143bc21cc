import { Page, expect } from '@playwright/test';
import { reliableGoto } from '../otherUtils/smartRetry';

export async function connectToClass(
  page: Page,
  welcomeDialog: boolean = true,
  role: 'student' | 'teacher' | 'guest',
  classroomId: string,
): Promise<void> {
  try {
    await connectToClassUtility(page, welcomeDialog, role);
    console.log('Connecting to class: Successfully connected to class!');
  } catch (error) {
    console.error('Connecting to class: Failed to connect to class:', error);

    await reliableGoto(page, `/classrooms/${classroomId}`);
    await page.waitForLoadState('domcontentloaded');
    try {
      await expect(
        page.getByTestId('create-class-trigger-header'),
      ).toBeVisible();
      await page.getByTestId('create-class-trigger-header').click();
      await connectToClassUtility(page, welcomeDialog, role);
    } catch (error) {
      console.error('Active class exist or other error', error);
      await expect(
        page.getByTestId('classroom-active-join-button'),
      ).toBeVisible();
      await page.getByTestId('classroom-active-join-button').click();
      await connectToClassUtility(page, welcomeDialog, role);
    }
  }
}

export async function connectToClassUtility(
  page: Page,
  welcomeDialog: boolean = true,
  role: 'student' | 'teacher' | 'guest',
): Promise<void> {
  const EXTENDED_TIMEOUT = 25000;

  if (welcomeDialog === true) {
    await expect(
      page.getByTestId('welcome-dialog-continue-button'),
    ).toBeVisible({
      timeout: EXTENDED_TIMEOUT,
    });
    await page.getByTestId('welcome-dialog-continue-button').click();
  } else {
    await expect(
      page.getByTestId('welcome-dialog-continue-button'),
    ).toBeVisible({
      timeout: EXTENDED_TIMEOUT,
    });
    await page.getByTestId('welcome-dialog-continue-button').click();
  }

  console.log('Connecting to class: Role is', role);

  // if (role === 'student' || role === 'teacher') {
  //   await expect(
  //     page.getByTestId('audio-selection-computer-option'),
  //   ).toBeVisible({
  //     timeout: EXTENDED_TIMEOUT,
  //   });
  //   await page.getByTestId('audio-selection-computer-option').click();
  // }

  await expect(page.getByTestId('action-bar-exit-button')).toBeVisible({
    timeout: EXTENDED_TIMEOUT,
  });

  console.log('Connecting to class: Successfully connected to class!');
}
