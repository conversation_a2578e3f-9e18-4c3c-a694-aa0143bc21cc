import { expect, Page } from '@playwright/test';

export interface ClassroomSettingsOptions {
  title?: string;
  description?: string;
  canAnonymousJoin?: boolean;
  allowInviteGuests?: boolean;
  allowRecording?: boolean;
  allowSharing?: boolean;
  allowComments?: boolean;
  allowTranscription?: boolean;
  allowMaterialsUpload?: boolean;
  allowMaterialsDownload?: boolean;
  allowChat?: boolean;
}

/**
 * Opens the classroom settings dialog by clicking the settings button
 */
export async function openClassroomSettingsDialog(page: Page): Promise<void> {
  await page.waitForLoadState('domcontentloaded');
  
  // Click the settings dialog button
  const settingsButton = page.getByTestId('settings-dialog-button');
  await expect(settingsButton).toBeVisible({ timeout: 15000 });
  await settingsButton.click();
  
  // Wait for the dialog to open (check for either dialog or drawer based on screen size)
  const dialog = page.getByTestId('classroom-settings-dialog');
  const drawer = page.getByTestId('classroom-settings-drawer');
  
  // Wait for either dialog or drawer to be visible
  await expect(dialog.or(drawer)).toBeVisible({ timeout: 15000 });
}

/**
 * Navigates to a specific category in the classroom settings
 * @param page - Playwright page object
 * @param categoryIndex - Index of the category (0: General, 1: Access, 2: Recording, 3: Translation, 4: Materials, 5: In-Class)
 */
export async function navigateToCategory(page: Page, categoryIndex: number): Promise<void> {
  const categoryButton = page.getByTestId(`classroom-settings-category-${categoryIndex}`);
  await expect(categoryButton).toBeVisible({ timeout: 10000 });
  await categoryButton.click();
  
  // Wait for the category content to be visible
  const categoryContent = page.getByTestId('classroom-settings-category-content');
  await expect(categoryContent).toBeVisible({ timeout: 10000 });
}

/**
 * Enables all switches in the current category
 * @param page - Playwright page object
 */
export async function enableAllSwitchesInCurrentCategory(page: Page): Promise<void> {
  // Find all switch toggles in the current category
  const switches = page.getByTestId('classroom-settings-switch-toggle');
  const switchCount = await switches.count();
  
  for (let i = 0; i < switchCount; i++) {
    const switchElement = switches.nth(i);
    
    // Check if the switch is currently disabled (unchecked)
    const isChecked = await switchElement.isChecked();
    
    if (!isChecked) {
      await switchElement.click();
      // Wait a bit for the state to update
      await page.waitForTimeout(100);
    }
  }
}

/**
 * Disables all switches in the current category
 * @param page - Playwright page object
 */
export async function disableAllSwitchesInCurrentCategory(page: Page): Promise<void> {
  // Find all switch toggles in the current category
  const switches = page.getByTestId('classroom-settings-switch-toggle');
  const switchCount = await switches.count();
  
  for (let i = 0; i < switchCount; i++) {
    const switchElement = switches.nth(i);
    
    // Check if the switch is currently enabled (checked)
    const isChecked = await switchElement.isChecked();
    
    if (isChecked) {
      await switchElement.click();
      // Wait a bit for the state to update
      await page.waitForTimeout(100);
    }
  }
}

/**
 * Sets the classroom title and description in the General category
 * @param page - Playwright page object
 * @param title - Classroom title
 * @param description - Classroom description
 */
export async function setClassroomGeneralInfo(page: Page, title: string, description: string): Promise<void> {
  // Navigate to General category (index 0)
  await navigateToCategory(page, 0);
  
  // Set title
  const titleInput = page.getByTestId('classroom-settings-title-input');
  await expect(titleInput).toBeVisible({ timeout: 10000 });
  await titleInput.click();
  await titleInput.fill(title);
  
  // Set description
  const descriptionInput = page.getByTestId('classroom-settings-description-input');
  await expect(descriptionInput).toBeVisible({ timeout: 10000 });
  await descriptionInput.click();
  await descriptionInput.fill(description);
}

/**
 * Enables all switches in Access & Participation category
 * @param page - Playwright page object
 */
export async function enableAccessParticipationSwitches(page: Page): Promise<void> {
  await navigateToCategory(page, 1); // Access & Participation
  await enableAllSwitchesInCurrentCategory(page);
}

/**
 * Disables all switches in Access & Participation category
 * @param page - Playwright page object
 */
export async function disableAccessParticipationSwitches(page: Page): Promise<void> {
  await navigateToCategory(page, 1); // Access & Participation
  await disableAllSwitchesInCurrentCategory(page);
}

/**
 * Enables all switches in Recording & Sharing category
 * @param page - Playwright page object
 */
export async function enableRecordingSharingSwitches(page: Page): Promise<void> {
  await navigateToCategory(page, 2); // Recording & Sharing
  await enableAllSwitchesInCurrentCategory(page);
}

/**
 * Disables all switches in Recording & Sharing category
 * @param page - Playwright page object
 */
export async function disableRecordingSharingSwitches(page: Page): Promise<void> {
  await navigateToCategory(page, 2); // Recording & Sharing
  await disableAllSwitchesInCurrentCategory(page);
}

/**
 * Enables all switches in Translation category
 * @param page - Playwright page object
 */
export async function enableTranslationSwitches(page: Page): Promise<void> {
  await navigateToCategory(page, 3); // Translation
  await enableAllSwitchesInCurrentCategory(page);
}

/**
 * Disables all switches in Translation category
 * @param page - Playwright page object
 */
export async function disableTranslationSwitches(page: Page): Promise<void> {
  await navigateToCategory(page, 3); // Translation
  await disableAllSwitchesInCurrentCategory(page);
}

/**
 * Enables all switches in Materials category
 * @param page - Playwright page object
 */
export async function enableMaterialsSwitches(page: Page): Promise<void> {
  await navigateToCategory(page, 4); // Materials
  await enableAllSwitchesInCurrentCategory(page);
}

/**
 * Disables all switches in Materials category
 * @param page - Playwright page object
 */
export async function disableMaterialsSwitches(page: Page): Promise<void> {
  await navigateToCategory(page, 4); // Materials
  await disableAllSwitchesInCurrentCategory(page);
}

/**
 * Enables all switches in In-Class Interaction category
 * @param page - Playwright page object
 */
export async function enableInClassInteractionSwitches(page: Page): Promise<void> {
  await navigateToCategory(page, 5); // In-Class Interaction
  await enableAllSwitchesInCurrentCategory(page);
}

/**
 * Disables all switches in In-Class Interaction category
 * @param page - Playwright page object
 */
export async function disableInClassInteractionSwitches(page: Page): Promise<void> {
  await navigateToCategory(page, 5); // In-Class Interaction
  await disableAllSwitchesInCurrentCategory(page);
}

/**
 * Enables all switches across all categories
 * @param page - Playwright page object
 */
export async function enableAllClassroomSwitches(page: Page): Promise<void> {
  // Enable switches in each category
  await enableAccessParticipationSwitches(page);
  await enableRecordingSharingSwitches(page);
  await enableTranslationSwitches(page);
  await enableMaterialsSwitches(page);
  await enableInClassInteractionSwitches(page);
}

/**
 * Disables all switches across all categories (disables all admin settings)
 * @param page - Playwright page object
 */
export async function disableAllClassroomSwitches(page: Page): Promise<void> {
  // Disable switches in each category
  await disableAccessParticipationSwitches(page);
  await disableRecordingSharingSwitches(page);
  await disableTranslationSwitches(page);
  await disableMaterialsSwitches(page);
  await disableInClassInteractionSwitches(page);
}

/**
 * Closes the classroom settings dialog
 * @param page - Playwright page object
 */
export async function closeClassroomSettingsDialog(page: Page): Promise<void> {
  // Try to close via the close button (X button)
  const closeButton = page.locator('.dialog-close');
  if (await closeButton.isVisible()) {
    await closeButton.click();
  } else {
    // If no close button, try pressing Escape
    await page.keyboard.press('Escape');
  }
  
  // Wait for dialog/drawer to be hidden
  const dialog = page.getByTestId('classroom-settings-dialog');
  const drawer = page.getByTestId('classroom-settings-drawer');
  await expect(dialog.or(drawer)).toBeHidden({ timeout: 10000 });
}

/**
 * Complete workflow: Opens settings dialog and enables all switches
 * @param page - Playwright page object
 * @param options - Optional classroom settings to configure
 */
export async function configureClassroomSettings(
  page: Page, 
  options: ClassroomSettingsOptions = {}
): Promise<void> {
  // Open the settings dialog
  await openClassroomSettingsDialog(page);
  
  // Set general info if provided
  if (options.title || options.description) {
    await setClassroomGeneralInfo(
      page, 
      options.title || 'Test Classroom', 
      options.description || 'Test Description'
    );
  }
  
  // Enable all switches in all categories
  await enableAllClassroomSwitches(page);
  
  await page.waitForTimeout(1000);
  await closeClassroomSettingsDialog(page);
  await page.waitForTimeout(1000);
}

/**
 * Complete workflow: Opens settings dialog and disables all admin settings
 * @param page - Playwright page object
 * @param options - Optional classroom settings to configure
 */
export async function disableAllAdminSettings(
  page: Page, 
  options: ClassroomSettingsOptions = {}
): Promise<void> {
  // Open the settings dialog
  await openClassroomSettingsDialog(page);
  
  // Set general info if provided
  if (options.title || options.description) {
    await setClassroomGeneralInfo(
      page, 
      options.title || 'Test Classroom', 
      options.description || 'Test Description'
    );
  }
  
  // Disable all switches in all categories (disable all admin settings)
  await disableAllClassroomSwitches(page);
  
  await page.waitForTimeout(1000);
  await closeClassroomSettingsDialog(page);
  await page.waitForTimeout(1000);
}
