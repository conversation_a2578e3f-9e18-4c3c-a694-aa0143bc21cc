import { expect, Page } from "@playwright/test";
import { reliableGoto } from "../otherUtils/smartRetry";
import { configureClassroomSettings } from "../classroomUtils/classroomSettingsUtil";

export async function createClassroom(page: Page): Promise<string> {
    await page.waitForLoadState('domcontentloaded');
    await page.getByTestId('create-classroom-button').waitFor({ state: 'attached', timeout: 5000 });
    await page.getByTestId('create-classroom-button').click();
    await expect(page.getByTestId('settings-dialog-button')).toBeVisible({ timeout: 10000 });
    await configureClassroomSettings(page);

    await expect(page.getByTestId('classroom-settibbbngs-dialog')).toBeHidden({ timeout: 10000 });

    await page.waitForTimeout(1000);
    
    await page.waitForLoadState('domcontentloaded');
    const url = page.url();
    const pathParts = url.split('/classrooms/');
    
    if (pathParts.length < 2) {
        throw new Error(`Expected to be on a classroom page, but got: ${url}`);
    }
    
    const classroomId = pathParts[1].split('?')[0].split('#')[0];
    return classroomId;
}

export async function deleteClassroom(page: Page) {

    //Test delete classroom
    await page.evaluate(() => window.scrollTo(0, 0));
    await page.waitForLoadState('domcontentloaded');
    await page.getByTestId('settings-dialog-button').scrollIntoViewIfNeeded();
    await expect(page.getByTestId('settings-dialog-button')).toBeVisible();
    await page.getByTestId('settings-dialog-button').click();
    await expect(page.getByTestId('delete-classroom-button')).toBeVisible();
    await page.getByTestId('delete-classroom-button').click();
    await expect(page.getByTestId('delete-classroom-confirm-button')).toBeVisible();
    await page.getByTestId('delete-classroom-confirm-button').click();
    await page.waitForURL('/classrooms', { timeout: 10000 });
}

export async function renameClassroom(page: Page, newName: string, newDescription: string, classroomId: string) {
    await reliableGoto(page, `/classrooms/${classroomId}`);
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByTestId('classroom-title')).toBeVisible();
    await page.getByTestId('classroom-title').click();
    await page.getByTestId('classroom-title').fill(newName);
    await expect(page.getByTestId('classroom-title')).toHaveText(newName);

    await page.getByTestId('classroom-description').click();
    await page.getByTestId('classroom-description').fill(newDescription);
    await expect(page.getByTestId('classroom-description')).toHaveText(newDescription);
}
