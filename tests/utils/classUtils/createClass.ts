import { BrowserContext, expect } from '@playwright/test';
import { Page} from '@playwright/test';
import { connectToClass } from '../classroomUtils/connectToClass';
import { setupAdvancedEmulatedDevices } from './advancedEmulatedDevices';

export async function createClass(
  page: Page,
  classroomId: string,
) {
  await expect(page.getByTestId('create-class-trigger-header')).toBeVisible();
  await page.getByTestId('create-class-trigger-header').click();
  await connectToClass(page, false, 'teacher', classroomId);
}

export const newClass = async (
  page: Page,
  context: BrowserContext,
  classroomId: string,
): Promise<void> => {
  await expect(page.getByTestId('create-class-trigger-header')).toBeVisible();
  await page.getByTestId('create-class-trigger-header').click();

  // // We need to clear it later
  // await expect(page.getByTestId('aec-headphones-option')).toBeVisible({ timeout: 15000 });
  // await page.getByTestId('aec-headphones-option').click();
  // await page.waitForLoadState('domcontentloaded');

  await page.waitForLoadState('domcontentloaded');
  await expect(page.getByTestId('local-media-error-blocked-title')).toBeVisible(
    { timeout: 15000 },
  );
  await setupAdvancedEmulatedDevices(page, context);
  await page.getByTestId('local-media-error-reload-button').click();
  await page.waitForLoadState('domcontentloaded');
  await connectToClass(page, false, 'teacher', classroomId);
};