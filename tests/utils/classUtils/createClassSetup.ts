import { test, expect, Page, BrowserContext, chromium } from '@playwright/test';
import { reliableGoto } from '../otherUtils/smartRetry.ts';
import { loginWithFullAccess } from '../accountUtils/loginUtils.ts';
import { createClassroom } from '../classroomUtils/createClassroomUtils';
import { InviteLinks } from '../otherUtils/testWrappers.ts';
import { setupAdvancedEmulatedDevices } from './advancedEmulatedDevices';
import { createClass } from './createClass.ts';
import { connectToClass } from '../classroomUtils/connectToClass.ts';
import { inviteToClass } from './createInvites.ts';

export async function createClassSetup(
  page: Page,
  context: BrowserContext,
  browserName: string,
  accountNumber: {
    main:
      | 1
      | 2
      | 3
      | 4
      | 5
      | 6
      | 7
      | 8
      | 11
      | 15
      | 16
      | 17
      | 20
      | 21
      | 22
      | 23
      | 24;
    student:
      | 1
      | 2
      | 3
      | 4
      | 5
      | 6
      | 7
      | 8
      | 11
      | 15
      | 16
      | 17
      | 20
      | 21
      | 22
      | 23
      | 24;
    teacher:
      | 1
      | 2
      | 3
      | 4
      | 5
      | 6
      | 7
      | 8
      | 11
      | 15
      | 16
      | 17
      | 20
      | 21
      | 22
      | 23
      | 24;
    guest:
      | 1
      | 2
      | 3
      | 4
      | 5
      | 6
      | 7
      | 8
      | 11
      | 15
      | 16
      | 17
      | 20
      | 21
      | 22
      | 23
      | 24;
  },
) {
  test.skip(browserName === 'webkit');

  await setupAdvancedEmulatedDevices(page, context);
  await reliableGoto(page, '/');
  await loginWithFullAccess(page, accountNumber.main);
  const classroomId = await createClassroom(page);
  await createClass(page, classroomId);
  const inviteLinks = await inviteToClass(page);

  const currentBrowserType = context.browser()?.browserType() || chromium;
  const browserConfigs = [
    { type: currentBrowserType, device: null, name: 'Browser 1' },
    { type: currentBrowserType, device: null, name: 'Browser 2' },
    { type: currentBrowserType, device: null, name: 'Browser 3' },
  ];

  const roles = ['student', 'teacher', 'guest'];
  const openBrowsers: import('@playwright/test').Browser[] = [];

  for (let i = 0; i < 3; i++) {
    const role = roles[i];
    const inviteLink = inviteLinks[role as keyof InviteLinks];
    const browserConfig = browserConfigs[i];
    const newBrowser = await browserConfig.type.launch();
    openBrowsers.push(newBrowser);

    const newContext = await newBrowser.newContext();
    const newPage = await newContext.newPage();

    await setupAdvancedEmulatedDevices(newPage, newContext);

    await reliableGoto(newPage, inviteLink);
    await expect(newPage.getByTestId('sign-in-button')).toBeVisible();
    await expect(newPage).toHaveURL('/?login=true');

    const email = `Test${accountNumber[role as keyof typeof accountNumber]}@gmail.com`;
    const password = `Test${accountNumber[role as keyof typeof accountNumber]}@gmail.com`;

    await expect(newPage.getByTestId('sign-in-email-input')).toBeVisible();
    await newPage.getByTestId('sign-in-email-input').click();
    await newPage.getByTestId('sign-in-email-input').fill(email);
    await newPage.getByTestId('sign-in-password-input').click();
    await newPage.getByTestId('sign-in-password-input').fill(password);
    await newPage.getByTestId('dialog-sign-in-button').click();

    await connectToClass(
      newPage,
      true,
      role as 'student' | 'teacher' | 'guest',
      classroomId,
    );
  }
  return {
    inviteLinks,
    openBrowsers,
    classroomId,
  };
}
