// Class-related utilities
export { 
  setupAdvancedEmulatedDevices, 
  blockAdvancedMediaPermissions, 
  grantAdvancedMediaPermissions, 
  EMULATED_DEVICES, 
  getEmulatedDevice, 
  waitForDevicesReady, 
  forceDeviceContextRefresh, 
  forceReactContextPopulation, 
  debugReactDeviceState, 
  debugDeviceState, 
  verifyPermissionsBlocked 
} from './advancedEmulatedDevices';
export { audioDeviceTestBefore, audioDeviceTestAfter } from './audioDeviceTest';
export { createClass } from './createClass';
export { newClass } from './createClass';
export { createClassSetup } from './createClassSetup';
export { createInvites, inviteToClass, createInviteWithCustomSettings, type InviteLinks } from './createInvites';
export { 
  detectIncomingAudio, 
  detectMeaningfulAudio, 
  isPageReceivingAudio, 
  getCurrentAudioLevel, 
  waitForAudioChange, 
  type AudioDetectionResult, 
  type AudioDetectionOptions 
} from './detectIncomingAudio';
export { muteUnmute } from './muteUnmute';
