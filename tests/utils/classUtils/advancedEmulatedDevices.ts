import { Page, BrowserContext } from '@playwright/test';

interface EmulatedDevice {
    deviceId: string;
    groupId: string;
    kind: MediaDeviceKind;
    label: string;
}

interface ExtendedMediaTrack extends MediaStreamTrack {
    customLabel?: string;
    instanceId?: string;
    deviceId?: string;
}

// Device configurations
const CAMERA_DEVICES: EmulatedDevice[] = [
    {
        deviceId: 'camera-built-in-001',
        groupId: 'group-camera-1',
        kind: 'videoinput',
        label: 'Built-in Camera (Front)'
    },
    {
        deviceId: 'camera-external-002',
        groupId: 'group-camera-2',
        kind: 'videoinput',
        label: 'External USB Camera'
    },
    {
        deviceId: 'camera-virtual-003',
        groupId: 'group-camera-3',
        kind: 'videoinput',
        label: 'Virtual Camera Pro'
    }
];

const MICROPHONE_DEVICES: EmulatedDevice[] = [
    {
        deviceId: 'mic-built-in-001',
        groupId: 'group-mic-1',
        kind: 'audioinput',
        label: 'Built-in Microphone'
    },
    {
        deviceId: 'mic-headset-002',
        groupId: 'group-mic-2',
        kind: 'audioinput',
        label: 'Headset Microphone'
    },
    {
        deviceId: 'mic-external-003',
        groupId: 'group-mic-3',
        kind: 'audioinput',
        label: 'External USB Microphone'
    }
];

const SPEAKER_DEVICES: EmulatedDevice[] = [
    {
        deviceId: 'speaker-built-in-001',
        groupId: 'group-speaker-1',
        kind: 'audiooutput',
        label: 'Built-in Speakers'
    },
    {
        deviceId: 'speaker-headphones-002',
        groupId: 'group-speaker-2',
        kind: 'audiooutput',
        label: 'Headphones'
    },
    {
        deviceId: 'speaker-external-003',
        groupId: 'group-speaker-3',
        kind: 'audiooutput',
        label: 'External Speakers'
    }
];

async function grantPermissionsHelper(context: BrowserContext, _page: Page) {
    const permissionSets = [
        ['microphone', 'camera'],
        []
    ];

    for (const permissions of permissionSets) {
        try {
            if (permissions.length > 0) {
                await context.grantPermissions(permissions);
            } else {
                console.log('Advanced: No permissions to grant - relying on mocked getUserMedia');
            }
            return true;
        } catch (error) {
            console.log(`Advanced: Failed to grant permissions [${permissions.join(', ')}]: ${error}`);
        }
    }
    
    console.log('Advanced: Could not grant browser permissions, relying on mocked getUserMedia');
    return false;
}

export async function setupAdvancedEmulatedDevices(page: Page, context: BrowserContext, shouldBlockPermissions: boolean = false) {
    await page.addInitScript((blockPermissions) => {
        console.log('🎥 Setting up advanced emulated devices...');
        const CAMERA_DEVICES = [
            { deviceId: 'camera-built-in-001', groupId: 'group-camera-1', kind: 'videoinput' as MediaDeviceKind, label: 'Built-in Camera (Front)' },
            { deviceId: 'camera-external-002', groupId: 'group-camera-2', kind: 'videoinput' as MediaDeviceKind, label: 'External USB Camera' },
            { deviceId: 'camera-virtual-003', groupId: 'group-camera-3', kind: 'videoinput' as MediaDeviceKind, label: 'Virtual Camera Pro' }
        ];
        
        const MICROPHONE_DEVICES = [
            { deviceId: 'mic-built-in-001', groupId: 'group-mic-1', kind: 'audioinput' as MediaDeviceKind, label: 'Built-in Microphone' },
            { deviceId: 'mic-headset-002', groupId: 'group-mic-2', kind: 'audioinput' as MediaDeviceKind, label: 'Headset Microphone' },
            { deviceId: 'mic-external-003', groupId: 'group-mic-3', kind: 'audioinput' as MediaDeviceKind, label: 'External USB Microphone' }
        ];
        
        const SPEAKER_DEVICES = [
            { deviceId: 'speaker-built-in-001', groupId: 'group-speaker-1', kind: 'audiooutput' as MediaDeviceKind, label: 'Built-in Speakers' },
            { deviceId: 'speaker-headphones-002', groupId: 'group-speaker-2', kind: 'audiooutput' as MediaDeviceKind, label: 'Headphones' },
            { deviceId: 'speaker-external-003', groupId: 'group-speaker-3', kind: 'audiooutput' as MediaDeviceKind, label: 'External Speakers' }
        ];
        
        if (!navigator.mediaDevices) {
            (navigator as unknown as { mediaDevices: MediaDevices }).mediaDevices = {} as MediaDevices;
        }
        
        const allDevices = [...CAMERA_DEVICES, ...MICROPHONE_DEVICES, ...SPEAKER_DEVICES];
        
        // Create a unique identifier for this browser instance
        const instanceId = Math.random().toString(36).substr(2, 9);
        
        // Helper function to create animated video stream
        function createAnimatedVideoStream(selectedCamera: EmulatedDevice, instanceId: string): MediaStream {
            const canvas = document.createElement('canvas');
            canvas.width = 640;
            canvas.height = 480;
            const ctx = canvas.getContext('2d')!;
            
            const startTime = Date.now();
            let animationFrame = 0;
            
            function animate() {
                // Clear canvas
                ctx.clearRect(0, 0, 640, 480);
                
                // Set background color based on camera type
                if (selectedCamera.deviceId.includes('built-in')) {
                    ctx.fillStyle = '#4CAF50';
                } else if (selectedCamera.deviceId.includes('external')) {
                    ctx.fillStyle = '#2196F3';
                } else {
                    ctx.fillStyle = '#9C27B0';
                }
                
                ctx.fillRect(0, 0, 640, 480);
                
                // Add animated elements
                const currentTime = Date.now();
                const elapsed = (currentTime - startTime) / 1000;
                
                // Moving circle animation
                const circleX = 320 + Math.sin(elapsed) * 100;
                const circleY = 240 + Math.cos(elapsed * 0.7) * 80;
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.beginPath();
                ctx.arc(circleX, circleY, 30, 0, 2 * Math.PI);
                ctx.fill();
                
                // Instance ID and timestamp
                ctx.fillStyle = 'white';
                ctx.font = 'bold 20px Arial';
                ctx.fillText(`Instance: ${instanceId}`, 20, 40);
                ctx.font = 'bold 16px Arial';
                ctx.fillText(`Camera: ${selectedCamera.label}`, 20, 70);
                ctx.fillText(`Time: ${elapsed.toFixed(1)}s`, 20, 100);
                ctx.fillText(`Frame: ${animationFrame}`, 20, 130);
                
                // Add camera type icon
                ctx.font = 'bold 24px Arial';
                if (selectedCamera.deviceId.includes('built-in')) {
                    ctx.fillText('Built-in Camera', 180, 300);
                } else if (selectedCamera.deviceId.includes('external')) {
                    ctx.fillText('USB Camera', 180, 300);
                } else {
                    ctx.fillText('Virtual Camera', 180, 300);
                }
                
                animationFrame++;
                requestAnimationFrame(animate);
            }
            
            animate();
            
            // Create stream from canvas with higher frame rate
            const videoStream = canvas.captureStream(30);
            return videoStream;
        }
        
        // Enhanced device enumeration with better reliability
        Object.defineProperty(navigator.mediaDevices, 'enumerateDevices', {
            writable: true,
            value: async () => {
                console.log('📱 Enumerating devices:', allDevices.length, 'devices available');
                console.log('📱 Device types:', {
                    cameras: CAMERA_DEVICES.length,
                    microphones: MICROPHONE_DEVICES.length,
                    speakers: SPEAKER_DEVICES.length
                });
                
                // Ensure devices are properly formed MediaDeviceInfo objects
                const deviceInfos = allDevices.map(device => {
                    return {
                        deviceId: device.deviceId,
                        groupId: device.groupId,
                        kind: device.kind,
                        label: device.label,
                        toJSON: () => ({
                            deviceId: device.deviceId,
                            groupId: device.groupId,
                            kind: device.kind,
                            label: device.label
                        })
                    } as MediaDeviceInfo;
                });
                
                return deviceInfos;
            }
        });
        
        Object.defineProperty(navigator.mediaDevices, 'getUserMedia', {
            writable: true,
            value: async (constraints: MediaStreamConstraints) => {
                console.log('🎤 getUserMedia called with constraints:', JSON.stringify(constraints));
                
                // If permissions are blocked, throw DOMException like real browsers
                if (blockPermissions) {
                    console.log('🚫 Permissions blocked, throwing NotAllowedError');
                    throw new DOMException('Permission denied', 'NotAllowedError');
                }
                
                const stream = new MediaStream();
                
                // Handle video constraints
                if (constraints.video) {
                    let selectedCamera = CAMERA_DEVICES[0];
                    
                    if (typeof constraints.video === 'object' && constraints.video.deviceId) {
                        let requestedDeviceId: string | undefined;
                        
                        if (typeof constraints.video.deviceId === 'string') {
                            requestedDeviceId = constraints.video.deviceId;
                        } else if (constraints.video.deviceId && typeof constraints.video.deviceId === 'object') {
                            const deviceConstraint = constraints.video.deviceId as ConstrainDOMString;
                            requestedDeviceId = (typeof deviceConstraint === 'object' && deviceConstraint !== null) 
                                ? (deviceConstraint as { exact?: string; ideal?: string }).exact || (deviceConstraint as { exact?: string; ideal?: string }).ideal
                                : undefined;
                        }
                        
                        if (requestedDeviceId) {
                            const foundCamera = CAMERA_DEVICES.find(cam => cam.deviceId === requestedDeviceId);
                            if (foundCamera) {
                                selectedCamera = foundCamera;
                            }
                        }
                    }
                    
                    console.log('📹 Creating video stream with camera:', selectedCamera.label);
                    
                    // Create animated video stream
                    const videoStream = createAnimatedVideoStream(selectedCamera, instanceId);
                    const videoTrack = videoStream.getVideoTracks()[0];
                    
                    // Add custom properties to help identify the track
                    (videoTrack as ExtendedMediaTrack).customLabel = selectedCamera.label;
                    (videoTrack as ExtendedMediaTrack).instanceId = instanceId;
                    (videoTrack as ExtendedMediaTrack).deviceId = selectedCamera.deviceId;
                    
                    const originalVideoGetSettings = videoTrack.getSettings.bind(videoTrack);
                    videoTrack.getSettings = () => ({
                        ...originalVideoGetSettings(),
                        deviceId: selectedCamera.deviceId,
                        label: selectedCamera.label,
                    });
                    
                    stream.addTrack(videoTrack);
                }
                
                // Handle audio constraints  
                if (constraints.audio) {
                    let selectedMic = MICROPHONE_DEVICES[0];
                    
                    if (typeof constraints.audio === 'object' && constraints.audio.deviceId) {
                        let requestedDeviceId: string | undefined;
                        
                        if (typeof constraints.audio.deviceId === 'string') {
                            requestedDeviceId = constraints.audio.deviceId;
                        } else if (constraints.audio.deviceId && typeof constraints.audio.deviceId === 'object') {
                            const deviceConstraint = constraints.audio.deviceId as ConstrainDOMString;
                            requestedDeviceId = (typeof deviceConstraint === 'object' && deviceConstraint !== null) 
                                ? (deviceConstraint as { exact?: string; ideal?: string }).exact || (deviceConstraint as { exact?: string; ideal?: string }).ideal
                                : undefined;
                        }
                        
                        if (requestedDeviceId) {
                            const foundMic = MICROPHONE_DEVICES.find(mic => mic.deviceId === requestedDeviceId);
                            if (foundMic) {
                                selectedMic = foundMic;
                            }
                        }
                    }
                    
                    console.log('🎤 Creating audio stream with microphone:', selectedMic.label);
                    
                    try {
                        const audioContext = new AudioContext();
                        const dest = audioContext.createMediaStreamDestination();
                        
                        // Create voice-like audio with multiple oscillators and dynamic patterns
                        function createVoiceLikeAudio() {
                            // Base frequency depends on mic type (simulating different voice characteristics)
                            let baseFreq = 150; // Default bass voice
                            
                        if (selectedMic.deviceId.includes('built-in')) {
                                baseFreq = 200; // Mid-range voice
                        } else if (selectedMic.deviceId.includes('headset')) {
                                baseFreq = 250; // Higher voice
                            }
                            
                            
                            // Fundamental frequency (main voice)
                            const fundamental = audioContext.createOscillator();
                            const fundamentalGain = audioContext.createGain();
                            fundamental.frequency.value = baseFreq;
                            fundamental.type = 'sawtooth'; // More voice-like than sine wave
                            fundamentalGain.gain.value = 0.25; // Increased from 0.15
                            
                            // First harmonic (adds richness)
                            const harmonic1 = audioContext.createOscillator();
                            const harmonic1Gain = audioContext.createGain();
                            harmonic1.frequency.value = baseFreq * 2;
                            harmonic1.type = 'triangle';
                            harmonic1Gain.gain.value = 0.08;
                            
                            // Second harmonic (adds brightness)
                            const harmonic2 = audioContext.createOscillator();
                            const harmonic2Gain = audioContext.createGain();
                            harmonic2.frequency.value = baseFreq * 3;
                            harmonic2.type = 'sine';
                            harmonic2Gain.gain.value = 0.04;
                            
                            // Master gain for overall volume control
                            const masterGain = audioContext.createGain();
                            masterGain.gain.value = 0.3; // Increased from 0.1 to 0.3 for higher peaks
                            
                            // Connect all oscillators
                            fundamental.connect(fundamentalGain);
                            harmonic1.connect(harmonic1Gain);
                            harmonic2.connect(harmonic2Gain);
                            
                            fundamentalGain.connect(masterGain);
                            harmonic1Gain.connect(masterGain);
                            harmonic2Gain.connect(masterGain);
                            masterGain.connect(dest);
                            
                            // Start all oscillators
                            fundamental.start();
                            harmonic1.start();
                            harmonic2.start();
                            
                            // Create voice-like patterns with amplitude and frequency modulation
                            function createSpeechPattern() {
                                const now = audioContext.currentTime;
                                const patternDuration = 2 + Math.random() * 3; // 2-5 second patterns
                                
                                // Create speech-like volume envelope (rises then falls)
                                const attackTime = 0.1;
                                const sustainTime = patternDuration * 0.6;
                                
                                // Volume pattern: quiet -> loud -> medium -> quiet (increased peak levels)
                                masterGain.gain.setValueAtTime(0.05, now);
                                masterGain.gain.linearRampToValueAtTime(0.4, now + attackTime);
                                masterGain.gain.linearRampToValueAtTime(0.3, now + attackTime + sustainTime);
                                masterGain.gain.linearRampToValueAtTime(0.02, now + patternDuration);
                                
                                // Add pitch variations like natural speech
                                const pitchVariations = Math.floor(3 + Math.random() * 4); // 3-6 pitch changes
                                for (let i = 0; i < pitchVariations; i++) {
                                    const timePoint = now + (patternDuration / pitchVariations) * i;
                                    const pitchMultiplier = 0.9 + Math.random() * 0.3; // ±15% pitch variation
                                    
                                    fundamental.frequency.setValueAtTime(baseFreq * pitchMultiplier, timePoint);
                                    harmonic1.frequency.setValueAtTime(baseFreq * 2 * pitchMultiplier, timePoint);
                                    harmonic2.frequency.setValueAtTime(baseFreq * 3 * pitchMultiplier, timePoint);
                                }
                                
                                // Schedule next pattern
                                setTimeout(() => {
                                    if (audioContext.state !== 'closed') {
                                        createSpeechPattern();
                                    }
                                }, patternDuration * 1000);
                            }
                            
                            // Start the speech pattern
                            createSpeechPattern();
                            
                            // Audio setup complete - oscillators are running
                        }
                        
                        // Create the voice-like audio
                        createVoiceLikeAudio();
                        
                        const audioTrack = dest.stream.getAudioTracks()[0];
                        
                        // Add custom properties
                        (audioTrack as ExtendedMediaTrack).customLabel = selectedMic.label;
                        (audioTrack as ExtendedMediaTrack).instanceId = instanceId;
                        (audioTrack as ExtendedMediaTrack).deviceId = selectedMic.deviceId;
                        

                        const originalGetSettings = audioTrack.getSettings.bind(audioTrack);
                        audioTrack.getSettings = () => ({
                            ...originalGetSettings(),
                            deviceId: selectedMic.deviceId,
                            label: selectedMic.label,
                        });
                        
                        stream.addTrack(audioTrack);
                    } catch (error) {
                        console.log('Could not create audio track:', error);
                    }
                }
                
                console.log('✅ Stream created successfully with tracks:', {
                    audio: stream.getAudioTracks().length,
                    video: stream.getVideoTracks().length,
                    total: stream.getTracks().length
                });
                
                return stream;
            }
        });
        
        // Add legacy browser support (copied from emulatedDevices.ts)
        if ((navigator as unknown as Record<string, unknown>).getUserMedia) {
            (navigator as unknown as Record<string, unknown>).getUserMedia = navigator.mediaDevices.getUserMedia;
        }
        
        if ((navigator as unknown as Record<string, unknown>).webkitGetUserMedia) {
            (navigator as unknown as Record<string, unknown>).webkitGetUserMedia = navigator.mediaDevices.getUserMedia;
        }
        
        // Store instance ID and device readiness globally for debugging
        (window as unknown as { 
            testInstanceId: string;
            devicesReady: boolean;
            getDeviceCount: () => number;
        }).testInstanceId = instanceId;
        
        (window as unknown as { devicesReady: boolean }).devicesReady = true;
        (window as unknown as { getDeviceCount: () => number }).getDeviceCount = () => allDevices.length;
        
        // Force initial device enumeration and storage
        const forceInitialDeviceSetup = async () => {
            try {
                console.log('🔄 Forcing initial device enumeration...');
                
                // Manually trigger device enumeration to populate context
                const devices = await navigator.mediaDevices.enumerateDevices();
                console.log('📱 Initial enumeration found:', devices.length, 'devices');
                
                // Trigger multiple devicechange events to ensure hooks catch it
                for (let i = 0; i < 3; i++) {
                    setTimeout(() => {
                        if (navigator.mediaDevices && navigator.mediaDevices.dispatchEvent) {
                            const event = new Event('devicechange');
                            navigator.mediaDevices.dispatchEvent(event);
                            console.log(`🔄 Triggered devicechange event #${i + 1}`);
                        }
                    }, 100 + (i * 200)); // Stagger the events
                }
                
            } catch (error) {
                console.log('❌ Error in initial device setup:', error);
            }
        };
        
        // Run initial setup immediately and after a short delay
        forceInitialDeviceSetup();
        setTimeout(forceInitialDeviceSetup, 500);
        
        console.log('🎉 Advanced emulated devices setup complete!');
    }, shouldBlockPermissions);
    
    if (shouldBlockPermissions) {
        try {
            await context.clearPermissions();
        } catch (error) {
            console.log('Advanced: Could not clear permissions:', error);
        }
    } else {
        await grantPermissionsHelper(context, page);
    }
    
}

export async function blockAdvancedMediaPermissions(page: Page, context: BrowserContext) {
    await setupAdvancedEmulatedDevices(page, context, true);
}

export async function grantAdvancedMediaPermissions(page: Page, context: BrowserContext) {
    await setupAdvancedEmulatedDevices(page, context, false);
}

// Export device configurations for tests that need to reference specific devices
export const EMULATED_DEVICES = {
    cameras: CAMERA_DEVICES,
    microphones: MICROPHONE_DEVICES,
    speakers: SPEAKER_DEVICES,
    all: [...CAMERA_DEVICES, ...MICROPHONE_DEVICES, ...SPEAKER_DEVICES]
};

// Helper function to get device by type and index
export function getEmulatedDevice(type: 'camera' | 'microphone' | 'speaker', index: number): EmulatedDevice | null {
    const devices = {
        camera: CAMERA_DEVICES,
        microphone: MICROPHONE_DEVICES, 
        speaker: SPEAKER_DEVICES
    };
    
    return devices[type][index] || null;
}

// Utility function to wait for devices to be ready
export async function waitForDevicesReady(page: Page, timeout: number = 15000): Promise<boolean> {
    console.log('⏳ Waiting for emulated devices to be ready...');
    
    try {
        const result = await page.waitForFunction(
            () => {
                // Check if our emulation is ready
                const devicesReady = (window as unknown as { devicesReady?: boolean })?.devicesReady;
                const deviceCount = (window as unknown as { getDeviceCount?: () => number })?.getDeviceCount?.();
                
                console.log('Device readiness check:', { devicesReady, deviceCount });
                
                if (!devicesReady || !deviceCount) {
                    return false;
                }
                
                // Verify devices are actually enumerable
                if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
                    navigator.mediaDevices.enumerateDevices().then(devices => {
                        console.log('Current device count:', devices.length);
                    }).catch(e => {
                        console.log('Error enumerating devices:', e);
                    });
                }
                
                return deviceCount >= 9; // 3 cameras + 3 mics + 3 speakers
            },
            {},
            { timeout }
        );
        
        console.log('✅ Emulated devices are ready!');
        return !!result;
    } catch (error) {
        console.log('❌ Timeout waiting for emulated devices:', error);
        return false;
    }
}

// Utility function to force device context refresh
export async function forceDeviceContextRefresh(page: Page): Promise<void> {
    console.log('🔄 Forcing device context refresh...');
    
    await page.evaluate(() => {
        // Force multiple devicechange events after a stream might be available
        const triggerDeviceChange = () => {
            if (navigator.mediaDevices && navigator.mediaDevices.dispatchEvent) {
                const event = new Event('devicechange');
                navigator.mediaDevices.dispatchEvent(event);
                console.log('🔄 Manual devicechange event triggered');
            }
        };
        
        // Trigger immediately and multiple times
        triggerDeviceChange();
        setTimeout(triggerDeviceChange, 100);
        setTimeout(triggerDeviceChange, 300);
        setTimeout(triggerDeviceChange, 500);
        setTimeout(triggerDeviceChange, 1000);
    });
    
    await page.waitForTimeout(1500); // Wait for events to process
}

// Utility function to force React context population
export async function forceReactContextPopulation(page: Page): Promise<void> {
    console.log('⚛️ Forcing React context population...');
    
    await page.evaluate(async () => {
        try {
            // Try to find and directly populate React context
            const devices = await navigator.mediaDevices.enumerateDevices();
            console.log('⚛️ Found devices for context:', devices.length);
            
            // Look for React components in the DOM that might need refreshing
            const settingsComponents = document.querySelectorAll('[data-testid*="device"], [data-testid*="audio"], [data-testid*="video"]');
            console.log('⚛️ Found settings components:', settingsComponents.length);
            
            // Trigger custom events that React components might listen to
            const customEvent = new CustomEvent('forceDeviceRefresh', { 
                detail: { devices } 
            });
            document.dispatchEvent(customEvent);
            window.dispatchEvent(customEvent);
            
            // Try to force a re-render by dispatching multiple events
            ['devicechange', 'mediadevicechange', 'change'].forEach(eventType => {
                const event = new Event(eventType, { bubbles: true });
                if (navigator.mediaDevices && navigator.mediaDevices.dispatchEvent) {
                    navigator.mediaDevices.dispatchEvent(event);
                }
                document.dispatchEvent(event);
                window.dispatchEvent(event);
            });
            
            console.log('⚛️ Triggered custom events for React context');
            
        } catch (error) {
            console.log('⚛️ Error forcing React context population:', error);
        }
    });
    
    await page.waitForTimeout(1000);
}

// Utility function to debug React component state
export async function debugReactDeviceState(page: Page): Promise<void> {
    console.log('⚛️ === REACT DEVICE STATE DEBUG ===');
    
    const reactState = await page.evaluate(() => {
        try {
            // Check if settings sidebar is available
            const settingsSidebar = document.querySelector('[data-testid="settings-sidebar"]');
            const settingsAccordion = document.querySelector('[data-testid="settings-accordion"]');
            
            // Look for device components
            const audioInputDevices = document.querySelectorAll('[data-testid*="audio-input-device"]');
            const audioOutputDevices = document.querySelectorAll('[data-testid*="audio-output-device"]');
            const videoDevices = document.querySelectorAll('[data-testid*="video-device"]');
            
            // Check if device containers exist
            const audioInputContainers = document.querySelectorAll('[data-testid*="audio-input-device-container"]');
            const audioOutputContainers = document.querySelectorAll('[data-testid*="audio-output-device-container"]');
            const videoContainers = document.querySelectorAll('[data-testid*="video-device-container"]');
            
            return {
                domElements: {
                    settingsSidebar: !!settingsSidebar,
                    settingsAccordion: !!settingsAccordion,
                    audioInputDevices: audioInputDevices.length,
                    audioOutputDevices: audioOutputDevices.length,
                    videoDevices: videoDevices.length,
                    audioInputContainers: audioInputContainers.length,
                    audioOutputContainers: audioOutputContainers.length,
                    videoContainers: videoContainers.length
                },
                deviceLabels: {
                    audioInput: Array.from(audioInputDevices).map(el => el.textContent?.trim()),
                    audioOutput: Array.from(audioOutputDevices).map(el => el.textContent?.trim()),
                    video: Array.from(videoDevices).map(el => el.textContent?.trim())
                }
            };
        } catch (error) {
            return {
                error: error.message,
                domElements: {},
                deviceLabels: {}
            };
        }
    });
    
    console.log(JSON.stringify(reactState, null, 2));
    console.log('====================================');
}

// Utility function to debug current device state
export async function debugDeviceState(page: Page): Promise<void> {
    const deviceState = await page.evaluate(async () => {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            return {
                success: true,
                deviceCount: devices.length,
                cameras: devices.filter(d => d.kind === 'videoinput').length,
                microphones: devices.filter(d => d.kind === 'audioinput').length,
                speakers: devices.filter(d => d.kind === 'audiooutput').length,
                devices: devices.map(d => ({
                    id: d.deviceId,
                    kind: d.kind,
                    label: d.label,
                    groupId: d.groupId
                })),
                windowState: {
                    devicesReady: (window as unknown as { devicesReady?: boolean })?.devicesReady,
                    instanceId: (window as unknown as { testInstanceId?: string })?.testInstanceId,
                    deviceCount: (window as unknown as { getDeviceCount?: () => number })?.getDeviceCount?.()
                }
            };
        } catch (error) {
            return {
                success: false,
                error: (error as Error).message,
                windowState: {
                    devicesReady: (window as unknown as { devicesReady?: boolean })?.devicesReady,
                    instanceId: (window as unknown as { testInstanceId?: string })?.testInstanceId,
                    deviceCount: (window as unknown as { getDeviceCount?: () => number })?.getDeviceCount?.()
                }
            };
        }
    });
    
    console.log('🔍 === DEVICE STATE DEBUG ===');
    console.log(JSON.stringify(deviceState, null, 2));
    console.log('============================');
}

// Utility function to test if permissions are properly blocked
export async function verifyPermissionsBlocked(page: Page): Promise<boolean> {
    try {
        const permissionTest = await page.evaluate(async () => {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
                // If we get here, permissions weren't blocked
                stream.getTracks().forEach(track => track.stop());
                return { blocked: false, error: null };
            } catch (error: unknown) {
                const err = error as Error;
                return { 
                    blocked: true, 
                    error: err.name,
                    message: err.message 
                };
            }
        });
        
        return permissionTest.blocked && permissionTest.error === 'NotAllowedError';
    } catch (error) {
        console.log('Advanced: Could not verify permissions:', error);
        return false;
    }
}
