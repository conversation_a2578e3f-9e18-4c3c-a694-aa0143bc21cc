import { expect, Page } from '@playwright/test';

export interface InviteLinks {
    teacher: string;
    student: string;
    guest: string;
}

export async function createInvites(page: Page, teacher: boolean = true, student: boolean = true, guest: boolean = true) {
    const inviteLinks: { [key: string]: string } = {};

    await page.context().grantPermissions(['clipboard-read', 'clipboard-write', 'notifications']);

    // Test Teacher invite
    if (teacher) {
        await expect(page.getByTestId('invite-form-role-teacher')).toBeVisible();
        await page.getByTestId('invite-form-role-teacher').click();
        await expect(page.getByTestId('invite-share-copy-button')).toBeVisible();
        
        // Copy the invite link
        await page.getByTestId('invite-share-copy-button').click();
        await page.waitForTimeout(3000);
        
        // Get the copied link from clipboard
        const teacherLink = await page.evaluate(async () => {
            try {
                const text = await navigator.clipboard.readText();
                console.log('Teacher invite link copied:', text);
                return text;
            } catch (error) {
                console.log('Failed to read clipboard for teacher:', error);
                return null;
            }
        });
        
        if (teacherLink) {
            inviteLinks.teacher = teacherLink;
        }
        await page.waitForTimeout(500);
    }

    // Test Student invite
    if (student) {
        await expect(page.getByTestId('invite-form-role-student')).toBeVisible();
        await page.getByTestId('invite-form-role-student').click();
        await expect(page.getByTestId('invite-share-copy-button')).toBeVisible();
        
        // Copy the invite link
        await page.getByTestId('invite-share-copy-button').click();
        await page.waitForTimeout(3000);
        
        // Get the copied link from clipboard
        const studentLink = await page.evaluate(async () => {
            try {
                const text = await navigator.clipboard.readText();
                console.log('Student invite link copied:', text);
                return text;
            } catch (error) {
                console.log('Failed to read clipboard for student:', error);
                return null;
            }
        });
        
        if (studentLink) {
            inviteLinks.student = studentLink;
        }
        await page.waitForTimeout(500);
    }

    // Test Guest invite
    if (guest) {    
        await expect(page.getByTestId('invite-form-role-guest')).toBeVisible();
        await page.getByTestId('invite-form-role-guest').click();
        await expect(page.getByTestId('invite-share-copy-button')).toBeVisible();
        
        // Copy the invite link
        await page.getByTestId('invite-share-copy-button').click();
        await page.waitForTimeout(3000);
        
        // Get the copied link from clipboard
        const guestLink = await page.evaluate(async () => {
            try {
                const text = await navigator.clipboard.readText();
                console.log('Guest invite link copied:', text);
                return text;
            } catch (error) {
                console.log('Failed to read clipboard for guest:', error);
                return null;
            }
        });
        
        if (guestLink) {
            inviteLinks.guest = guestLink;
        }
    }
    
    console.log('All invite links captured:', inviteLinks);
    await page.keyboard.press('Escape');
    await page.waitForTimeout(500);
    return inviteLinks;
} 

export async function inviteToClass(page: Page) {
  await page.context().grantPermissions(['clipboard-read', 'clipboard-write', 'notifications']);

    await page.getByTestId('action-bar-invite-button').click();
    await expect(page.getByTestId('invite-form-role-teacher')).toBeVisible();

    const inviteLinks: InviteLinks = {} as InviteLinks;

    // Select teacher role and copy link
    await page.getByTestId('invite-form-role-teacher').click();
    await expect(page.getByTestId('invite-share-copy-button')).toBeVisible();
    await page.waitForTimeout(1000);
    
    await page.getByTestId('invite-share-copy-button').click();
    await page.waitForTimeout(3000);
    
    // Get teacher invite link from clipboard
    const teacherLink = await page.evaluate(async () => {
        try {
            const text = await navigator.clipboard.readText();
            console.log('Teacher invite link copied:', text);
            return text;
        } catch (error) {
            console.log('Failed to read clipboard for teacher:', error);
            return null;
        }
    });
    
    if (teacherLink) {
        inviteLinks.teacher = teacherLink;
    }

    // Select student role and copy link
    await page.getByTestId('invite-form-role-student').click();
    await page.waitForTimeout(1000);
    
    await page.getByTestId('invite-share-copy-button').click();
    await page.waitForTimeout(3000);
    
    // Get student invite link from clipboard
    const studentLink = await page.evaluate(async () => {
        try {
            const text = await navigator.clipboard.readText();
            console.log('Student invite link copied:', text);
            return text;
        } catch (error) {
            console.log('Failed to read clipboard for student:', error);
            return null;
        }
    });
    
    if (studentLink) {
        inviteLinks.student = studentLink;
    }

    // Select guest role and copy link
    await page.getByTestId('invite-form-role-guest').click();
    await page.waitForTimeout(1000);
    
    await page.getByTestId('invite-share-copy-button').click();
    await page.waitForTimeout(3000);
    
    // Get guest invite link from clipboard
    const guestLink = await page.evaluate(async () => {
        try {
            const text = await navigator.clipboard.readText();
            console.log('Guest invite link copied:', text);
            return text;
        } catch (error) {
            console.log('Failed to read clipboard for guest:', error);
            return null;
        }
    });
    
    if (guestLink) {
        inviteLinks.guest = guestLink;
    }

    // Close the dialog
    await page.keyboard.press('Escape');
    
    console.log('Class invite links captured:', inviteLinks);
    return inviteLinks;
}

export async function createInviteWithCustomSettings(
    page: Page, 
    role: 'teacher' | 'student' | 'guest' = 'guest',
    options: {
        isEvent?: boolean;
        requireSignUp?: boolean;
        title?: string;
        description?: string;
        expirationPeriod?: string;
        usesLimit?: number;
    } = {}
) {
    // Grant clipboard permissions first
    await page.context().grantPermissions(['clipboard-read', 'clipboard-write']);
    
    // Click the appropriate invite button based on context
    if (page.url().includes('/class/')) {
        await page.getByTestId('action-bar-invite-button').click();
    } else {
        await page.getByTestId(`participants-invite-${role}`).click();
    }
    
    await expect(page.getByTestId('invite-form-role-teacher')).toBeVisible();
    
    // Set role
    await page.getByTestId(`invite-form-role-${role}`).click();
    
    // Set title if provided
    if (options.title) {
        await page.getByTestId('invite-form-title-input').fill(options.title);
    }
    
    // Set description if provided
    if (options.description) {
        await page.getByTestId('invite-form-description-input').fill(options.description);
    }
    
    // Toggle require sign up if specified
    if (options.requireSignUp !== undefined) {
        const currentState = await page.getByTestId('invite-form-access-switch').getAttribute('data-state');
        if ((currentState === 'checked') !== options.requireSignUp) {
            await page.getByTestId('invite-form-access-switch').click();
        }
    }
    
    // Toggle event mode if specified
    if (options.isEvent !== undefined) {
        const currentState = await page.getByTestId('invite-form-event-switch').getAttribute('data-state');
        if ((currentState === 'checked') !== options.isEvent) {
            await page.getByTestId('invite-form-event-switch').click();
        }
    }
    
    // Set expiration period if event mode is enabled and period is specified
    if (options.isEvent && options.expirationPeriod) {
        await page.getByTestId('invite-form-expiration-select').click();
        await page.getByTestId(`invite-form-expiration-${options.expirationPeriod}`).click();
    }
    
    // Set uses limit if event mode is enabled and limit is specified
    if (options.isEvent && options.usesLimit !== undefined) {
        await page.getByTestId('invite-form-uses-limit-select').click();
        await page.getByTestId('invite-form-uses-limit-limited').click();
        await page.getByTestId('invite-form-uses-limit-input').fill(options.usesLimit.toString());
    }
    
    // Copy the invite link
    await page.getByTestId('invite-share-copy-button').click();
    await page.waitForTimeout(3000);
    
    // Get the copied invite link from clipboard
    const inviteLink = await page.evaluate(async () => {
        try {
            const text = await navigator.clipboard.readText();
            console.log('Custom invite link copied:', text);
            return text;
        } catch (error) {
            console.log('Failed to read clipboard for custom invite:', error);
            return null;
        }
    });
    
    // Close the dialog
    await page.keyboard.press('Escape');
    
    console.log('Custom invite link captured:', inviteLink);
    return inviteLink || true;
}