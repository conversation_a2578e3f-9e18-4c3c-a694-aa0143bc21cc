import { Page } from '@playwright/test';

export interface AudioDetectionResult {
    isReceivingAudio: boolean;
    averageLevel: number;
    peakLevel: number;
    duration: number;
}

export interface AudioDetectionOptions {
    /**
     * Duration to monitor audio in milliseconds
     * @default 3000
     */
    monitorDuration?: number;
    /**
     * Minimum audio level threshold to consider as "receiving audio"
     * @default 0.01
     */
    threshold?: number;
    /**
     * How often to sample audio levels in milliseconds
     * @default 100
     */
    sampleInterval?: number;
    /**
     * Minimum percentage of samples that must be above threshold
     * @default 0.3
     */
    minimumActivePercentage?: number;
}

/**
 * Detects if a page is currently receiving audio
 * @param page The Playwright page to monitor
 * @param options Configuration options for audio detection
 * @returns Promise resolving to audio detection results
 */
export async function detectIncomingAudio(
    page: Page, 
    options: AudioDetectionOptions = {}
): Promise<AudioDetectionResult> {
    const {
        monitorDuration = 3000,
        threshold = 0.01,
        sampleInterval = 100,
        minimumActivePercentage = 0.3
    } = options;

    // Inject audio monitoring script into the page
    const result = await page.evaluate(async ({
        duration,
        threshold: thresh,
        interval,
        minActivePercentage
    }) => {
        return new Promise<{
            isReceivingAudio: boolean;
            averageLevel: number;
            peakLevel: number;
            duration: number;
        }>((resolve) => {
            // Try to get existing audio context or create a new one
            let audioContext: AudioContext;
            
            try {
                // Check if there's already an AudioContext in the window
                const windowWithAudio = window as typeof window & {
                    AudioContext?: typeof AudioContext;
                    webkitAudioContext?: typeof AudioContext;
                };
                const existingContext = windowWithAudio.AudioContext || windowWithAudio.webkitAudioContext;
                if (existingContext) {
                    audioContext = new existingContext();
                } else {
                    throw new Error('AudioContext not available');
                }
            } catch (error) {
                resolve({
                    isReceivingAudio: false,
                    averageLevel: 0,
                    peakLevel: 0,
                    duration: 0
                });
                
                console.error('Error creating AudioContext:', error);
                return;
            }

            const analyzer = audioContext.createAnalyser();
            analyzer.fftSize = 256;
            const bufferLength = analyzer.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);

            const samples: number[] = [];
            let peakLevel = 0;

            // Get the audio destination (speakers) to monitor output
            try {
                // Connect to the audio destination to monitor what's being played
                const destination = audioContext.destination;
                
                // Create a gain node to tap into the audio stream
                const gainNode = audioContext.createGain();
                gainNode.gain.value = 1.0;
                
                // Connect the gain node to analyzer
                gainNode.connect(analyzer);
                
                // Try to connect to the destination's input
                // Note: This approach monitors audio output from the page
                const mediaElement = document.querySelector('audio, video') as HTMLMediaElement;
                if (mediaElement) {
                    const source = audioContext.createMediaElementSource(mediaElement);
                    source.connect(gainNode);
                    source.connect(destination);
                }
            } catch (error) {
               console.error('Error creating AudioContext:', error);
                // Fallback: try to get microphone input for testing
                navigator.mediaDevices?.getUserMedia({ audio: true })
                    .then(stream => {
                        const source = audioContext.createMediaStreamSource(stream);
                        source.connect(analyzer);
                    })
                    .catch(() => {
                        // If all fails, just monitor the destination
                        try {
                            const destination = audioContext.destination as AudioDestinationNode & {
                                connect?: (node: AudioNode) => void;
                            };
                            destination.connect?.(analyzer);
                        } catch (e) {
                            console.error('Error connecting to destination:', e);
                            // Last resort: create a dummy source
                            const oscillator = audioContext.createOscillator();
                            oscillator.frequency.value = 0;
                            oscillator.connect(analyzer);
                            oscillator.start();
                        }
                    });
            }

            const startTime = Date.now();
            
            const monitorAudio = () => {
                analyzer.getByteFrequencyData(dataArray);
                
                // Calculate RMS (Root Mean Square) for more accurate level detection
                let sum = 0;
                for (let i = 0; i < bufferLength; i++) {
                    const normalized = dataArray[i] / 255.0;
                    sum += normalized * normalized;
                }
                const rms = Math.sqrt(sum / bufferLength);
                
                samples.push(rms);
                peakLevel = Math.max(peakLevel, rms);
                
                const elapsed = Date.now() - startTime;
                
                if (elapsed < duration) {
                    setTimeout(monitorAudio, interval);
                } else {
                    // Calculate results
                    const averageLevel = samples.reduce((a, b) => a + b, 0) / samples.length;
                    const samplesAboveThreshold = samples.filter(level => level > thresh).length;
                    const activePercentage = samplesAboveThreshold / samples.length;
                    
                    resolve({
                        isReceivingAudio: activePercentage >= minActivePercentage && averageLevel > thresh,
                        averageLevel,
                        peakLevel,
                        duration: elapsed
                    });
                }
            };

            // Start monitoring after a brief delay to allow audio context to initialize
            setTimeout(monitorAudio, 100);
        });
    }, {
        duration: monitorDuration,
        threshold,
        interval: sampleInterval,
        minActivePercentage: minimumActivePercentage
    });

    return result;
}

/**
 * More robust audio detection that filters out background noise
 * @param page The Playwright page to monitor
 * @param options Configuration options for audio detection
 * @returns Promise resolving to audio detection results
 */
export async function detectMeaningfulAudio(
    page: Page, 
    options: AudioDetectionOptions = {}
): Promise<AudioDetectionResult> {
    const {
        monitorDuration = 3000,
        threshold = 0.01,
        sampleInterval = 100,
        minimumActivePercentage = 0.3
    } = options;

    // Inject enhanced audio monitoring script into the page
    const result = await page.evaluate(async ({
        duration,
        threshold: thresh,
        interval,
        minActivePercentage
    }) => {
        return new Promise<{
            isReceivingAudio: boolean;
            averageLevel: number;
            peakLevel: number;
            duration: number;
        }>((resolve) => {
            let audioContext: AudioContext;
            
            try {
                const windowWithAudio = window as typeof window & {
                    AudioContext?: typeof AudioContext;
                    webkitAudioContext?: typeof AudioContext;
                };
                const existingContext = windowWithAudio.AudioContext || windowWithAudio.webkitAudioContext;
                if (existingContext) {
                    audioContext = new existingContext();
                } else {
                    throw new Error('AudioContext not available');
                }
            } catch (error) {
                console.error('Error creating AudioContext:', error);
                resolve({
                    isReceivingAudio: false,
                    averageLevel: 0,
                    peakLevel: 0,
                    duration: 0
                });
                return;
            }

            const analyzer = audioContext.createAnalyser();
            analyzer.fftSize = 1024; // Larger FFT for better frequency resolution
            analyzer.smoothingTimeConstant = 0.1; // Less smoothing for more responsive detection
            const bufferLength = analyzer.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);

            const samples: number[] = [];
            const frequencyData: number[][] = [];
            let peakLevel = 0;

            // Try to connect to actual audio streams
            let connected = false;
            
            // Try to find audio/video elements first
            const mediaElements = document.querySelectorAll('audio, video') as NodeListOf<HTMLMediaElement>;
            for (const element of mediaElements) {
                try {
                    if (!element.paused && element.currentTime > 0) {
                        const source = audioContext.createMediaElementSource(element);
                        source.connect(analyzer);
                        source.connect(audioContext.destination);
                        connected = true;
                        break;
                    }
                } catch (e) {
                    console.error('Error connecting to media element:', e);
                }
            }

            // If no media elements, try Web Audio API connections
            if (!connected) {
                try {
                    // Create a gain node to monitor destination
                    const gainNode = audioContext.createGain();
                    gainNode.gain.value = 1.0;
                    gainNode.connect(analyzer);
                    
                    // Try to monitor the destination's output (if possible)
                    const destination = audioContext.destination as AudioDestinationNode & {
                        numberOfInputs?: number;
                        connect?: (node: AudioNode) => void;
                    };
                    if (destination.numberOfInputs && destination.numberOfInputs > 0) {
                        // Some browsers allow connecting to destination's input
                        destination.connect?.(gainNode);
                        connected = true;
                    }
                } catch (e) {
                    console.error('Error connecting to destination:', e);
                }
            }

            const startTime = Date.now();
            
            const monitorAudio = () => {
                analyzer.getByteFrequencyData(dataArray);
                
                // Calculate enhanced RMS with frequency analysis
                let totalEnergy = 0;
                let meaningfulFreqCount = 0;
                const frequencySnapshot: number[] = [];
                
                // Focus on meaningful frequency ranges (avoid DC and very high frequencies)
                const minFreqBin = Math.floor(bufferLength * 0.02); // Skip lowest 2%
                const maxFreqBin = Math.floor(bufferLength * 0.8);  // Skip highest 20%
                
                for (let i = minFreqBin; i < maxFreqBin; i++) {
                    const normalized = dataArray[i] / 255.0;
                    frequencySnapshot.push(normalized);
                    
                    if (normalized > thresh * 0.5) { // Count frequencies above half threshold
                        meaningfulFreqCount++;
                        totalEnergy += normalized * normalized;
                    }
                }
                
                frequencyData.push(frequencySnapshot);
                
                // Enhanced RMS calculation
                const meaningfulRms = meaningfulFreqCount > 0 ? 
                    Math.sqrt(totalEnergy / meaningfulFreqCount) : 0;
                
                // Additional noise filtering: require consistent signal
                const recentSamples = samples.slice(-5); // Last 5 samples
                const isConsistent = recentSamples.length === 0 || 
                    recentSamples.every(s => Math.abs(s - meaningfulRms) < thresh * 2);
                
                const finalLevel = isConsistent ? meaningfulRms : 0;
                
                samples.push(finalLevel);
                peakLevel = Math.max(peakLevel, finalLevel);
                
                const elapsed = Date.now() - startTime;
                
                if (elapsed < duration) {
                    setTimeout(monitorAudio, interval);
                } else {
                    // Enhanced analysis
                    const averageLevel = samples.reduce((a, b) => a + b, 0) / samples.length;
                    const samplesAboveThreshold = samples.filter(level => level > thresh).length;
                    const activePercentage = samplesAboveThreshold / samples.length;
                    
                    // Additional validation: check for frequency consistency
                    let frequencyConsistency = 0;
                    if (frequencyData.length > 1) {
                        for (let i = 1; i < frequencyData.length; i++) {
                            const prev = frequencyData[i-1];
                            const curr = frequencyData[i];
                            let similarities = 0;
                            
                            for (let j = 0; j < Math.min(prev.length, curr.length); j++) {
                                if (Math.abs(prev[j] - curr[j]) < 0.1) similarities++;
                            }
                            
                            frequencyConsistency += similarities / Math.min(prev.length, curr.length);
                        }
                        frequencyConsistency /= (frequencyData.length - 1);
                    }
                    
                    // Require both level threshold AND some frequency consistency for "real" audio
                    const hasConsistentFrequencies = frequencyConsistency > 0.3;
                    const meetsLevelRequirement = activePercentage >= minActivePercentage && averageLevel > thresh;
                    
                    resolve({
                        isReceivingAudio: meetsLevelRequirement && (hasConsistentFrequencies || averageLevel > thresh * 3),
                        averageLevel,
                        peakLevel,
                        duration: elapsed
                    });
                }
            };

            // Start monitoring after a brief delay
            setTimeout(monitorAudio, 200);
        });
    }, {
        duration: monitorDuration,
        threshold,
        interval: sampleInterval,
        minActivePercentage: minimumActivePercentage
    });

    return result;
}

/**
 * Simple helper function to check if a page is receiving audio with default settings
 * @param page The Playwright page to monitor
 * @returns Promise resolving to boolean indicating if audio is detected
 */
export async function isPageReceivingAudio(page: Page): Promise<boolean> {
    const result = await detectIncomingAudio(page);
    return result.isReceivingAudio;
}

/**
 * Gets the current audio level from a page
 * @param page The Playwright page to monitor
 * @returns Promise resolving to the current audio level (0-1)
 */
export async function getCurrentAudioLevel(page: Page): Promise<number> {
    const result = await detectIncomingAudio(page, { 
        monitorDuration: 500,
        sampleInterval: 50 
    });
    return result.averageLevel;
}

/**
 * Waits for audio to start or stop on a page
 * @param page The Playwright page to monitor
 * @param expectAudio Whether to wait for audio to start (true) or stop (false)
 * @param timeout Maximum time to wait in milliseconds
 * @returns Promise that resolves when the audio condition is met
 */
export async function waitForAudioChange(
    page: Page, 
    expectAudio: boolean, 
    timeout: number = 10000
): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
        const hasAudio = await isPageReceivingAudio(page);
        
        if (hasAudio === expectAudio) {
            return;
        }
        
        await page.waitForTimeout(200);
    }
    
    throw new Error(`Timeout waiting for audio to ${expectAudio ? 'start' : 'stop'}`);
}
