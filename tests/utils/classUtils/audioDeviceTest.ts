import { Browser, <PERSON>, expect } from '@playwright/test';

// Define window type for centralAudioProcessing
type WindowWithCentralAudio = Window & {
  centralAudioProcessing?: {
    getRemoteAudioMixer?: () => {
      participants?: Map<string, { getStream?: () => MediaStream | null }>;
    };
  };
};

export async function audioDeviceTestBefore(_page: Page, openBrowsers: Browser[]) {
  const initialRemoteStreamCounts: number[] = [];
  const initialRemoteStreamIds: string[][] = [];
  for (let i = 0; i < openBrowsers.length; i++) {
    const br = openBrowsers[i];
    const remotePage = br.contexts()[0].pages()[0];
    await remotePage.waitForTimeout(10000);
    const getStreamInfo = async () =>
      await remotePage.evaluate(() => {
        const centralAudioProcessing = (window as WindowWithCentralAudio).centralAudioProcessing;
        if (!centralAudioProcessing) {
          console.log('centralAudioProcessing not found on window');
          return { count: 0, ids: [] as string[] };
        }
        
        const remoteAudioMixer = centralAudioProcessing.getRemoteAudioMixer?.();
        if (!remoteAudioMixer) {
          console.log('getRemoteAudioMixer() returned null/undefined');
          return { count: 0, ids: [] as string[] };
        }
        
        if (!remoteAudioMixer.participants) {
          console.log('remoteAudioMixer.participants not found');
          return { count: 0, ids: [] as string[] };
        }
        
        console.log('remoteAudioMixer.participants size:', remoteAudioMixer.participants.size);
        
        const ids: string[] = [];
        remoteAudioMixer.participants.forEach((p, key) => {
          console.log('Processing participant:', key, p);
          const stream = p.getStream ? p.getStream() : null;
          if (stream) {
            console.log('Found stream with id:', stream.id);
            ids.push(stream.id);
          } else {
            console.log('No stream found for participant:', key);
          }
        });
        const uniqueIds = Array.from(new Set(ids));
        console.log('Unique stream ids found:', uniqueIds);
        return { count: uniqueIds.length, ids: uniqueIds };
      });

    // Wait until **three** audio tracks arrive (max 25 s)
    let initialCount = 0;
    let initialIds: string[] = [];
    const start = Date.now();
    while (Date.now() - start < 25_000) {
      const info = await getStreamInfo();
      initialCount = info.count;
      initialIds = info.ids;
      console.log(`Browser ${i} - Current count: ${initialCount}, ids: ${initialIds.join(', ')}`);
      if (initialCount >= 2) break;
      await remotePage.waitForTimeout(500);
    }
    initialRemoteStreamCounts.push(initialCount);
    initialRemoteStreamIds.push(initialIds);

    // Validate exactly three unique tracks are present initially
    console.log('Initial remote audio stream info:', initialCount, initialIds);
    // Expect at least three incoming streams
    expect(initialCount).toBeGreaterThanOrEqual(2);
  }
  console.log('Initial remote audio stream info:', initialRemoteStreamCounts, initialRemoteStreamIds);

  return {
    initialRemoteStreamCounts,
    initialRemoteStreamIds,
  };
}

export async function audioDeviceTestAfter(page: Page, openBrowsers: Browser[], initialRemoteStreamCounts: number[], initialRemoteStreamIds: string[][]) {
  await page.waitForTimeout(5000);
  const timeoutMs = 15_000;
  const intervalMs = 500;

  for (let i = 0; i < openBrowsers.length; i++) {
    const br = openBrowsers[i];
    const remotePage = br.contexts()[0].pages()[0];
    const initialCount = initialRemoteStreamCounts[i];
    const initialIds = initialRemoteStreamIds[i];

    await remotePage.waitForTimeout(10_000);
    const getStreamInfo = async () =>
      await remotePage.evaluate(() => {
        const centralAudioProcessing = (window as WindowWithCentralAudio).centralAudioProcessing;
        if (!centralAudioProcessing) return { count: 0, ids: [] as string[] };
        
        const remoteAudioMixer = centralAudioProcessing.getRemoteAudioMixer?.();
        if (!remoteAudioMixer || !remoteAudioMixer.participants) return { count: 0, ids: [] as string[] };
        
        const ids: string[] = [];
        remoteAudioMixer.participants.forEach((p) => {
          const stream = p.getStream ? p.getStream() : null;
          if (stream) {
            ids.push(stream.id);
          }
        });
        const uniqueIds = Array.from(new Set(ids));
        return { count: uniqueIds.length, ids: uniqueIds };
      });

    let currentCount = initialCount;
    let currentIds: string[] = [];
    const start = Date.now();
    while (Date.now() - start < timeoutMs) {
      const info = await getStreamInfo();
      currentCount = info.count;
      currentIds = info.ids;
      if (currentCount >= 2) break;
      await remotePage.waitForTimeout(intervalMs);
    }

    console.log(`Browser ${i} audio stream count before/after:`, initialCount, '→', currentCount);
    console.log(`Browser ${i} audio stream ids before/after:`, initialIds, '→', currentIds);

    // Expect at least three streams after switch
    expect(currentCount).toBeGreaterThanOrEqual(2);
  }
}

