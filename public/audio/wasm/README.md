# AEC AGC - Acoustic Echo Cancellation with Automatic Gain Control

A high-performance, real-time acoustic echo cancellation (AEC) and automatic gain control (AGC) library written in Rust with WebAssembly support. Features include residual echo suppression (RES), musical dynamics preservation, and platform-specific SIMD optimizations.

## 🎯 Key Features

- **Linear AEC**: Adaptive FIR filter using NLMS algorithm with pre-emphasis
- **Residual Echo Suppressor (RES)**: Frequency-domain suppression with tonal artifact detection
- **Musical Dynamics AGC**: Preserves musical dynamics while preventing clipping
- **Multi-platform**: Native Rust, WASM for web, optimized for ARM (NEON) and WASM SIMD
- **Real-time Processing**: Fixed 128-sample block size for low latency
- **Double-talk Protection**: Preserves near-end speech during simultaneous talking

## 📋 Table of Contents

- [Architecture Overview](#architecture-overview)
- [Setup and Installation](#setup-and-installation)
- [Building and Testing](#building-and-testing)
- [Web Demo](#web-demo)
- [Integration Guide](#integration-guide)
- [API Reference](#api-reference)
- [Performance](#performance)
- [Troubleshooting](#troubleshooting)

## Architecture Overview

### Core Components

1. **SimpleAec**: Linear echo canceller with adaptive FIR filter
    - Pre-emphasis filter (α=0.9) for improved high-frequency performance
    - Ring buffer for efficient convolution
    - DC blocking filter

2. **ResidualEchoSuppressor**: Frequency-domain post-processor
    - Coherence-based suppression
    - Tonal artifact detection and suppression
    - Double-talk protection

3. **MusicalDynamicsAGC**: Gain control that preserves musical dynamics
    - Peak-based gain calculation with 30-second memory
    - Fast attack for new peaks, slow release
    - Echo detection prevents amplifying residual echo

### Processing Pipeline

```
Far-end Signal → Pre-emphasis → AEC → RES → AGC → Output
                     ↑                  ↑
Near-end Signal → Pre-emphasis ────────┘
```

## Setup and Installation

### Prerequisites

- Rust 1.70+ with cargo
- For WASM: wasm-pack, Node.js 16+
- For native: C compiler (gcc/clang)
- Optional: wasm-opt for size optimization

### Clone Repository

```bash
git clone https://github.com/VirtuosoHub-Team/aec-agc.git
cd aec-agc
```

### Install Dependencies

```bash
# Install Rust (if not already installed)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Install wasm-pack for WASM builds
curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh

# Optional: Install wasm-opt for optimization
npm install -g wasm-opt
```

## Building and Testing

### Native Build

```bash
# Debug build
cargo build

# Release build with optimizations
cargo build --release

# Run tests
cargo test

# Run tests with audio file output
cargo test -- --nocapture

# Run specific test
cargo test test_real_music_double_talk -- --nocapture
```

### WASM Build

```bash
# Make build script executable
chmod +x build_wasm.sh

# Build WASM module
./build_wasm.sh

# Output will be in ./wasm directory
```

### Platform-Specific Optimizations

The `.cargo/config.toml` file contains optimizations for:
- **Apple Silicon (M1/M2)**: NEON SIMD instructions
- **Linux ARM64**: Full ARM64 feature set
- **Android**: Baseline ARM64 optimizations

## Web Demo

### Running the Demo

1. Build WASM module:
   ```bash
   ./build_wasm.sh
   ```

2. Serve with CORS headers:
   ```bash
   npx http-server -p 8000 --cors
   ```

3. Open browser to: `http://localhost:8000`

### Demo Features

- Load WAV files or use generated test signals
- Adjust echo parameters (delay, gain, reverb)
- Configure AEC settings (RES, AGC, pre-emphasis)
- Real-time visualization (waveform, spectrum, ERLE)
- Performance metrics display
- Download processed results

## Integration Guide

### Basic Usage (Rust)

```rust
use aec_agc::*;

// 1. Calibration Phase
let calibration = calibrate_aec_with_res_option(
    &calibration_signal,  // Far-end reference
    &echo_signal,        // Near-end with echo
    512,                 // FIR filter length
    true,                // Use pre-emphasis
    0.9,                 // Pre-emphasis alpha
    true,                // Enable RES
    0.1,                 // NLMS step size
    None                 // No previous calibration
);

// 2. Create AEC processor
let mut aec = SimpleAecWithRES::new(&calibration, true, true);

// 3. Process audio blocks (128 samples)
let mut echo_estimate = vec![0.0f32; BLOCK_SIZE];
let mut output = vec![0.0f32; BLOCK_SIZE];

aec.process_block(
    &far_samples,      // Reference (speaker)
    &near_samples,     // Microphone input
    &mut echo_estimate,
    &mut output
);
```

### Web Audio Integration

```javascript
// 1. Load WASM module
import init, * as AEC from './wasm/aec_agc.js';
await init();

// 2. Create calibration
const calibration = AEC.calibrate_aec(
    farSignal,        // Float32Array
    nearSignal,       // Float32Array
    512,              // FIR length
    true,             // pre-emphasis
    0.9,              // alpha
    true,             // RES
    0.1               // mu
);

// 3. Create processor
const aec = new AEC.WasmAec(calibration, true, true);

// 4. Process in AudioWorklet
class AECProcessor extends AudioWorkletProcessor {
    process(inputs, outputs) {
        const far = inputs[0][0];   // Far-end channel
        const near = inputs[1][0];  // Near-end channel
        
        // Process 128-sample blocks
        aec.process_block(
            farBuffer,
            nearBuffer,
            echoBuffer,
            outputBuffer
        );
        
        outputs[0][0].set(outputBuffer);
        return true;
    }
}
```

### Custom Audio Pipeline Integration

#### Mono Source Example

```javascript
// WebRTC + Web Audio API integration
async function setupAECPipeline(localStream, remoteStream) {
    const audioContext = new AudioContext({ sampleRate: 48000 });
    
    // Create source nodes
    const localSource = audioContext.createMediaStreamSource(localStream);
    const remoteSource = audioContext.createMediaStreamSource(remoteStream);
    
    // Create processing node
    await audioContext.audioWorklet.addModule('aec-processor.js');
    const aecNode = new AudioWorkletNode(audioContext, 'aec-processor', {
        numberOfInputs: 2,
        numberOfOutputs: 1,
        channelCount: 1
    });
    
    // Connect: remote (far) → AEC input 0
    remoteSource.connect(aecNode, 0, 0);
    
    // Connect: local mic (near) → AEC input 1  
    localSource.connect(aecNode, 0, 1);
    
    // Get processed output
    const destination = audioContext.createMediaStreamDestination();
    aecNode.connect(destination);
    
    // Replace audio track in peer connection
    const processedTrack = destination.stream.getAudioTracks()[0];
    replaceTrack(peerConnection, processedTrack);
}
```

#### Stereo Source Handling

```javascript
// Convert stereo to mono for AEC processing
function setupStereoAEC(stereoRemoteStream, localStream) {
    const audioContext = new AudioContext({ sampleRate: 48000 });
    
    // Split stereo remote audio
    const remoteSource = audioContext.createMediaStreamSource(stereoRemoteStream);
    const splitter = audioContext.createChannelSplitter(2);
    const merger = audioContext.createChannelMerger(1);
    
    // Mix stereo to mono
    remoteSource.connect(splitter);
    splitter.connect(merger, 0, 0); // L → mono
    splitter.connect(merger, 1, 0); // R → mono
    
    // Create gain for proper mixing
    const mixGain = audioContext.createGain();
    mixGain.gain.value = 0.5; // -6dB for each channel
    merger.connect(mixGain);
    
    // Now process mono through AEC
    const aecNode = new AudioWorkletNode(audioContext, 'aec-processor', {
        numberOfInputs: 2,
        numberOfOutputs: 1
    });
    
    mixGain.connect(aecNode, 0, 0);        // Far-end (mono)
    localSource.connect(aecNode, 0, 1);    // Near-end
    
    return aecNode;
}
```

## Multi-Turn Calibration

### Overview

Multi-turn calibration is an advanced technique that significantly improves AEC performance by iteratively refining the adaptive filter coefficients. Each calibration turn uses a progressively smaller step size (μ) and builds upon the previous results.

### How It Works

1. **First Turn**: Large step size for rapid initial convergence
2. **Subsequent Turns**: Step size is halved each turn for fine-tuning
3. **Convergence**: Filter coefficients stabilize around optimal values

```
Turn 1: μ = 0.1   → Fast initial adaptation
Turn 2: μ = 0.05  → Refine major coefficients  
Turn 3: μ = 0.025 → Fine-tune details
Turn 4: μ = 0.0125 → Polish final response
```

### Implementation Example

#### Rust
```rust
let mut calibration = None;
let start_mu = 0.1;
let num_turns = 4;

for turn in 0..num_turns {
    let mu = start_mu / 2.0_f32.powi(turn as i32);
    
    calibration = Some(calibrate_aec_with_res_option(
        &calibration_signal,
        &echo_signal,
        512,
        true,
        0.9,
        true,
        mu,
        calibration.as_ref()  // Pass previous calibration
    ));
    
    println!("Turn {}: μ={:.4}, peak={:.1}ms", 
             turn + 1, mu, calibration.as_ref().unwrap().peak_delay_ms);
}
```

#### JavaScript
```javascript
async function multiTurnCalibration(farSignal, nearSignal, turns = 4) {
    let calibration = null;
    const startMu = 0.1;
    
    for (let turn = 0; turn < turns; turn++) {
        const mu = startMu / Math.pow(2, turn);
        
        console.log(`Calibration turn ${turn + 1}/${turns}, μ=${mu.toFixed(4)}`);
        
        calibration = await AEC.calibrate_aec(
            farSignal,
            nearSignal,
            512,
            true,   // pre-emphasis
            0.9,    // alpha
            true,   // RES
            mu
        );
        
        // Optional: Show progress
        onProgress?.((turn + 1) / turns * 100);
    }
    
    return calibration;
}
```

### Benefits

1. **Improved Convergence**: Avoids local minima and overshooting
2. **Better Noise Rejection**: Later turns with small μ preserve signal while reducing noise
3. **Stable Coefficients**: Less susceptible to temporary disturbances
4. **Optimal Performance**: Typically 15-20% better ERLE than single-turn

### Recommended Settings

| Scenario | Turns | Initial μ | Notes |
|----------|-------|-----------|-------|
| Quick Setup | 1-2 | 0.2-0.3 | Fast but less optimal |
| Balanced | 3-4 | 0.1-0.15 | Good performance/speed |
| High Quality | 5-8 | 0.05-0.1 | Best results, slower |
| Difficult Echo | 8-10 | 0.02-0.05 | Complex acoustics |

### Performance Impact

```
Calibration Time (10s signal, 512 taps):
- 1 turn:  ~100ms
- 4 turns: ~400ms  
- 8 turns: ~800ms

ERLE Improvement:
- 1 turn:  15-20 dB
- 4 turns: 20-25 dB (+5 dB)
- 8 turns: 22-27 dB (+2 dB more)
```

### Calibration Signal

The repository includes an optimized calibration signal (`calibration.wav`) that provides optimal conditions for AEC training. This signal combines:

- **Musical Content**: All piano notes from A0 (27.5 Hz) to C8 (4186 Hz)
- **Pink Noise**: Natural 1/f frequency spectrum for broad coverage
- **Human Speech**: Male voice for speech-specific optimization


#### Usage

```rust
// Load the provided calibration signal
let calibration_audio = load_wav("calibration.wav")?;

// Use for echo simulation
let mut echo_signal = simulate_echo(&calibration_audio, delay_ms, gain, reverb);

// Multi-turn calibration
let calibration = multi_turn_calibration(
    &calibration_audio,
    &echo_signal,
    4  // turns
)?;
```

#### Benefits

1. **Full Spectrum Coverage**: Ensures filter adapts to all frequency ranges
2. **Natural Dynamics**: Speech and music content tests real-world scenarios
3. **Noise Robustness**: Pink noise component improves noise floor estimation
4. **Consistent Results**: Standardized signal provides reproducible calibration


## API Reference

### Calibration Functions

```rust
pub fn calibrate_aec_with_res_option(
    far_signal: &[f32],      // Reference signal
    near_signal: &[f32],     // Signal with echo
    fir_length: usize,       // Filter taps (typ. 512)
    use_preemphasis: bool,   // Enable pre-emphasis
    preemphasis_alpha: f32,  // Pre-emphasis coefficient (0.9)
    enable_res: bool,        // Enable RES
    mu: f32,                 // NLMS step size (0.01-0.5)
    calibration: Option<&Calibration>  // Previous calibration
) -> Calibration
```

### Processing Classes

#### SimpleAecWithRES
```rust
impl SimpleAecWithRES {
    pub fn new(
        calibration: &Calibration,
        enable_res: bool,
        enable_agc: bool
    ) -> Self
    
    pub fn process_block(
        &mut self,
        far_samples: &[f32],    // Must be BLOCK_SIZE (128)
        near_samples: &[f32],   // Must be BLOCK_SIZE (128)
        echo: &mut [f32],       // Output: estimated echo
        output: &mut [f32]      // Output: processed signal
    )
    
    pub fn reset(&mut self)    // Clear all internal state
}
```

### Constants

```rust
pub const BLOCK_SIZE: usize = 128;           // Processing block size
pub const DEFAULT_SAMPLE_RATE: f32 = 48000.0; // Default sample rate
pub const RES_FRAME_SIZE: usize = 256;       // RES FFT size
pub const RES_HOP_SIZE: usize = 128;         // RES hop size (50% overlap)
```

## Performance

### Benchmarks (Apple M1)

| Operation | Time | Real-time Factor |
|-----------|------|------------------|
| AEC only | 0.8ms/block | 120x |
| AEC + RES | 1.5ms/block | 64x |
| AEC + RES + AGC | 1.7ms/block | 56x |

### Optimization Tips

1. **Use Release Build**: 5-10x faster than debug
2. **Enable SIMD**: Automatic on supported platforms
3. **Batch Processing**: Process multiple blocks when possible
4. **Pre-allocation**: Reuse buffers to avoid allocations

## Troubleshooting

### Common Issues

**1. Poor Echo Cancellation**
- Ensure calibration uses representative signals
- Check sample rate matches (48kHz expected)
- Verify delay is within FIR filter length
- Try increasing calibration turns

**2. Distorted Output**
- Check input signal levels (should be normalized)
- Disable AGC to isolate issue
- Verify no sample rate conversion

**3. WASM Build Fails**
- Update wasm-pack: `wasm-pack --version`
- Clear cache: `rm -rf pkg/ target/`
- Check Rust target: `rustup target add wasm32-unknown-unknown`

**4. High CPU Usage**
- Use release build
- Reduce FIR length if possible
- Disable RES if not needed

### Debug Mode

Enable debug logging:
```rust
// In Rust
env_logger::init();
RUST_LOG=debug cargo run

// In JavaScript
const aec = new AEC.WasmAec(calibration, true, true);
// Check browser console for WASM logs
```
