declare namespace wasm_bindgen {
	/* tslint:disable */
	/* eslint-disable */
	export function init_panic_hook(): void;
	export function calibrate_aec(far_signal: Float32Array, near_signal: Float32Array, fir_length: number, use_preemphasis: boolean, preemphasis_alpha: number, enable_res: boolean, mu: number): WasmCalibration;
	export function get_block_size(): number;
	export function get_default_sample_rate(): number;
	export class WasmAec {
	  free(): void;
	  constructor(calibration: WasmCalibration, enable_res: boolean, enable_agc: boolean);
	  reset(): void;
	  get_block_size(): number;
	  process_block(far_samples: Float32Array, near_samples: Float32Array, echo_output: Float32Array, output: Float32Array): void;
	}
	export class WasmCalibration {
	  free(): void;
	  constructor();
	  set_coefficients(coefficients: Float32Array): void;
	  set_preemphasis(enabled: boolean, alpha: number): void;
	  set_peak_info(index: number, delay_ms: number, value: number): void;
	  set_res_enabled(enabled: boolean): void;
	  get_coefficients(): Float32Array;
	  get_peak_delay_ms(): number;
	  get_peak_sample_index(): number;
	}
	
}

declare type InitInput = RequestInfo | URL | Response | BufferSource | WebAssembly.Module;

declare interface InitOutput {
  readonly memory: WebAssembly.Memory;
  readonly __wbg_wasmcalibration_free: (a: number, b: number) => void;
  readonly wasmcalibration_new: () => number;
  readonly wasmcalibration_set_coefficients: (a: number, b: number, c: number) => void;
  readonly wasmcalibration_set_preemphasis: (a: number, b: number, c: number) => void;
  readonly wasmcalibration_set_peak_info: (a: number, b: number, c: number, d: number) => void;
  readonly wasmcalibration_set_res_enabled: (a: number, b: number) => void;
  readonly wasmcalibration_get_coefficients: (a: number, b: number) => void;
  readonly wasmcalibration_get_peak_delay_ms: (a: number) => number;
  readonly wasmcalibration_get_peak_sample_index: (a: number) => number;
  readonly __wbg_wasmaec_free: (a: number, b: number) => void;
  readonly wasmaec_new: (a: number, b: number, c: number) => number;
  readonly wasmaec_reset: (a: number) => void;
  readonly wasmaec_get_block_size: (a: number) => number;
  readonly wasmaec_process_block: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number) => void;
  readonly calibrate_aec: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number) => number;
  readonly get_block_size: () => number;
  readonly get_default_sample_rate: () => number;
  readonly init_panic_hook: () => void;
  readonly __wbindgen_export_0: (a: number, b: number) => number;
  readonly __wbindgen_add_to_stack_pointer: (a: number) => number;
  readonly __wbindgen_export_1: (a: number, b: number, c: number) => void;
}

/**
* If `module_or_path` is {RequestInfo} or {URL}, makes a request and
* for everything else, calls `WebAssembly.instantiate` directly.
*
* @param {{ module_or_path: InitInput | Promise<InitInput> }} module_or_path - Passing `InitInput` directly is deprecated.
*
* @returns {Promise<InitOutput>}
*/
declare function wasm_bindgen (module_or_path?: { module_or_path: InitInput | Promise<InitInput> } | InitInput | Promise<InitInput>): Promise<InitOutput>;
