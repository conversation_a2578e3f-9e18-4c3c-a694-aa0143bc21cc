/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export const __wbg_wasmcalibration_free: (a: number, b: number) => void;
export const wasmcalibration_new: () => number;
export const wasmcalibration_set_coefficients: (a: number, b: number, c: number) => void;
export const wasmcalibration_set_preemphasis: (a: number, b: number, c: number) => void;
export const wasmcalibration_set_peak_info: (a: number, b: number, c: number, d: number) => void;
export const wasmcalibration_set_res_enabled: (a: number, b: number) => void;
export const wasmcalibration_get_coefficients: (a: number, b: number) => void;
export const wasmcalibration_get_peak_delay_ms: (a: number) => number;
export const wasmcalibration_get_peak_sample_index: (a: number) => number;
export const __wbg_wasmaec_free: (a: number, b: number) => void;
export const wasmaec_new: (a: number, b: number, c: number) => number;
export const wasmaec_reset: (a: number) => void;
export const wasmaec_get_block_size: (a: number) => number;
export const wasmaec_process_block: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number) => void;
export const calibrate_aec: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number) => number;
export const get_block_size: () => number;
export const get_default_sample_rate: () => number;
export const init_panic_hook: () => void;
export const __wbindgen_export_0: (a: number, b: number) => number;
export const __wbindgen_add_to_stack_pointer: (a: number) => number;
export const __wbindgen_export_1: (a: number, b: number, c: number) => void;
