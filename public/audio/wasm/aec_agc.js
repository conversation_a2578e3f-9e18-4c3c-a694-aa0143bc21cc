let wasm_bindgen;
(function() {
    const __exports = {};
    let script_src;
    if (typeof document !== 'undefined' && document.currentScript !== null) {
        script_src = new URL(document.currentScript.src, location.href).toString();
    }
    let wasm = undefined;

    const cachedTextDecoder = (typeof TextDecoder !== 'undefined' ? new TextDecoder('utf-8', { ignoreBOM: true, fatal: true }) : { decode: () => { throw Error('TextDecoder not available') } } );

    if (typeof TextDecoder !== 'undefined') { cachedTextDecoder.decode(); };

    let cachedUint8ArrayMemory0 = null;

    function getUint8ArrayMemory0() {
        if (cachedUint8ArrayMemory0 === null || cachedUint8ArrayMemory0.byteLength === 0) {
            cachedUint8ArrayMemory0 = new Uint8Array(wasm.memory.buffer);
        }
        return cachedUint8ArrayMemory0;
    }

    function getStringFromWasm0(ptr, len) {
        ptr = ptr >>> 0;
        return cachedTextDecoder.decode(getUint8ArrayMemory0().subarray(ptr, ptr + len));
    }

    function getArrayU8FromWasm0(ptr, len) {
        ptr = ptr >>> 0;
        return getUint8ArrayMemory0().subarray(ptr / 1, ptr / 1 + len);
    }

    const heap = new Array(128).fill(undefined);

    heap.push(undefined, null, true, false);

    function getObject(idx) { return heap[idx]; }

    let heap_next = heap.length;

    function dropObject(idx) {
        if (idx < 132) return;
        heap[idx] = heap_next;
        heap_next = idx;
    }

    function takeObject(idx) {
        const ret = getObject(idx);
        dropObject(idx);
        return ret;
    }

    function addHeapObject(obj) {
        if (heap_next === heap.length) heap.push(heap.length + 1);
        const idx = heap_next;
        heap_next = heap[idx];

        heap[idx] = obj;
        return idx;
    }

    __exports.init_panic_hook = function() {
        wasm.init_panic_hook();
    };

    let cachedFloat32ArrayMemory0 = null;

    function getFloat32ArrayMemory0() {
        if (cachedFloat32ArrayMemory0 === null || cachedFloat32ArrayMemory0.byteLength === 0) {
            cachedFloat32ArrayMemory0 = new Float32Array(wasm.memory.buffer);
        }
        return cachedFloat32ArrayMemory0;
    }

    let WASM_VECTOR_LEN = 0;

    function passArrayF32ToWasm0(arg, malloc) {
        const ptr = malloc(arg.length * 4, 4) >>> 0;
        getFloat32ArrayMemory0().set(arg, ptr / 4);
        WASM_VECTOR_LEN = arg.length;
        return ptr;
    }

    let cachedDataViewMemory0 = null;

    function getDataViewMemory0() {
        if (cachedDataViewMemory0 === null || cachedDataViewMemory0.buffer.detached === true || (cachedDataViewMemory0.buffer.detached === undefined && cachedDataViewMemory0.buffer !== wasm.memory.buffer)) {
            cachedDataViewMemory0 = new DataView(wasm.memory.buffer);
        }
        return cachedDataViewMemory0;
    }

    function getArrayF32FromWasm0(ptr, len) {
        ptr = ptr >>> 0;
        return getFloat32ArrayMemory0().subarray(ptr / 4, ptr / 4 + len);
    }

    function _assertClass(instance, klass) {
        if (!(instance instanceof klass)) {
            throw new Error(`expected instance of ${klass.name}`);
        }
    }
    /**
     * @param {Float32Array} far_signal
     * @param {Float32Array} near_signal
     * @param {number} fir_length
     * @param {boolean} use_preemphasis
     * @param {number} preemphasis_alpha
     * @param {boolean} enable_res
     * @param {number} mu
     * @returns {WasmCalibration}
     */
    __exports.calibrate_aec = function(far_signal, near_signal, fir_length, use_preemphasis, preemphasis_alpha, enable_res, mu) {
        const ptr0 = passArrayF32ToWasm0(far_signal, wasm.__wbindgen_export_0);
        const len0 = WASM_VECTOR_LEN;
        const ptr1 = passArrayF32ToWasm0(near_signal, wasm.__wbindgen_export_0);
        const len1 = WASM_VECTOR_LEN;
        const ret = wasm.calibrate_aec(ptr0, len0, ptr1, len1, fir_length, use_preemphasis, preemphasis_alpha, enable_res, mu);
        return WasmCalibration.__wrap(ret);
    };

    /**
     * @returns {number}
     */
    __exports.get_block_size = function() {
        const ret = wasm.get_block_size();
        return ret >>> 0;
    };

    /**
     * @returns {number}
     */
    __exports.get_default_sample_rate = function() {
        const ret = wasm.get_default_sample_rate();
        return ret;
    };

    const WasmAecFinalization = (typeof FinalizationRegistry === 'undefined')
        ? { register: () => {}, unregister: () => {} }
        : new FinalizationRegistry(ptr => wasm.__wbg_wasmaec_free(ptr >>> 0, 1));

    class WasmAec {

        __destroy_into_raw() {
            const ptr = this.__wbg_ptr;
            this.__wbg_ptr = 0;
            WasmAecFinalization.unregister(this);
            return ptr;
        }

        free() {
            const ptr = this.__destroy_into_raw();
            wasm.__wbg_wasmaec_free(ptr, 0);
        }
        /**
         * @param {WasmCalibration} calibration
         * @param {boolean} enable_res
         * @param {boolean} enable_agc
         */
        constructor(calibration, enable_res, enable_agc) {
            _assertClass(calibration, WasmCalibration);
            const ret = wasm.wasmaec_new(calibration.__wbg_ptr, enable_res, enable_agc);
            this.__wbg_ptr = ret >>> 0;
            WasmAecFinalization.register(this, this.__wbg_ptr, this);
            return this;
        }
        reset() {
            wasm.wasmaec_reset(this.__wbg_ptr);
        }
        /**
         * @returns {number}
         */
        get_block_size() {
            const ret = wasm.wasmaec_get_block_size(this.__wbg_ptr);
            return ret >>> 0;
        }
        /**
         * @param {Float32Array} far_samples
         * @param {Float32Array} near_samples
         * @param {Float32Array} echo_output
         * @param {Float32Array} output
         */
        process_block(far_samples, near_samples, echo_output, output) {
            try {
                const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);
                const ptr0 = passArrayF32ToWasm0(far_samples, wasm.__wbindgen_export_0);
                const len0 = WASM_VECTOR_LEN;
                const ptr1 = passArrayF32ToWasm0(near_samples, wasm.__wbindgen_export_0);
                const len1 = WASM_VECTOR_LEN;
                var ptr2 = passArrayF32ToWasm0(echo_output, wasm.__wbindgen_export_0);
                var len2 = WASM_VECTOR_LEN;
                var ptr3 = passArrayF32ToWasm0(output, wasm.__wbindgen_export_0);
                var len3 = WASM_VECTOR_LEN;
                wasm.wasmaec_process_block(retptr, this.__wbg_ptr, ptr0, len0, ptr1, len1, ptr2, len2, addHeapObject(echo_output), ptr3, len3, addHeapObject(output));
                var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);
                var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);
                if (r1) {
                    throw takeObject(r0);
                }
            } finally {
                wasm.__wbindgen_add_to_stack_pointer(16);
            }
        }
    }
    __exports.WasmAec = WasmAec;

    const WasmCalibrationFinalization = (typeof FinalizationRegistry === 'undefined')
        ? { register: () => {}, unregister: () => {} }
        : new FinalizationRegistry(ptr => wasm.__wbg_wasmcalibration_free(ptr >>> 0, 1));

    class WasmCalibration {

        static __wrap(ptr) {
            ptr = ptr >>> 0;
            const obj = Object.create(WasmCalibration.prototype);
            obj.__wbg_ptr = ptr;
            WasmCalibrationFinalization.register(obj, obj.__wbg_ptr, obj);
            return obj;
        }

        __destroy_into_raw() {
            const ptr = this.__wbg_ptr;
            this.__wbg_ptr = 0;
            WasmCalibrationFinalization.unregister(this);
            return ptr;
        }

        free() {
            const ptr = this.__destroy_into_raw();
            wasm.__wbg_wasmcalibration_free(ptr, 0);
        }
        constructor() {
            const ret = wasm.wasmcalibration_new();
            this.__wbg_ptr = ret >>> 0;
            WasmCalibrationFinalization.register(this, this.__wbg_ptr, this);
            return this;
        }
        /**
         * @param {Float32Array} coefficients
         */
        set_coefficients(coefficients) {
            const ptr0 = passArrayF32ToWasm0(coefficients, wasm.__wbindgen_export_0);
            const len0 = WASM_VECTOR_LEN;
            wasm.wasmcalibration_set_coefficients(this.__wbg_ptr, ptr0, len0);
        }
        /**
         * @param {boolean} enabled
         * @param {number} alpha
         */
        set_preemphasis(enabled, alpha) {
            wasm.wasmcalibration_set_preemphasis(this.__wbg_ptr, enabled, alpha);
        }
        /**
         * @param {number} index
         * @param {number} delay_ms
         * @param {number} value
         */
        set_peak_info(index, delay_ms, value) {
            wasm.wasmcalibration_set_peak_info(this.__wbg_ptr, index, delay_ms, value);
        }
        /**
         * @param {boolean} enabled
         */
        set_res_enabled(enabled) {
            wasm.wasmcalibration_set_res_enabled(this.__wbg_ptr, enabled);
        }
        /**
         * @returns {Float32Array}
         */
        get_coefficients() {
            try {
                const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);
                wasm.wasmcalibration_get_coefficients(retptr, this.__wbg_ptr);
                var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);
                var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);
                var v1 = getArrayF32FromWasm0(r0, r1).slice();
                wasm.__wbindgen_export_1(r0, r1 * 4, 4);
                return v1;
            } finally {
                wasm.__wbindgen_add_to_stack_pointer(16);
            }
        }
        /**
         * @returns {number}
         */
        get_peak_delay_ms() {
            const ret = wasm.wasmcalibration_get_peak_delay_ms(this.__wbg_ptr);
            return ret;
        }
        /**
         * @returns {number}
         */
        get_peak_sample_index() {
            const ret = wasm.wasmcalibration_get_peak_sample_index(this.__wbg_ptr);
            return ret >>> 0;
        }
    }
    __exports.WasmCalibration = WasmCalibration;

    async function __wbg_load(module, imports) {
        if (typeof Response === 'function' && module instanceof Response) {
            if (typeof WebAssembly.instantiateStreaming === 'function') {
                try {
                    return await WebAssembly.instantiateStreaming(module, imports);

                } catch (e) {
                    if (module.headers.get('Content-Type') != 'application/wasm') {
                        console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve Wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n", e);

                    } else {
                        throw e;
                    }
                }
            }

            const bytes = await module.arrayBuffer();
            return await WebAssembly.instantiate(bytes, imports);

        } else {
            const instance = await WebAssembly.instantiate(module, imports);

            if (instance instanceof WebAssembly.Instance) {
                return { instance, module };

            } else {
                return instance;
            }
        }
    }

    function __wbg_get_imports() {
        const imports = {};
        imports.wbg = {};
        imports.wbg.__wbg_log_5774a5fe8d449bb6 = function(arg0, arg1) {
            console.log(getStringFromWasm0(arg0, arg1));
        };
        imports.wbg.__wbindgen_copy_to_typed_array = function(arg0, arg1, arg2) {
            new Uint8Array(getObject(arg2).buffer, getObject(arg2).byteOffset, getObject(arg2).byteLength).set(getArrayU8FromWasm0(arg0, arg1));
        };
        imports.wbg.__wbindgen_object_drop_ref = function(arg0) {
            takeObject(arg0);
        };
        imports.wbg.__wbindgen_string_new = function(arg0, arg1) {
            const ret = getStringFromWasm0(arg0, arg1);
            return addHeapObject(ret);
        };
        imports.wbg.__wbindgen_throw = function(arg0, arg1) {
            throw new Error(getStringFromWasm0(arg0, arg1));
        };

        return imports;
    }

    function __wbg_init_memory(imports, memory) {

    }

    function __wbg_finalize_init(instance, module) {
        wasm = instance.exports;
        __wbg_init.__wbindgen_wasm_module = module;
        cachedDataViewMemory0 = null;
        cachedFloat32ArrayMemory0 = null;
        cachedUint8ArrayMemory0 = null;



        return wasm;
    }

    function initSync(module) {
        if (wasm !== undefined) return wasm;


        if (typeof module !== 'undefined') {
            if (Object.getPrototypeOf(module) === Object.prototype) {
                ({module} = module)
            } else {
                console.warn('using deprecated parameters for `initSync()`; pass a single object instead')
            }
        }

        const imports = __wbg_get_imports();

        __wbg_init_memory(imports);

        if (!(module instanceof WebAssembly.Module)) {
            module = new WebAssembly.Module(module);
        }

        const instance = new WebAssembly.Instance(module, imports);

        return __wbg_finalize_init(instance, module);
    }

    async function __wbg_init(module_or_path) {
        if (wasm !== undefined) return wasm;


        if (typeof module_or_path !== 'undefined') {
            if (Object.getPrototypeOf(module_or_path) === Object.prototype) {
                ({module_or_path} = module_or_path)
            } else {
                console.warn('using deprecated parameters for the initialization function; pass a single object instead')
            }
        }

        if (typeof module_or_path === 'undefined' && typeof script_src !== 'undefined') {
            module_or_path = script_src.replace(/\.js$/, '_bg.wasm');
        }
        const imports = __wbg_get_imports();

        if (typeof module_or_path === 'string' || (typeof Request === 'function' && module_or_path instanceof Request) || (typeof URL === 'function' && module_or_path instanceof URL)) {
            module_or_path = fetch(module_or_path);
        }

        __wbg_init_memory(imports);

        const { instance, module } = await __wbg_load(await module_or_path, imports);

        return __wbg_finalize_init(instance, module);
    }

    wasm_bindgen = Object.assign(__wbg_init, { initSync }, __exports);

})();
