// cross-correlate-worker.js
// Web Worker for cross-correlation calculations

self.onmessage = function(e) {
  const { signal1, signal2, maxDelay, taskId } = e.data;
  
  try {
    const result = crossCorrelate(
      new Float32Array(signal1), 
      new Float32Array(signal2), 
      maxDelay
    );
    
    // Send result back to main thread
    self.postMessage({
      taskId,
      success: true,
      result
    });
  } catch (error) {
    // Send error back to main thread
    self.postMessage({
      taskId,
      success: false,
      error: error.message
    });
  }
};

function crossCorrelate(
  signal1,
  signal2,
  maxDelay = 4800
) {
  const correlations = [];
  let maxCorrelation = -Infinity;
  let bestOffset = 0;

  // Search range: -maxDelay to +maxDelay samples
  for (let delay = -maxDelay; delay <= maxDelay; delay++) {
    let correlation = 0;
    let count = 0;

    // Calculate correlation at this delay
    const startIdx1 = Math.max(0, -delay);
    const endIdx1 = Math.min(signal1.length, signal2.length - delay);
    const startIdx2 = Math.max(0, delay);

    for (let i = 0; i < endIdx1 - startIdx1; i++) {
      const idx1 = startIdx1 + i;
      const idx2 = startIdx2 + i;

      if (idx1 < signal1.length && idx2 < signal2.length) {
        correlation += signal1[idx1] * signal2[idx2];
        count++;
      }
    }

    if (count > 0) {
      correlation /= count; // Normalize
      correlations.push(correlation);

      if (correlation > maxCorrelation) {
        maxCorrelation = correlation;
        bestOffset = delay;
      }
    }
  }

  return { bestOffset, maxCorrelation, correlationValues: correlations };
}