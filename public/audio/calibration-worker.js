class CalibrationWorker {
    constructor() {
        this.wasmModule = null;
        this.initialized = false;
    }

    async initialize(wasmBinary, jsWrapper) {
        try {
            // Convert to ArrayBuffer if needed
            let actualWasmBinary;
            if (wasmBinary instanceof ArrayBuffer) {
                actualWasmBinary = wasmBinary;
            } else if (wasmBinary && wasmBinary.buffer) {
                actualWasmBinary = wasmBinary.buffer;
            } else {
                throw new Error('Invalid WASM binary format');
            }
        
            function TextDecoder() {
                this.decode = function(bytes) {
                    if (!bytes || bytes.length === 0) return '';
                    let result = '';
                    for (let i = 0; i < bytes.length; i++) {
                       result += String.fromCharCode(bytes[i]);
                    }
                    return result;
                };
            }

            function TextEncoder() {
                this.encode = function(str) {
                    if (!str) return new Uint8Array(0);
                    const bytes = new Uint8Array(str.length);
                    for (let i = 0; i < str.length; i++) {
                        bytes[i] = str.charCodeAt(i);
                    }
                    return bytes;
                };
            }
            
            // Same WASM initialization as AudioWorklet
            const func = new Function(
                'WebAssembly', 'TextDecoder', 'TextEncoder',
                `${jsWrapper}; return (typeof wasm_bindgen !== 'undefined') ? wasm_bindgen : this;`
            );
            
            const wasmBindgen = func(WebAssembly, TextDecoder, TextEncoder);
            
            if (wasmBindgen.default) {
                await wasmBindgen.default(wasmBinary);
            } else {
                const initFn = Object.values(wasmBindgen).find(fn => typeof fn === 'function');
                if (initFn) {
                    await initFn(wasmBinary);
                }
            }
            
            this.wasmModule = wasmBindgen;
            this.wasmModule.init_panic_hook();
            this.initialized = true;
            
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async calibrate(calibrationSignal, calibrationEcho, turns = 1) {
        if (!this.initialized) {
            throw new Error('Worker not initialized');
        }

        try {
            let calibration = null;
            
            for (let turnIndex = 0; turnIndex < turns; turnIndex++) {
                const mu = 0.1 / Math.pow(2, turnIndex);
                console.log(`[Calibration Worker] Turn ${turnIndex + 1}/${turns}, μ=${mu.toFixed(4)}`);
                
                const wasmCalibration = this.wasmModule.calibrate_aec(
                    calibrationSignal,
                    calibrationEcho,
                    512, true, 0.9, true, mu
                );

                // Extract and copy data to prevent memory issues
                const coefficientsView = wasmCalibration.get_coefficients();
                const coefficientsCopy = new Float32Array(coefficientsView.length);
                coefficientsCopy.set(coefficientsView);

                calibration = {
                    coefficients: coefficientsCopy,
                    use_preemphasis: true,
                    preemphasis_alpha: 0.9,
                    peak_sample_index: wasmCalibration.get_peak_sample_index(),
                    peak_delay_ms: wasmCalibration.get_peak_delay_ms(),
                    peak_value: 0.0,
                    enable_res: true,
                    enable_agc: true
                };

                wasmCalibration.free();
            }

            return { success: true, calibration };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
}

const worker = new CalibrationWorker();

self.onmessage = async function(event) {
    const { type, data, id } = event.data;

    switch (type) {
        case 'initialize':
            const initResult = await worker.initialize(data.wasmBinary, data.jsWrapper);
            self.postMessage({ type: 'initialized', id, ...initResult });
            break;

        case 'calibrate':
            const calibResult = await worker.calibrate(
                data.calibrationSignal, 
                data.calibrationEcho, 
                data.turns
            );
            self.postMessage({ type: 'calibrationResult', id, ...calibResult });
            break;
    }
};