class AECProcessor extends AudioWorkletProcessor {
    constructor() {
        super();

        this.initialized = false;
        this.wasmModule = null;
        this.aec = null;
        this.calibration = null;
        this.isProcessing = false;

        // Get block size from WASM
        this.blockSize = 128; // Will be updated from WASM

        // Pre-allocate typed arrays for WASM memory efficiency
        this.farBuffer = null;
        this.nearBuffer = null;
        this.echoBuffer = null;
        this.outputBuffer = null;

        // Performance metrics with proper names
        this.frameCount = 0;
        
        this.inputEchoEnergy = 0; // Energy removed by AEC
        this.outputEnergy = 0; // Output energy
        this.inputSignalEnergy = 0; // Total input signal energy
        
        // Add diagnostic counters
        this.zeroOutputBlocks = 0;
        this.validOutputBlocks = 0;
        this.needsStateReset = false;

        // Temporary buffers for accumulation
        this.farAccumulator = [];
        this.nearAccumulator = [];

        // Add to your AECProcessor class
        this.isEchoOnlyPeriod = false;
        this.echoOnlyEnergy = 0;
        this.echoOnlyFrames = 0;

        // Handle messages from main thread
        this.port.onmessage = this.handleMessage.bind(this);
    }

    async handleMessage(event) {
        const { type, data } = event.data;

        switch (type) {
            case 'init':
                await this.initialize(data.wasmBinary, data.jsWrapper);
                break;
            
            case 'setCalibration':
                this.setCalibration(data.calibration);
                break;

            case 'startProcessing':
                this.startProcessing();
                break;

            case 'stop':
                this.stop();
                break;
        }
    }

    async initialize(wasmBinary, jsWrapper) {
        try {            
            // Convert to ArrayBuffer if needed
            let actualWasmBinary;
            if (wasmBinary instanceof ArrayBuffer) {
                actualWasmBinary = wasmBinary;
            } else if (wasmBinary && wasmBinary.buffer) {
                actualWasmBinary = wasmBinary.buffer;
            } else {
                throw new Error('Invalid WASM binary format');
            }
        
            function TextDecoder() {
                this.decode = function(bytes) {
                    if (!bytes || bytes.length === 0) return '';
                    let result = '';
                    for (let i = 0; i < bytes.length; i++) {
                       result += String.fromCharCode(bytes[i]);
                    }
                    return result;
                };
            }

            function TextEncoder() {
                this.encode = function(str) {
                    if (!str) return new Uint8Array(0);
                    const bytes = new Uint8Array(str.length);
                    for (let i = 0; i < str.length; i++) {
                        bytes[i] = str.charCodeAt(i);
                    }
                    return bytes;
                };
            }

            const func = new Function(
                'WebAssembly', 'TextDecoder', 'TextEncoder',
                `${jsWrapper}; return (typeof wasm_bindgen !== 'undefined') ? wasm_bindgen : this;`
            );
        
            const wasmBindgen = func(WebAssembly, TextDecoder, TextEncoder);
        
            // Use the actual ArrayBuffer
            if (wasmBindgen.default) {
                await wasmBindgen.default(actualWasmBinary);
            } else {
                const initFn = Object.values(wasmBindgen).find(fn => typeof fn === 'function');
                if (initFn) {
                    await initFn(actualWasmBinary);
                }
            }
        
            // Now you have the full wasm-bindgen API
            this.wasmModule = wasmBindgen;
    
            // Initialize WASM module
            this.wasmModule.init_panic_hook();

            // Get constants from WASM
            this.blockSize = this.wasmModule.get_block_size();
            const defaultSampleRate = this.wasmModule.get_default_sample_rate();

            // Allocate buffers in WASM memory
            this.farBuffer = new Float32Array(this.blockSize);
            this.nearBuffer = new Float32Array(this.blockSize);
            this.echoBuffer = new Float32Array(this.blockSize);
            this.outputBuffer = new Float32Array(this.blockSize);

            this.initialized = true;

            this.port.postMessage({
                type: 'initialized',
                sampleRate: sampleRate,
                blockSize: this.blockSize,
                defaultSampleRate: defaultSampleRate
            });

            console.log(`[AEC Processor] Initialized: blockSize=${this.blockSize}, sampleRate=${sampleRate}`);
        } catch (error) {
            console.error('[AEC Processor] Failed to initialize:', error);
            this.port.postMessage({
                type: 'error',
                error: error.message
            });
        }
    }

    setCalibration(calibration) {
        try {
            this.calibration = calibration;
            this.port.postMessage({ type: 'calibrationComplete' });
            console.log('[AEC Processor] Calibration received from worker');
        } catch (error) {
            this.port.postMessage({ 
                type: 'error', 
                error: `Failed to set calibration: ${error.message}` 
            });
        }
    }

    startProcessing() {
        if (this.needsStateReset) {
            // Reset ALL metrics
            this.frameCount = 0;
            
            this.inputEchoEnergy = 0;
            this.outputEnergy = 0;
            this.inputSignalEnergy = 0;
            this.zeroOutputBlocks = 0;
            this.validOutputBlocks = 0;

            // ERLE metrics
            this.echoEstimateEnergy = 0;
            this.residualEnergy = 0;
            this.echoOnlyFrames = 0;
            this.isEchoOnlyPeriod = true;

            // Clear accumulators
            this.farAccumulator = [];
            this.nearAccumulator = [];
        }

        // Create AEC instance with calibration
        try {            
            const wasmCalibration = new this.wasmModule.WasmCalibration();
            wasmCalibration.set_coefficients(new Float32Array(this.calibration.coefficients));
            wasmCalibration.set_preemphasis(
                this.calibration.use_preemphasis,
                this.calibration.preemphasis_alpha
            );
            wasmCalibration.set_peak_info(
                this.calibration.peak_sample_index,
                this.calibration.peak_delay_ms,
                this.calibration.peak_value
            );
            wasmCalibration.set_res_enabled(this.calibration.enable_res);

            this.aec = new this.wasmModule.WasmAec(
                wasmCalibration,
                this.calibration.enable_res,
                this.calibration.enable_agc || false
            );

            wasmCalibration.free();

            // Clear accumulators
            this.farAccumulator = [];
            this.nearAccumulator = [];

            this.isProcessing = true;
            this.port.postMessage({ type: 'processingStarted' });
        } catch (error) {
            this.port.postMessage({
                type: 'error',
                error: error.message
            });
        }
    }

    process(inputs, outputs, parameters) {
        const output = outputs[0];

        if (!this.initialized || inputs[0].length === 0) {
            return true;
        }

        const farChannel = inputs[0][0];
        const nearChannel = inputs[1][0];

        if (this.isProcessing && nearChannel && farChannel && this.aec) {
            // Accumulate samples until we have enough for a block
            for (let i = 0; i < farChannel.length; i++) {
                this.farAccumulator.push(farChannel[i]);
                this.nearAccumulator.push(nearChannel[i]);
            }

            // Process complete blocks
            let outputIndex = 0;
            while (this.farAccumulator.length >= this.blockSize &&
                   this.nearAccumulator.length >= this.blockSize) {

                // Fill buffers
                for (let i = 0; i < this.blockSize; i++) {
                    this.farBuffer[i] = this.farAccumulator.shift();
                    this.nearBuffer[i] = this.nearAccumulator.shift();
                }

                // Clear output buffers
                this.echoBuffer.fill(0);
                this.outputBuffer.fill(0);

                try {
                    // Calculate input energies before processing
                    let blockInputEnergy = 0;
                    let blockFarEnergy = 0;
                    for (let i = 0; i < this.blockSize; i++) {
                        blockInputEnergy += this.nearBuffer[i] * this.nearBuffer[i];
                        blockFarEnergy += this.farBuffer[i] * this.farBuffer[i];
                    }

                    // Process through WASM AEC
                    this.aec.process_block(
                        this.farBuffer,
                        this.nearBuffer,
                        this.echoBuffer,
                        this.outputBuffer
                    );

                    // Calculate output energy after processing
                    let blockOutputEnergy = 0;
                    for (let i = 0; i < this.blockSize; i++) {
                        blockOutputEnergy += this.outputBuffer[i] * this.outputBuffer[i];
                    }

                    this.inputSignalEnergy += blockInputEnergy;
                    this.outputEnergy += blockOutputEnergy;
                    const echoEnergyRemoved = Math.max(0, blockInputEnergy - blockOutputEnergy);
                    this.inputEchoEnergy += echoEnergyRemoved;

                    // Simple VAD (Voice Activity Detection) for near-end
                    const nearEndActive = this.detectNearEndActivity(
                        this.nearBuffer, 
                        this.echoBuffer
                    );

                    if (!nearEndActive && this.hasFarEndActivity(this.farBuffer)) {
                        // This is an echo-only period!
                        this.isEchoOnlyPeriod = true;
    
                        // nearBuffer contains ONLY echo during this period
                        let echoOnlyInputEnergy = 0;
                        let echoOnlyOutputEnergy = 0;
    
                        for (let i = 0; i < this.blockSize; i++) {
                            echoOnlyInputEnergy += this.nearBuffer[i] * this.nearBuffer[i];
                            echoOnlyOutputEnergy += this.outputBuffer[i] * this.outputBuffer[i];
                        }
    
                        this.echoOnlyEnergy += echoOnlyInputEnergy;
                        this.echoOnlyOutputEnergy += echoOnlyOutputEnergy;
                        this.echoOnlyFrames++;
                    }

                    // Validate WASM output and calculate metrics
                    let outputNonZero = false;
                    let maxOutput = 0;
                    
                    for (let i = 0; i < this.blockSize; i++) {
                        if (Math.abs(this.outputBuffer[i]) > 1e-10) {
                            outputNonZero = true;
                        }
                        maxOutput = Math.max(maxOutput, Math.abs(this.outputBuffer[i]));
                    }

                    if (!outputNonZero) {
                        this.zeroOutputBlocks++;
                        // Log warning if too many zero blocks
                        if (this.zeroOutputBlocks % 50 === 1) {
                            console.warn(`[AEC Processor] WASM producing zero output (${this.zeroOutputBlocks} blocks)`);
                        }
                    } else {
                        this.validOutputBlocks++;
                    }

                    // Copy to output channels
                    for (let i = 0; i < this.blockSize && outputIndex + i < output[0].length; i++) {
                        output[0][outputIndex + i] = this.outputBuffer[i];
                        if (output[1]) {
                            output[1][outputIndex + i] = this.echoBuffer[i];
                        }
                    }

                    this.frameCount++;
                    outputIndex += this.blockSize;

                    // Debug logging for first few blocks
                    if (this.frameCount <= 100) {
                        console.log(`[AEC Processor] Block ${this.frameCount}:`, {
                            inputEnergy: blockInputEnergy.toFixed(8),
                            outputEnergy: blockOutputEnergy.toFixed(8),
                            isEchoOnly: this.isEchoOnlyPeriod,
                            erle: this.calculateEchoOnlyERLE().toFixed(2),
                        });
                    }

                } catch (error) {
                    console.error('[AEC Processor] Processing error:', error);
                    // Fall back to passthrough on error
                    for (let i = 0; i < this.blockSize && outputIndex + i < output[0].length; i++) {
                        output[0][outputIndex + i] = this.nearBuffer[i];
                        if (output[1]) {
                            output[1][outputIndex + i] = 0;
                        }
                    }
                    outputIndex += this.blockSize;
                }
            }

            // Fill any remaining output samples with zeros
            for (let i = outputIndex; i < output[0].length; i++) {
                output[0][i] = 0;
                if (output[1]) output[1][i] = 0;
            }

            // Enhanced periodic updates with comprehensive diagnostics
            if (this.frameCount % 1000 === 0) {
                const diagnostics = {
                    zeroOutputBlocks: this.zeroOutputBlocks,
                    validOutputBlocks: this.validOutputBlocks,
                    processingSuccess: (this.validOutputBlocks / (this.validOutputBlocks + this.zeroOutputBlocks)) * 100,
                    avgInputEnergy: this.inputSignalEnergy / this.frameCount,
                    avgOutputEnergy: this.outputEnergy / this.frameCount,
                    avgEchoRemoved: this.inputEchoEnergy / this.frameCount,
                    erle: this.calculateEchoOnlyERLE().toFixed(2),
                    isEchoOnlyPeriod: this.isEchoOnlyPeriod,
                    currentTimeSeconds: this.frameCount * this.blockSize / sampleRate
                };

                this.port.postMessage({
                    type: 'progress',
                    data: {
                        frameCount: this.frameCount,
                        currentErle: diagnostics.currentErle,
                        diagnostics: diagnostics,
                        bufferStatus: {
                            farAccumulator: this.farAccumulator.length,
                            nearAccumulator: this.nearAccumulator.length
                        }
                    }
                });

                // Console logging for development
                console.log(`[AEC Processor] Frame ${this.frameCount}:`, diagnostics);
            }
        } else {
            // Proper passthrough logic that prevents echo loops
            if (nearChannel && nearChannel.length > 0) {
                // Pass through microphone audio (this is correct)
                output[0].set(nearChannel);
            } else {
                // When mic is muted, output SILENCE, not far-end audio
                output[0].fill(0);
            }
        }

        return true;
    }

    stop() {
        this.isProcessing = false;

        // Clean up WASM resources
        if (this.aec) {
            this.aec.free();
            this.aec = null;
        }

        // Send final metrics with proper variable references
        if (this.frameCount > 0) {
            const finalMetrics = {
                frameCount: this.frameCount,
                erle: this.calculateEchoOnlyERLE(),
                echoOnlyFrames: this.echoOnlyFrames,
                avgInputEchoEnergy: this.inputEchoEnergy / this.frameCount,
                avgOutputEnergy: this.outputEnergy / this.frameCount,
                avgInputSignalEnergy: this.inputSignalEnergy / this.frameCount,
                avgEchoEstimate: this.echoOnlyFrames > 0 ? this.echoEstimateEnergy / this.echoOnlyFrames : 0,
                avgResidual: this.echoOnlyFrames > 0 ? this.residualEnergy / this.echoOnlyFrames : 0,
                processingEfficiency: this.validOutputBlocks / (this.validOutputBlocks + this.zeroOutputBlocks + 1e-10),
                totalBlocks: this.validOutputBlocks + this.zeroOutputBlocks
            };

            console.log(`[AEC Processor] Final metrics:`, finalMetrics);

            this.port.postMessage({
                type: 'metrics',
                data: finalMetrics
            });
        }

        this.port.postMessage({ type: 'stopped' });
    }

    // Simple near-end activity detection
    detectNearEndActivity(nearBuffer, echoEstimate) {
        // If near energy is significantly higher than echo estimate
        const nearEnergy = nearBuffer.reduce((sum, x) => sum + x*x, 0);
        const echoEnergy = echoEstimate.reduce((sum, x) => sum + x*x, 0);
    
        // Near-end is active if mic energy > 1.5x echo estimate
        return nearEnergy > 1.5 * echoEnergy;
    }

    hasFarEndActivity(farBuffer) {
        // Calculate energy of far-end signal
        let farEnergy = 0;
        for (let i = 0; i < farBuffer.length; i++) {
            farEnergy += farBuffer[i] * farBuffer[i];
        }
    
        // Threshold can be adjusted based on your noise floor
        const threshold = 0.001; // -30 dB relative to unit signal
    
        return farEnergy > threshold;
    }

    // Calculate ERLE for echo-only periods
    calculateEchoOnlyERLE() {
        if (this.echoOnlyFrames === 0) return 0;
    
        const avgEchoInput = this.echoOnlyEnergy / this.echoOnlyFrames;
        const avgEchoOutput = this.echoOnlyOutputEnergy / this.echoOnlyFrames;
    
       return 10 * Math.log10(avgEchoInput / avgEchoOutput);
    }
}

registerProcessor('aec-processor', AECProcessor);