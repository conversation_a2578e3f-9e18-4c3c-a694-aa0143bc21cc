.flyer-fixed-export {
  display: flex !important;
  flex-wrap: nowrap !important;
  box-sizing: border-box !important;
  max-width: none !important;
  max-height: none !important;
  z-index: -1 !important;
  position: absolute !important;
  overflow: hidden !important;
  top: 0 !important;
  left: -9999px !important;
}

.flyer {
  width: 100%;
  height: fit-content;
  max-width: 720px;
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
}

.flyer__background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/assets/images/flyer-background.jpeg');
  background-size: cover;
  z-index: 50;
  opacity: 0.2;
}

.flyer-content {
  font-family: 'Noto Sans', 'Noto Sans HK', sans-serif;
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.flyer__line {
  width: 100%;
  height: 12px;
  background-image: linear-gradient(to right, #302219, #f2834f);
}

.flyer-header {
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 1.5rem;
  border-bottom: 2px solid #efe9e2;
  background-color: #30221905;
}

.flyer-header__logo {
  width: 3.75rem;
  height: 3.75rem;
}

.flyer-header-headings {
  display: flex;
  flex-direction: column;
}

.flyer-header-headings__title {
  font-weight: 700;
  color: #302219;
  font-size: 1.5rem;
  margin: 0;
  line-height: 1.3;
}

.flyer-header-headings__subtitle {
  font-weight: 500;
  color: #736a67;
  font-size: 1rem;
  font-style: italic;
  margin: 0;
  line-height: 1.5;
}

.flyer-body {
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 1.5rem;
}

.flyer-body-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.flyer-body-header__title {
  font-size: 1.7rem;
  font-weight: 400;
  font-style: italic;
  color: #736a67;
  text-align: center;
}

.flyer-body-header__hr {
  width: 100px;
  height: 2px;
  background-color: #f2834f;
}

.flyer-body-classroom-name {
  width: calc(100% - 48px);
  padding: 1.5rem;
  border-radius: 2.2rem;
  border: 0.125rem solid rgba(242, 131, 79, 0.3);
  background-color: rgba(242, 131, 79, 0.1);
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  align-items: center;
  justify-content: center;
}

.flyer-body-classroom-name__subtitle {
  font-size: 1rem;
  font-weight: 500;
  color: #736a67;
  margin: 0;
  line-height: 1.5;
}

.flyer-body-classroom-name__title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e1410;
  margin: 0;
  line-height: 1.3;
}

.flyer-body-date {
  border-radius: 1.5rem;
  background-color: rgba(48, 34, 25, 0.08);
  border: 1px solid rgba(48, 34, 25, 0.2);
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  width: calc(100% - 32px);
}

.flyer-body-date__icon {
  width: 2rem;
  height: 2rem;
  color: #302219;
}

.flyer-body-date-content {
  display: flex;
  flex-direction: column;
}

.flyer-body-date-content__date {
  font-size: 1.05rem;
  font-weight: 700;
  color: #484041;
  margin: 0;
  line-height: 1.4;
}

.flyer-body-date-content__time {
  font-size: 1rem;
  font-weight: 400;
  color: #736a67;
  margin: 0;
  line-height: 1.6;
}

.flyer-body-info {
  width: 100%;
  margin-top: 1rem;
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  margin-bottom: 1rem;
}

.flyer-body-info-card {
  width: 100%;
  max-width: calc(100% - 2rem);
  padding: 1rem;
  border-radius: 1rem;
}

.flyer-body-info-card.flyer-body-info-card_red {
  background-color: rgba(216, 103, 79, 0.1);
  border: 1px solid rgba(216, 103, 79, 0.2);
}

.flyer-body-info-card.flyer-body-info-card_gray {
  background-color: rgba(30, 20, 16, 0.1);
  border: 1px solid rgba(30, 20, 16, 0.2);
}

.flyer-body-info-card.flyer-body-info-card_green {
  background-color: rgba(127, 176, 105, 0.1);
  border: 1px solid rgba(127, 176, 105, 0.2);
}

.flyer-body-info-card.flyer-body-info-card_blue {
  background-color: rgba(158, 193, 204, 0.1);
  border: 1px solid rgba(158, 193, 204, 0.3);
}

.flyer-body-info-card.flyer-body-info-card_yellow {
  background-color: rgba(248, 188, 90, 0.1);
  border: 1px solid rgba(248, 188, 90, 0.4);
}

.flyer-body-info-card-header {
  display: flex;
  align-items: center;
  justify-items: start;
  gap: 0.6rem;
}

.flyer-body-info-card-header__title {
  font-size: 0.75rem;
  line-height: 1.5;
  font-weight: 400;
  color: #736a67;
  text-transform: uppercase;
  margin: 0;
}

.flyer-body-info-card-header__icon {
  width: 1.25rem;
  height: 1.25rem;
}

.flyer-body-info-card_red .flyer-body-info-card-header__icon,
.flyer-body-info-card_red .flyer-body-info-card-content {
  color: #e76f51;
}

.flyer-body-info-card_green .flyer-body-info-card-header__icon,
.flyer-body-info-card_green .flyer-body-info-card-content {
  color: #7fb069;
}

.flyer-body-info-card_blue .flyer-body-info-card-header__icon,
.flyer-body-info-card_blue .flyer-body-info-card-content {
  color: #6d9dad;
}

.flyer-body-info-card_yellow .flyer-body-info-card-header__icon,
.flyer-body-info-card_yellow .flyer-body-info-card-content {
  color: #e69e31;
}

.flyer-body-info-card_gray .flyer-body-info-card-header__icon,
.flyer-body-info-card_gray .flyer-body-info-card-content {
  color: #484041;
}

.flyer-body-info-card-content {
  margin-top: 0.3rem;
  font-size: 1rem;
  line-height: 1.6;
  font-weight: 700;
}

.flyer-body-qr {
  width: 200px;
  height: 200px;
}

.flyer-body-footer {
  padding: 1rem;
  width: 100%;
  max-width: calc(100% - 2rem);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-image: linear-gradient(90deg, #f2834f 0%, #302219 100%);
  color: white;
}

.flyer-body-footer__title {
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
  font-weight: 400;
}

.flyer-body-footer__subtitle {
  margin: 0;
  font-size: 0.75rem;
  line-height: 1.5;
  font-weight: 400;
  opacity: 0.8;
}
