{"title": "VirtuosoHub - Connect and learn across language barriers", "header": {"dashboard": "Dashboard"}, "pagination": {"previous": "Previous", "next": "Next"}, "sidebar": {"main": "Main Page", "news": "News", "onlineClassroom": "Online Classroom", "practiceTools": "Practice Tools", "musicItems": "Music Items", "events": "Events", "ensembles": "Ensembles", "people": "People", "institutions": "Institutions", "venues": "Venues", "financing": "Financing", "profile": "Profile", "settings": "Settings", "logout": "Sign Out", "profileTooltip": "Click to open your profile", "reportForm": "Contact Support"}, "globals": {"watcherDisabled": "Available only for teachers and students", "passwordStrength": {"title": "Password Strength", "weak": "Weak", "fair": "Fair", "good": "Good", "strong": "Strong", "tooltip": "A secure password should be at least 8 characters long and include uppercase and lowercase letters, numbers, and optionally special characters. The more character types and greater length you use, the stronger your password."}, "permissions": {"canCommentOnRecordings": "You have no permission to comment on recordings", "canDownloadMaterials": "You have no permission to download materials", "canGuestInvite": "You have no permission to invite guests", "canKickParticipant": "You have no permission to remove participants", "canParticipantInvite": "You have no permission to invite participants", "canStartClass": "You have no permission to start a class", "canUpdateClass": "You have no permission to update a class", "canUpdateParticipant": "You have no permission to update a participant", "canUploadMaterials": "You have no permission to upload materials", "canViewRecordings": "You have no permission to view recordings", "canDeleteMaterial": "You have no permission to delete material"}}, "globalFields": {"displayName": {"label": "Display Name", "validation": {"required": "Display Name is required", "minLength": "Display Name must be at least 3 characters", "maxLength": "Display Name must be at most 20 characters"}}, "email": {"label": "Email Address", "validation": {"required": "Email is required", "invalid": "Invalid email address. A valid address follows <NAME_EMAIL> (for example, <EMAIL>).", "minLength": "Email must be at least 3 characters", "maxLength": "Email must be at most 50 characters"}}, "password": {"label": "Password", "validation": {"required": "Password is required", "minLength": "Password must be at least 8 characters long", "maxLength": "Password must be at most 50 characters", "pattern": "Password must contain at least one uppercase letter, one lowercase letter, and one number", "weakStrength": "Password strength is too weak, please choose a stronger password"}}, "confirmPassword": {"label": "Confirm Password", "validation": {"required": "Confirm Password is required", "match": "Passwords do not match"}}, "classroomName": {"label": "Classroom Name", "validation": {"required": "Classroom name is required", "minLength": "Classroom name must be at least 3 characters", "maxLength": "Classroom name must be at most 30 characters"}}, "classroomDescription": {"label": "Classroom Description", "validation": {"maxLength": "Description must be at most 200 characters"}}, "languages": {"locale": {"label": "Locale", "tooltip": "Select language you want to the app to be translated to."}, "speakingLanguage": {"label": "Speaking Language", "tooltip": "Select language you speak."}, "translationLanguage": {"label": "Translation Language", "tooltip": "Select language you want to translate other participants to."}}, "uploadFiles": {"text": "Click to upload a file (max {{size}}MB)", "textImageOnly": "Click to upload an image (max {{size}}MB)", "validation": {"imageOnly": "Only image files are allowed.", "fileSize": "{{name}} exceeds the maximum file size of {{size}}MB.", "maxFiles": "You can upload up to {{number}} files only."}}}, "legal": {"privacy": "Privacy Policy", "terms": "Terms & Conditions", "codeOfConduct": "Code of Conduct", "copyright": "Copyright & Third-Party Notices", "licenses": "Licenses"}, "cookiesConsentDialog": {"title": "<PERSON><PERSON>", "description": {"text1": "We use necessary cookies to make our site work. We’d like to set additional analytics cookies to measure how you use our site and improve your experience. Read our ", "text2": " for full details", "link": "Privacy Policy"}, "buttons": {"accept": "Accept & Continue", "decline": "Decline Cookies"}}, "home": {"description": "Connect and learn across language barriers in real-time", "buttons": {"signIn": "Sign In", "guest": "Continue as Guest", "create": {"text": "Create Classroom", "tooltip": "Create and setup a new classroom environment with customizable options. Invite students, teachers, and guests to your professionally organized virtual music classroom."}, "instant": {"text": "Instant Class Session", "tooltip": "Start a class session instantly. Create a new classroom with default settings and invite students."}, "noAccount": {"text": "Dont have an account? ", "link": "Sign Up"}}, "dialogs": {"signIn": {"title": "Welcome back!", "buttons": {"signIn": "Sign In", "signUp": "Sign Up", "forgotPassword": "Forgot Password?", "or": "Or continue with", "guest": {"text": "Continue as Guest", "warning": "Some features are limited in guest mode"}}, "errors": {"invalidCredentials": "Invalid email or password. Check your credentials and try again.", "unexpected": "An unexpected error occurred. Please try again later.", "emailVerification": {"text1": "You need to verify your email address. <br /> Check your email <b>({{email}})</b>", "text2": "Did not recieve an email?"}}}, "signUp": {"title": "Create an account", "steps": {"create": "Create an account", "terms": "Terms & Conditions", "consent": "Consents & Permissions", "verify": "Verify your email", "complete": "Complete Verification", "buttons": {"next": "Continue", "back": "Back"}}, "fields": {"terms": {"text1": "Please read our ", "text2": "Terms & Conditions", "text3": " and ", "text4": "Privacy Policy", "text5": " before proceeding.", "checkbox": "I agree to the Terms & Conditions, Privacy Policy and confirm that I am at least {{age}} years old.", "validation": {"required": "You must agree to the terms"}}, "consent": {"canRecord": "Allow others in the class to record you?", "canShareClassRecordings": "Allow others to share recordings of your classes?", "canShareLiveStream": "Allow others to share your live video stream?", "canAIProcess": "Allow the platform to use your data for AI features?", "canTeacherInviteGuests": "Allow teachers to invite guests to the class you are in?", "canStudentInviteGuests": "Allow students to invite guests to the class you are in?", "canAnonymousJoin": "Allow people without an account to join the class you are in?", "canPartyChat": "Allow participants to use group chat during the class you are in?", "canInviteMultipleTeachers": "Allow more than one teacher to be invited to the class?", "canInviteMultipleStudents": "Allow more than one student to be invited to the class?"}, "verify": {"label": "To verify your account, we'll send a verification link to "}, "complete": {"label": "🎉 We've sent a verification link to {{email}} Please check your inbox (and spam folder) to verify your account.", "didntReceive": "Didn't receive the email?", "resend": {"button": "Resend", "disabled": "Resend after ({{time}}s)", "tooltip": "You need to wait {{time}} seconds to resend the email."}}}}, "guest": {"title": "Guest Access", "description": "Enter your details to continue as a guest. You'll have limited access.", "buttons": {"continue": "Begin Your Musical Journey"}, "fields": {"translationLanguage": {"label": "Video conferences will be automatically translated to the selected language."}, "terms": {"text1": "I agree to the ", "text2": "Terms & Conditions", "text3": ", ", "text4": "Privacy Policy", "text5": " and confirm that I am at least {{age}} years old.", "validation": {"required": "You must agree to the terms"}}}}, "instant": {"title": "Instant Class Session", "description": "Start a class session instantly. Create a new classroom with default settings and invite students.", "buttons": {"continue": "Start Instant Class"}}, "onboarding": {"title": "Welcome to Virtuo<PERSON><PERSON><PERSON>", "text": "Thank you for signing up for our app! You’re officially part of our community, and we can’t wait to see what you’ll accomplish here.", "buttons": {"dashboard": "Go to Classrooms", "create": "Create Classroom"}}, "resetPassword": {"title": "Reset Password", "description": "Enter your email and click the button — we’ll send you an email with instructions on how to reset your password. Just follow the link in the email to set a new one.", "buttons": {"back": "Back to Sign In", "send": "Send Reset Link", "save": "Save New Password"}}}}, "profile": {"title": "Profile", "details": {"title": "Details", "labels": {"firstName": "First Name", "lastName": "Last Name", "registredAt": "Registred Since", "type": {"title": "Account Type", "registred": "Full Access", "anonymous": "Limited Access"}}}, "actions": {"title": "Actions", "privacy": "Privacy Policy", "terms": "Terms & Conditions", "codeOfConduct": "Code of Conduct", "consents": "Consents", "copyright": "Copyright & Third-Party Notices", "delete": {"button": "Delete Account", "disabled": "Delete Account functionality is currently unavailable"}}, "dialogs": {"deleteAccount": {"title": "Are you sure?", "description": {"text1": "We will delete your account and data according to our ", "text2": "Terms and conditions", "text3": ". After the account deletion you might not be able to restore your account data."}, "confirmationPassword": "You have to enter your password to confirm your action. Your account and all personal data will be deleted.", "confirmationDisplayName": "You have to enter your display name ({{displayName}}) to confirm your action. Your account and all personal data will be deleted.", "buttons": {"confirm": "Delete", "deleting": "Deleting...", "cancel": "Cancel"}, "fields": {"password": "Password", "displayName": "Display Name"}}}}, "settings": {"title": "Settings", "description": "Edit your preferences and profile details here. These settings will be automatically saved to your account.", "buttons": {"save": "Save Changes"}}, "anonymousAlert": {"text": "You are using the app as a guest. Please sign up to save your data.", "action": "Sign Up"}, "classrooms": {"title": "My Classrooms", "activeClasses": "Active Classes", "my": "My classrooms", "empty": {"active": {"title": "You don't have any live classrooms", "description": "Wait for someone to start a live call"}, "all": {"title": "You dont't have any classrooms yet", "description": "Create your first classroom to get started"}}, "actions": {"create": "Create a Classroom", "filter": {"title": "Filter", "tooltip": "Filter by date", "disabled": "Filtering functionality is currently unavailable"}, "load": "Load More"}, "card": {"live": "Live", "students": "students", "teachers": "teachers", "actions": {"join": "Join", "open": "Open Classroom"}}, "dialogs": {"create": {"title": "Create Classroom", "description": "Create a new Classroom and set up details.", "titlePlaceholder": "{{username}}'s Classroom", "buttons": {"create": "Create Classroom"}}, "edit": {"triggers": {"title": "Edit Classname", "description": "Edit Classroom Description", "delete": "Delete Classroom"}, "labels": {"title": "Classname", "description": "Classroom Description", "delete": "Delete classroom?"}, "buttons": {"save": "Save", "cancel": "Cancel", "delete": "Delete"}, "placeholders": {"title": "Enter new classname", "description": "Enter new description"}}}}, "classroom": {"header": {"loading": "Loading...", "addDescription": "Click here to add description", "noDescription": "There is no description yet", "teacherLabel": "Teacher: {{name}}"}, "participants": {"title": "Participants", "categories": {"teacher": "Teachers", "student": "Students", "guest": "Guests"}, "summary": "{{category}}: {{count}}", "membersLabel": "Members", "empty": {"noTeachers": "There are no teachers at the moment.", "noStudents": "There are no students at the moment.", "noGuests": "There are no guests at the moment."}, "participantManagement": {"teacher": "Teachers List", "student": "Students List", "guest": "Guests List", "buttons": {"close": "Close", "remove": "Remove?", "pending": "Removing..."}}}, "history": {"title": "History", "durationLabel": "Duration", "units": {"hour": "hour", "hour_plural": "hours", "minute": "minute", "minute_plural": "minutes"}, "duration": "{{count}} {{unit}}", "noSummary": "No summary yet", "viewDetails": "View Details"}, "materials": {"title": "Classroom Materials", "types": {"pdf": "PDF", "mp3": "MP3", "mp4": "MP4"}, "buttons": {"view": "View", "delete": "Delete"}, "dialogs": {"delete": {"title": "Delete Material", "description": "Are you sure you want to delete this material?", "buttons": {"cancel": "Cancel", "confirm": "Delete"}}}, "tooltips": {"report": "Report inappropriate or harmful content"}}, "empty": {"history": {"title": "You don't have any class history", "description": "Create a class to get started"}, "materials": {"title": "You don't have any materials", "description": "Upload a material to get started"}}, "active": {"live": "Live", "startedAt": "Started at {{time}}", "buttonJoin": "Join"}, "dialogs": {"settings": {"title": "Classroom Settings", "trigger": "Settings", "categories": {"general": "General", "access": "Access & Participation", "recording": "Recording & Sharing", "translation": "Translation", "materials": "Materials", "in-class": "In-Class Interaction"}, "general": {"title": "General", "description": "Manage your classroom details."}, "access": {"title": "Access & Participation", "description": "Control who can enter the classroom and how they engage with it structurally.", "fields": {"canAnonymousJoin": {"title": "Allow Anonymous Users to Join", "description": "Allows unregistered users (“guests”) to join the classroom."}, "allowInviteGuests": {"title": "Allow Invite Guests", "description": "Allows participants to invite guests to the class."}}}, "recording": {"title": "Recording & Sharing", "description": "Manage recording, sharing, and feedback on class content.", "fields": {"allowRecording": {"title": "Allow Recording", "description": "Allow the class to be recorded."}, "allowSharing": {"title": "Allow Sharing", "description": "Allow sharing of class recordings."}, "allowComments": {"title": "Allow Comments", "description": "Enable comments on class recordings."}}}, "translation": {"title": "Translation", "description": "Control live captions, transcripts, and automatic translation.", "fields": {"allowTranscription": {"title": "Allow Transcription", "description": "Create a written transcript of everything said in class."}}}, "materials": {"title": "Materials", "description": "Control whether participants can upload or download class files.", "fields": {"allowMaterialsUpload": {"title": "Allow Materials Upload", "description": "Lets participants upload files or materials to the class."}, "allowMaterialsDownload": {"title": "Allow Materials Download", "description": "Lets participants download class materials."}}}, "in-class": {"title": "In-Class Interaction", "description": "Manage live communication during class.", "fields": {"allowChat": {"title": "Allow Chat", "description": "Enables a chat window during class."}}}, "buttons": {"save": "Save and Close"}}, "delete": {"title": "Delete Classroom", "trigger": "Delete", "description": "Deleting this classroom will permanently remove all associated class history, recordings, comments, and uploaded materials. This action cannot be undone.", "buttons": {"cancel": "Cancel", "confirm": "Delete Classroom"}}, "create": {"trigger": "New Class", "title": "Create Class", "description": "Create a new class and set up details.", "buttons": {"create": "Create Class"}, "fields": {"name": {"label": "Class Name", "validation": {"required": "Class name is required", "minLength": "Class name must be at least 3 characters", "maxLength": "Class name must be at most 50 characters"}}}, "tooltip": {"disabled": {"title": "Active class is already created!", "description": "Only one active class is possible!"}}}, "history": {"trigger": "View More", "title": "Class History", "tooltips": {"disabled": "History is currently unavailable"}, "noRecording": "There is no recording available", "header": {"copyLink": {"button": "Copy Link", "tooltip": "Link sharing is currently unavailable"}}, "summary": {"title": "Summary", "noSummary": "There is no summary available"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "noFeedback": "There is no feedback available"}, "keyMoments": {"title": "Key Moments", "time": "{{time}}", "description": "{{description}}", "noKeyMoments": "No key moments available"}, "participants": {"title": "Participants", "roles": {"lead_teacher": "Lead Teacher", "teacher": "Teacher", "student": "Student", "guest": "Guest"}}, "transcript": {"title": "Transcript", "time": "{{time}}", "noTranscript": "No transcript available", "pagination": {"previous": "Previous", "next": "Next"}}, "player": {"unsupported": "Your browser does not support playback of this recording."}, "buttons": {"close": "Close"}}, "uploadMaterial": {"trigger": "Upload Material", "title": "Upload Material", "description": "Upload your classroom materials here. Maximum file size is 10MB.", "fields": {"chooseFile": {"label": "Choose a file", "validation": {"required": "Please select a file", "type": "Please select a PDF file only", "size": "File size exceeds 10MB limit"}}, "title": {"label": "Material title", "validation": {"required": "Material title is required", "minLength": "Material title must be at least 5 characters", "maxLength": "Material title cannot exceed 50 characters"}}}, "buttons": {"upload": "Upload Material", "uploading": "Uploading..."}}}}, "class": {"status": {"fetching": "Loading active class details...", "audioProcessing": "Setting up local environment...", "permissions": "Requesting Camera and Microphone permission...", "waitingForOthers": "Waiting for others to join to the class, <br /> click on invite button to add participants", "livekit": {"connecting": "We are connecting you to the class session...", "failed": "We couldn’t establish your live session. <br /> This might be due to unstable or offline network. <br /> Please check your network conditions, <br /> then click “<PERSON><PERSON>” to try joining again."}}, "buttons": {"retry": "Retry"}, "localMediaError": {"mobile": {"chrome_edge": {"text1": "Tap the website settings icon to the left from the URL.", "text2": "Tap Permissions.", "text3": "Set Camera and Microphone to Allow.", "text4": "Return to the page and refresh."}, "yandex": {"text1": "Tap the three dots in the address bar.", "text2": "Tap Virtuosohub.ai.", "text3": "Change Camera and Microphone to Allow, then reload."}, "firefox": {"text1": "Refresh the page.", "text2": "If prompted, allow camera and microphone.", "text3": "If not, tap the 🔒 lock icon to the left of the address bar.", "text4": "Change Camera and Microphone to Allow, then reload."}, "safari": {"text1": "Click Refresh page button or simply refresh page.", "text2": "Allow Camera and Microphone.", "text3": "If that didn’t work, open Settings → Privacy & Security → Camera/Microphone, enable for your browser, then reopen & reload."}, "default": {"text1": "Please check your browser’s settings to allow camera &amp; mic, then reload."}}, "pc": {"edge_yandex": {"text1": "Click the lock icon 🔒 in the left side of the address bar.", "text2": "Set Camera and Microphone to Allow.", "text3": "Reload the page."}, "chrome": {"text1": "Click the Permissions icon to the left of the address bar.", "text2": "Set Camera and Microphone to Allow.", "text3": "Refresh the page."}, "firefox": {"text1": "Click the camera icon or microphone icon in the left of the address bar.", "text2": "Click on the cross icon to remove the block from camera and microphone.", "text3": "Reload the page.", "text4": "Allow camera and microphone."}, "safari": {"text1": "In the menu bar choose Safari ▸ Settings for This Website…", "text2": "In the pop-up, set Camera and Microphone to Allow.", "text3": "If you don’t see them, open Safari ▸ Settings ▸ Websites and adjust under Camera / Microphone.", "text4": "Refresh the page."}, "default": {"text1": "Please check your browser’s site or privacy settings and grant this page access to your camera &amp; microphone, then reload."}}, "blocked": {"title": "Permissions Blocked", "description": "We need access to your <strong>microphone</strong> and <strong>camera</strong> to join the class."}, "notFound": {"title": "Device Not Found", "text": "Make sure your device(s) are connected and not in use by another app, then refresh.", "noAuido": "We couldn’t detect a <strong>microphone</strong>.", "noVideo": "We couldn’t detect a <strong>camera</strong>."}, "inUse": {"title": "Device In Use", "text": "Your camera or microphone appears to be in use by another app or browser tab. Please close any other application that might be using them, then reload."}, "reload": "Refresh Page"}, "dialogs": {"welcome": {"preview": {"party": {"title": "Welcome to the Class!", "description": "You’ve joined the session as an <strong>Active Participant</strong>. You can share your audio and video, ask questions, and collaborate with the teacher and classmates in real time. Dive in and enjoy the learning experience!", "consent": {"trigger": "Enable Recording and AI Features", "dialog": {"title": "Are you sure?", "description": " If you disable this option, you will lose access to subtitles, translations, and AI features. In addition, you will not be recorded, and your words will not be included in the class summary or transcription.", "buttons": {"no": "No", "yes": "Yes"}}}, "tooltips": {"microphone": {"on": "Unmute microphone", "off": "Mute microphone"}, "camera": {"on": "Turn camera on", "off": "Turn camera off"}}}, "watcher": {"title": "Welcome, Guest!", "description": "You’ve joined an active session in <strong>Watcher</strong> mode. You can observe the live class and follow along with the speakers, but you won’t be able to share audio or video yourself. Sit back and enjoy!"}, "buttons": {"leave": "Leave", "continue": "Continue"}}, "audioSelection": {"title": "How are using your device?", "types": {"computer": {"title": "Computer audio", "description": "We will calibrate your microphone and set up your rehearsal with maximum quality."}, "headphones": {"title": "Headphones", "description": "It's pure sound, without any processing. But you will have to use headphones as a sound source."}}, "close": "Close"}, "codeOfConduct": {"respect": {"title": "Respect & Listen", "description": {"text1": "1. Speak politely; no insults or slurs.", "text2": "2. Give constructive feedback — <br /> focus on ideas, not people.", "text3": "3. Take turns: indicate that you want <br /> to speak or chat so everyone is heard."}}, "checks": {"title": "Smart Safety Checks", "description": {"text1": "1. Our AI watches for harmful or illegal content.", "text2": "2. If it spots a red flag, a human moderator <br /> double-checks before anything is removed.", "text3": "3. No AI-only bans—people make the final call."}}, "report": {"title": "See Something? Report It", "description": {"text1": "1. Click the Report button in-app or <br /> visit ", "text2": "2. Our Trust team reviews every report <br /> and will follow up with you."}, "full": "Full code of conduct for more information"}, "buttons": {"close": "Close", "next": "Next", "back": "Back"}}}}, "actionBar": {"processing": {"computer": "Computer audio", "headphones": "Headphones", "tooltip": {"on": "Enable audio processing for computer use (echo cancellation and enhancements)", "off": "Disable processing for raw audio. Use headphones to avoid echo"}}, "fullscreen": {"enter": "Enter fullscreen", "exit": "Exit fullscreen"}, "camera": {"text": "Camera", "tooltip": {"on": "Turn camera on", "off": "Turn camera off"}}, "mic": {"text": "Microphone", "tooltip": {"on": "Unmute microphone", "off": "Mute microphone"}}, "translate": {"text": "Translate", "tooltip": {"on": "Turn translation on", "off": "Turn translation off"}, "disabled": "Translation is currently unavailable"}, "invite": {"text": "Invite", "tooltipParticipant": "Invite other participants", "tooltipGuest": "Invite guests", "tooltip": "Invite participants and guests", "type": {"teacher": "Teacher", "student": "Student", "guest": "Guest"}}, "exit": {"text": "Exit", "tooltip": "Exit from the class session"}}, "watcherCount": "Guests observing your class session: {{count}}", "you": "You ({{name}})", "tools": {"settings": {"text": "Settings", "tooltip": "Settings", "class-settings": {"title": "Class Settings", "items": {"sharing": {"title": "Allow Sharing", "text": "Let participants share class recording."}, "chat": {"title": "Allow Chat", "text": "Enable or disable the class chat for participants."}}}, "translation": {"title": "Translation Settings", "items": {"language": {"title": "Language", "text": "Choose the language for translation. Messages and captions will be translated into this language.", "placeholder": "English"}, "translate": {"title": "Translate", "text": "Translate speech into captions or audio in your selected language."}, "captions": {"title": "Show Captions", "text": "Display captions that show the translated speech of participants in your selected language."}, "audio": {"title": "Audio Translation", "text": "Enable or disable audio translation. When enabled, participants’ speech will be played back in your selected language."}}}, "general": {"title": "General Settings", "items": {"notificationSound": {"title": "Enabled Chat Notification Sound", "text": "Play a sound whenever you receive a new chat message."}, "automaticParticipant": {"title": "Automatic Active Speaker Selection", "text": "Automatically switch the screen to whoever is speaking or playing an instrument."}, "sharing": {"title": "Sharing Consent", "text": "Allow or deny consent to share this class recording with others."}}}, "sharing": {"title": "Sharing Settings", "items": {"invites": {"title": "Allow participant invites", "text": "Let other participants invite users to this session"}, "recordings": {"title": "Allow recording share", "text": "Let participants share recordings with non-participants"}, "status": {"title": "Session share status", "text": "Control who can access this session", "placeholder": "Private"}}}, "device": {"title": "<PERSON><PERSON>s", "audioOut": {"title": "Audio Output Devices", "text": "Select one audio output device"}, "audioIn": {"title": "Audio Input Devices", "text": "Select multiple devices to mix audio"}, "video": {"title": "Video Devices", "text": "Select one video device"}}, "audioSettings": {"title": "Audio Settings", "instrument": {"title": "Select you instrument type", "description": "Choose the primary instrument you’ll be using so we can optimize the audio accordingly.", "type": {"string": "String", "wind": "Wind", "percussion": "Percussion", "piano": "Piano", "vocal": "Vocal", "general": "General"}}, "roomAcoustics": {"title": "Select you room acoustics", "description": "Select the acoustic profile of your room so we can fine-tune echo cancellation and sound processing.", "type": {"dead": "Dead", "normal": "Normal", "live": "Live"}}, "outputVolume": {"title": "Output Volume", "description": "Adjusts the final output gain applied to your audio stream. This controls how loud your audio is played for others.", "audioMeter": {"db": "dB", "level": "Level", "peak": "Peak", "clip": "CLIP"}}, "advancedAuioSettings": {"title": "Advanced Audio Settings", "compressor": {"title": "Dynamic Compressor Settings", "description": "Compression reduces the volume of loud sounds to balance audio dynamics. Adjust these parameters to shape how the compressor reacts to your input signal.", "threshold": {"title": "<PERSON><PERSON><PERSON><PERSON> (dB)", "description": "Sets the level (in dB) where compression starts. Lower = more compression on quiet signals."}, "knee": {"title": "<PERSON><PERSON> (dB)", "description": "Controls how gradually compression starts. Higher = smoother transition."}, "ratio": {"title": "<PERSON><PERSON>", "description": "How much compression is applied. 4:1 means for every 4 dB over threshold, output increases by 1 dB."}, "attack": {"title": "Attack (s)", "description": "How quickly the compressor responds. Shorter = faster clamp on peaks."}, "release": {"title": "Release (s)", "description": "How quickly compression stops after signal drops below threshold."}}, "jitterBuffer": {"title": "Jitter Buffer Control", "description": "Adjust audio buffer to prevent pitch changes during poor network conditions. Higher values increase stability but add latency.", "currentBuffer": "Current buffer"}}}}, "metronome": {"title": "Metronome", "tooltip": "Metronome", "bpm": "bpm", "bpmLabel": "Beats per Minute", "buttons": {"start": "Start", "stop": "Stop"}, "select": {"2/4": "2/4", "3/4": "3/4", "4/4": "4/4", "6/8": "6/8", "9/8": "9/8", "12/8": "12/8", "5/4": "5/4", "7/8": "7/8", "3/8": "3/8", "2/2": "2/2", "5/8": "5/8", "7/4": "7/4"}, "volume": "Volume", "mixing": {"title": "Mix Metronome", "description": "When enabled, others in the session will hear your tempo"}}, "participants": {"title": "Participants", "tooltip": "Participants", "teachers": "Teachers", "students": "Students", "guests": "Guests", "search": {"placeholder": "Search...", "notFound": "No participants found"}, "participant": {"status": {"live": "LIVE", "offline": "OFFLINE"}, "tooltips": {"report": "Report a user for inappropriate behavior"}}}, "chat": {"title": "Cha<PERSON>", "tooltip": "Cha<PERSON>", "message": {"placeholder": "Type a message...", "tooltip": "Ask AI Assistant with Voice (Coming Soon)"}, "you": "You ({{name}})", "noMessage": {"title": "No messages yet...", "description": " This space lets you talk with other attendees in real-time—ask questions, share insights, and collaborate throughout the session."}}, "materials": {"title": "Materials", "tooltip": "Materials", "main": {"header": {"title": "Materials", "files": "Files: {{count}}", "sorting": {"disabled": "Sorting functionality is currently unavailable"}, "filter": {"disabled": "Filtering functionality is currently unavailable"}}, "search": {"placeholder": "Search materials...", "disabled": "Search functionality is currently unavailable"}, "actions": {"upload": {"text": "Upload", "disabled": "Upload functionality is currently unavailable"}, "scores": "Search Scores"}, "empty": {"title": "There are no materials yet.", "description": "Search for sheet music or upload your own files."}, "noAccess": {"title": "No Access to Materials", "description": "You don’t have permission to view the class materials."}}, "imslpSearch": {"header": {"title": "IMSLP Search"}, "alert": "IMSLP (International Music Score Library Project) is an external resource. Please ensure you comply with copyright laws in your jurisdiction when downloading scores.", "search": {"placeholder": "Search IMSLP for scores..."}, "searchResults": {"empty": {"title": "Search for sheet music and scores", "description": "Enter composer names, work titles, instruments, or paste an IMSLP URL"}, "errors": {"title": "Sorry", "onlyEnglish": "We currently support only English language for IMSLP search"}, "notFound": "No results found."}}, "imslpSearchResults": {"header": {"title": "IMSLP Search Results"}, "results": {"notFound": {"title": "Sorry", "description": "We couldn’t find any information about this work"}, "noEditionsFound": {"title": "Sorry", "description": "We couldn’t find any editions of this work"}, "workInformation": {"title": "Work Information", "labels": {"title": "Title", "altTitle": "Alternative Title", "opus": "Opus/Catalogue Number", "year": "Year of Composition", "style": "Style", "instrumentation": "Instrumentation"}}, "editions": {"title": "Available Editions", "unknowns": {"editor": "Unknown Editor", "publisher": "Unknown Publisher", "copyright": "Unknown Copyright Info"}, "pages": "Pages: {{count}}"}}}, "pdf": {"notFound": {"title": "Document Was not found", "description": "The requested PDF document could not be loaded."}, "saveIMSLP": {"tooltip": "Add item to classroom materials"}, "download": {"tooltip": "Download document"}, "fullscreen": {"tooltip": {"enter": "Enter fullscreen mode", "exit": "Exit fullscreen mode"}}}}, "report": {"trigger": {"tooltip": "Report an issue or send feedback"}}}}, "invite": {"trigger": "Invite", "title": "Create Invite", "form": {"role": {"label": "Select role for invite", "more": "Learn More", "teacher": "Teacher", "student": "Student", "guest": "Guest", "tooltip": {"teacher": {"label": "This invite is set for teachers only. ", "content": "This invite was created for teachers, so anyone who uses it will join the classroom as a teacher. To invite someone as Student or Guest, select the appropriate role from the menu before sending the invite."}, "student": {"label": "This invite is set for students only. ", "content": "This invite was created for students, so anyone who uses it will join the classroom as a student. To invite someone as Teacher or Guest, select the appropriate role from the menu before sending the invite."}, "guest": {"label": "This invite is set for guests only. ", "content": "This invite was created for guests, so anyone who uses it will join the classroom as a guest. To invite someone as Teacher or Student, select the appropriate role from the menu before sending the invite."}}}, "title": {"label": "Title*", "placeholder": "Enter Invite Title", "defaults": {"classroom": "Join {{name}}", "event": "The Ultimate Event Experience"}}, "description": {"label": "Description", "placeholder": "Enter Invite Description", "defaults": {"classroom": "You have been invited to join a classroom on VirtuosoHub.", "event": "You have been invited to a musical event on VirtuosoHub"}}, "access": "Require sign up to join", "event": "Make this an event", "date": "Date*", "time": "Time*", "expires": {"label": "Expires in", "options": {"never": "Never", "1d": "1 Day", "3d": "3 Days", "7d": "7 Days", "1m": "1 Month", "1y": "1 Year"}}, "userLimit": {"label": "User Limit", "options": {"unlimited": "Unlimited", "custom": "Custom"}}}, "share": {"title": "Copy, Share or Download the Invite", "copy": {"label": "Share the link", "button": "Copy", "success": "Link copied successfully"}, "or": "or", "email": {"label": "Share via Email", "placeholder": "Enter email address", "send": "Send", "tooltip": "Send via email is currently unavailable"}, "social": {"label": "Share via Social Media", "linkText1": "Follow the link to join my classroom on VirtuosoHub.", "linkText2": "Follow the link to join my classroom on VirtuosoHub: {{link}}"}, "pdf": {"button": "Download PDF", "pending": "Preparing PDF..."}}, "flyer": {"joiningAs": {"title": "Joining As", "teacher": "Teacher", "student": "Student", "guest": "Guest"}, "access": {"title": "Access", "open": "Open Access", "signup": "Sign-up Only"}, "validUntil": {"title": "<PERSON>id <PERSON>", "never": "No expiration"}, "users": {"title": "Users", "unlimited": "Unlimited"}}}, "notFound": {"text": "Requested page was not found", "button": "Back to Home Page"}, "emailVerificationError": {"button": "Back to Sign In Page", "errors": {"invalidOrExpired": {"title": "Oops! That link isn’t working anymore.", "description": "It looks like your verification link has expired or is invalid. No worries—just click below to send yourself a fresh verification email and get back on track!"}}}, "report": {"header": "Report an issue", "steps": {"typeSelection": {"title": "What would you like to report?", "types": {"bug": {"title": "Technical Issue", "description": "Something isn’t working correctly — like audio, video, or connection problems."}, "user": {"title": "Class Participant", "description": "Report someone for disruptive, abusive, or suspicious behavior."}, "content": {"title": "Inappropriate Content", "description": "Flag offensive, harmful, or rule-breaking content you’ve encountered."}}}, "form": {"title": "Describe the issue you encountered", "description": {"label": "1. Provide a description of the issue", "info": {"tooltip": "Not sure what to write? See our helper note."}, "popover": {"bug": {"title": "How to write a great bug report:", "summary": {"label": "Summary: ", "text": "One-sentence overview of the problem."}, "steps": {"label": "Steps to reproduce: ", "text": "List exactly what you did, in order."}, "expectation": {"label": "Expected vs. actual: ", "text": "What did you expect? What happened instead?"}, "context": {"label": "Context: ", "text": "Name the page/feature, device, OS/browser."}, "evidence": {"label": "Evidence: ", "text": "Paste error messages or attach screenshots."}}, "user": {"title": "When reporting another user, please include:", "username": {"label": "Username or email: ", "text": "Who did this?"}, "behavior": {"label": "Behaviour details: ", "text": "Describe exactly what they said or did."}, "time": {"label": "When & where: ", "text": "Date, time, and location (URL or channel)."}, "examples": {"label": "Examples: ", "text": "Copy/paste chat snippets or attach screenshots."}, "impact": {"label": "Impact: ", "text": "How did this make you or others feel?"}}, "content": {"title": "To flag content that violates our guidelines:", "link": {"label": "Content link or screenshot: ", "text": "Show us exactly what you’re reporting."}, "reason": {"label": "Reason: ", "text": "Why is this content inappropriate (e.g. hate speech, explicit, spam)?"}, "location": {"label": "Location: ", "text": "Post URL, message thread, or page where you found it."}, "time": {"label": "Date/time: ", "text": "When did you encounter it?"}, "context": {"label": "Additional context: ", "text": "Any background that helps us assess severity."}}}, "required": "Please describe the issue — this field is required", "placeholder": "Briefly explain what happened or what you're reporting...", "maxLength": "Description must be at most 200 characters"}, "attachments": {"label": "2. Upload evidence (Optional)"}, "buttons": {"submit": "Submit Report", "back": "Back"}}, "success": {"title": "Report submitted successfully", "description": "Thank you for your report. We will be in touch with you shortly.", "buttons": {"home": "Back To Main Page"}}}}, "notifications": {"success": {"settingsUpdated": "Settings updated successfully", "classroomDeleted": "Classroom deleted successfully", "classroomUpdated": "Classroom updated successfully", "classroomCreated": "Classroom created successfully", "linkCopied": "Invite link copied to clipboard", "joined": "{{displayName}} joined the class", "left": "{{displayName}} left the class", "requestPasswordReset": "We have sent you an email with instructions to reset your password.", "passwordChanged": "Your password has been successfully changed.", "participantUpdated": "Participant updated successfully.", "participantRemoved": "Participant removed successfully.", "materialDeleted": "Material deleted successfully.", "materialUploaded": "Material uploaded successfully.", "userDeleted": {"title": "Your account has been successfully deleted.", "description": "Your account and all associated personal data will be permanently deleted within three days. If you wish to restore your account, you may do so within this period by simply logging in with your email address and password. Doing so will automatically cancel the deletion process."}, "reportCreated": "Report created successfully."}, "errors": {"serverError": {"title": "Oops...", "description": "It seems like something went wrong on our end.<br />Please try again later."}, "classroomNotFound": {"title": "Sorry", "description": "This classroom was deleted."}, "expiredInviteLink": {"title": "Invite <PERSON> Expired", "description": "Your invitation link is invalid or has expired. Please request a new invitation."}, "expiredPasswordToken": {"title": "Password Reset Link Expired", "description": "The password reset link you used has expired. Please request a new one."}, "materialNotFound": {"title": "Material Not Found", "description": "The requested material could not be found."}, "wrongConfirmation": {"title": "Wrong Confirmation", "description": "The confirmation you entered is incorrect. Please, enter correct one."}}, "popup": {"close": "Close"}}}