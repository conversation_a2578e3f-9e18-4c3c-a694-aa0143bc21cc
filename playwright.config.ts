import { defineConfig, devices } from '@playwright/test';

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
// import dotenv from 'dotenv';
// import path from 'path';
// dotenv.config({ path: path.resolve(__dirname, '.env') });

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
  testDir: './tests',
  /* Run all tests sequentially */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 1 : 0,
  /* Use a single worker to ensure sequential execution across all projects */
  workers: process.env.CI ? 1 : 1,

  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: 'html',

  /* Timeout for each test */
  timeout: process.env.CI ? 120_000 : 90_000,
  expect: { timeout: process.env.CI ? 15_000 : 10_000 },

  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'https://localhost:5173',

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',

    /* Take screenshot on failure */
    screenshot: process.env.CI ? 'off' : 'only-on-failure',

    /* Record video on failure */
    video: process.env.CI ? 'off' : 'retain-on-failure',

    /* Ignore HTTPS certificate errors for local development */
    ignoreHTTPSErrors: true,

    /* Optional: give actions extra room only on CI */
    actionTimeout: process.env.CI ? 8_000 : 0,
    navigationTimeout: process.env.CI ? 30_000 : 0,
  },

  /* Configure projects for test execution and browsers */
  projects: [
    {
      name: 'class-tests',
      testMatch: '**/class.test.ts',
      testIgnore: ['**/class2.test.ts'],
      fullyParallel: false,
      workers: 1,
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--enable-features=VaapiVideoDecoder',
            '--use-gl=egl',
            '--enable-accelerated-video-decode',
            '--disable-background-timer-throttling',
            '--autoplay-policy=no-user-gesture-required',
            '--disable-web-security',
            '--use-fake-ui-for-media-stream',
            '--use-fake-device-for-media-stream',
            '--allow-running-insecure-content',
            // '--disable-features=VizDisplayCompositor',
            // '--enable-webrtc-srtp-aes-gcm',
            // '--force-webrtc-ip-handling-policy=default',
            // '--disable-webrtc-encryption',
            // '--enable-webrtc-stun-origin',
            // '--enable-logging=stderr',
            // '--log-level=0',
          ],
        },
      },
    },

    {
      name: 'class2-tests',
      testMatch: '**/class2.test.ts',
      testIgnore: ['**/class.test.ts'],
      fullyParallel: false,
      workers: 1,
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--enable-features=VaapiVideoDecoder',
            '--use-gl=egl',
            '--enable-accelerated-video-decode',
            '--disable-background-timer-throttling',
            '--autoplay-policy=no-user-gesture-required',
            '--disable-web-security',
          ],
        },
      },
    },
         {
       name: 'email-tests',
       testMatch: 'tests/emailTests/**/*.test.ts',  // Matches all .test.ts files in emailTests folder
       fullyParallel: false,
       workers: 1, // Email tests often need sequential execution
       use: {
         ...devices['Desktop Chrome'],
         launchOptions: {
           args: [
             '--enable-features=VaapiVideoDecoder',
             '--use-gl=egl',
             '--enable-accelerated-video-decode',
             '--disable-background-timer-throttling',
             '--autoplay-policy=no-user-gesture-required',
             '--disable-web-security',
           ],
         },
       },
     },
     {
      name: 'other-tests-1',
      testMatch: 'tests/other-tests-1/**/*.test.ts',  // Matches all .test.ts files in emailTests folder
      fullyParallel: false,
      workers: 1, // Other tests often need sequential execution
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--enable-features=VaapiVideoDecoder',
            '--use-gl=egl',
            '--enable-accelerated-video-decode',
            '--disable-background-timer-throttling',
            '--autoplay-policy=no-user-gesture-required',
            '--disable-web-security',
          ],
        },
      },
    },
     {
      name: 'other-tests-2',
      testMatch: 'tests/other-tests-2/**/*.test.ts',  // Matches all .test.ts files in emailTests folder
      testIgnore: ['**/class.test.ts', '**/class2.test.ts', '**/emailTests/**'],
      fullyParallel: false,
      workers: 1, // Other tests often need sequential execution
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--enable-features=VaapiVideoDecoder',
            '--use-gl=egl',
            '--enable-accelerated-video-decode',
            '--disable-background-timer-throttling',
            '--autoplay-policy=no-user-gesture-required',
            '--disable-web-security',
          ],
        },
      },
    },
    // Cross-browser testing projects (test branch only)
    {
      name: 'chromium-mobile-email',
      testMatch: '**/emailTests/**/*.test.ts',
      fullyParallel: false,
      workers: 1,
      use: {
        ...devices['Galaxy S9+'],
      },
    },
    {
      name: 'chromium-mobile-other',
      testIgnore: ['**/class.test.ts', '**/class2.test.ts', '**/emailTests/**'],
      fullyParallel: false,
      workers: 1,
      use: {
        ...devices['Galaxy S9+'],
      },
    },
    {
      name: 'safari-desktop-email',
      testMatch: '**/emailTests/**/*.test.ts',
      fullyParallel: false,
      workers: 1,
      use: {
        ...devices['Desktop Safari'],
      },
    },
    {
      name: 'safari-desktop-other',
      testIgnore: ['**/class.test.ts', '**/class2.test.ts', '**/emailTests/**'],
      fullyParallel: false,
      workers: 1,
      use: {
        ...devices['Desktop Safari'],
      },
    },
    {
      name: 'safari-mobile-email',
      testMatch: '**/emailTests/**/*.test.ts',
      fullyParallel: false,
      workers: 1,
      use: {
        ...devices['iPhone 13'],
      },
    },
    {
      name: 'safari-mobile-other',
      testIgnore: ['**/class.test.ts', '**/class2.test.ts', '**/emailTests/**'],
      fullyParallel: false,
      workers: 1,
      use: {
        ...devices['iPhone 13'],
      },
    },
    {
      name: 'firefox-desktop-email',
      testMatch: '**/emailTests/**/*.test.ts',
      fullyParallel: false,
      workers: 1,
      use: {
        ...devices['Desktop Firefox'],
      },
    },
    {
      name: 'firefox-desktop-other',
      testIgnore: ['**/class.test.ts', '**/class2.test.ts', '**/emailTests/**'],
      fullyParallel: false,
      workers: 1,
      use: {
        ...devices['Desktop Firefox'],
      },
    },
    {
      name: 'firefox-mobile-email',
      testMatch: '**/emailTests/**/*.test.ts',
      fullyParallel: false,
      workers: 1,
      use: {
        ...devices['Mobile Firefox'],
      },
    },
    {
      name: 'firefox-mobile-other',
      testIgnore: ['**/class.test.ts', '**/class2.test.ts', '**/emailTests/**'],
      fullyParallel: false,
      workers: 1,
      use: {
        ...devices['Mobile Firefox'],
      },
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'npm run dev',
    url: 'https://localhost:5173/',
    reuseExistingServer: !process.env.CI,
    timeout: 30000,
    ignoreHTTPSErrors: true,
    stdout: 'pipe',
    stderr: 'pipe',
    env: {
      VITE_PLAYWRIGHT: 'true',
      VITE_GA_ID: 'G-23FW48R3QH',
    },
  },
});