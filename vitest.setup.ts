// vitest.setup.ts
// Pull in Jest DOM matchers so you can use
// toBeInTheDocument(), toHaveAttribute(), etc.
import '@testing-library/jest-dom';
import { vi } from 'vitest';

if (typeof globalThis.DOMMatrix === 'undefined') {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (globalThis as any).DOMMatrix = class {
    a = 1;
    b = 0;
    c = 0;
    d = 1;
    e = 0;
    f = 0;

    scale() {
      return this;
    }
    translate() {
      return this;
    }
    multiply() {
      return this;
    }
    inverse() {
      return this;
    }
  };
}

/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types */
class MockAudioNode {
  connect() {
    return this;
  }
  disconnect() {}
}

class MockGainNode extends MockAudioNode {
  gain = {
    value: 1,
    setValueAtTime: vi.fn(),
    linearRampToValueAtTime: vi.fn(),
  };
}
(globalThis as any).GainNode = class extends MockGainNode {};

class MockAnalyserNode extends MockAudioNode {
  fftSize = 0;
  smoothingTimeConstant = 0;
  frequencyBinCount = 1;
  getByteFrequencyData = vi.fn();
  getByteTimeDomainData = vi.fn();
}
class MockStereoPannerNode extends MockAudioNode {
  pan = { value: 0, setValueAtTime: vi.fn() };
}
class MockDynamicsCompressorNode extends MockAudioNode {
  threshold = { value: 0 };
  knee = { value: 0 };
  ratio = { value: 0 };
  attack = { value: 0 };
  release = { value: 0 };
}
class MockMediaStreamAudioSourceNode extends MockAudioNode {}

class MockChannelSplitterNode extends MockAudioNode {
  connect = vi.fn();
  disconnect = vi.fn();
}
class MockChannelMergerNode extends MockAudioNode {
  connect = vi.fn();
  disconnect = vi.fn();
}

class MockAudioContext {
  state = 'running';
  currentTime = 0;
  sampleRate = 48000;
  destination = new MockAudioNode();
  resume = vi.fn().mockResolvedValue(undefined);
  close = vi.fn().mockResolvedValue(undefined);
  createGain = () => new MockGainNode();
  createAnalyser = () => new MockAnalyserNode();
  createStereoPanner = () => new MockStereoPannerNode();
  createDynamicsCompressor = () => new MockDynamicsCompressorNode();
  createMediaElementSource = () => new MockMediaStreamAudioSourceNode();
  createMediaStreamDestination = () => ({
    stream: new MediaStream(),
    connect: vi.fn(),
  });

  // ✅ Add this method to mock fallback strategy support
  createMediaStreamSource = (_stream: MediaStream) =>
    new MockMediaStreamAudioSourceNode();
  createChannelSplitter = (_: number) => new MockChannelSplitterNode();
  createChannelMerger = (_: number) => new MockChannelMergerNode();
}

globalThis.AudioContext = MockAudioContext as any;
globalThis.MediaStream = class MediaStream {
  getTracks() {
    return [];
  }
} as any;
globalThis.Audio = class {
  srcObject: any = null;
  muted = false;
  autoplay = false;
  play = vi.fn().mockResolvedValue(undefined);
} as any;
(globalThis as any).AnalyserNode = MockAnalyserNode;
