const fs = require('fs');
const path = require('path');

const original = JSON.parse(fs.readFileSync(path.resolve(__dirname, 'translation.json'), 'utf8'));
const update = JSON.parse(fs.readFileSync(path.resolve(__dirname, 'update.json'), 'utf8'));

const notUpdated = [];

function updateI18n(originalObj, updateObj, pathSoFar = []) {
  const result = Array.isArray(originalObj) ? [] : {};

  for (const key in originalObj) {
    const fullPath = [...pathSoFar, key];

    if (
      updateObj &&
      typeof updateObj === 'object' &&
      key in updateObj
    ) {
      if (
        typeof originalObj[key] === 'object' &&
        typeof updateObj[key] === 'object'
      ) {
        // Recurse
        result[key] = updateI18n(originalObj[key], updateObj[key], fullPath);
      } else {
        // Update value
        result[key] = updateObj[key];
      }
    } else {
      // Keep original and mark as not updated
      result[key] = originalObj[key];
      notUpdated.push(fullPath.join('.'));
    }
  }

  return result;
}

const merged = updateI18n(original, update);

fs.writeFileSync(
  path.resolve(__dirname, 'merged.json'),
  JSON.stringify(merged, null, 2),
  'utf8'
);

console.log('\n⚠️ Keys not updated (missing in update.json):');
notUpdated.forEach(k => console.log(`- ${k}`));
