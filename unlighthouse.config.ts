import 'unlighthouse';
import { defineConfig } from 'unlighthouse';

let cachedToken: { token: string; refreshToken: string } | null = null;

const { VH_TEST_TOKEN, VH_CLASSROOM_ID } = process.env as Record<string, string | undefined>;

async function sleep(ms: number) {
  return new Promise((res) => setTimeout(res, ms));
}

async function fetchWithRetry(input: string, init: RequestInit, retries = 2, backoffMs = 500): Promise<Response> {
  let lastErr: unknown;
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const res = await fetch(input, init);
      if (res.ok) return res;
      // Retry on 5xx
      if (res.status >= 500 && res.status < 600 && attempt < retries) {
        await sleep(backoffMs * (attempt + 1));
        continue;
      }
      return res;
    } catch (e) {
      lastErr = e;
      if (attempt < retries) {
        await sleep(backoffMs * (attempt + 1));
        continue;
      }
      throw lastErr;
    }
  }
  // Should never reach
  throw lastErr as Error;
}

/**
 * Fetch a JWT once, prefer test token from env, then anonymous login, then register.
 */
async function getAnonTokens(): Promise<{ token: string; refreshToken: string }> {
  if (cachedToken) return cachedToken;

  // Prefer explicit test token from env for stability in CI
  if (VH_TEST_TOKEN) {
    cachedToken = { token: VH_TEST_TOKEN, refreshToken: VH_TEST_TOKEN };
    return cachedToken;
  }

  // Try login anonymous first
  const loginRes = await fetch('https://dev.backend.virtuosohub.ai/api/v1/auth/login/anonymous', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      nickname: 'cibot',
      userId: '68305edc577cbe30d01b5fc4',
    }),
  });

  if (loginRes.ok) {
    const responseData = await loginRes.json();
    console.log('Anonymous login response:', JSON.stringify(responseData));
    const token = responseData?.data?.token ?? responseData?.token;
    const refreshToken = responseData?.data?.refreshToken ?? responseData?.refreshToken;
    if (token && refreshToken) {
      cachedToken = { token, refreshToken };
      return cachedToken;
    }
  }

  // Fallback to register anonymous
  const res = await fetch(
    'https://dev.backend.virtuosohub.ai/api/v1/auth/register/anonymous',
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        nickname: 'cibot',
        userId: '68305edc577cbe30d01b5fc4',
      }),
    },
  );

  if (!res.ok) {
    const errorText = await res.text();
    console.error('Anonymous register failed:', res.status, res.statusText, errorText);
    throw new Error(`Anonymous auth failed: ${res.status}`);
  }

  const responseData = await res.json();
  console.log('Anonymous register response:', JSON.stringify(responseData));

  const token = responseData?.data?.token ?? responseData?.token;
  const refreshToken = responseData?.data?.refreshToken ?? responseData?.refreshToken;

  if (!token || !refreshToken) {
    console.error('Extracted tokens - access:', token, 'refresh:', refreshToken);
    throw new Error('Failed to extract tokens from anonymous auth response');
  }

  console.log('Extracted tokens - access: present refresh: present');
  cachedToken = { token, refreshToken };
  return cachedToken;
}

function getAuthHeaderToken(tokenFromAnon: string): string {
  return VH_TEST_TOKEN || tokenFromAnon;
}

async function createClassroomIfNeeded(authToken: string): Promise<string | null> {
  try {
    console.log('No VH_CLASSROOM_ID provided. Attempting to create a temporary classroom...');
    const apiUrl = 'https://dev.backend.virtuosohub.ai/api/v1/classrooms';
    const payload = {
      title: 'Unlighthouse Temp Classroom',
      description: 'Temporary classroom for Unlighthouse scanning',
      settings: {
        defaultLanguage: 'en',
        translationLanguages: ['en'],
        enableTranscription: false,
        enableTranslation: false,
        enableRecording: false,
        maxParties: 2,
        maxWatchers: 5,
        egressLayout: 'grid',
        canAnonymouslyJoin: true,
      },
    };

    const res = await fetchWithRetry(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(payload),
    }, 1, 600);

    console.log(`Create classroom response: ${res.status} ${res.statusText}`);

    if (!res.ok) {
      const errorText = await res.text();
      console.warn('Failed to create classroom for Unlighthouse:', errorText);
      return null;
    }

    const body = await res.json();
    const id = body?.data?.id || body?.id || body?.data?.data?.id || null;
    console.log('Temporary classroom created with id:', id);
    return id;
  } catch (e) {
    console.warn('Error creating temporary classroom:', e);
    return null;
  }
}

async function createClassForScanning(authToken: string, classroomId: string): Promise<string | null> {
  try {
    console.log('Attempting to create a class for Unlighthouse scanning...');
    const apiUrl = `https://dev.backend.virtuosohub.ai/api/v1/classrooms/${classroomId}/classes`;
    const payload = { name: 'Unlighthouse Scan Class' };

    const res = await fetchWithRetry(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(payload),
    }, 1, 500);

    console.log(`Create class response: ${res.status} ${res.statusText}`);

    if (!res.ok) {
      const errorText = await res.text();
      console.warn('Failed to create class for Unlighthouse:', errorText);
      return null;
    }

    const body = await res.json();
    const classId = body?.data?.id || body?.id || body?.data?.data?.id || null;
    console.log('Class created for scanning with id:', classId);
    return classId;
  } catch (e) {
    console.warn('Error creating class for scanning:', e);
    return null;
  }
}

/**
 * Create invite links for Lighthouse testing
 */
async function createInviteLinks(): Promise<string[]> {
  const classroomIdFallback = '68306424577cbe30d01b5fc5';
  const inviteLinks: string[] = [];

  try {
    console.log('=== PREPARING CLASSROOM AND CLASS URLS FOR LIGHTHOUSE ===');

    // Resolve or create a classroom id
    const tokens = await getAnonTokens();
    const authToken = getAuthHeaderToken(tokens.token);
    let resolvedClassroomId = VH_CLASSROOM_ID || null as string | null;

    if (!resolvedClassroomId) {
      resolvedClassroomId = await createClassroomIfNeeded(authToken);
    }

    if (!resolvedClassroomId) {
      console.warn('Could not resolve or create classroom id, using fallback.');
      resolvedClassroomId = classroomIdFallback;
    }

    // Add classroom URL
    const classroomPath = `/classrooms/${resolvedClassroomId}`;
    console.log('Adding CLASSROOM page URL to test list:', classroomPath);
    inviteLinks.push(classroomPath);

    // Create a class and add class page URL
    const classId = await createClassForScanning(authToken, resolvedClassroomId);
    if (classId) {
      const classPath = `/classrooms/${resolvedClassroomId}/class/${classId}`;
      console.log('Adding CLASS page URL to test list:', classPath);
      inviteLinks.push(classPath);
    } else {
      console.warn('Could not create a class for scanning; skipping class page URL');
    }

    console.log('Final URLs for Lighthouse:', inviteLinks);
    console.log('=== URL PREPARATION COMPLETED ===');

  } catch (error) {
    console.error('=== URL PREPARATION FAILED ===');
    console.error('Error details:', error);
    console.warn('Falling back to basic classroom URL only');
    inviteLinks.push(`/classrooms/${classroomIdFallback}`);
  }

  console.log('Returning URLs array:', inviteLinks);
  return inviteLinks;
}

// Create invite links at module initialization
const inviteLinksPromise = createInviteLinks();

// Helper function to create the config with invite links
async function createConfig() {
  console.log('=== CREATING UNLIGHTHOUSE CONFIG ===');

  const inviteLinks = await inviteLinksPromise;
  console.log('Invite links received for config:', inviteLinks);

  const staticUrls = [
    '/',
    '/classrooms',
    '/profile',
    '/legal/privacy-policy',
    '/legal/terms-and-conditions',
    '/legal/code-of-conduct',
  ];

  const allUrls = [...staticUrls, ...inviteLinks];

  console.log('Final URLs that Lighthouse will audit:');
  allUrls.forEach((url, index) => {
    console.log(`  ${index + 1}. ${url}`);
    console.log(`     Length: ${url.length} characters`);
    if (url.length > 500) {
      console.warn(`     ⚠️  URL is quite long (${url.length} chars)`);
    }
    if (url.length > 1000) {
      console.error(`     ❌ URL may be too long for Unlighthouse (${url.length} chars)`);
    }
  });

  console.log('\n=== URL ANALYSIS ===');
  console.log(`Total URLs: ${allUrls.length}`);
  console.log(`Static URLs: ${staticUrls.length}`);
  console.log(`Invite-generated URLs: ${inviteLinks.length}`);
  console.log('Unique URLs check:', new Set(allUrls).size === allUrls.length ? 'All unique' : 'Contains duplicates');

  if (new Set(allUrls).size !== allUrls.length) {
    console.warn('Duplicate URLs detected:');
    const seen = new Set<string>();
    const duplicates: string[] = [];
    allUrls.forEach(url => {
      if (seen.has(url)) {
        duplicates.push(url);
      } else {
        seen.add(url);
      }
    });
    duplicates.forEach(dup => console.warn(`  - ${dup}`));
  }

  console.log('=== CONFIG CREATION COMPLETE ===');

  return defineConfig({
    site: 'http://localhost:4173',
    root: './',
    urls: allUrls,
    // Fix outputPath for GitHub Actions - use current directory instead of parent
    outputPath: './.unlighthouse',
    debug: true,

    lighthouseOptions: {
      // Enable debug screenshots and detailed logging
      onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
      screenEmulation: {
        width: 1350,
        height: 940,
        disabled: false
      },
      // Keep screenshot thumbnails for debugging
      skipAudits: [], // Don't skip screenshot audits
      // Remove throttling for realistic CI performance scores
      throttlingMethod: 'provided',
      // Optimize for CI environment
      onlyAudits: null, // Don't limit audits
      // Disable CPU throttling for more realistic scores in CI
      throttling: {
        rttMs: 0,
        throughputKbps: 0,
        cpuSlowdownMultiplier: 1,
        requestLatencyMs: 0,
        downloadThroughputKbps: 0,
        uploadThroughputKbps: 0
      }
    },

    puppeteerOptions: {
      args: [
        // Essential flags only for better performance
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',

        // Media device simulation (minimal set)
        '--use-fake-ui-for-media-stream',
        '--use-fake-device-for-media-stream',
        '--autoplay-policy=no-user-gesture-required',

        // Performance optimizations
        '--disable-gpu',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
      ],
      // Ensure each browser instance is completely isolated
      defaultViewport: null,
      headless: true,
    },

    hooks: {
      /**
       * Runs before each page navigation. Sets authentication via headers and cookies.
       */
      'puppeteer:before-goto': async (page) => {
        try {
          console.log(`Setting up auth for: ${page.url()}`);

          // Set permissions first
          const context = page.browserContext();
          await context.overridePermissions('http://localhost:4173', [
            'camera', 'microphone'
          ]);

          // Check if this is the homepage - don't set auth tokens there
          const url = new URL(page.url());
          const isHomepage = url.pathname === '/' && !url.search;

          if (isHomepage) {
            console.log('Homepage detected - skipping authentication (to avoid redirect to /classrooms)');
            return;
          }

          // Get full auth response for protected pages
          const authData = await getAnonTokens();
          if (authData) {
            console.log('Setting authentication via correct cookies for protected page...');

            // Set the correct authentication cookies (matching app expectations)
            try {
              await page.setCookie({
                name: 'token',
                value: authData.token,
                domain: 'localhost',
                path: '/',
                httpOnly: false,
                secure: false, // localhost can't use secure
                sameSite: 'Lax'
              });

              // Set refreshToken from the auth response
              await page.setCookie({
                name: 'refreshToken',
                value: authData.refreshToken,
                domain: 'localhost',
                path: '/',
                httpOnly: false,
                secure: false,
                sameSite: 'Lax'
              });
            } catch (cookieError) {
              console.warn('Cookie setting failed (target may be closing):', (cookieError as Error).message);
            }

            console.log('Authentication cookies set correctly for protected page');
          } else {
            console.warn('No token available for authentication');
          }
        } catch (error) {
          console.warn('Error in puppeteer:before-goto:', error);
        }
      },

      /**
       * Runs after all scanning is complete to ensure clean exit
       */
      'worker-finished': async () => {
        console.log('🎉 All Lighthouse scans completed successfully!');
        console.log('Cleaning up and exiting process...');

        // Give a small delay for cleanup, then force exit
        setTimeout(() => {
          console.log('Force exiting process...');
          process.exit(0);
        }, 2000);
      },
    },

    scanner: {
      device: 'desktop',
      throttle: false, // Disable scanner throttling for realistic CI performance
      // Control concurrency to prevent resource conflicts
      maxRoutes: 10,
      // Single sample is enough for CI
      samples: 1,
      // Ensure proper mobile testing
      skipJavascript: false,
    },
    ci: {
      buildStatic: true, // Generate static HTML reports in CI and exit cleanly
    },
  });
}

// Export the config with invite links
export default createConfig();
