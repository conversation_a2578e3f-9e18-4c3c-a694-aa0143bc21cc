import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';
import mkcert from 'vite-plugin-mkcert';
import path from 'path';

export default defineConfig(({ mode }) => {
  const isTest = mode === 'test' || process.env.VITEST;

  return {
    plugins: [react(), tailwindcss(), !isTest && mkcert()].filter(Boolean),
    resolve: {
      alias: { '@': path.resolve(__dirname, './src') },
    },
    build: {
      sourcemap: true,
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './vitest.setup.ts',
      include: ['src/**/*.{test,spec}.{ts,tsx,js,jsx}'],
    },
  };
});
