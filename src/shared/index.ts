export {
  LANGUAGES,
  SERVER_URL,
  IMSLP_PROXY_URL,
  ZENDESK_RPOXY_URL,
} from './constants';

export { api, retry } from './api';

export {
  useIsAppleDevice,
  useUpdateReduxState,
  useAppDispatch,
  useAppSelector,
  usePlatform,
  useFullscreen,
  useClickOutside,
  useMediaQuery,
} from './hooks';

export type { DeepPartial, Consents, ClassLoggerMessage } from './types';
export { Role } from './types';

export {
  getInitials,
  getFileSize,
  formatFileSize,
  getFileSizeFromURL,
  getClientData,
  isIOSDevice,
  isMobile,
  isLowEndDevice,
  getProviderLink,
  calculatePasswordStrength,
  PASSWORD_MIN_LENGTH,
  PASSWORD_PATTERN,
  safePlay,
} from './utils';
export type { PasswordMetrics } from './utils';

export {
  Loader,
  LoaderPage,
  Notifications,
  ConsentCheckboxList,
  Sidebar,
  TriggerButton,
  AccordionItem,
  Switch,
  Select,
} from './ui';

export {
  initAnalytics,
  userIdListener,
  initListeners,
  updateConsent,
  logger,
  useNotifications,
  addNotification,
  removeNotification,
  notificationsReducer,
  ACLProvider,
  useACL,
  useACLContext,
  ClassroomSettingsProvider,
  useClassroomSettings,
  useClassroomSettingsContext,
} from './lib';
