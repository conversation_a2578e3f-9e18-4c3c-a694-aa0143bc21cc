import { createContext, FC, PropsWithChildren, useState } from 'react';

type ACL = {
  canCommentOnRecordings: boolean;
  canDownloadMaterials: boolean;
  canGuestInvite: boolean;
  canKickParticipant: boolean;
  canParticipantInvite: boolean;
  canStartClass: boolean;
  canUpdateClass: boolean;
  canUpdateParticipant: boolean;
  canUploadMaterials: boolean;
  canViewRecordings: boolean;
};

export interface ACLContextValue {
  acl: Record<string, ACL>;
  updateACL: (classroomId: string, value: ACL) => void;
}

export const ACLContext = createContext<ACLContextValue | undefined>(undefined);

export const ACLProvider: FC<PropsWithChildren> = ({ children }) => {
  const [acl, setAcl] = useState<Record<string, ACL>>({});

  const updateACL = (classroomId: string, value: ACL) => {
    setAcl((prev) => ({ ...prev, [classroomId]: value }));
  };

  return (
    <ACLContext.Provider
      value={{
        acl,
        updateACL,
      }}
    >
      {children}
    </ACLContext.Provider>
  );
};
