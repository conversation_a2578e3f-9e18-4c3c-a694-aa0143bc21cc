import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import ReactGA from 'react-ga4';

// Mock ReactGA
vi.mock('react-ga4', () => ({
  default: {
    gtag: vi.fn(),
  },
}));

// Mock buttonTracker and errorTracker
vi.mock('../buttonTracker', () => ({
  startButtonTracker: vi.fn(),
}));

vi.mock('../errorTracker', () => ({
  startErrorTracker: vi.fn(),
}));

// Mock environment variables
beforeEach(() => {
  vi.stubEnv('PROD', true);
});

afterEach(() => {
  vi.unstubAllEnvs();
});

// Mock localStorage
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: vi.fn(),
    setItem: vi.fn(),
  },
  writable: true,
});

describe('updateConsent', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should update consent to granted in production', async () => {
    const mockLocalStorage = {
      getItem: vi.fn(),
      setItem: vi.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    const { updateConsent } = await import('../updateConsent');

    updateConsent(true);

    expect(ReactGA.gtag).toHaveBeenCalledWith('consent', 'update', {
      analytics_storage: 'granted',
      security_storage: 'granted',
    });
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('userConsent', 'true');
  });

  it('should update consent to denied in production', async () => {
    const mockLocalStorage = {
      getItem: vi.fn(),
      setItem: vi.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    const { updateConsent } = await import('../updateConsent');

    updateConsent(false);

    expect(ReactGA.gtag).toHaveBeenCalledWith('consent', 'update', {
      analytics_storage: 'denied',
      security_storage: 'denied',
    });
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('userConsent', 'false');
  });

  it('should not update consent in non-production environment', async () => {
    // Temporarily mock PROD as false
    vi.stubEnv('PROD', false);

    const mockLocalStorage = {
      getItem: vi.fn(),
      setItem: vi.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    const { updateConsent } = await import('../updateConsent');

    updateConsent(true);

    expect(ReactGA.gtag).not.toHaveBeenCalled();
    expect(mockLocalStorage.setItem).not.toHaveBeenCalled();
  });
});

describe('initListeners', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should start trackers when consent is true in production', async () => {
    const mockLocalStorage = {
      getItem: vi.fn().mockReturnValue('true'),
      setItem: vi.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    const { initListeners } = await import('../updateConsent');
    const { startButtonTracker } = await import('../buttonTracker');
    const { startErrorTracker } = await import('../errorTracker');

    initListeners();

    expect(startButtonTracker).toHaveBeenCalled();
    expect(startErrorTracker).toHaveBeenCalled();
  });

  it('should not start trackers when consent is false in production', async () => {
    const mockLocalStorage = {
      getItem: vi.fn().mockReturnValue('false'),
      setItem: vi.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    const { initListeners } = await import('../updateConsent');
    const { startButtonTracker } = await import('../buttonTracker');
    const { startErrorTracker } = await import('../errorTracker');

    initListeners();

    expect(startButtonTracker).not.toHaveBeenCalled();
    expect(startErrorTracker).not.toHaveBeenCalled();
  });

  it('should not start trackers when consent is null in production', async () => {
    const mockLocalStorage = {
      getItem: vi.fn().mockReturnValue(null),
      setItem: vi.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    const { initListeners } = await import('../updateConsent');
    const { startButtonTracker } = await import('../buttonTracker');
    const { startErrorTracker } = await import('../errorTracker');

    initListeners();

    expect(startButtonTracker).not.toHaveBeenCalled();
    expect(startErrorTracker).not.toHaveBeenCalled();
  });

  it('should not start trackers in non-production environment', async () => {
    // Temporarily mock PROD as false
    vi.stubEnv('PROD', false);

    const mockLocalStorage = {
      getItem: vi.fn().mockReturnValue('true'),
      setItem: vi.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    const { initListeners } = await import('../updateConsent');
    const { startButtonTracker } = await import('../buttonTracker');
    const { startErrorTracker } = await import('../errorTracker');

    initListeners();

    expect(startButtonTracker).not.toHaveBeenCalled();
    expect(startErrorTracker).not.toHaveBeenCalled();
  });
}); 