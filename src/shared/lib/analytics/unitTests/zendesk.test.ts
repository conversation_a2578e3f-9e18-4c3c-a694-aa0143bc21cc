import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ZENDESK_RPOXY_URL } from '@/shared/constants/server';

// Mock fetch globally with proper typing
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock server constants
vi.mock('@/shared/constants/server', () => ({
  ZENDESK_RPOXY_URL: 'https://vh-zendesk-632260335818.us-central1.run.app',
}));

describe('Zendesk Backend API', () => {
  const baseUrl = ZENDESK_RPOXY_URL;
  const apiUrl = `${baseUrl}/api/v1`;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Health Check Endpoint', () => {
    it('should return healthy status', async () => {
      // Arrange
      const mockResponse = { status: 'healthy' };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockResponse,
      });

      // Act
      const response = await fetch(`${apiUrl}/health`);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(`${apiUrl}/health`);
    });

    it('should handle health check errors gracefully', async () => {
      // Arrange
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      // Act & Assert
      await expect(fetch(`${apiUrl}/health`)).rejects.toThrow('Network error');
    });
  });

  describe('Ticket Management', () => {
    const mockTicket = {
      subject: 'Test Ticket',
      description: 'This is a test ticket',
      priority: 'medium',
      requester_id: 12345,
    };

    const mockTicketResponse = {
      id: 'TICKET-001',
      subject: 'Test Ticket',
      status: 'open',
      created_at: '2024-01-01T00:00:00Z',
    };

    describe('Create Ticket', () => {
      it('should create a new ticket successfully', async () => {
        // Arrange
        mockFetch.mockResolvedValueOnce({
          ok: true,
          status: 201,
          json: async () => mockTicketResponse,
        });

        // Act
        const response = await fetch(`${apiUrl}/tickets`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token',
          },
          body: JSON.stringify(mockTicket),
        });
        const data = await response.json();

        // Assert
        expect(response.status).toBe(201);
        expect(data).toEqual(mockTicketResponse);
        expect(mockFetch).toHaveBeenCalledWith(`${apiUrl}/tickets`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token',
          },
          body: JSON.stringify(mockTicket),
        });
      });

      it('should handle ticket creation validation errors', async () => {
        // Arrange
        const validationError = {
          error: 'Validation failed',
          details: ['Subject is required', 'Description is required'],
        };
        mockFetch.mockResolvedValueOnce({
          ok: false,
          status: 400,
          json: async () => validationError,
        });

        // Act
        const response = await fetch(`${apiUrl}/tickets`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({}),
        });
        const data = await response.json();

        // Assert
        expect(response.status).toBe(400);
        expect(data).toEqual(validationError);
      });

      it('should handle unauthorized access', async () => {
        // Arrange
        mockFetch.mockResolvedValueOnce({
          ok: false,
          status: 401,
          json: async () => ({ error: 'Unauthorized' }),
        });

        // Act
        const response = await fetch(`${apiUrl}/tickets`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(mockTicket),
        });

        // Assert
        expect(response.status).toBe(401);
      });
    });

    describe('Get Ticket', () => {
      it('should retrieve a ticket by ID successfully', async () => {
        // Arrange
        const ticketId = 'TICKET-001';
        mockFetch.mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => mockTicketResponse,
        });

        // Act
        const response = await fetch(`${apiUrl}/tickets/${ticketId}`, {
          method: 'GET',
          headers: { 'Authorization': 'Bearer test-token' },
        });
        const data = await response.json();

        // Assert
        expect(response.status).toBe(200);
        expect(data).toEqual(mockTicketResponse);
        expect(mockFetch).toHaveBeenCalledWith(`${apiUrl}/tickets/${ticketId}`, {
          method: 'GET',
          headers: { 'Authorization': 'Bearer test-token' },
        });
      });

      it('should handle ticket not found', async () => {
        // Arrange
        const ticketId = 'NONEXISTENT';
        mockFetch.mockResolvedValueOnce({
          ok: false,
          status: 404,
          json: async () => ({ error: 'Ticket not found' }),
        });

        // Act
        const response = await fetch(`${apiUrl}/tickets/${ticketId}`, {
          method: 'GET',
        });

        // Assert
        expect(response.status).toBe(404);
      });
    });

    describe('Delete Ticket', () => {
      it('should delete a ticket successfully', async () => {
        // Arrange
        const ticketId = 'TICKET-001';
        mockFetch.mockResolvedValueOnce({
          ok: true,
          status: 204,
        });

        // Act
        const response = await fetch(`${apiUrl}/tickets/${ticketId}`, {
          method: 'DELETE',
          headers: { 'Authorization': 'Bearer test-token' },
        });

        // Assert
        expect(response.status).toBe(204);
        expect(mockFetch).toHaveBeenCalledWith(`${apiUrl}/tickets/${ticketId}`, {
          method: 'DELETE',
          headers: { 'Authorization': 'Bearer test-token' },
        });
      });

      it('should handle deletion of non-existent ticket', async () => {
        // Arrange
        const ticketId = 'NONEXISTENT';
        mockFetch.mockResolvedValueOnce({
          ok: false,
          status: 404,
          json: async () => ({ error: 'Ticket not found' }),
        });

        // Act
        const response = await fetch(`${apiUrl}/tickets/${ticketId}`, {
          method: 'DELETE',
        });

        // Assert
        expect(response.status).toBe(404);
      });
    });
  });

  describe('Report Management', () => {
    const mockReport = {
      report_type: 'bug',
      description: 'Critical bug found',
      severity: 'high',
      user_id: 12345,
    };

    it('should create a report ticket successfully', async () => {
      // Arrange
      const mockReportResponse = {
        id: 'REPORT-001',
        report_type: 'bug',
        status: 'open',
        created_at: '2024-01-01T00:00:00Z',
      };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: async () => mockReportResponse,
      });

      // Act
      const response = await fetch(`${apiUrl}/reports`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token',
        },
        body: JSON.stringify(mockReport),
      });
      const data = await response.json();

      // Assert
      expect(response.status).toBe(201);
      expect(data).toEqual(mockReportResponse);
    });
  });

  describe('Attachment Management', () => {
    it('should download attachment successfully', async () => {
      // Arrange
      const mockAttachmentData = new ArrayBuffer(1024);
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        arrayBuffer: async () => mockAttachmentData,
        headers: new Headers({
          'Content-Type': 'application/octet-stream',
          'Content-Disposition': 'attachment; filename="test.pdf"',
        }),
      });

      // Act
      const response = await fetch(`${apiUrl}/attachments/download?file_id=123`, {
        method: 'GET',
        headers: { 'Authorization': 'Bearer test-token' },
      });
      const data = await response.arrayBuffer();

      // Assert
      expect(response.status).toBe(200);
      expect(data).toEqual(mockAttachmentData);
      expect(response.headers.get('Content-Type')).toBe('application/octet-stream');
    });

    it('should handle attachment not found', async () => {
      // Arrange
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ error: 'Attachment not found' }),
      });

      // Act
      const response = await fetch(`${apiUrl}/attachments/download?file_id=999`, {
        method: 'GET',
      });

      // Assert
      expect(response.status).toBe(404);
    });
  });

  describe('CORS Handling', () => {
    it('should handle preflight OPTIONS request', async () => {
      // Arrange
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }),
      });

      // Act
      const response = await fetch(`${apiUrl}/tickets`, {
        method: 'OPTIONS',
        headers: {
          'Origin': 'https://localhost:3000',
          'Access-Control-Request-Method': 'POST',
          'Access-Control-Request-Headers': 'Content-Type, Authorization',
        },
      });

      // Assert
      expect(response.status).toBe(200);
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
    });

    it('should include CORS headers in responses', async () => {
      // Arrange
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ status: 'success' }),
        headers: new Headers({
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }),
      });

      // Act
      const response = await fetch(`${apiUrl}/health`);

      // Assert
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
      expect(response.headers.get('Access-Control-Allow-Methods')).toContain('POST');
    });
  });

  describe('Error Handling', () => {
    it('should handle server errors gracefully', async () => {
      // Arrange
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ error: 'Internal server error' }),
      });

      // Act
      const response = await fetch(`${apiUrl}/tickets`);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(500);
      expect(data.error).toBe('Internal server error');
    });

    it('should handle network timeouts', async () => {
      // Arrange
      mockFetch.mockRejectedValueOnce(new Error('Request timeout'));

      // Act & Assert
      await expect(fetch(`${apiUrl}/tickets`)).rejects.toThrow('Request timeout');
    });

    it('should handle malformed JSON responses', async () => {
      // Arrange
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        text: async () => 'Invalid JSON response',
      });

      // Act
      const response = await fetch(`${apiUrl}/tickets`);
      
      // Assert
      expect(response.status).toBe(200);
      // Note: In real scenarios, you'd want to handle this gracefully
    });
  });

  describe('API Endpoint Structure', () => {
    it('should have correct base URL structure', () => {
      expect(baseUrl).toBe('https://vh-zendesk-632260335818.us-central1.run.app');
      expect(apiUrl).toBe('https://vh-zendesk-632260335818.us-central1.run.app/api/v1');
    });

    it('should support all required HTTP methods', () => {
      const supportedMethods = ['GET', 'POST', 'DELETE', 'OPTIONS'];
      
      supportedMethods.forEach(method => {
        expect(['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']).toContain(method);
      });
    });

    it('should have consistent endpoint patterns', () => {
      const endpoints = [
        '/health',
        '/tickets',
        '/reports',
        '/attachments/download',
      ];

      endpoints.forEach(endpoint => {
        expect(`${apiUrl}${endpoint}`).toMatch(/^https:\/\/.*\/api\/v1\/.*$/);
      });
    });
  });
});
