import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import ReactGA from 'react-ga4';
import { startErrorTracker } from '../errorTracker';

// Mock ReactGA
vi.mock('react-ga4', () => ({
  default: {
    event: vi.fn(),
  },
}));

// Mock window object
Object.defineProperty(window, 'gaErrorTrackerStarted', {
  value: undefined,
  writable: true,
  configurable: true,
});

Object.defineProperty(window, 'location', {
  value: { pathname: '/test-page' },
  writable: true,
});

describe('startErrorTracker', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();
    
    // Reset window properties
    window.gaErrorTrackerStarted = undefined;
    window.location.pathname = '/test-page';
    
    // Clear document body
    document.body.innerHTML = '';
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should not start tracking if already started', () => {
    window.gaErrorTrackerStarted = true;
    
    startErrorTracker();
    
    expect(ReactGA.event).not.toHaveBeenCalled();
  });

  it('should set gaErrorTrackerStarted flag', () => {
    startErrorTracker();
    
    expect(window.gaErrorTrackerStarted).toBe(true);
  });

  it('should track error elements with data-testid containing "error"', () => {
    // Create an error element first
    const errorDiv = document.createElement('div');
    errorDiv.setAttribute('data-testid', 'error-message');
    errorDiv.textContent = 'Something went wrong';
    document.body.appendChild(errorDiv);
    
    startErrorTracker();
    
    expect(ReactGA.event).toHaveBeenCalledWith('error_displayed', {
      data_testid: 'error-message',
      error_message: 'Something went wrong',
      page_path: '/test-page',
    });
    
    document.body.removeChild(errorDiv);
  });

  it('should not track elements without data-testid', () => {
    startErrorTracker();
    
    // Create an element without data-testid
    const div = document.createElement('div');
    div.textContent = 'Error message';
    document.body.appendChild(div);
    
    startErrorTracker();
    
    expect(ReactGA.event).not.toHaveBeenCalled();
    
    document.body.removeChild(div);
  });

  it('should not track elements with data-testid that does not contain "error"', () => {
    startErrorTracker();
    
    // Create an element with data-testid that doesn't contain "error"
    const div = document.createElement('div');
    div.setAttribute('data-testid', 'success-message');
    div.textContent = 'Success!';
    document.body.appendChild(div);
    
    startErrorTracker();
    
    expect(ReactGA.event).not.toHaveBeenCalled();
    
    document.body.removeChild(div);
  });

  it('should handle empty text content', () => {
    // Create an error element with empty text first
    const errorDiv = document.createElement('div');
    errorDiv.setAttribute('data-testid', 'error-message');
    document.body.appendChild(errorDiv);
    
    startErrorTracker();
    
    expect(ReactGA.event).toHaveBeenCalledWith('error_displayed', {
      data_testid: 'error-message',
      error_message: '',
      page_path: '/test-page',
    });
    
    document.body.removeChild(errorDiv);
  });

  it('should handle whitespace-only text content', () => {
    // Create an error element with whitespace first
    const errorDiv = document.createElement('div');
    errorDiv.setAttribute('data-testid', 'error-message');
    errorDiv.textContent = '   \n\t  ';
    document.body.appendChild(errorDiv);
    
    startErrorTracker();
    
    expect(ReactGA.event).toHaveBeenCalledWith('error_displayed', {
      data_testid: 'error-message',
      error_message: '',
      page_path: '/test-page',
    });
    
    document.body.removeChild(errorDiv);
  });

  it('should use current page pathname', () => {
    window.location.pathname = '/different-page';
    
    const errorDiv = document.createElement('div');
    errorDiv.setAttribute('data-testid', 'error-message');
    errorDiv.textContent = 'Test error';
    document.body.appendChild(errorDiv);
    
    startErrorTracker();
    
    expect(ReactGA.event).toHaveBeenCalledWith('error_displayed', {
      data_testid: 'error-message',
      error_message: 'Test error',
      page_path: '/different-page',
    });
    
    document.body.removeChild(errorDiv);
  });

  it('should not process the same element twice', () => {
    const errorDiv = document.createElement('div');
    errorDiv.setAttribute('data-testid', 'error-message');
    errorDiv.textContent = 'Test error';
    document.body.appendChild(errorDiv);
    
    // Call startErrorTracker twice
    startErrorTracker();
    startErrorTracker();
    
    // Should only be called once due to WeakSet tracking
    expect(ReactGA.event).toHaveBeenCalledTimes(1);
    
    document.body.removeChild(errorDiv);
  });
}); 