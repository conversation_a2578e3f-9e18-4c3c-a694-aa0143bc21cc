import { describe, it, expect, beforeEach, vi } from 'vitest';
import ReactGA from 'react-ga4';
import {
  Action,
  createListenerMiddleware,
  Dispatch,
  ListenerMiddlewareInstance,
} from '@reduxjs/toolkit';
import { setUser } from '@/entities/user/model';

// Mock ReactGA
vi.mock('react-ga4', () => ({
  default: {
    set: vi.fn(),
  },
}));

// Mock Redux Toolkit
vi.mock('@reduxjs/toolkit', () => ({
  createListenerMiddleware: vi.fn(() => ({
    startListening: vi.fn(),
  })),
}));

// Mock store slice
vi.mock('@/entities/user/model', () => ({
  setUser: vi.fn(),
}));

describe('userIdListener', () => {
  let userIdListener: unknown;
  let mockStartListening: ReturnType<typeof vi.fn>;

  beforeEach(async () => {
    vi.clearAllMocks();
    vi.resetAllMocks();

    // Reset modules to ensure fresh import
    vi.resetModules();

    // Set up mock for startListening
    mockStartListening = vi.fn();
    const mockListener = {
      startListening: mockStartListening,
      middleware: vi.fn(),
      stopListening: vi.fn(),
      clearListeners: vi.fn(),
    };

    vi.mocked(createListenerMiddleware).mockReturnValue(
      mockListener as unknown as ListenerMiddlewareInstance<
        unknown,
        Dispatch<Action>,
        unknown
      >,
    );

    // Import the module after mocks are set up
    const module = await import('../userIdListener');
    userIdListener = module.userIdListener;
  });

  it('should be a listener middleware instance', () => {
    expect(userIdListener).toBeDefined();
    expect(createListenerMiddleware).toHaveBeenCalled();
  });

  it('should start listening to setUser action', () => {
    expect(mockStartListening).toHaveBeenCalledWith({
      actionCreator: setUser,
      effect: expect.any(Function),
    });
  });

  describe('effect function', () => {
    let effectFunction: (action: { payload: unknown }) => Promise<void>;

    beforeEach(async () => {
      vi.clearAllMocks();

      // Capture the effect function when startListening is called
      mockStartListening.mockImplementation(
        (config: {
          effect: (action: { payload: unknown }) => Promise<void>;
        }) => {
          effectFunction = config.effect;
        },
      );

      // Re-import to trigger the effect setup
      vi.resetModules();
      await import('../userIdListener');
    });

    it('should set user_id when user has id', async () => {
      const mockUser = { id: 'user123', displayName: 'Test User' };
      const mockAction = { payload: mockUser };

      await effectFunction(mockAction);

      expect(ReactGA.set).toHaveBeenCalledWith({ user_id: 'user123' });
    });

    it('should set user_id to undefined when user is null', async () => {
      const mockAction = { payload: null };

      await effectFunction(mockAction);

      expect(ReactGA.set).toHaveBeenCalledWith({ user_id: undefined });
    });

    it('should set user_id to undefined when user has no id', async () => {
      const mockUser = { displayName: 'Test User' }; // No id
      const mockAction = { payload: mockUser };

      await effectFunction(mockAction);

      expect(ReactGA.set).toHaveBeenCalledWith({ user_id: undefined });
    });

    it('should set user_id to undefined when user is undefined', async () => {
      const mockAction = { payload: undefined };

      await effectFunction(mockAction);

      expect(ReactGA.set).toHaveBeenCalledWith({ user_id: undefined });
    });
  });
});
