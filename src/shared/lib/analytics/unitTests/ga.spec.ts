import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import ReactGA from 'react-ga4';

// Mock ReactGA
vi.mock('react-ga4', () => ({
  default: {
    initialize: vi.fn(),
    gtag: vi.fn(),
  },
}));

// Mock updateConsent module
vi.mock('../updateConsent', () => ({
  initListeners: vi.fn(),
}));

beforeEach(() => {
  vi.stubEnv('PROD', true);
  vi.stubEnv('VITE_GA_ID', 'test-ga-id');
});

afterEach(() => {
  vi.unstubAllEnvs();
});

describe('initAnalytics', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();
    
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
      },
      writable: true,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should initialize analytics in production environment', async () => {
    const mockLocalStorage = {
      getItem: vi.fn().mockReturnValue('true'),
    };
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    // Import after mocks are set up
    const { initAnalytics } = await import('../ga');
    const { initListeners } = await import('../updateConsent');

    initAnalytics();

    expect(ReactGA.initialize).toHaveBeenCalledWith('test-ga-id', {
      gaOptions: { client_storage: 'none' },
    });
    expect(ReactGA.gtag).toHaveBeenCalledWith('consent', 'default', {
      analytics_storage: 'granted',
      security_storage: 'granted',
    });
    expect(initListeners).toHaveBeenCalled();
  });

  it('should set denied consent when userConsent is not true', async () => {
    const mockLocalStorage = {
      getItem: vi.fn().mockReturnValue('false'),
    };
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    // Import after mocks are set up
    const { initAnalytics } = await import('../ga');
    const { initListeners } = await import('../updateConsent');

    initAnalytics();

    expect(ReactGA.initialize).toHaveBeenCalledWith('test-ga-id', {
      gaOptions: { client_storage: 'none' },
    });
    expect(ReactGA.gtag).toHaveBeenCalledWith('consent', 'default', {
      analytics_storage: 'denied',
      security_storage: 'denied',
    });
    expect(initListeners).not.toHaveBeenCalled();
  });

  it('should handle null userConsent', async () => {
    const mockLocalStorage = {
      getItem: vi.fn().mockReturnValue(null),
    };
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    // Import after mocks are set up
    const { initAnalytics } = await import('../ga');
    const { initListeners } = await import('../updateConsent');

    initAnalytics();

    expect(ReactGA.initialize).toHaveBeenCalledWith('test-ga-id', {
      gaOptions: { client_storage: 'none' },
    });
    expect(ReactGA.gtag).toHaveBeenCalledWith('consent', 'default', {
      analytics_storage: 'denied',
      security_storage: 'denied',
    });
    expect(initListeners).not.toHaveBeenCalled();
  });

  it('should not initialize analytics in non-production environment', async () => {
    // Temporarily mock PROD as false
    vi.stubEnv('PROD', false);

    // Import after mocks are set up
    const { initAnalytics } = await import('../ga');
    const { initListeners } = await import('../updateConsent');

    initAnalytics();

    expect(ReactGA.initialize).not.toHaveBeenCalled();
    expect(ReactGA.gtag).not.toHaveBeenCalled();
    expect(initListeners).not.toHaveBeenCalled();
  });
}); 