import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import ReactGA from 'react-ga4';
import { startButtonTracker } from '../buttonTracker';

// Mock ReactGA
vi.mock('react-ga4', () => ({
  default: {
    event: vi.fn(),
  },
}));

// Mock window object
Object.defineProperty(window, 'gaButtonTrackerStarted', {
  value: undefined,
  writable: true,
  configurable: true,
});

Object.defineProperty(window, 'location', {
  value: { pathname: '/test-page' },
  writable: true,
});

describe('startButtonTracker', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();
    
    // Reset window properties
    window.gaButtonTrackerStarted = undefined;
    window.location.pathname = '/test-page';
    
    // Clear any existing event listeners
    document.removeEventListener('click', vi.fn());
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should not start tracking if already started', () => {
    window.gaButtonTrackerStarted = true;
    
    startButtonTracker();
    
    expect(ReactGA.event).not.toHaveBeenCalled();
  });

  it('should set gaButtonTrackerStarted flag', () => {
    startButtonTracker();
    
    expect(window.gaButtonTrackerStarted).toBe(true);
  });

  it('should track button clicks with data-testid', () => {
    startButtonTracker();
    
    // Create a button element with data-testid
    const button = document.createElement('button');
    button.setAttribute('data-testid', 'test-button');
    document.body.appendChild(button);
    
    // Simulate click
    button.click();
    
    expect(ReactGA.event).toHaveBeenCalledWith('button_clicked', {
      data_testid: 'test-button',
      page_path: '/test-page',
    });
    
    document.body.removeChild(button);
  });

  it('should track role="button" elements with data-testid', () => {
    startButtonTracker();
    
    // Create a div with role="button" and data-testid
    const div = document.createElement('div');
    div.setAttribute('role', 'button');
    div.setAttribute('data-testid', 'test-div-button');
    document.body.appendChild(div);
    
    // Simulate click
    div.click();
    
    expect(ReactGA.event).toHaveBeenCalledWith('button_clicked', {
      data_testid: 'test-div-button',
      page_path: '/test-page',
    });
    
    document.body.removeChild(div);
  });

  it('should not track clicks on elements without data-testid', () => {
    startButtonTracker();
    
    // Create a button element without data-testid
    const button = document.createElement('button');
    document.body.appendChild(button);
    
    // Simulate click
    button.click();
    
    expect(ReactGA.event).not.toHaveBeenCalled();
    
    document.body.removeChild(button);
  });

  it('should not track clicks on elements outside button selector', () => {
    startButtonTracker();
    
    // Create a div without role="button" or button tag
    const div = document.createElement('div');
    div.setAttribute('data-testid', 'test-div');
    document.body.appendChild(div);
    
    // Simulate click
    div.click();
    
    expect(ReactGA.event).not.toHaveBeenCalled();
    
    document.body.removeChild(div);
  });

  it('should handle clicks on child elements of buttons', () => {
    startButtonTracker();
    
    // Create a button with child elements
    const button = document.createElement('button');
    button.setAttribute('data-testid', 'parent-button');
    
    const span = document.createElement('span');
    span.textContent = 'Click me';
    button.appendChild(span);
    
    document.body.appendChild(button);
    
    // Simulate click on the span (child element)
    span.click();
    
    expect(ReactGA.event).toHaveBeenCalledWith('button_clicked', {
      data_testid: 'parent-button',
      page_path: '/test-page',
    });
    
    document.body.removeChild(button);
  });

  it('should use current page pathname', () => {
    window.location.pathname = '/different-page';
    startButtonTracker();
    
    const button = document.createElement('button');
    button.setAttribute('data-testid', 'test-button');
    document.body.appendChild(button);
    
    button.click();
    
    expect(ReactGA.event).toHaveBeenCalledWith('button_clicked', {
      data_testid: 'test-button',
      page_path: '/different-page',
    });
    
    document.body.removeChild(button);
  });
}); 