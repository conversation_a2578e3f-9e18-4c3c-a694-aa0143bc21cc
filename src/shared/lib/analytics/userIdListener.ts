import { createListenerMiddleware } from '@reduxjs/toolkit';
import ReactGA from 'react-ga4';
import { setUser } from '@/entities/user/model';

export const userIdListener = createListenerMiddleware();

userIdListener.startListening({
  actionCreator: setUser,
  effect: async (action) => {
    const user = action.payload;

    if (user && user.id) {
      ReactGA.set({ user_id: user.id });
    } else {
      ReactGA.set({ user_id: undefined });
    }
  },
});
