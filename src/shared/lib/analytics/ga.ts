import ReactGA from 'react-ga4';
import { initListeners } from './updateConsent';

export function initAnalytics() {
  if (!import.meta.env.PROD && !window.cookieConsentTest) return;

  ReactGA.initialize(import.meta.env.VITE_GA_ID, {
    gaOptions: { client_storage: 'none'  },
  });

  if (localStorage.getItem('userConsent') === 'true') {
    ReactGA.gtag('consent', 'default', {
      analytics_storage: 'granted',
      security_storage: 'granted',
    });
    initListeners();
  } else {
    ReactGA.gtag('consent', 'default', {
      analytics_storage: 'denied',
      security_storage: 'denied',
    });
  }
}
