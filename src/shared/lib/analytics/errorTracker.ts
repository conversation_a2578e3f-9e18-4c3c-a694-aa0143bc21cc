import ReactGA from 'react-ga4';

export function startErrorTracker() {
  if (window.gaErrorTrackerStarted) return;
  window.gaErrorTrackerStarted = true;

  const SELECTOR = '[data-testid*="error"]';
  const processed = new WeakSet<HTMLElement>();

  function send(el: HTMLElement) {
    if (processed.has(el)) return;
    processed.add(el);

    const testId = el.getAttribute('data-testid') ?? '';
    if (!testId) return;

    const message = el.textContent?.trim() ?? '';

    ReactGA.event('error_displayed', {
      data_testid: testId,
      error_message: message,
      page_path: window.location.pathname,
    });
  }

  document.querySelectorAll<HTMLElement>(SELECTOR).forEach(send);

  const observer = new MutationObserver((mutations) => {
    for (const m of mutations) {
      for (const n of m.addedNodes) {
        if (n.nodeType !== Node.ELEMENT_NODE) continue;
        const el = n as HTMLElement;

        if (el.matches(SELECTOR)) send(el);

        el.querySelectorAll<HTMLElement>(SELECTOR).forEach(send);
      }
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });
}
