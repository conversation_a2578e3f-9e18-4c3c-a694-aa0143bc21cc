import ReactGA from 'react-ga4';

export function startButtonTracker() {
  if (window.gaButtonTrackerStarted) return;
  window.gaButtonTrackerStarted = true;

  const BUTTON_SELECTOR = 'button[data-testid], [role="button"][data-testid]';

  document.addEventListener('click', (e) => {
    const btn = (e.target as HTMLElement).closest<HTMLElement>(BUTTON_SELECTOR);
    if (!btn) return;

    const testId = btn.getAttribute('data-testid');
    if (!testId) return;

    ReactGA.event('button_clicked', {
      data_testid: testId,
      page_path: window.location.pathname,
    });
  });
}
