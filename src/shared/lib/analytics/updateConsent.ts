import ReactGA from 'react-ga4';
import { startButtonTracker } from './buttonTracker';
import { startErrorTracker } from './errorTracker';

export const updateConsent = (consent: boolean) => {
  if (!import.meta.env.PROD && !window.cookieConsentTest) return;

  ReactGA.gtag('consent', 'update', {
    analytics_storage: consent ? 'granted' : 'denied',
    security_storage: consent ? 'granted' : 'denied',
  });

  localStorage.setItem('userConsent', JSON.stringify(consent));
};

export const initListeners = () => {
  if (!import.meta.env.PROD && !window.cookieConsentTest) return;

  const consent = localStorage.getItem('userConsent');

  if (consent === 'true') {
    startButtonTracker();
    startErrorTracker();
  }
};
