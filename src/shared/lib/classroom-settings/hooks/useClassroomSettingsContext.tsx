import { useContext } from 'react';
import {
  ClassroomSettingsContext,
  ClassroomSettingsContextValue,
} from '../context';

export function useClassroomSettingsContext(): ClassroomSettingsContextValue {
  const context = useContext(ClassroomSettingsContext);
  if (!context) {
    throw new Error(
      'useClassroomSettingsContext must be used within a ClassroomSettingsProvider',
    );
  }
  return context;
}
