import { createContext, FC, PropsWithChildren, useState } from 'react';

type ClassroomSettings = {
  canAnonymousJoin: boolean;
  allowInviteGuests: boolean;
  allowRecording: boolean;
  allowSharing: boolean;
  allowComments: boolean;
  allowTranscription: boolean;
  allowMaterialsUpload: boolean;
  allowMaterialsDownload: boolean;
  allowChat: boolean;
};

export interface ClassroomSettingsContextValue {
  classroomSettings: Record<string, ClassroomSettings>;
  updateClassroomSettings: (
    classroomId: string,
    value: ClassroomSettings,
  ) => void;
}

export const ClassroomSettingsContext = createContext<
  ClassroomSettingsContextValue | undefined
>(undefined);

export const ClassroomSettingsProvider: FC<PropsWithChildren> = ({
  children,
}) => {
  const [classroomSettings, setClassroomSettings] = useState<
    Record<string, ClassroomSettings>
  >({});

  const updateClassroomSettings = (
    classroomId: string,
    value: ClassroomSettings,
  ) => {
    setClassroomSettings((prev) => ({
      ...prev,
      [classroomId]: value,
    }));
  };

  return (
    <ClassroomSettingsContext.Provider
      value={{
        classroomSettings,
        updateClassroomSettings,
      }}
    >
      {children}
    </ClassroomSettingsContext.Provider>
  );
};
