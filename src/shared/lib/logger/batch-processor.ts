interface BatchItem<T> {
  id: string;
  data: T;
  timestamp: number;
  retryCount?: number;
}

interface BatchProcessorOptions {
  batchSize?: number;
  processingInterval?: number;
  maxRetries?: number;
  onError?: <T>(error: Error, item: BatchItem<T>) => void;
  onBatchComplete?: (processedCount: number, failedCount: number) => void;
}

export class BatchProcessor<T> {
  private queue: BatchItem<T>[] = [];
  private isProcessing = false;
  private timer: NodeJS.Timeout | null = null;
  private readonly options: Required<BatchProcessorOptions>;

  constructor(
    private processor: (items: BatchItem<T>[]) => Promise<void>,
    options: BatchProcessorOptions = {},
  ) {
    this.options = {
      batchSize: 100,
      processingInterval: 30000,
      maxRetries: 3,
      onError: () => {},
      onBatchComplete: () => {},
      ...options,
    };
  }

  enqueue(data: T): string {
    const id = this.generateId();
    const item: BatchItem<T> = {
      id,
      data,
      timestamp: Date.now(),
      retryCount: 0,
    };

    this.queue.push(item);

    if (!this.timer) {
      this.start();
    }

    return id;
  }

  enqueueBatch(items: T[]): string[] {
    return items.map((item) => this.enqueue(item));
  }

  start(): void {
    if (this.timer) {
      return;
    }

    this.timer = setInterval(async () => {
      await this.processBatch();
    }, this.options.processingInterval);

    if (this.queue.length > 0) {
      setImmediate(() => this.processBatch());
    }
  }

  stop(): void {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }

  private async processBatch(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      const batchItems = this.queue.splice(0, this.options.batchSize);

      if (batchItems.length === 0) {
        return;
      }

      await this.processor(batchItems);

      this.options.onBatchComplete(batchItems.length, 0);
    } catch (error) {
      console.error('Batch processing error:', error);

      const failedItems = this.queue.splice(0, this.options.batchSize);
      const retriableItems: BatchItem<T>[] = [];
      let permanentFailures = 0;

      for (const item of failedItems) {
        item.retryCount = (item.retryCount || 0) + 1;

        if (item.retryCount <= this.options.maxRetries) {
          retriableItems.push(item);
        } else {
          permanentFailures++;
          this.options.onError(error as Error, item);
        }
      }

      this.queue.unshift(...retriableItems);

      this.options.onBatchComplete(0, permanentFailures);
    } finally {
      this.isProcessing = false;
    }
  }

  getStatus(): {
    queueLength: number;
    isProcessing: boolean;
    isRunning: boolean;
  } {
    return {
      queueLength: this.queue.length,
      isProcessing: this.isProcessing,
      isRunning: this.timer !== null,
    };
  }

  clear(): void {
    this.queue = [];
  }

  async forceProcess(): Promise<void> {
    await this.processBatch();
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
