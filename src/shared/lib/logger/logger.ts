import { LOGGER_URL } from '@/shared/constants';
import { BatchProcessor } from './batch-processor';

interface LogEntry {
  level: string;
  message: string;
  labels?: Record<string, string | number | boolean | object>;
  timestamp: number;
}

interface MetricEntry {
  type: string;
  name: string;
  session_id: string;
  participant_id: string;
  timestamp: number;
  labels?: Record<string, string | number | boolean | object>;
}

interface CombinedData {
  metrics: MetricEntry[];
  logs: LogEntry[];
}

export class Logger {
  private static instance: Logger;
  private processor: BatchProcessor<CombinedData>;
  private readonly loggerUrl = `${LOGGER_URL}/api/metrics`;
  private pendingMetrics: MetricEntry[] = [];
  private pendingLogs: LogEntry[] = [];
  private userId: string | null = null;

  private constructor(options?: {
    batchSize?: number;
    processingInterval?: number;
  }) {
    const { batchSize = 100, processingInterval = 30000 } = options || {};

    this.processor = new BatchProcessor<CombinedData>(
      async (items) => {
        const isPlaywright = import.meta.env.VITE_PLAYWRIGHT === 'true';

        if (isPlaywright) return;

        const allMetrics: MetricEntry[] = [];
        const allLogs: LogEntry[] = [];

        items.forEach((item) => {
          allMetrics.push(...item.data.metrics);
          allLogs.push(...item.data.logs);
        });

        await this.sendToApi(allMetrics, allLogs);
      },
      {
        batchSize,
        processingInterval,
        maxRetries: 3,
        onError: (error, item) => {
          console.error(`Failed to process batch ${item.id}:`, error);
        },
        onBatchComplete: () => {},
      },
    );

    this.processor.start();
  }

  public static getInstance(options?: {
    batchSize?: number;
    processingInterval?: number;
  }): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(options);
    }
    return Logger.instance;
  }

  public setUserId(userId: string | null): void {
    this.userId = userId;
  }

  public getUserId(): string | null {
    return this.userId;
  }

  private formatLogMessage(message: string): string {
    if (this.userId) {
      return `[${this.userId}] ${message}`;
    }
    return message;
  }

  private addToQueue(): void {
    if (this.pendingMetrics.length > 0 || this.pendingLogs.length > 0) {
      const data: CombinedData = {
        metrics: [...this.pendingMetrics],
        logs: [...this.pendingLogs],
      };

      this.processor.enqueue(data);

      this.pendingMetrics = [];
      this.pendingLogs = [];
    }
  }

  private async sendToApi(
    metrics: MetricEntry[],
    logs: LogEntry[],
  ): Promise<void> {
    try {
      const response = await fetch(this.loggerUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metrics,
          logs,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to send data to logger API:', error);
      throw error;
    }
  }

  public info(
    data: string | LogEntry | Array<string | LogEntry>,
    labels?: Record<string, string | number | boolean>,
  ): void {
    this.processLogs('info', data, labels);
  }

  public warn(
    data: string | LogEntry | Array<string | LogEntry>,
    labels?: Record<string, string | number | boolean>,
  ): void {
    this.processLogs('warn', data, labels);
  }

  public error(
    data: string | LogEntry | Array<string | LogEntry>,
    labels?: Record<string, string | number | boolean>,
  ): void {
    this.processLogs('error', data, labels);
  }

  public metric(data: MetricEntry | MetricEntry[]): void {
    const metrics = Array.isArray(data) ? data : [data];

    const metricEntries = metrics.map((metric) => ({
      ...metric,
      timestamp: metric.timestamp || Date.now(),
    }));

    this.pendingMetrics.push(...metricEntries);
    this.addToQueue();
  }

  private processLogs(
    level: string,
    data: string | LogEntry | Array<string | LogEntry>,
    labels?: Record<string, string | number | boolean>,
  ): void {
    const items = Array.isArray(data) ? data : [data];

    const logEntries = items.map((item) => {
      if (typeof item === 'string') {
        return {
          level,
          message: this.formatLogMessage(item),
          labels,
          timestamp: Date.now(),
        };
      } else {
        return {
          ...item,
          message: this.formatLogMessage(item.message),
          timestamp: item.timestamp || Date.now(),
        };
      }
    });

    this.pendingLogs.push(...logEntries);
    this.addToQueue();
  }

  public getStatus(): ReturnType<BatchProcessor<CombinedData>['getStatus']> & {
    pendingMetrics: number;
    pendingLogs: number;
    userId: string | null;
  } {
    return {
      ...this.processor.getStatus(),
      pendingMetrics: this.pendingMetrics.length,
      pendingLogs: this.pendingLogs.length,
      userId: this.userId,
    };
  }

  public async forceFlush(): Promise<void> {
    this.addToQueue();
    return this.processor.forceProcess();
  }

  public shutdown(): void {
    this.addToQueue();
    this.processor.stop();
  }
}
