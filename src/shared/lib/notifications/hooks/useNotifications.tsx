import { toast } from 'sonner';
import { useTranslation } from 'react-i18next';
import { addNotification as dispatchAddNotification } from '../model';
import { useAppDispatch } from '@/shared/hooks';

export const useNotifications = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const addToast = ({
    text,
    params,
  }: {
    text: string;
    params?: Record<string, string>;
  }) => {
    toast(t(text, params));
  };

  const addNotification = ({
    title,
    description,
    allowFullscreen = false,
  }: {
    title: string;
    description: string;
    allowFullscreen?: boolean;
  }) => {
    dispatch(
      dispatchAddNotification({
        title: t(title),
        description: t(description),
        allowFullscreen,
      }),
    );
  };

  return { addNotification, addToast };
};
