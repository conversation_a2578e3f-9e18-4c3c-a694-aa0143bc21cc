import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface INotification {
  id: string;
  title: string;
  description: string;
  allowFullscreen?: boolean;
}

interface IInitialState {
  notifications: INotification[];
}

const initialState: IInitialState = {
  notifications: [],
};

const userSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    addNotification: (
      state,
      action: PayloadAction<{
        title: string;
        description: string;
        allowFullscreen?: boolean;
      }>,
    ) => {
      const id = crypto.randomUUID();
      const newNotification: INotification = {
        id,
        title: action.payload.title,
        description: action.payload.description,
        allowFullscreen: action.payload.allowFullscreen,
      };

      state.notifications.push(newNotification);
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(
        (notification) => notification.id !== action.payload,
      );
    },
  },
});

export const { addNotification, removeNotification } = userSlice.actions;
export default userSlice.reducer;
