export const getProviderLink = (email: string): string | null => {
  const domain = email.split('@').pop()?.toLowerCase() || '';
  const providers: [string, string][] = [
    ['gmail', 'https://mail.google.com'],
    ['googlemail', 'https://mail.google.com'],
    ['yahoo', 'https://mail.yahoo.com'],
    ['outlook', 'https://outlook.live.com'],
    ['hotmail', 'https://outlook.live.com'],
    ['live.', 'https://outlook.live.com'],
    ['msn', 'https://outlook.live.com'],
    ['icloud', 'https://www.icloud.com/mail'],
    ['aol', 'https://mail.aol.com'],
    ['zoho', 'https://mail.zoho.com'],
    ['protonmail', 'https://mail.protonmail.com'],
    ['mail.ru', 'https://mail.ru'],
    ['yandex', 'https://mail.yandex.com'],
    ['gmx', 'https://www.gmx.com'],
    ['fastmail', 'https://mail.fastmail.com'],
    ['hushmail', 'https://mail.hushmail.com'],
  ];
  for (const [key, url] of providers) {
    if (domain.includes(key)) return url;
  }
  return null;
};
