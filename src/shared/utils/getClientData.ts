export const getClientData = (userAgent: string) => {
  if (!userAgent || typeof userAgent !== 'string') {
    return {
      browser: 'Unknown browser',
      device: 'Unknown device',
      os: 'Unknown OS',
    };
  }

  const ua = userAgent.toLowerCase();

  // Parse Browser
  let browser = 'Unknown browser';
  if (ua.includes('chrome') && !ua.includes('edg') && !ua.includes('opr')) {
    browser = 'Chrome';
  } else if (ua.includes('firefox')) {
    browser = 'Firefox';
  } else if (ua.includes('safari') && !ua.includes('chrome')) {
    browser = 'Safari';
  } else if (ua.includes('edg')) {
    browser = 'Edge';
  } else if (ua.includes('opr') || ua.includes('opera')) {
    browser = 'Opera';
  } else if (ua.includes('msie') || ua.includes('trident')) {
    browser = 'Internet Explorer';
  }

  // Parse Operating System
  let os = 'Unknown OS';
  if (ua.includes('windows nt')) {
    if (ua.includes('windows nt 10.0')) os = 'Windows 10/11';
    else if (ua.includes('windows nt 6.3')) os = 'Windows 8.1';
    else if (ua.includes('windows nt 6.2')) os = 'Windows 8';
    else if (ua.includes('windows nt 6.1')) os = 'Windows 7';
    else os = 'Windows';
  } else if (ua.includes('mac os x')) {
    os = 'macOS';
  } else if (ua.includes('linux') && !ua.includes('android')) {
    os = 'Linux';
  } else if (ua.includes('android')) {
    os = 'Android';
  } else if (
    ua.includes('iphone') ||
    ua.includes('ipad') ||
    ua.includes('ipod')
  ) {
    os = 'iOS';
  } else if (ua.includes('cros')) {
    os = 'Chrome OS';
  }

  // Parse Device
  let device = 'Desktop';
  if (
    ua.includes('mobile') ||
    ua.includes('android') ||
    ua.includes('iphone')
  ) {
    device = 'Mobile';
  } else if (ua.includes('tablet') || ua.includes('ipad')) {
    device = 'Tablet';
  }

  // More specific device detection for mobile
  if (device === 'Mobile') {
    if (ua.includes('iphone')) device = 'iPhone';
    else if (ua.includes('android')) device = 'Android Phone';
    else if (ua.includes('windows phone')) device = 'Windows Phone';
  } else if (device === 'Tablet') {
    if (ua.includes('ipad')) device = 'iPad';
    else if (ua.includes('android')) device = 'Android Tablet';
  }

  return {
    browser,
    device,
    os,
  };
};
