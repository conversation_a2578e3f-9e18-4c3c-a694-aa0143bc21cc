export const PASSWORD_MIN_LENGTH = 8;
export const PASSWORD_PATTERN = /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/;

export type PasswordMetrics = {
  percentage: number;
  strength:
    | 'globals.passwordStrength.weak'
    | 'globals.passwordStrength.fair'
    | 'globals.passwordStrength.good'
    | 'globals.passwordStrength.strong';
  colorClass: string;
};

const STRENGTH_LABELS: PasswordMetrics['strength'][] = [
  'globals.passwordStrength.weak',
  'globals.passwordStrength.fair',
  'globals.passwordStrength.good',
  'globals.passwordStrength.strong',
];
const COLOR_CLASSES = ['chart-2', 'chart-3', 'chart-4', 'chart-1'];

export function calculatePasswordStrength(password: string): PasswordMetrics {
  let score = 0;
  if (password.length >= PASSWORD_MIN_LENGTH) score++;
  if (password.length >= 12) score++;
  if (/[a-z]/.test(password)) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/\d/.test(password)) score++;
  if (/[^A-Za-z0-9]/.test(password)) score++;

  let idx: number;
  if (score < 3) idx = 0;
  else if (score < 4) idx = 1;
  else if (score < 5) idx = 2;
  else idx = 3;

  return {
    percentage: Math.round((score / 6) * 100),
    strength: STRENGTH_LABELS[idx],
    colorClass: COLOR_CLASSES[idx],
  };
}
