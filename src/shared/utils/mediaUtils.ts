export const safePlay = async (
  element: HTMLVideoElement | HTMLAudioElement,
  onFail?: () => void,
  maxRetries = 3,
): Promise<boolean> => {
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      await element.play();
      return true;
    } catch (error) {
      console.warn(`${element}.play() attempt ${attempt + 1} failed:`, error);

      if (attempt < maxRetries - 1) {
        await new Promise((resolve) =>
          setTimeout(resolve, Math.pow(2, attempt) * 100),
        );
      }
    }
  }

  if (onFail) onFail();
  return false;
};
