export { getInitials } from './getInitials';
export { getFileSizeFromURL, getFileSize, formatFileSize } from './getFileSize';
export { getClientData } from './getClientData';
export { isIOSDevice, isMobile, isLowEndDevice } from './getDevices';
export { stateUpdateLoop } from './state-update-loop';
export { getProviderLink } from './getProviderLink';
export type { PasswordMetrics } from './password';
export {
  calculatePasswordStrength,
  PASSWORD_MIN_LENGTH,
  PASSWORD_PATTERN,
} from './password';
export { safePlay } from './mediaUtils';
