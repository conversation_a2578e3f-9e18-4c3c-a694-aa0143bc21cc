export const isIOSDevice = (): boolean => {
  const uaData = navigator.userAgentData;
  if (uaData) {
    return (
      (uaData.platform === 'iOS' || uaData.platform === 'iPadOS') &&
      uaData.mobile === true
    );
  }

  const ua = navigator.userAgent;
  if (/\b(iPad|iPhone|iPod)\b/.test(ua)) return true;
  if (navigator.maxTouchPoints > 1 && /\bMacintosh\b/.test(ua)) return true;
  return false;
};

export const isMobile = () =>
  /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent);

export const isLowEndDevice = () => navigator.hardwareConcurrency <= 4;
