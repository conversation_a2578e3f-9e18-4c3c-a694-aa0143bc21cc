/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { stateUpdateLoop } from './stateUpdateLoop';

describe('StateUpdateLoop', () => {
  beforeEach(() => {
    // Reset internal state before each test
    (stateUpdateLoop as any).queue.length = 0;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('runs synchronous updates in order', async () => {
    const results: number[] = [];

    stateUpdateLoop.enqueue(() => {
      results.push(1);
    });
    stateUpdateLoop.enqueue(() => {
      results.push(2);
    });
    stateUpdateLoop.enqueue(() => {
      results.push(3);
    });

    await new Promise((r) => setTimeout(r, 50));
    expect(results).toEqual([1, 2, 3]);
  });

  it('handles asynchronous updates and respects await timing', async () => {
    const results: string[] = [];

    stateUpdateLoop.enqueue(async () => {
      results.push('start');
      await new Promise((res) => setTimeout(res, 10));
      results.push('end');
    });

    stateUpdateLoop.enqueue(() => {
      results.push('after');
    });

    await new Promise((r) => setTimeout(r, 40));
    expect(results).toEqual(['start', 'end', 'after']);
  });

  it('continues processing after an error in an update function', async () => {
    const results: string[] = [];
    const errorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    stateUpdateLoop.enqueue(() => {
      results.push('before error');
      throw new Error('bad update');
    });

    stateUpdateLoop.enqueue(() => {
      results.push('after error');
    });

    await new Promise((r) => setTimeout(r, 50));
    expect(results).toEqual(['before error', 'after error']);
    expect(errorSpy).toHaveBeenCalledWith(
      'Error in state update function:',
      expect.any(Error),
    );
  });

  it('continues polling even when queue is empty', async () => {
    const spy = vi.spyOn(global, 'setTimeout');
    await new Promise((r) => setTimeout(r, 30));
    expect(spy).toHaveBeenCalledWith(expect.any(Function), 10);
    spy.mockRestore();
  });
});
