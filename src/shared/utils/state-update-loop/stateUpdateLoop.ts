type StateUpdateFn = () => Promise<void> | void;

class StateUpdateLoop {
  private queue: StateUpdateFn[] = [];
  private running = false;

  constructor() {
    this.start();
  }

  enqueue(fn: StateUpdateFn) {
    this.queue.push(fn);
  }

  private async start() {
    if (this.running) return;
    this.running = true;

    while (true) {
      if (this.queue.length === 0) {
        await new Promise((res) => setTimeout(res, 10));
        continue;
      }

      const fn = this.queue.shift();
      if (!fn) continue;

      try {
        await fn();
      } catch (err) {
        console.error('Error in state update function:', err);
      }
    }
  }
}

export const stateUpdateLoop = new StateUpdateLoop();
