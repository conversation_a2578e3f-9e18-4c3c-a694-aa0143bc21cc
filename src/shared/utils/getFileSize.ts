export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const getFileSizeFromURL = async (
  url: string,
): Promise<number | null> => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    const contentLength = response.headers.get('Content-Length');
    return contentLength ? parseInt(contentLength, 10) : null;
  } catch {
    return null;
  }
};

export const getFileSize = async (url: string): Promise<string | null> => {
  const fileSize = await getFileSizeFromURL(url);
  if (fileSize !== null) {
    return formatFileSize(fileSize);
  }
  return null;
};
