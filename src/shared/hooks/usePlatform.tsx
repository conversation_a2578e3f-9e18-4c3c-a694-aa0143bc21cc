import { useState, useEffect } from 'react';

interface PlatformInfo {
  type: 'mobile' | 'desktop';
  os: string;
}

export const usePlatform = (): PlatformInfo => {
  const [platform, setPlatform] = useState<PlatformInfo>({
    type: 'desktop',
    os: 'Unknown',
  });

  useEffect(() => {
    const detectPlatform = (): PlatformInfo => {
      const userAgent = navigator.userAgent.toLowerCase();
      const platform = navigator.platform?.toLowerCase() || '';

      // Detect OS
      let os = 'Unknown';
      if (userAgent.includes('windows')) {
        os = 'Windows';
      } else if (userAgent.includes('mac') || platform.includes('mac')) {
        os = 'macOS';
      } else if (userAgent.includes('linux')) {
        os = 'Linux';
      } else if (userAgent.includes('android')) {
        os = 'Android';
      } else if (
        userAgent.includes('iphone') ||
        userAgent.includes('ipad') ||
        userAgent.includes('ipod')
      ) {
        os = 'iOS';
      }

      // Detect device type
      const isMobile =
        /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
          userAgent,
        ) ||
        (navigator.maxTouchPoints &&
          navigator.maxTouchPoints > 2 &&
          /macintosh/i.test(userAgent));

      return {
        type: isMobile ? 'mobile' : 'desktop',
        os,
      };
    };

    setPlatform(detectPlatform());
  }, []);

  return platform;
};
