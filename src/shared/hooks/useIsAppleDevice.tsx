import { useEffect, useState } from 'react';

declare global {
  interface NavigatorUAData {
    readonly platform?: string;
  }
  interface Navigator {
    readonly userAgentData?: NavigatorUAData;
  }
}

export function useIsAppleDevice(): boolean {
  const [isApple, setIsApple] = useState(false);

  useEffect(() => {
    if (typeof navigator === 'undefined') {
      setIsApple(false);
      return;
    }

    const uaData = window.navigator.userAgentData;
    if (uaData?.platform) {
      const p = uaData.platform.toLowerCase();
      if (p === 'ios' || p === 'ipados' || p === 'macos') {
        setIsApple(true);
        return;
      }
    }

    const ua = navigator.userAgent;
    const isiOS = /\b(iPhone|iPad|iPod)\b/.test(ua) && !('MSStream' in window);
    const isMacOS = /\bMacintosh\b|\bMac OS X\b/.test(ua);

    setIsApple(isiOS || isMacOS);
  }, []);

  return isApple;
}
