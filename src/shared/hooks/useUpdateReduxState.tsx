import { AppDispatch } from '@/app/store';
import { stateUpdateLoop } from '../utils';
import { useAppDispatch } from './useAppDispatch';

export const useUpdateReduxState = () => {
  const dispatch = useAppDispatch();

  const updateReduxState = (stateFunction: Parameters<AppDispatch>[0]) => {
    stateUpdateLoop.enqueue(() => {
      dispatch(stateFunction);
    });
  };

  return updateReduxState;
};
