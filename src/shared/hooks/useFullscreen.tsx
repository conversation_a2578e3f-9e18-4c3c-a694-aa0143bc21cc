import { useState, useCallback, useEffect } from 'react';

type UseFullscreenReturn = {
  isFullscreen: boolean;
  supportsFs: boolean;
  toggle: () => Promise<void>;
};

export function useFullscreen(selector: string): UseFullscreenReturn {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const supportsFs =
    typeof document !== 'undefined' && !!document.fullscreenEnabled;

  const onFullscreenChange = useCallback(() => {
    const el = document.querySelector(selector);
    setIsFullscreen(document.fullscreenElement === el);
  }, [selector]);

  useEffect(() => {
    if (!supportsFs) return;
    document.addEventListener('fullscreenchange', onFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', onFullscreenChange);
    };
  }, [supportsFs, onFullscreenChange]);

  const toggle = useCallback(async () => {
    if (!supportsFs) return;
    const el = document.querySelector(selector) as HTMLElement | null;
    if (!el) return;

    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen();
      } else {
        await el.requestFullscreen();
      }
    } catch {
      onFullscreenChange();
    }
  }, [supportsFs, selector, onFullscreenChange]);

  return { isFullscreen, supportsFs, toggle };
}
