import { Checkbox } from '@/shadcn/components/ui/checkbox';
import { Controller, Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Consents } from '../types';

interface ConsentCheckboxListProps {
  control: Control<{ [K in keyof Consents]?: boolean }>;
  onConsentChange?: (name: keyof Consents, value: boolean) => void;
}

export const ConsentCheckboxList = ({
  control,
  onConsentChange,
}: ConsentCheckboxListProps) => {
  const { t } = useTranslation();

  const consents = [
    {
      name: 'canRecord' as keyof Consents,
      title: t('home.dialogs.signUp.fields.consent.canRecord'),
      dataTestId: 'profile-actions-consents-canRecord',
    },
    {
      name: 'canShareClassRecordings' as keyof Consents,
      title: t('home.dialogs.signUp.fields.consent.canShareClassRecordings'),
      dataTestId: 'profile-actions-consents-canShareClassRecordings',
    },
    {
      name: 'canShareLiveStream' as keyof Consents,
      title: t('home.dialogs.signUp.fields.consent.canShareLiveStream'),
      dataTestId: 'profile-actions-consents-canShareLiveStream',
    },
    {
      name: 'canAIProcess' as keyof Consents,
      title: t('home.dialogs.signUp.fields.consent.canAIProcess'),
      dataTestId: 'profile-actions-consents-canAIProcess',
    },
    {
      name: 'canTeacherInviteGuests' as keyof Consents,
      title: t('home.dialogs.signUp.fields.consent.canTeacherInviteGuests'),
      dataTestId: 'profile-actions-consents-canTeacherInviteGuests',
    },
    {
      name: 'canStudentInviteGuests' as keyof Consents,
      title: t('home.dialogs.signUp.fields.consent.canStudentInviteGuests'),
      dataTestId: 'profile-actions-consents-canStudentInviteGuests',
    },
    {
      name: 'canAnonymousJoin' as keyof Consents,
      title: t('home.dialogs.signUp.fields.consent.canAnonymousJoin'),
      dataTestId: 'profile-actions-consents-canAnonymousJoin',
    },
    {
      name: 'canPartyChat' as keyof Consents,
      title: t('home.dialogs.signUp.fields.consent.canPartyChat'),
      dataTestId: 'profile-actions-consents-canPartyChat',
    },
    {
      name: 'canInviteMultipleTeachers' as keyof Consents,
      title: t('home.dialogs.signUp.fields.consent.canInviteMultipleTeachers'),
      dataTestId: 'profile-actions-consents-canInviteMultipleTeachers',
    },
    {
      name: 'canInviteMultipleStudents' as keyof Consents,
      title: t('home.dialogs.signUp.fields.consent.canInviteMultipleStudents'),
      dataTestId: 'profile-actions-consents-canInviteMultipleStudents',
    },
  ];

  return (
    <div className="flex flex-col gap-3">
      {consents.map((consent) => (
        <div key={consent.name} className="flex items-start space-x-3">
          <Controller
            name={consent.name}
            control={control}
            render={({ field }) => (
              <Checkbox
                checked={Boolean(field.value)}
                onCheckedChange={(checked) => {
                  const value = Boolean(checked);
                  field.onChange(value);
                  onConsentChange?.(consent.name, value);
                }}
                id={consent.name}
                className="size-5 data-[state=checked]:bg-accent data-[state=checked]:border-transparent border-primary/40 mt-0.5 cursor-pointer"
                data-testid={consent.dataTestId}
              />
            )}
          />
          <label
            htmlFor={consent.name}
            className="text-sm font-medium text-card-foreground/80 leading-relaxed cursor-pointer"
          >
            {consent.title}
          </label>
        </div>
      ))}
    </div>
  );
};
