import {
  AccordionItem as ShadncAccordionItem,
  AccordionContent,
  AccordionTrigger,
} from '@/shadcn/components/ui/accordion';
import { cloneElement, FC, ReactElement, ReactNode } from 'react';

interface IAccordionItemProps {
  value: string;
  icon: ReactElement<React.SVGProps<SVGSVGElement>>;
  title: string;
  children: ReactNode[] | ReactNode;
  'data-testid'?: string;
}

export const AccordionItem: FC<IAccordionItemProps> = ({
  icon,
  children,
  title,
  value,
  'data-testid': testId,
}) => {
  return (
    <ShadncAccordionItem value={value} className="border-white/20">
      <AccordionTrigger
        data-testid={testId}
        className="[&>svg]:size-6 [&>svg]:text-white"
      >
        <div className="w-fit flex items-center gap-[12px]">
          {cloneElement(icon, { className: 'size-7' })}
          <h3 className="text-base">{title}</h3>
        </div>
      </AccordionTrigger>
      <AccordionContent>
        <div className="flex flex-col gap-6 py-3 pl-4">{children}</div>
      </AccordionContent>
    </ShadncAccordionItem>
  );
};
