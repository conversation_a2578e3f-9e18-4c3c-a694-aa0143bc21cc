import { LoaderCircle } from 'lucide-react';
import { cn } from '../../shadcn/lib/utils';

interface ILoaderProps {
  color?: 'white' | 'accent';
}

export const Loader = ({ color = 'accent' }: ILoaderProps) => {
  return (
    <div className="w-full h-full flex justify-center items-center ">
      <LoaderCircle
        className={cn(
          'size-8 animate-spin',
          color === 'white' ? 'text-white' : 'text-accent',
        )}
      />
    </div>
  );
};
