import { Switch as ShadcnSwitch } from '@/shadcn/components/ui/switch';
import { FC } from 'react';

interface ISwitchProps {
  id: string;
  title: string;
  description: string;
  value?: boolean;
  setValue?: (value: boolean) => void;
  disabled?: boolean;
  'data-testid'?: string;
}

export const Switch: FC<ISwitchProps> = ({
  title,
  description,
  id,
  disabled,
  value,
  setValue,
  'data-testid': dataTestId,
}) => {
  return (
    <div className="w-full grid grid-cols-[1fr_auto] items-center gap-4">
      <label htmlFor={id}>
        <div className="flex flex-col gap-1">
          <h1 className="text-base text-white">{title}</h1>
          <h3 className="text-base text-white/60">{description}</h3>
        </div>
      </label>

      <ShadcnSwitch
        id={id}
        disabled={disabled}
        checked={value ?? true}
        onCheckedChange={(value) => setValue?.(value as boolean)}
        data-testid={dataTestId}
        className="data-[state=unchecked]:!bg-white/20"
      />
    </div>
  );
};
