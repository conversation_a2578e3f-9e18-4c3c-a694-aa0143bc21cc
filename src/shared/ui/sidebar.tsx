import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetHeader,
  Sheet<PERSON><PERSON><PERSON>,
  SheetTrigger,
} from '@/shadcn/components/ui/sheet';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
} from '@/shadcn/components/ui/tooltip';
import { TooltipTrigger } from '@radix-ui/react-tooltip';
import { TriggerButton } from './trigger-button';
import { FC, ReactElement, ReactNode, SVGProps } from 'react';
import { X } from 'lucide-react';
import { cn } from '@/shadcn/lib/utils';

interface ISidebarProps {
  title: string;
  disabled: boolean;
  tooltip: string;
  triggerIcon: ReactElement<SVGProps<SVGSVGElement>>;
  children: ReactNode;
  contentClassName?: string;
  contentContainerClassName?: string;
  indicator?: number;
  hideHeader?: boolean;
  open?: boolean;
  setOpen?: (open: boolean) => void;
  'data-testid'?: string;
  'indicator-testid'?: string;
}

export const Sidebar: FC<ISidebarProps> = ({
  disabled,
  tooltip,
  triggerIcon,
  title,
  children,
  contentClassName,
  contentContainerClassName,
  indicator,
  hideHeader = false,
  open,
  setOpen,
  'data-testid': testId,
  'indicator-testid': indicatorTestId,
}) => {
  return (
    <TooltipProvider>
      <Sheet modal={false} open={open} onOpenChange={setOpen}>
        {open === undefined && (
          <Tooltip>
            <TooltipTrigger asChild>
              <SheetTrigger asChild>
                {indicator && indicator > 0 ? (
                  <span className="relative">
                    <div
                      data-testid={indicatorTestId || 'sidebar-indicator'}
                      className="absolute top-0.5 right-0.5 w-5 h-auto aspect-square rounded-full bg-accent flex justify-center items-center text-sm text-white"
                    >
                      {indicator}
                    </div>
                    <TriggerButton
                      icon={triggerIcon}
                      disabled={disabled}
                      data-testid={testId}
                    />
                  </span>
                ) : (
                  <TriggerButton
                    icon={triggerIcon}
                    disabled={disabled}
                    data-testid={testId}
                  />
                )}
              </SheetTrigger>
            </TooltipTrigger>

            <TooltipContent side="left">
              <p className="text-center">{tooltip}</p>
            </TooltipContent>
          </Tooltip>
        )}

        <SheetContent
          container={document.querySelector('#main-class-area')}
          className={cn(
            'bg-black/70 backdrop-blur-[10px] optimized-blur text-white p-4 pb-12 min-w-screen min-[600px]:min-w-[85%] border-l border-white/20 [&>button:first-of-type]:hidden overflow-y-auto scrollbar min-[1200px]:!max-w-[650px] min-[1200px]:!w-fit has-[.fullscreen]:!min-w-full transition-all duration-100',
            contentClassName,
          )}
          style={{
            paddingRight: 'max(env(safe-area-inset-right), 1rem)',
            paddingLeft: 'max(env(safe-area-inset-left), 1rem)',
            paddingTop: 'max(env(safe-area-inset-top), 1rem)',
            paddingBottom: 'max(env(safe-area-inset-bottom), 3rem)',
          }}
          onOpenAutoFocus={(e) => e.preventDefault()}
          onCloseAutoFocus={(e) => e.preventDefault()}
        >
          <div
            className={cn('flex-1 flex flex-col', contentContainerClassName)}
            id="sidebar-content-container"
          >
            {!hideHeader && (
              <SheetHeader className="p-0 mb-2 flex flex-row items-center justify-between gap-4 static">
                <SheetTitle className="text-white text-[17.1px] font-semibold">
                  {title}
                </SheetTitle>
                <SheetDescription />
                <div className="p-1">
                  <SheetClose className="cursor-pointer" asChild>
                    <X className="size-6 text-white hover:opacity-100 opacity-90 transition-opacity duration-300" />
                  </SheetClose>
                </div>
              </SheetHeader>
            )}

            {children}
          </div>
        </SheetContent>
      </Sheet>
    </TooltipProvider>
  );
};
