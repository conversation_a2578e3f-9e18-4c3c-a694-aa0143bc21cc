import { FC, ReactNode } from 'react';
import {
  SelectContent,
  SelectTrigger,
  SelectValue,
  Select as ShadcnSelect,
} from '@/shadcn/components/ui/select';

interface ISelectProps {
  id: string;
  title: string;
  description: string;
  placeholder: string;
  disabled?: boolean;
  value?: string;
  onValueChange?: (value: string) => void;
  children?: ReactNode[];
}

export const Select: FC<ISelectProps> = ({
  id,
  title,
  placeholder,
  description,
  disabled,
  value,
  onValueChange,
  children,
}) => {
  return (
    <div className="w-full grid grid-cols-[1fr_auto] items-center gap-4">
      <label htmlFor={id}>
        <div className="flex flex-col gap-1">
          <h1 className="text-base text-white">{title}</h1>
          <h3 className="text-md text-white/60">{description}</h3>
        </div>
      </label>

      <ShadcnSelect
        value={value}
        disabled={disabled}
        onValueChange={onValueChange}
      >
        <SelectTrigger className="border-white/20 !text-white [&>svg]:!text-white">
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent container={document.querySelector('#main-class-area')}>
          {children}
        </SelectContent>
      </ShadcnSelect>
    </div>
  );
};
