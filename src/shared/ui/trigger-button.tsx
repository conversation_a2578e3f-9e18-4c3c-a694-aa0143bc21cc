import { cloneElement, forwardRef, ReactElement, SVGProps } from 'react';

interface ITriggerButtonProps {
  disabled: boolean;
  icon: ReactElement<SVGProps<SVGSVGElement>>;
  'data-testid'?: string;
}

export const TriggerButton = forwardRef<HTMLButtonElement, ITriggerButtonProps>(
  ({ icon, 'data-testid': testId, ...props }, ref) => {
    const getStyledIcon = (icon: ReactElement<React.SVGProps<SVGSVGElement>>) =>
      cloneElement(icon, {
        className: 'w-6 h-auto aspect-square',
      });

    return (
      <button
        {...props}
        ref={ref}
        data-testid={testId}
        className="text-white p-3 rounded-full max-[1200px]:p-2 enabled:hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
      >
        {getStyledIcon(icon)}
      </button>
    );
  },
);

TriggerButton.displayName = 'TriggerButton';
