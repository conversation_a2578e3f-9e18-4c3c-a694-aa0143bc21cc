import { Toaster } from 'sonner';
import { createPortal } from 'react-dom';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shadcn/components/ui/dialog';
import { Button } from '@/shadcn/components/ui/button';
import { removeNotification } from '../lib';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../hooks';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router';

export const Notifications = () => {
  const { t } = useTranslation();
  const { pathname } = useLocation();
  const { notifications } = useAppSelector((state) => state.notifications);
  const [portalTarget, setPortalTarget] = useState(
    document.querySelector('#main-class-area') ?? document.body,
  );
  const dispatch = useAppDispatch();

  const toaster = createPortal(
    <Toaster
      closeButton
      duration={6000}
      position="top-right"
      expand
      theme="light"
    />,
    portalTarget,
  );

  useEffect(() => {
    const newTarget =
      document.querySelector('#main-class-area') ?? document.body;
    setPortalTarget(newTarget);
  }, [pathname]);

  return (
    <>
      {toaster}

      {notifications.map(({ id, title, description, allowFullscreen }) => (
        <Dialog
          key={id}
          open={true}
          onOpenChange={() => dispatch(removeNotification(id))}
        >
          <DialogContent
            onOpenAutoFocus={(e) => e.preventDefault()}
            onCloseAutoFocus={(e) => e.preventDefault()}
            container={
              allowFullscreen
                ? document.querySelector('#main-class-area')
                : document.body
            }
          >
            <DialogHeader>
              <DialogTitle className="text-2xl">{title}</DialogTitle>
              <DialogDescription
                className="text-base text-center"
                dangerouslySetInnerHTML={{ __html: description }}
                data-testid="notification-description"
              />
            </DialogHeader>
            <DialogFooter>
              <DialogClose asChild>
                <Button 
                variant="destructive" 
                size="lg" 
                className="mx-auto"
                data-testid="notification-close-button"
                >
                  {t('notifications.popup.close')}
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      ))}
    </>
  );
};
