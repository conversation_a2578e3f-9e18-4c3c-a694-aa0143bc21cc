import axios, { AxiosError, InternalAxiosRequestConfig } from 'axios';
import Cookies from 'universal-cookie';
import { SERVER_URL } from '../constants';

const cookies = new Cookies();

export const api = axios.create({
  baseURL: SERVER_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export async function retry<T>(
  fn: () => Promise<T>,
  retries = 3,
  delayMs = 1000,
): Promise<T> {
  let lastError;
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await fn();
    } catch (err) {
      lastError = err;
      console.warn(`publish attempt ${attempt} failed`, err);
      if (attempt < retries) {
        await new Promise((res) => setTimeout(res, delayMs));
      }
    }
  }
  throw lastError;
}

// Helpers for cookie management
const getToken = () => cookies.get('token');
const getRefreshToken = () => cookies.get('refreshToken');
const setToken = (token: string, expiresAt: number) =>
  cookies.set('token', token, {
    path: '/',
    maxAge: expiresAt,
    secure: true,
    sameSite: 'lax',
  });
const setRefreshToken = (token: string, expiresAt: number) =>
  cookies.set('refreshToken', token, {
    path: '/',
    maxAge: expiresAt,
    secure: true,
    sameSite: 'lax',
  });

const clearAuthCookies = () => {
  cookies.remove('token', { path: '/' });
  cookies.remove('refreshToken', { path: '/' });
};

// Attach token to headers
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error: AxiosError) => Promise.reject(error),
);

// Intercept 401 responses
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    if (!error.config) return Promise.reject(error);

    const originalRequest = error.config as InternalAxiosRequestConfig;

    if (error.response && error.response.status === 401) {
      try {
        const refreshToken = getRefreshToken();
        const { data } = await axios.post(
          `${SERVER_URL}/auth/refresh`,
          { refreshToken },
          { headers: { 'Content-Type': 'application/json' } },
        );

        setToken(data.token, data.expiresAt);
        setRefreshToken(data.refreshToken, data.expiresAt);
        originalRequest.headers.Authorization = `Bearer ${data.token}`;
        return api(originalRequest);
      } catch (refreshError) {
        clearAuthCookies();

        const anonymousUserId = cookies.get('anonymousUserId');
        const nickname = cookies.get('nickname');

        if (anonymousUserId && nickname) {
          try {
            const response = await fetch(SERVER_URL + '/auth/login/anonymous', {
              method: 'POST',
              body: JSON.stringify({ userId: anonymousUserId, nickname }),
            });

            if (!response.ok) {
              let errorMessage = 'Failed to login an anonymous user!';
              try {
                const errorData = await response.json();
                errorMessage = errorData.error || errorMessage;
              } catch (parseError) {
                console.log(parseError);
              }
              throw new Error(errorMessage);
            }

            const data = await response.json();
            setToken(data.token, data.expiresAt);
            setRefreshToken(data.refreshToken, data.expiresAt);

            originalRequest.headers.Authorization = `Bearer ${data.token}`;
            return api(originalRequest);
          } catch (anonLoginError) {
            window.location.href = '/';
            return Promise.reject(anonLoginError);
          }
        } else {
          window.location.href = '/';
        }

        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  },
);
