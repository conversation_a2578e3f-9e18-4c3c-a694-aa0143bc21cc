/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { createClassroom } from './createClassroom';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { post: vi.fn() },
}));

describe('createClassroom', () => {
  let mockPost: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockPost = api.post as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' && error !== null && 'response' in error,
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  it('resolves with the new classroom id on success', async () => {
    const mockId = 'abc123';
    // Mock response structure matches service expectation: response.data.data.id
    mockPost.mockResolvedValueOnce({ data: { data: { id: mockId } } });

    const id = await createClassroom({ title: 'Test', description: 'Desc' });

    expect(id).toBe(mockId);
    expect(mockPost).toHaveBeenCalledWith('/classrooms', {
      title: 'Test', // Changed from 'name' to 'title'
      description: 'Desc',
      settings: {
        defaultLanguage: 'en',
        translationLanguages: ['en', 'es', 'fr'],
        enableTranscription: true,
        enableTranslation: true,
        enableRecording: true,
        maxParties: 2,
        maxWatchers: 10,
        egressLayout: 'grid',
        canAnonymouslyJoin: true,
      },
    });
  });

  it('throws generic on non-Axios error', async () => {
    isAxiosErrorSpy.mockReturnValue(false);
    mockPost.mockRejectedValueOnce(new Error('oops'));

    await expect(
      createClassroom({ title: 'X', description: 'Y' }),
    ).rejects.toThrow('Failed to create a classroom!');
  });

  it('throws server error message when present', async () => {
    const err = {
      response: { data: { error: 'Name already taken' } },
    };
    isAxiosErrorSpy.mockReturnValue(true);
    mockPost.mockRejectedValueOnce(err);

    await expect(
      createClassroom({ title: 'X', description: 'Y' }),
    ).rejects.toThrow('Name already taken');
  });

  it('falls back to generic when no server message', async () => {
    const err = {
      response: { data: {} },
    };
    isAxiosErrorSpy.mockReturnValue(true);
    mockPost.mockRejectedValueOnce(err);

    await expect(
      createClassroom({ title: 'X', description: 'Y' }),
    ).rejects.toThrow('Failed to create a classroom!');
  });
});
