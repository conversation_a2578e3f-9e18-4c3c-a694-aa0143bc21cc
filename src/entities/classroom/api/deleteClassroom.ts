import { api } from '@/shared';
import axios from 'axios';

export const deleteClassroom = async (id: string): Promise<void> => {
  try {
    await api.delete(`/classrooms/${id}`);
  } catch (error) {
    let errorMessage = 'Failed to delete classroom!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
