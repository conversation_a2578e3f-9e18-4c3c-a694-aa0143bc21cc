import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import type { Classroom } from '../types';
import { getClassrooms } from './getClassrooms';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { get: vi.fn() },
}));

describe('getClassrooms', () => {
  let mockGet: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockGet = api.get as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' &&
          error !== null &&
          'response' in (error as object),
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  const mockClassroom: Classroom = {
    id: 'c1',
    title: 'Room 1',
    description: 'Desc 1',
    createdBy: 'user1',
    creator: 'User One',
    status: 'active',
    settings: {
      defaultLanguage: 'en',
      translationLanguages: ['en', 'es'],
      enableTranscription: true,
      enableTranslation: false,
      enableRecording: false,
      maxParties: 2,
      maxWatchers: 5,
      egressLayout: 'grid',
      canAnonymousJoin: false,
    },
    participants: [],
    activeClass: 'classA',
    createdAt: 1670000000000,
    updatedAt: 1670001000000,
    isAnonymous: false,
    defaultClassSettings: {
      allowRecording: true,
      allowTranscription: true,
      allowTranslation: true,
      allowCaptions: true,
      allowSharing: true,
      allowMaterialsUpload: true,
      allowMaterialsDownload: true,
      allowChat: true,
      allowAnonymousJoin: true,
      allowInviteGuests: true,
      allowMultipleTeachers: true,
      allowMultipleStudents: true,
      allowComments: true,
    },
  };

  const mockApiResponse = {
    data: {
      items: [mockClassroom],
      pagination: {
        page: 1,
        limit: 10,
        total: 1,
        total_pages: 1,
        has_more: false,
      },
    },
    meta: {
      request_id: 'req-123',
      timestamp: '2023-01-01T00:00:00Z',
    },
  };

  it('should return full API response on success with no parameters', async () => {
    mockGet.mockResolvedValue({ data: mockApiResponse });

    const result = await getClassrooms();

    expect(mockGet).toHaveBeenCalledWith('/classrooms?', {
      headers: { 'Content-Type': 'application/json' },
    });
    expect(result).toEqual(mockApiResponse);
  });

  it('should handle page parameter', async () => {
    mockGet.mockResolvedValue({ data: mockApiResponse });

    await getClassrooms({ page: 2 });

    expect(mockGet).toHaveBeenCalledWith('/classrooms?page=2', {
      headers: { 'Content-Type': 'application/json' },
    });
  });

  it('should handle limit parameter', async () => {
    mockGet.mockResolvedValue({ data: mockApiResponse });

    await getClassrooms({ limit: 20 });

    expect(mockGet).toHaveBeenCalledWith('/classrooms?limit=20', {
      headers: { 'Content-Type': 'application/json' },
    });
  });

  it('should handle sort_by parameter', async () => {
    mockGet.mockResolvedValue({ data: mockApiResponse });

    await getClassrooms({ sort_by: 'created_at' });

    expect(mockGet).toHaveBeenCalledWith('/classrooms?sort_by=created_at', {
      headers: { 'Content-Type': 'application/json' },
    });
  });

  it('should handle all parameters together', async () => {
    mockGet.mockResolvedValue({ data: mockApiResponse });

    await getClassrooms({
      page: 3,
      limit: 25,
      sort_by: 'updated_at',
    });

    expect(mockGet).toHaveBeenCalledWith(
      '/classrooms?page=3&limit=25&sort_by=updated_at',
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );
  });

  it('should handle empty parameters object', async () => {
    mockGet.mockResolvedValue({ data: mockApiResponse });

    await getClassrooms({});

    expect(mockGet).toHaveBeenCalledWith('/classrooms?', {
      headers: { 'Content-Type': 'application/json' },
    });
  });

  it('should ignore falsy parameters', async () => {
    mockGet.mockResolvedValue({ data: mockApiResponse });

    await getClassrooms({
      page: 0, // falsy, should be ignored
      limit: 5,
      sort_by: '', // falsy, should be ignored
    });

    expect(mockGet).toHaveBeenCalledWith('/classrooms?limit=5', {
      headers: { 'Content-Type': 'application/json' },
    });
  });

  it('should return response with multiple classrooms', async () => {
    const secondClassroom: Classroom = {
      ...mockClassroom,
      id: 'c2',
      title: 'Room 2',
      description: 'Desc 2',
    };

    const multipleClassroomsResponse = {
      ...mockApiResponse,
      data: {
        ...mockApiResponse.data,
        items: [mockClassroom, secondClassroom],
        pagination: {
          ...mockApiResponse.data.pagination,
          total: 2,
        },
      },
    };

    mockGet.mockResolvedValue({ data: multipleClassroomsResponse });

    const result = await getClassrooms();

    expect(result.data.items).toHaveLength(2);
    expect(result.data.items[0]).toEqual(mockClassroom);
    expect(result.data.items[1]).toEqual(secondClassroom);
  });

  it('should throw provided error message when API error with response', async () => {
    const errorResponse = {
      response: { data: { error: 'Access denied' } },
    } as AxiosError;
    mockGet.mockRejectedValue(errorResponse);

    await expect(getClassrooms()).rejects.toThrow('Access denied');
  });

  it('should throw default message when response has no error message', async () => {
    const errorResponse = {
      response: { data: {} },
    } as AxiosError;
    mockGet.mockRejectedValue(errorResponse);

    await expect(getClassrooms()).rejects.toThrow('Failed to get classrooms!');
  });

  it('should throw default message when response data is null', async () => {
    const errorResponse = {
      response: { data: null },
    } as AxiosError;
    mockGet.mockRejectedValue(errorResponse);

    await expect(getClassrooms()).rejects.toThrow('Failed to get classrooms!');
  });

  it('should throw default message on non-Axios error', async () => {
    mockGet.mockRejectedValue(new Error('Network timeout'));
    isAxiosErrorSpy.mockReturnValue(false);

    await expect(getClassrooms()).rejects.toThrow('Failed to get classrooms!');
  });

  it('should handle pagination metadata correctly', async () => {
    const paginatedResponse = {
      ...mockApiResponse,
      data: {
        ...mockApiResponse.data,
        pagination: {
          page: 2,
          limit: 5,
          total: 15,
          total_pages: 3,
          has_more: true,
        },
      },
    };

    mockGet.mockResolvedValue({ data: paginatedResponse });

    const result = await getClassrooms({ page: 2, limit: 5 });

    expect(result.data.pagination).toEqual({
      page: 2,
      limit: 5,
      total: 15,
      total_pages: 3,
      has_more: true,
    });
  });

  it('should handle meta information correctly', async () => {
    mockGet.mockResolvedValue({ data: mockApiResponse });

    const result = await getClassrooms();

    expect(result.meta).toEqual({
      request_id: 'req-123',
      timestamp: '2023-01-01T00:00:00Z',
    });
  });
});
