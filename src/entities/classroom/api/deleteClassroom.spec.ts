import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { deleteClassroom } from './deleteClassroom';

describe('deleteClassroom()', () => {
  let originalDelete: typeof api.delete;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    originalDelete = api.delete;
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' &&
          error !== null &&
          'response' in (error as object),
      );
    vi.clearAllMocks();
  });

  afterEach(() => {
    api.delete = originalDelete;
    isAxiosErrorSpy.mockRestore();
  });

  it('should call delete API and resolve on success', async () => {
    api.delete = vi.fn().mockResolvedValue({});
    await expect(deleteClassroom('room1')).resolves.toBeUndefined();
    expect(api.delete).toHaveBeenCalledWith('/classrooms/room1');
  });

  it('should throw provided error message when API error with response', async () => {
    const errorResponse = {
      response: { data: { error: 'Delete error' } },
    } as AxiosError;
    api.delete = vi.fn().mockRejectedValue(errorResponse);

    await expect(deleteClassroom('room1')).rejects.toThrow('Delete error');
  });

  it('should throw default message on non-Axios error', async () => {
    api.delete = vi.fn().mockRejectedValue(new Error('Fail'));
    isAxiosErrorSpy.mockReturnValue(false);

    await expect(deleteClassroom('room1')).rejects.toThrow(
      'Failed to delete classroom!',
    );
  });
});
