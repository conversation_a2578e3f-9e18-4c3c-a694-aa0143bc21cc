import { api } from '@/shared';
import axios from 'axios';
import { Classroom } from '../types';

interface GetClassroomsParams {
  page?: number;
  limit?: number;
  sort_by?: string;
}

interface ClassroomsApiResponse {
  data: {
    items: Classroom[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      total_pages: number;
      has_more: boolean;
    };
  };
  meta: {
    request_id: string;
    timestamp: string;
  };
}

export const getClassrooms = async (
  params?: GetClassroomsParams,
): Promise<ClassroomsApiResponse> => {
  try {
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.sort_by) queryParams.set('sort_by', params.sort_by);

    const response = await api.get<ClassroomsApiResponse>(
      `/classrooms?${queryParams.toString()}`,
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );

    return response.data;
  } catch (error) {
    let errorMessage = 'Failed to get classrooms!';
    if (axios.isAxiosError(error) && error.response && error.response.data) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
