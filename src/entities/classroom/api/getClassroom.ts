import { api } from '@/shared';
import axios from 'axios';
import { Classroom } from '../types';

export const getClassroom = async (id: string): Promise<Classroom> => {
  try {
    const response = await api.get<{ data: Classroom }>(`/classrooms/${id}`, {
      headers: { 'Content-Type': 'application/json' },
    });
    return response.data.data;
  } catch (error) {
    let errorMessage = 'Failed to get classroom!';
    if (axios.isAxiosError(error) && error.response?.data?.error) {
      errorMessage = error.response.data.error;
      if (error.status === 404) {
        errorMessage = 'not_found';
      }
    }
    throw new Error(errorMessage);
  }
};
