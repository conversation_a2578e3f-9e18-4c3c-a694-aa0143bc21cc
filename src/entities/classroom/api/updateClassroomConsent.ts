import { api } from '@/shared';
import axios from 'axios';

export const updateClassroomConsent = async (
  classroomId: string,
  recording: boolean,
  aiAgent: boolean,
) => {
  try {
    await api.put(`/classrooms/${classroomId}/consent`, {
      canRecord: recording,
      canAIProcess: aiAgent,
    });
  } catch (error) {
    let errorMessage = 'Failed to join the class!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
