import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { joinClassroom } from './joinClassroom';

describe('joinClassroom()', () => {
  let originalPost: typeof api.post;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;
  let consoleLogSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    vi.clearAllMocks(); // Clear mocks first

    originalPost = api.post;
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' &&
          error !== null &&
          'response' in (error as object),
      );
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    api.post = originalPost;
    isAxiosErrorSpy.mockRestore();
    consoleLogSpy.mockRestore();
  });

  it('should return data on successful join', async () => {
    const fakeData = { success: true };
    // Function returns response.data.data, so mock should wrap in data property
    api.post = vi.fn().mockResolvedValue({ data: { data: fakeData } });

    const result = await joinClassroom({
      id: 'room1',
      invitationToken: 'token123',
    });

    // Fix: The actual API call path is different from test expectation
    expect(api.post).toHaveBeenCalledWith(
      '/classrooms/room1/invitations/accept',
      {
        token: 'token123',
      },
    );
    expect(result).toEqual(fakeData);
  });

  it('should throw provided error message on API axios error with response', async () => {
    const errorResponse = {
      response: { data: { error: 'Join error' } },
    } as AxiosError;
    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(
      joinClassroom({ id: 'room1', invitationToken: 'token123' }),
    ).rejects.toThrow('Join error');
    expect(consoleLogSpy).toHaveBeenCalledWith(errorResponse.response);
  });

  it('should throw default message when axios error has no response.data.error', async () => {
    const errorResponse = { response: { data: {} } } as AxiosError;
    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(
      joinClassroom({ id: 'room1', invitationToken: 'token123' }),
    ).rejects.toThrow('Failed to join a classroom!');
    expect(consoleLogSpy).toHaveBeenCalledWith(errorResponse.response);
  });

  it('should throw default message on non-axios error', async () => {
    api.post = vi.fn().mockRejectedValue(new Error('Network fail'));
    isAxiosErrorSpy.mockReturnValue(false);

    await expect(
      joinClassroom({ id: 'room1', invitationToken: 'token123' }),
    ).rejects.toThrow('Failed to join a classroom!');
    expect(consoleLogSpy).not.toHaveBeenCalled();
  });

  it('should throw default message when axios error has no response', async () => {
    const errorResponse = { message: 'Network Error' } as AxiosError;
    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(
      joinClassroom({ id: 'room1', invitationToken: 'token123' }),
    ).rejects.toThrow('Failed to join a classroom!');
    expect(consoleLogSpy).not.toHaveBeenCalled();
  });
});
