import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { updateClassroom } from './updateClassroom';
import { Classroom } from '../types';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { put: vi.fn() },
}));

describe('updateClassroom()', () => {
  let mockPut: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockPut = api.put as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' &&
          error !== null &&
          'response' in (error as object),
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  it('should update classroom and return updated data on success', async () => {
    const fakeClassroom: Classroom = {
      id: 'u1',
      title: 'Updated',
      description: 'Desc',
      createdBy: 'user1',
      creator: 'user1',
      status: 'active',
      settings: {
        defaultLanguage: 'en',
        translationLanguages: ['en', 'es', 'fr'],
        enableTranscription: true,
        enableTranslation: true,
        enableRecording: true,
        maxParties: 2,
        maxWatchers: 10,
        egressLayout: 'grid',
        canAnonymousJoin: true,
      },
      participants: [],
      activeClass: 'class1',
      createdAt: 1,
      updatedAt: 2,
      isAnonymous: false,
      defaultClassSettings: {
        allowRecording: true,
        allowTranscription: true,
        allowTranslation: true,
        allowCaptions: true,
        allowSharing: true,
        allowMaterialsUpload: true,
        allowMaterialsDownload: true,
        allowChat: true,
        allowAnonymousJoin: true,
        allowInviteGuests: true,
        allowMultipleTeachers: true,
        allowMultipleStudents: true,
        allowComments: true,
      },
    };
    mockPut.mockResolvedValue({ data: fakeClassroom });

    await expect(
      updateClassroom('room1', { title: 'Updated', description: 'Desc' }),
    ).resolves.toEqual(fakeClassroom);

    expect(mockPut).toHaveBeenCalledWith('/classrooms/room1', {
      title: 'Updated',
      description: 'Desc',
      settings: {
        defaultLanguage: 'en',
        translationLanguages: ['en', 'es', 'fr'],
        enableTranscription: true,
        enableTranslation: true,
        enableRecording: true,
        maxParties: 2,
        maxWatchers: 10,
        egressLayout: 'grid',
        canAnonymouslyJoin: true,
      },
    });
  });

  it('should handle empty description by converting to space', async () => {
    const fakeClassroom: Classroom = {
      id: 'u1',
      title: 'Test',
      description: ' ',
      createdBy: 'user1',
      creator: 'user1',
      status: 'active',
      settings: {
        defaultLanguage: 'en',
        translationLanguages: ['en', 'es', 'fr'],
        enableTranscription: true,
        enableTranslation: true,
        enableRecording: true,
        maxParties: 2,
        maxWatchers: 10,
        egressLayout: 'grid',
        canAnonymousJoin: true,
      },
      participants: [],
      activeClass: 'class1',
      createdAt: 1,
      updatedAt: 2,
      isAnonymous: false,
      defaultClassSettings: {
        allowRecording: true,
        allowTranscription: true,
        allowTranslation: true,
        allowCaptions: true,
        allowSharing: true,
        allowMaterialsUpload: true,
        allowMaterialsDownload: true,
        allowChat: true,
        allowAnonymousJoin: true,
        allowInviteGuests: true,
        allowMultipleTeachers: true,
        allowMultipleStudents: true,
        allowComments: true,
      },
    };
    mockPut.mockResolvedValue({ data: fakeClassroom });

    await updateClassroom('room1', { title: 'Test', description: '' });

    expect(mockPut).toHaveBeenCalledWith('/classrooms/room1', {
      title: 'Test',
      description: ' ', // Empty string converted to space
      settings: {
        defaultLanguage: 'en',
        translationLanguages: ['en', 'es', 'fr'],
        enableTranscription: true,
        enableTranslation: true,
        enableRecording: true,
        maxParties: 2,
        maxWatchers: 10,
        egressLayout: 'grid',
        canAnonymouslyJoin: true,
      },
    });
  });

  it('should throw provided error message when API error with response', async () => {
    const errorResponse = {
      response: { data: { error: 'Update error' } },
    } as AxiosError;
    mockPut.mockRejectedValue(errorResponse);

    await expect(
      updateClassroom('room1', { title: 'X', description: '' }),
    ).rejects.toThrow('Update error');
  });

  it('should throw default message on non-Axios error', async () => {
    mockPut.mockRejectedValue(new Error('Fail'));
    isAxiosErrorSpy.mockReturnValue(false);

    await expect(
      updateClassroom('room1', { title: 'X', description: '' }),
    ).rejects.toThrow('Failed to get classroom!');
  });
});
