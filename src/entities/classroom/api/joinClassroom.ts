import { api } from '@/shared';
import axios from 'axios';

export const joinClassroom = async ({
  id,
  invitationToken,
}: {
  id: string;
  invitationToken: string;
}) => {
  try {
    const response = await api.post(`/classrooms/${id}/invitations/accept`, {
      token: invitationToken,
    });
    return response.data.data;
  } catch (error) {
    let errorMessage = 'Failed to join a classroom!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
      console.log(error.response);
    }
    throw new Error(errorMessage);
  }
};
