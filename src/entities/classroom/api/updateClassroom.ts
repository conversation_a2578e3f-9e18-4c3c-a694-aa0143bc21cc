import { api } from '@/shared';
import { Classroom } from '../types';
import axios from 'axios';

export const updateClassroom = async (
  id: string,
  data: { title: string; description: string },
): Promise<Classroom> => {
  try {
    const response = await api.put<Classroom>(`/classrooms/${id}`, {
      title: data.title,
      description: data.description ? data.description : ' ',
      settings: {
        defaultLanguage: 'en',
        translationLanguages: ['en', 'es', 'fr'],
        enableTranscription: true,
        enableTranslation: true,
        enableRecording: true,
        maxParties: 2,
        maxWatchers: 10,
        egressLayout: 'grid',
        canAnonymouslyJoin: true,
      },
    });

    return response.data;
  } catch (error) {
    let errorMessage = 'Failed to get classroom!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
