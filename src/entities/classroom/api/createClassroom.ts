import { api } from '@/shared';
import axios from 'axios';

export const createClassroom = async ({
  title,
  description,
}: {
  title: string;
  description: string;
}): Promise<string> => {
  try {
    const response = await api.post('/classrooms', {
      title,
      description,
      settings: {
        defaultLanguage: 'en',
        translationLanguages: ['en', 'es', 'fr'],
        enableTranscription: true,
        enableTranslation: true,
        enableRecording: true,
        maxParties: 2,
        maxWatchers: 10,
        egressLayout: 'grid',
        canAnonymouslyJoin: true,
      },
    });

    return response.data.data.id;
  } catch (error) {
    let errorMessage = 'Failed to create a classroom!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
