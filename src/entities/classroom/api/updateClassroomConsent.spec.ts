/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { api } from '@/shared';
import { updateClassroomConsent } from './updateClassroomConsent';

// Mock api from @/shared
vi.mock('@/shared', () => ({
  api: {
    put: vi.fn(),
  },
}));

describe('updateConsent', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('calls api.put with correct URL and payload', async () => {
    (api.put as any).mockResolvedValueOnce({});

    await updateClassroomConsent('classroom-123', true, false);

    expect(api.put).toHaveBeenCalledWith('/classrooms/classroom-123/consent', {
      canRecord: true,
      canAIProcess: false,
    });
  });

  it('throws default error message if non-axios error occurs', async () => {
    (api.put as any).mockRejectedValueOnce(new Error('Network issue'));

    await expect(updateClassroomConsent('room1', false, true)).rejects.toThrow(
      'Failed to join the class!',
    );
  });

  it('throws server-provided error message if axios error contains response error', async () => {
    const axiosError = {
      isAxiosError: true,
      response: { data: { error: 'Invalid consent update' } },
    };
    (api.put as any).mockRejectedValueOnce(axiosError);

    await expect(updateClassroomConsent('room2', true, true)).rejects.toThrow(
      'Invalid consent update',
    );
  });

  it('falls back to default error if axios error has no error field', async () => {
    const axiosError = {
      isAxiosError: true,
      response: { data: {} },
    };
    (api.put as any).mockRejectedValueOnce(axiosError);

    await expect(updateClassroomConsent('room3', true, false)).rejects.toThrow(
      'Failed to join the class!',
    );
  });
});
