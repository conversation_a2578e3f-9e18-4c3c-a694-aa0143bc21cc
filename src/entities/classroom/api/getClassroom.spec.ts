/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, { type AxiosError } from 'axios';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { getClassroom } from './getClassroom';
import { api } from '@/shared';
import type { Classroom, Participant } from '../types';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { get: vi.fn() },
}));

describe('getClassroom', () => {
  let mockGet: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockGet = api.get as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' && error !== null && 'response' in error,
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  it('resolves with classroom data on success', async () => {
    const fakeSettings = {
      defaultLanguage: 'en',
      translationLanguages: ['en', 'es'],
      enableTranscription: true,
      enableTranslation: false,
      enableRecording: true,
      maxParties: 2,
      maxWatchers: 10,
      egressLayout: 'grid',
      canAnonymousJoin: true,
    };

    const fakeParticipant = {
      userId: 'user1',
      displayName: 'User One',
      profileImage: 'https://example.com/avatar.png',
      role: 'student',
      status: 'active',
      joinedAt: 1620000000000,
      leftAt: null,
      isAnonymous: false,
      isConnected: true,
    } as Participant;

    const fakeClassroom: Classroom = {
      id: 'room123',
      title: 'Test Room', // Changed from 'name' to 'title'
      description: 'A test classroom',
      createdBy: 'user1',
      creator: 'user1',
      status: 'active',
      settings: fakeSettings,
      participants: [fakeParticipant],
      activeClass: 'lesson1',
      createdAt: 1620000000000,
      updatedAt: 1620000001000,
      isAnonymous: false,
      defaultClassSettings: {
        allowRecording: true,
        allowTranscription: true,
        allowTranslation: true,
        allowCaptions: true,
        allowSharing: true,
        allowMaterialsUpload: true,
        allowMaterialsDownload: true,
        allowChat: true,
        allowAnonymousJoin: true,
        allowInviteGuests: true,
        allowMultipleTeachers: true,
        allowMultipleStudents: true,
        allowComments: true,
      },
    };

    // Mock response structure matches service expectation: response.data.data
    mockGet.mockResolvedValueOnce({ data: { data: fakeClassroom } });

    const result = await getClassroom('room123');
    expect(result).toEqual(fakeClassroom);
    expect(mockGet).toHaveBeenCalledWith('/classrooms/room123', {
      headers: { 'Content-Type': 'application/json' },
    });
  });

  it('throws generic message on non-Axios error', async () => {
    isAxiosErrorSpy.mockReturnValue(false);
    mockGet.mockRejectedValueOnce(new Error('oops'));

    await expect(getClassroom('room123')).rejects.toThrow(
      'Failed to get classroom!',
    );
  });

  it('throws server-provided error when AxiosError with response.data.error', async () => {
    const err = {
      response: { data: { error: 'Not found' } },
    };
    isAxiosErrorSpy.mockReturnValue(true);
    mockGet.mockRejectedValueOnce(err);

    await expect(getClassroom('room999')).rejects.toThrow('Not found');
  });

  it('falls back to generic when AxiosError without response error message', async () => {
    const err = {
      response: { data: {} },
    };
    isAxiosErrorSpy.mockReturnValue(true);
    mockGet.mockRejectedValueOnce(err);

    await expect(getClassroom('room999')).rejects.toThrow(
      'Failed to get classroom!',
    );
  });
});
