import { Role } from '../../../shared';

export interface Participant {
  userId: string;
  displayName: string;
  profileImage: string;
  role: Role;
  status: string;
  joinedAt: number;
  leftAt: number | null;
  isAnonymous: boolean;
  isConnected: boolean;
  acl: {
    canCommentOnRecordings: boolean;
    canDownloadMaterials: boolean;
    canGuestInvite: boolean;
    canKickParticipant: boolean;
    canParticipantInvite: boolean;
    canStartClass: boolean;
    canUpdateClass: boolean;
    canUpdateParticipant: boolean;
    canUploadMaterials: boolean;
    canViewRecordings: boolean;
  };
}
