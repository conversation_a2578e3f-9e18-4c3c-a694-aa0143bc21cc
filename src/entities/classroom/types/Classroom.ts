import { ClassroomSettings } from './ClassroomSettings';
import { Participant } from './Participant';

export interface Classroom {
  id: string;
  title: string;
  description: string;
  createdBy: string;
  creator: string;
  status: string;
  settings: ClassroomSettings;
  defaultClassSettings: {
    allowRecording: boolean;
    allowTranscription: boolean;
    allowTranslation: boolean;
    allowCaptions: boolean;
    allowSharing: boolean;
    allowMaterialsUpload: boolean;
    allowMaterialsDownload: boolean;
    allowChat: boolean;
    allowAnonymousJoin: boolean;
    allowInviteGuests: boolean;
    allowMultipleTeachers: boolean;
    allowMultipleStudents: boolean;
    allowComments: boolean;
  };
  participants: Participant[];
  activeClass: string;
  createdAt: number;
  updatedAt: number;
  isAnonymous: boolean;
  activeClasses?: string[];
}
