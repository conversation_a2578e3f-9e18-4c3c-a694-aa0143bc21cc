import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router';
import { createClassroom } from '../api';
import { useNotifications } from '@/shared';

export const useCreateClassroom = (onSuccess?: (id: string) => void) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { addNotification, addToast } = useNotifications();

  return useMutation({
    mutationFn: ({
      title,
      description,
    }: {
      title: string;
      description: string;
    }) => createClassroom({ title, description }),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['classrooms'] });
      addToast({ text: 'notifications.success.classroomCreated' });
      navigate(`/classrooms/${data}`);

      if (onSuccess) onSuccess(data);
    },
    onError: () => {
      addNotification?.({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
