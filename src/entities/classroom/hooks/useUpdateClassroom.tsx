import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateClassroom } from '../api';
import { useNavigate } from 'react-router';
import { useNotifications } from '@/shared';

export const useUpdateClassroom = (
  id: string,
  setShow?: (show: boolean) => void,
) => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { addNotification, addToast } = useNotifications();

  return useMutation({
    mutationFn: (data: { title: string; description: string }) =>
      updateClassroom(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classrooms'] });
      queryClient.invalidateQueries({ queryKey: ['classroom', id] });
      addToast?.({ text: 'notifications.success.classroomUpdated' });
      if (setShow) {
        setShow(false);
      }
    },
    onError: (error) => {
      if (error.message === 'classroom not found') {
        navigate('/classrooms');
        addNotification?.({
          title: 'notifications.errors.classroomNotFound.title',
          description: 'notifications.errors.classroomNotFound.description',
        });
      } else
        addNotification?.({
          title: 'notifications.errors.serverError.title',
          description: 'notifications.errors.serverError.description',
        });
    },
  });
};
