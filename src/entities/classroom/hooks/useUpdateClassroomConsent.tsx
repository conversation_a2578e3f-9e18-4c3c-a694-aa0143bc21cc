import { useMutation } from '@tanstack/react-query';
import { updateClassroomConsent } from '../api';

export const useUpdateClassroomConsent = (onSuccess: () => void) => {
  return useMutation({
    mutationFn: async ({
      classroomId,
      recording,
      aiAgent,
    }: {
      classroomId: string;
      recording: boolean;
      aiAgent: boolean;
    }) => {
      return updateClassroomConsent(classroomId, recording, aiAgent);
    },
    onSuccess: onSuccess,
  });
};
