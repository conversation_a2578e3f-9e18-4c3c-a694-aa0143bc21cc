import { useQuery } from '@tanstack/react-query';
import { getClassroom } from '../api';
import { useEffect } from 'react';
import {
  Role,
  useACLContext,
  useAppDispatch,
  useClassroomSettingsContext,
} from '@/shared';
import { setUserRole, setUserRoleType, useUser } from '../../user';

export const useClassroom = (id: string) => {
  const dispatch = useAppDispatch();
  const { user } = useUser();

  const { updateACL } = useACLContext();
  const { updateClassroomSettings } = useClassroomSettingsContext();

  const { data, isPending, error } = useQuery({
    queryKey: ['classroom', id],
    queryFn: () => getClassroom(id!),
    retry: 0,
    staleTime: Infinity,
    gcTime: 10 * 60 * 1000,
  });

  useEffect(() => {
    if (data) {
      data.participants.map((participant) => {
        if (participant.userId === user!.id) {
          const role = participant.role as Role;
          const roleType = role !== Role.Guest ? 'party' : 'watcher';
          updateACL(data.id, participant.acl);
          dispatch(setUserRole(role));
          dispatch(setUserRoleType(roleType));
        }
      });

      updateClassroomSettings(data.id, {
        canAnonymousJoin: data.settings.canAnonymousJoin,
        ...data.defaultClassSettings,
      });
    }
  }, [data, user, dispatch]);

  return { data, isPending, error };
};
