import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router';
import { joinClassroom } from '../api';
import { useNotifications } from '@/shared';
import { useJoinClass } from '@/entities';

export const useJoinClassroom = (classroomId?: string) => {
  const navigate = useNavigate();
  const { mutate: joinClass } = useJoinClass();
  const { addNotification } = useNotifications();

  return useMutation({
    mutationFn: (invite: { id: string; invitationToken: string }) =>
      joinClassroom(invite),
    onSuccess: (data) => {
      if (classroomId && data.classroom.activeClasses.length !== 0) {
        joinClass({
          classroomId: data.classroom.id,
          classId: data.classroom.activeClasses[0],
        });
      } else {
        if (data.success) {
          navigate(`/classrooms/${data.classroom.id}`);
        }
      }
    },
    onError: () => {
      if (classroomId) navigate(`/classrooms/${classroomId}`);
      addNotification?.({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
