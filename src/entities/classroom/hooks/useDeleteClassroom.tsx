import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router';
import { deleteClassroom } from '../api';
import { useNotifications } from '@/shared';

export const useDeleteClassroom = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { addNotification, addToast } = useNotifications();

  return useMutation({
    mutationFn: (id: string) => deleteClassroom(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classrooms'] });
      navigate('/classrooms');
      addToast?.({ text: 'notifications.success.classroomDeleted' });
    },
    onError: (error) => {
      if (error.message === 'classroom not found') {
        navigate('/classrooms');
        addNotification?.({
          title: 'notifications.errors.classroomNotFound.title',
          description: 'notifications.errors.classroomNotFound.description',
        });
      } else
        addNotification?.({
          title: 'notifications.errors.serverError.title',
          description: 'notifications.errors.serverError.description',
        });
    },
  });
};
