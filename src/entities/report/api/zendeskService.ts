import { ZENDESK_RPOXY_URL } from '../../../shared';
import { ReportForm } from '../types';

interface TicketResponse {
  id: number;
  url: string;
  subject: string;
  description: string;
  status: string;
  priority: string;
  type: string;
  tags: string[];
  created_at: string;
  updated_at: string;
}

interface ErrorResponse {
  error: string;
  message: string;
  code: number;
}

export async function createReport(
  data: ReportForm,
  files?: File[],
): Promise<TicketResponse> {
  const formData = new FormData();
  formData.append('report', JSON.stringify(data));

  if (files) {
    files.forEach((file) => {
      formData.append('attachments', file);
    });
  }

  const response = await fetch(`${ZENDESK_RPOXY_URL}/api/v1/reports`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const error: ErrorResponse = await response.json();
    throw new Error(error.message);
  }

  return response.json();
}
