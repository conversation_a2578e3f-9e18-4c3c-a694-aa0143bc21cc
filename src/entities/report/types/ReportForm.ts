import { ReportType } from './ReportType';
import { Severity } from './Severity';

export interface ReportForm {
  // Always present
  report_type: ReportType; // drives conditional requirements
  subject?: string; // defaults from description/summary
  description: string; // user text (native comment.body)
  severity?: Severity; // routing/triage
  tags?: string[]; // src:* / kind:* / ctype:* / lang:* analytics
  app_env?: 'dev' | 'test' | 'prod'; // dev/test/prod etc

  // Reporting user details (human flows)
  reporting_user_id?: string; // platform user ID
  reporting_user_display_name?: string;
  reporting_user_email?: string;
  reporting_user_account_type?: 'full_access' | 'guest'; // guest | full_access
  reporting_user_region?: string; // account creation region
  reporting_user_language?: string; // preferred language (BCP‑47)
  reporting_user_device?: string; // e.g., "iOS 17 / iPhone 14"
  reporting_user_browser?: string; // e.g., "Chrome 127.0.0"

  // Classroom & class context (used by human & automod flows)
  classroom_id?: string;
  class_id?: string;
  participants?: string[]; // participants relevant to this item/class
  all_participants?: string[]; // full roster (optionally agent‑only)
  content_type?: string; // post/comment/.../other
  content_url?: string; // permalink to the content
  content_id?: string; // internal content ID
  uploaded_file_url?: string; // direct link to uploaded file
  upload_timestamp?: string; // ISO 8601 timestamp
  recording_url?: string; // class recording URL

  // Auto‑moderation shared fields
  detection_rule?: string; // e.g., TOS_SAFE_2025_07
  model_version?: string; // e.g., v3.1.2
  confidence?: number; // 0..1 scale
  risk_flags?: string[]; // multiselect
  suspected_users?: string[]; // IDs

  // Moderation‑class specific outputs
  moderation_results?: string; // JSON string or concise summary
  transcription?: string; // long text; prefer file attach if large
  class_participants?: string[]; // IDs

  // Human-only helper fields
  issue_location?: string; // tech_issue UX
  reported_username?: string; // report_user username
  reported_user_email?: string; // report_user email
  reported_user_id?: string; // reported user id

  // Attachments
  upload_tokens?: string[]; // Upload tokens from Zendesk Uploads API

  // Agent-only misc
  internal_notes?: string; // diagnostics, traces, etc.
}
