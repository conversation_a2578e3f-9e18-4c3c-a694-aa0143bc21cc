import { useMutation } from '@tanstack/react-query';
import { useNotifications } from '../../../shared';
import { ReportForm } from '../types';
import { createReport } from '../api';

export const useCreateReport = (onSuccess?: () => void) => {
  const { addNotification, addToast } = useNotifications();

  return useMutation({
    mutationFn: (data: { report: ReportForm; files?: File[] }) =>
      createReport(data.report, data.files),
    onSuccess: () => {
      onSuccess?.();
      addToast?.({ text: 'notifications.success.reportCreated' });
    },
    onError: () => {
      addNotification?.({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
