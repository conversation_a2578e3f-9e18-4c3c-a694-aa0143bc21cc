import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { getMaterialDownloadLink } from './getMaterialDownloadLink';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { get: vi.fn() },
}));

describe('Material Download Link API', () => {
  let mockGet: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockGet = api.get as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' && error !== null && 'response' in error,
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  describe('getMaterialDownloadLink', () => {
    const mockDownloadUrl =
      'https://cdn.example.com/files/abc123/material.pdf?token=xyz789&expires=1704240000';

    it('should return download URL when API call succeeds', async () => {
      const mockResponse = {
        data: {
          data: {
            downloadUrl: mockDownloadUrl,
          },
        },
      };

      mockGet.mockResolvedValueOnce(mockResponse);

      const result = await getMaterialDownloadLink(
        'classroom123',
        'material456',
      );

      expect(mockGet).toHaveBeenCalledWith(
        '/classrooms/classroom123/materials/material456/download',
      );
      expect(result).toBe(mockDownloadUrl);
    });

    it('should work with different classroom and material IDs', async () => {
      const testCases = [
        { classroomId: 'abc-123', materialId: 'mat-001' },
        { classroomId: 'classroom-xyz-789', materialId: 'document-456' },
        { classroomId: '12345', materialId: 'video-999' },
        {
          classroomId: 'special-chars_classroom',
          materialId: 'file_with_underscores',
        },
        {
          classroomId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
          materialId: '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
        },
      ];

      for (const { classroomId, materialId } of testCases) {
        const mockResponse = {
          data: { data: { downloadUrl: `https://example.com/${materialId}` } },
        };
        mockGet.mockResolvedValueOnce(mockResponse);

        await getMaterialDownloadLink(classroomId, materialId);

        expect(mockGet).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/materials/${materialId}/download`,
        );
      }
    });

    describe('different download URL formats', () => {
      it('should handle signed S3 URLs', async () => {
        const s3Url =
          'https://my-bucket.s3.amazonaws.com/materials/file.pdf?AWSAccessKeyId=AKIAI44QH8DHBEXAMPLE&Expires=1704240000&Signature=abcd1234';
        const mockResponse = { data: { data: { downloadUrl: s3Url } } };

        mockGet.mockResolvedValueOnce(mockResponse);

        const result = await getMaterialDownloadLink(
          'classroom123',
          'material456',
        );

        expect(result).toBe(s3Url);
      });

      it('should handle CDN URLs with tokens', async () => {
        const cdnUrl =
          'https://cdn.myapp.com/protected/materials/document.pdf?token=jwt_token_here&user=123';
        const mockResponse = { data: { data: { downloadUrl: cdnUrl } } };

        mockGet.mockResolvedValueOnce(mockResponse);

        const result = await getMaterialDownloadLink(
          'classroom123',
          'material456',
        );

        expect(result).toBe(cdnUrl);
      });

      it('should handle simple download URLs', async () => {
        const simpleUrl = 'https://files.example.com/download/material456.pdf';
        const mockResponse = { data: { data: { downloadUrl: simpleUrl } } };

        mockGet.mockResolvedValueOnce(mockResponse);

        const result = await getMaterialDownloadLink(
          'classroom123',
          'material456',
        );

        expect(result).toBe(simpleUrl);
      });

      it('should handle URLs with special characters', async () => {
        const specialUrl =
          'https://example.com/files/My Document (2024) - Final Version.pdf?v=1';
        const mockResponse = { data: { data: { downloadUrl: specialUrl } } };

        mockGet.mockResolvedValueOnce(mockResponse);

        const result = await getMaterialDownloadLink(
          'classroom123',
          'material456',
        );

        expect(result).toBe(specialUrl);
      });
    });

    describe('URL construction validation', () => {
      it('should construct correct endpoint for standard IDs', async () => {
        const mockResponse = {
          data: { data: { downloadUrl: mockDownloadUrl } },
        };
        mockGet.mockResolvedValueOnce(mockResponse);

        await getMaterialDownloadLink('classroom123', 'material456');

        expect(mockGet).toHaveBeenCalledWith(
          '/classrooms/classroom123/materials/material456/download',
        );
      });

      it('should handle IDs with hyphens and underscores', async () => {
        const classroomId = 'classroom-with-dashes_and_underscores';
        const materialId = 'material-with-special-chars_123';
        const mockResponse = {
          data: { data: { downloadUrl: mockDownloadUrl } },
        };

        mockGet.mockResolvedValueOnce(mockResponse);

        await getMaterialDownloadLink(classroomId, materialId);

        expect(mockGet).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/materials/${materialId}/download`,
        );
      });

      it('should handle numeric IDs', async () => {
        const mockResponse = {
          data: { data: { downloadUrl: mockDownloadUrl } },
        };
        mockGet.mockResolvedValueOnce(mockResponse);

        await getMaterialDownloadLink('12345', '67890');

        expect(mockGet).toHaveBeenCalledWith(
          '/classrooms/12345/materials/67890/download',
        );
      });

      it('should handle UUID format IDs', async () => {
        const classroomId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
        const materialId = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';
        const mockResponse = {
          data: { data: { downloadUrl: mockDownloadUrl } },
        };

        mockGet.mockResolvedValueOnce(mockResponse);

        await getMaterialDownloadLink(classroomId, materialId);

        expect(mockGet).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/materials/${materialId}/download`,
        );
      });
    });

    describe('response data validation', () => {
      it('should extract downloadUrl from nested response structure', async () => {
        const expectedUrl = 'https://example.com/download/test.pdf';
        const mockResponse = {
          data: {
            data: {
              downloadUrl: expectedUrl,
            },
          },
        };

        mockGet.mockResolvedValueOnce(mockResponse);

        const result = await getMaterialDownloadLink(
          'classroom123',
          'material456',
        );

        expect(result).toBe(expectedUrl);
      });

      it('should handle response with additional metadata', async () => {
        const expectedUrl = 'https://example.com/download/test.pdf';
        const mockResponse = {
          data: {
            data: {
              downloadUrl: expectedUrl,
              expiresAt: '2024-01-01T00:00:00Z',
              fileSize: 1024000,
              fileName: 'test.pdf',
            },
          },
        };

        mockGet.mockResolvedValueOnce(mockResponse);

        const result = await getMaterialDownloadLink(
          'classroom123',
          'material456',
        );

        // Should only return the downloadUrl, ignoring other fields
        expect(result).toBe(expectedUrl);
      });
    });

    describe('error handling', () => {
      it('should throw axios error for material not found', async () => {
        const errorResponse = {
          response: { data: { error: 'Material not found' } },
        } as AxiosError;
        mockGet.mockRejectedValueOnce(errorResponse);

        await expect(
          getMaterialDownloadLink('classroom123', 'nonexistent-material'),
        ).rejects.toBe(errorResponse);
      });

      it('should throw axios error for classroom not found', async () => {
        const errorResponse = {
          response: { data: { error: 'Classroom not found' } },
        } as AxiosError;
        mockGet.mockRejectedValueOnce(errorResponse);

        await expect(
          getMaterialDownloadLink('nonexistent-classroom', 'material456'),
        ).rejects.toBe(errorResponse);
      });

      it('should throw axios error for access denied', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Access denied to download this material' },
          },
        } as AxiosError;
        mockGet.mockRejectedValueOnce(errorResponse);

        await expect(
          getMaterialDownloadLink('classroom123', 'material456'),
        ).rejects.toBe(errorResponse);
      });

      it('should throw axios error for material unavailable', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Material temporarily unavailable for download' },
          },
        } as AxiosError;
        mockGet.mockRejectedValueOnce(errorResponse);

        await expect(
          getMaterialDownloadLink('classroom123', 'material456'),
        ).rejects.toBe(errorResponse);
      });

      it('should throw axios error for expired link', async () => {
        const errorResponse = {
          response: { data: { error: 'Download link has expired' } },
        } as AxiosError;
        mockGet.mockRejectedValueOnce(errorResponse);

        await expect(
          getMaterialDownloadLink('classroom123', 'material456'),
        ).rejects.toBe(errorResponse);
      });

      it('should throw axios error when API error has no error field', async () => {
        const errorResponse = { response: { data: {} } } as AxiosError;
        mockGet.mockRejectedValueOnce(errorResponse);

        await expect(
          getMaterialDownloadLink('classroom123', 'material456'),
        ).rejects.toBe(errorResponse);
      });

      it('should throw axios error when API error has null error', async () => {
        const errorResponse = {
          response: { data: { error: null } },
        } as AxiosError;
        mockGet.mockRejectedValueOnce(errorResponse);

        await expect(
          getMaterialDownloadLink('classroom123', 'material456'),
        ).rejects.toBe(errorResponse);
      });

      it('should throw axios error when API error has undefined error', async () => {
        const errorResponse = {
          response: { data: { error: undefined } },
        } as AxiosError;
        mockGet.mockRejectedValueOnce(errorResponse);

        await expect(
          getMaterialDownloadLink('classroom123', 'material456'),
        ).rejects.toBe(errorResponse);
      });

      it('should throw axios error when API error has no response', async () => {
        const errorResponse = { response: undefined } as AxiosError;
        mockGet.mockRejectedValueOnce(errorResponse);

        await expect(
          getMaterialDownloadLink('classroom123', 'material456'),
        ).rejects.toBe(errorResponse);
      });

      it('should throw default message when error is not axios error', async () => {
        isAxiosErrorSpy.mockReturnValueOnce(false);
        mockGet.mockRejectedValueOnce(new Error('Network failure'));

        await expect(
          getMaterialDownloadLink('classroom123', 'material456'),
        ).rejects.toThrow('Failed to get material!');
      });

      it('should handle generic error correctly', async () => {
        mockGet.mockRejectedValueOnce(new Error('Some generic error'));

        await expect(
          getMaterialDownloadLink('classroom123', 'material456'),
        ).rejects.toThrow('Failed to get material!');
      });

      it('should throw axios error for timeout', async () => {
        const timeoutError = {
          response: {
            data: { error: 'Request timeout while generating download link' },
          },
        } as AxiosError;
        mockGet.mockRejectedValueOnce(timeoutError);

        await expect(
          getMaterialDownloadLink('classroom123', 'material456'),
        ).rejects.toBe(timeoutError);
      });

      it('should throw axios error for server error', async () => {
        const serverError = {
          response: { data: { error: 'Internal server error' } },
        } as AxiosError;
        mockGet.mockRejectedValueOnce(serverError);

        await expect(
          getMaterialDownloadLink('classroom123', 'material456'),
        ).rejects.toBe(serverError);
      });

      it('should throw axios error for rate limit', async () => {
        const rateLimitError = {
          response: {
            data: {
              error: 'Too many download requests. Please try again later.',
            },
          },
        } as AxiosError;
        mockGet.mockRejectedValueOnce(rateLimitError);

        await expect(
          getMaterialDownloadLink('classroom123', 'material456'),
        ).rejects.toBe(rateLimitError);
      });
    });

    describe('edge cases', () => {
      it('should handle very long URLs', async () => {
        const longUrl =
          'https://very-long-domain-name-example.com/very/long/path/to/material/with/many/subdirectories/and/a/very/long/filename/document.pdf?with=very&long=query&parameters=true&and=more&stuff=here&token=very_long_jwt_token_with_lots_of_data_encoded_inside_it';
        const mockResponse = { data: { data: { downloadUrl: longUrl } } };

        mockGet.mockResolvedValueOnce(mockResponse);

        const result = await getMaterialDownloadLink(
          'classroom123',
          'material456',
        );

        expect(result).toBe(longUrl);
      });

      it('should handle URLs with international characters', async () => {
        const intlUrl = 'https://example.com/files/文档.pdf?用户=123';
        const mockResponse = { data: { data: { downloadUrl: intlUrl } } };

        mockGet.mockResolvedValueOnce(mockResponse);

        const result = await getMaterialDownloadLink(
          'classroom123',
          'material456',
        );

        expect(result).toBe(intlUrl);
      });

      it('should handle empty string URLs (edge case)', async () => {
        const mockResponse = { data: { data: { downloadUrl: '' } } };

        mockGet.mockResolvedValueOnce(mockResponse);

        const result = await getMaterialDownloadLink(
          'classroom123',
          'material456',
        );

        expect(result).toBe('');
      });

      it('should handle data URLs (base64 content)', async () => {
        const dataUrl =
          'data:application/pdf;base64,JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwo=';
        const mockResponse = { data: { data: { downloadUrl: dataUrl } } };

        mockGet.mockResolvedValueOnce(mockResponse);

        const result = await getMaterialDownloadLink(
          'classroom123',
          'material456',
        );

        expect(result).toBe(dataUrl);
      });
    });
  });
});
