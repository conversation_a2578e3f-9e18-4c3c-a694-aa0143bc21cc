import { api } from '@/shared';
import axios from 'axios';

export const getMaterialDownloadLink = async (
  classroomId: string,
  materialId: string,
): Promise<string> => {
  try {
    const response = await api.get<{
      data: { downloadUrl: string };
    }>(`/classrooms/${classroomId}/materials/${materialId}/download`);

    return response.data.data.downloadUrl;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw error;
    }
    throw new Error('Failed to get material!');
  }
};
