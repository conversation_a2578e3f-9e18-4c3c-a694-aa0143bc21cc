import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { deleteMaterial } from './deleteMaterial';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { delete: vi.fn() },
}));

describe('Delete Material API', () => {
  let mockDelete: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockDelete = api.delete as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' && error !== null && 'response' in error,
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  describe('deleteMaterial', () => {
    it('should delete material successfully', async () => {
      mockDelete.mockResolvedValueOnce({ status: 204 });

      await deleteMaterial('classroom123', 'material456');

      expect(mockDelete).toHaveBeenCalledWith(
        '/classrooms/classroom123/materials/material456',
      );
    });

    it('should complete without returning anything', async () => {
      mockDelete.mockResolvedValueOnce({
        status: 200,
        data: { message: 'Deleted' },
      });

      const result = await deleteMaterial('classroom123', 'material456');

      expect(result).toBeUndefined();
    });

    it('should work with different classroom and material IDs', async () => {
      const testCases = [
        { classroomId: 'abc-123', materialId: 'mat-001' },
        { classroomId: 'classroom-xyz-789', materialId: 'document-456' },
        { classroomId: '12345', materialId: 'video-999' },
        {
          classroomId: 'special-chars_classroom',
          materialId: 'file_with_underscores',
        },
        {
          classroomId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
          materialId: '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
        },
      ];

      for (const { classroomId, materialId } of testCases) {
        mockDelete.mockResolvedValueOnce({ status: 204 });

        await deleteMaterial(classroomId, materialId);

        expect(mockDelete).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/materials/${materialId}`,
        );
      }
    });

    describe('URL construction validation', () => {
      it('should construct correct endpoint for standard IDs', async () => {
        mockDelete.mockResolvedValueOnce({ status: 204 });

        await deleteMaterial('classroom123', 'material456');

        expect(mockDelete).toHaveBeenCalledWith(
          '/classrooms/classroom123/materials/material456',
        );
      });

      it('should handle IDs with hyphens and underscores', async () => {
        const classroomId = 'classroom-with-dashes_and_underscores';
        const materialId = 'material-with-special-chars_123';

        mockDelete.mockResolvedValueOnce({ status: 204 });

        await deleteMaterial(classroomId, materialId);

        expect(mockDelete).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/materials/${materialId}`,
        );
      });

      it('should handle numeric IDs', async () => {
        mockDelete.mockResolvedValueOnce({ status: 204 });

        await deleteMaterial('12345', '67890');

        expect(mockDelete).toHaveBeenCalledWith(
          '/classrooms/12345/materials/67890',
        );
      });

      it('should handle UUID format IDs', async () => {
        const classroomId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
        const materialId = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';

        mockDelete.mockResolvedValueOnce({ status: 204 });

        await deleteMaterial(classroomId, materialId);

        expect(mockDelete).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/materials/${materialId}`,
        );
      });

      it('should handle long IDs', async () => {
        const longClassroomId =
          'very-long-classroom-id-with-many-characters-and-numbers-123456789';
        const longMaterialId =
          'very-long-material-id-with-descriptive-name-and-version-v2-final';

        mockDelete.mockResolvedValueOnce({ status: 204 });

        await deleteMaterial(longClassroomId, longMaterialId);

        expect(mockDelete).toHaveBeenCalledWith(
          `/classrooms/${longClassroomId}/materials/${longMaterialId}`,
        );
      });
    });

    describe('different response scenarios', () => {
      it('should handle 204 No Content response', async () => {
        mockDelete.mockResolvedValueOnce({ status: 204 });

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).resolves.toBeUndefined();
      });

      it('should handle 200 OK response', async () => {
        mockDelete.mockResolvedValueOnce({
          status: 200,
          data: { message: 'Material deleted successfully' },
        });

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).resolves.toBeUndefined();
      });

      it('should handle response with deletion metadata', async () => {
        mockDelete.mockResolvedValueOnce({
          status: 200,
          data: {
            deleted: true,
            materialId: 'material456',
            deletedAt: '2024-01-01T00:00:00Z',
          },
        });

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).resolves.toBeUndefined();
      });
    });

    describe('error handling', () => {
      it('should throw provided error message for material not found', async () => {
        const errorResponse = {
          response: { data: { error: 'Material not found' } },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'nonexistent-material'),
        ).rejects.toThrow('Material not found');
      });

      it('should throw provided error message for classroom not found', async () => {
        const errorResponse = {
          response: { data: { error: 'Classroom not found' } },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteMaterial('nonexistent-classroom', 'material456'),
        ).rejects.toThrow('Classroom not found');
      });

      it('should throw provided error message for access denied', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Insufficient permissions to delete this material' },
          },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow('Insufficient permissions to delete this material');
      });

      it('should throw provided error message for material in use', async () => {
        const errorResponse = {
          response: {
            data: {
              error:
                'Cannot delete material that is currently being used in active sessions',
            },
          },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow(
          'Cannot delete material that is currently being used in active sessions',
        );
      });

      it('should throw provided error message for material already deleted', async () => {
        const errorResponse = {
          response: { data: { error: 'Material has already been deleted' } },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow('Material has already been deleted');
      });

      it('should throw provided error message for storage deletion failure', async () => {
        const errorResponse = {
          response: {
            data: {
              error:
                'Failed to delete material from storage. Please try again.',
            },
          },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow(
          'Failed to delete material from storage. Please try again.',
        );
      });

      it('should throw default message when API error has no error field', async () => {
        const errorResponse = { response: { data: {} } };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow('Failed to delete material!');
      });

      it('should throw default message when API error has null error', async () => {
        const errorResponse = { response: { data: { error: null } } };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow('Failed to delete material!');
      });

      it('should throw default message when API error has undefined error', async () => {
        const errorResponse = { response: { data: { error: undefined } } };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow('Failed to delete material!');
      });

      it('should throw default message when API error has empty string error', async () => {
        const errorResponse = { response: { data: { error: '' } } };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow('Failed to delete material!');
      });

      it('should throw default message when API error has no response', async () => {
        const errorResponse = { response: undefined };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow('Failed to delete material!');
      });

      it('should throw default message when error is not axios error', async () => {
        isAxiosErrorSpy.mockReturnValueOnce(false);
        mockDelete.mockRejectedValueOnce(new Error('Network failure'));

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow('Failed to delete material!');
      });

      it('should handle generic error correctly', async () => {
        mockDelete.mockRejectedValueOnce(new Error('Some generic error'));

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow('Failed to delete material!');
      });

      it('should handle timeout error', async () => {
        const timeoutError = {
          response: {
            data: { error: 'Request timeout while deleting material' },
          },
        };
        mockDelete.mockRejectedValueOnce(timeoutError as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow('Request timeout while deleting material');
      });

      it('should handle server error', async () => {
        const serverError = {
          response: {
            data: { error: 'Internal server error during material deletion' },
          },
        };
        mockDelete.mockRejectedValueOnce(serverError as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow('Internal server error during material deletion');
      });

      it('should handle conflict error', async () => {
        const conflictError = {
          response: {
            data: {
              error: 'Material deletion conflicts with existing references',
            },
          },
        };
        mockDelete.mockRejectedValueOnce(conflictError as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow(
          'Material deletion conflicts with existing references',
        );
      });

      it('should handle quota exceeded error', async () => {
        const quotaError = {
          response: {
            data: { error: 'Storage quota operations temporarily unavailable' },
          },
        };
        mockDelete.mockRejectedValueOnce(quotaError as AxiosError);

        await expect(
          deleteMaterial('classroom123', 'material456'),
        ).rejects.toThrow('Storage quota operations temporarily unavailable');
      });
    });

    describe('edge cases', () => {
      it('should handle deletion of very large files', async () => {
        // Simulating deletion of a large file that might take longer
        mockDelete.mockResolvedValueOnce({
          status: 202, // Accepted - deletion in progress
          data: { message: 'Large file deletion initiated' },
        });

        await expect(
          deleteMaterial('classroom123', 'large-video-file'),
        ).resolves.toBeUndefined();
      });

      it('should handle deletion with cascade effects', async () => {
        mockDelete.mockResolvedValueOnce({
          status: 200,
          data: {
            message: 'Material and all associated data deleted',
            cascaded: ['thumbnails', 'metadata', 'access_logs'],
          },
        });

        await expect(
          deleteMaterial('classroom123', 'material-with-dependencies'),
        ).resolves.toBeUndefined();
      });

      it('should handle deletion of shared materials', async () => {
        mockDelete.mockResolvedValueOnce({
          status: 200,
          data: {
            message:
              'Material removed from classroom but preserved in other classrooms',
            sharedWith: 3,
          },
        });

        await expect(
          deleteMaterial('classroom123', 'shared-material'),
        ).resolves.toBeUndefined();
      });

      it('should handle rapid consecutive deletions', async () => {
        // Test multiple deletions in sequence
        const materials = ['material1', 'material2', 'material3'];

        materials.forEach(() => {
          mockDelete.mockResolvedValueOnce({ status: 204 });
        });

        const deletionPromises = materials.map((materialId) =>
          deleteMaterial('classroom123', materialId),
        );

        await expect(Promise.all(deletionPromises)).resolves.toEqual([
          undefined,
          undefined,
          undefined,
        ]);

        expect(mockDelete).toHaveBeenCalledTimes(3);
      });

      it('should handle deletion of materials with special file types', async () => {
        const specialMaterials = [
          'material.with.dots',
          'material-with-émojis-📄',
          'material%20with%20encoded%20chars',
          'material@with#special$chars',
        ];

        for (const materialId of specialMaterials) {
          mockDelete.mockResolvedValueOnce({ status: 204 });

          await deleteMaterial('classroom123', materialId);

          expect(mockDelete).toHaveBeenCalledWith(
            `/classrooms/classroom123/materials/${materialId}`,
          );
        }
      });
    });

    describe('integration scenarios', () => {
      it('should work in bulk deletion scenario', async () => {
        const materialIds = Array.from(
          { length: 10 },
          (_, i) => `material-${i + 1}`,
        );

        materialIds.forEach(() => {
          mockDelete.mockResolvedValueOnce({ status: 204 });
        });

        for (const materialId of materialIds) {
          await deleteMaterial('classroom123', materialId);
        }

        expect(mockDelete).toHaveBeenCalledTimes(10);
        materialIds.forEach((materialId, index) => {
          expect(mockDelete).toHaveBeenNthCalledWith(
            index + 1,
            `/classrooms/classroom123/materials/${materialId}`,
          );
        });
      });

      it('should handle mixed success and failure scenarios', async () => {
        // First deletion succeeds
        mockDelete.mockResolvedValueOnce({ status: 204 });

        // Second deletion fails
        const errorResponse = {
          response: { data: { error: 'Material not found' } },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        // First should succeed
        await expect(
          deleteMaterial('classroom123', 'existing-material'),
        ).resolves.toBeUndefined();

        // Second should fail
        await expect(
          deleteMaterial('classroom123', 'nonexistent-material'),
        ).rejects.toThrow('Material not found');
      });
    });
  });
});
