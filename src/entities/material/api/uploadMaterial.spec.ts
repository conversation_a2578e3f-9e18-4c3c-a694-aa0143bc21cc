import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { uploadMaterial } from './uploadMaterial';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { post: vi.fn() },
}));

// Mock FormData
const mockFormDataInstance = {
  append: vi.fn(),
};

global.FormData = vi.fn(
  () => mockFormDataInstance,
) as unknown as typeof FormData;

describe('Upload Material API', () => {
  let mockPost: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockPost = api.post as ReturnType<typeof vi.fn>;
    (global.FormData as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      mockFormDataInstance,
    );

    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' && error !== null && 'response' in error,
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  describe('uploadMaterial', () => {
    const createMockFile = (
      name: string,
      _size: number = 1024,
      type: string = 'application/pdf',
    ) => {
      return new File(['test content'], name, { type }) as File;
    };

    it('should upload material successfully', async () => {
      const mockFile = createMockFile('test-document.pdf');
      const uploadData = {
        title: 'Test Document',
        file: mockFile,
      };

      mockPost.mockResolvedValueOnce({
        status: 201,
        data: { message: 'Upload successful' },
      });

      await uploadMaterial('classroom123', uploadData);

      expect(global.FormData).toHaveBeenCalledTimes(1);
      expect(mockFormDataInstance.append).toHaveBeenCalledWith(
        'file',
        mockFile,
      );
      expect(mockFormDataInstance.append).toHaveBeenCalledWith(
        'title',
        'Test Document',
      );
      expect(mockFormDataInstance.append).toHaveBeenCalledWith('tags', 'pdf');

      expect(mockPost).toHaveBeenCalledWith(
        '/classrooms/classroom123/materials/upload',
        mockFormDataInstance,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );
    });

    it('should complete without returning anything', async () => {
      const mockFile = createMockFile('document.pdf');
      const uploadData = { title: 'Document', file: mockFile };

      mockPost.mockResolvedValueOnce({ status: 201 });

      const result = await uploadMaterial('classroom123', uploadData);

      expect(result).toBeUndefined();
    });

    it('should work with different classroom IDs', async () => {
      const testCases = [
        'abc-123',
        'classroom-xyz-789',
        '12345',
        'special-chars_classroom',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      ];

      const mockFile = createMockFile('test.pdf');

      for (const classroomId of testCases) {
        mockPost.mockResolvedValueOnce({ status: 201 });

        await uploadMaterial(classroomId, { title: 'Test', file: mockFile });

        expect(mockPost).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/materials/upload`,
          expect.any(Object),
          expect.any(Object),
        );
      }
    });

    describe('file handling', () => {
      it('should handle different file types', async () => {
        const fileTypes = [
          { name: 'document.pdf', type: 'application/pdf' },
          {
            name: 'presentation.pptx',
            type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          },
          {
            name: 'spreadsheet.xlsx',
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          },
          { name: 'image.png', type: 'image/png' },
          { name: 'video.mp4', type: 'video/mp4' },
          { name: 'text.txt', type: 'text/plain' },
        ];

        for (const { name, type } of fileTypes) {
          const mockFile = createMockFile(name, 1024, type);
          mockPost.mockResolvedValueOnce({ status: 201 });

          await uploadMaterial('classroom123', { title: name, file: mockFile });

          expect(mockFormDataInstance.append).toHaveBeenCalledWith(
            'file',
            mockFile,
          );
        }
      });

      it('should handle files with different sizes', async () => {
        const fileSizes = [
          { name: 'small.pdf', size: 1024 }, // 1KB
          { name: 'medium.pdf', size: 1024 * 1024 }, // 1MB
          { name: 'large.pdf', size: 10 * 1024 * 1024 }, // 10MB
          { name: 'huge.pdf', size: 100 * 1024 * 1024 }, // 100MB
        ];

        for (const { name, size } of fileSizes) {
          const mockFile = createMockFile(name, size);
          mockPost.mockResolvedValueOnce({ status: 201 });

          await uploadMaterial('classroom123', { title: name, file: mockFile });

          expect(mockFormDataInstance.append).toHaveBeenCalledWith(
            'file',
            mockFile,
          );
        }
      });

      it('should handle files with special characters in names', async () => {
        const specialFileNames = [
          'document with spaces.pdf',
          'document-with-dashes.pdf',
          'document_with_underscores.pdf',
          'document.with.dots.pdf',
          'document(with)parentheses.pdf',
          'document[with]brackets.pdf',
          'document@with#special$chars.pdf',
          'document%20encoded.pdf',
          'document émojis 📄.pdf',
        ];

        for (const fileName of specialFileNames) {
          const mockFile = createMockFile(fileName);
          mockPost.mockResolvedValueOnce({ status: 201 });

          await uploadMaterial('classroom123', {
            title: fileName,
            file: mockFile,
          });

          expect(mockFormDataInstance.append).toHaveBeenCalledWith(
            'file',
            mockFile,
          );
        }
      });
    });

    describe('title handling', () => {
      it('should handle different title formats', async () => {
        const mockFile = createMockFile('test.pdf');
        const titles = [
          'Simple Title',
          'Title with Numbers 123',
          'Title-with-dashes',
          'Title_with_underscores',
          'Title.with.dots',
          'Title (with parentheses)',
          'Title [with brackets]',
          'Title with émojis 📚',
          'Very Long Title That Might Exceed Normal Length Limits And Contains Many Words',
          'Title with "quotes" and \'apostrophes\'',
        ];

        for (const title of titles) {
          mockPost.mockResolvedValueOnce({ status: 201 });

          await uploadMaterial('classroom123', { title, file: mockFile });

          expect(mockFormDataInstance.append).toHaveBeenCalledWith(
            'title',
            title,
          );
        }
      });

      it('should handle empty title', async () => {
        const mockFile = createMockFile('test.pdf');
        mockPost.mockResolvedValueOnce({ status: 201 });

        await uploadMaterial('classroom123', { title: '', file: mockFile });

        expect(mockFormDataInstance.append).toHaveBeenCalledWith('title', '');
      });

      it('should handle title with only whitespace', async () => {
        const mockFile = createMockFile('test.pdf');
        mockPost.mockResolvedValueOnce({ status: 201 });

        await uploadMaterial('classroom123', { title: '   ', file: mockFile });

        expect(mockFormDataInstance.append).toHaveBeenCalledWith(
          'title',
          '   ',
        );
      });
    });

    describe('FormData construction', () => {
      it('should always append tags as "pdf"', async () => {
        const mockFile = createMockFile(
          'test.docx',
          1024,
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        );
        mockPost.mockResolvedValueOnce({ status: 201 });

        await uploadMaterial('classroom123', {
          title: 'Word Document',
          file: mockFile,
        });

        // Even for non-PDF files, tags should be "pdf"
        expect(mockFormDataInstance.append).toHaveBeenCalledWith('tags', 'pdf');
      });

      it('should append form fields in correct order', async () => {
        const mockFile = createMockFile('test.pdf');
        mockPost.mockResolvedValueOnce({ status: 201 });

        await uploadMaterial('classroom123', { title: 'Test', file: mockFile });

        expect(mockFormDataInstance.append).toHaveBeenNthCalledWith(
          1,
          'file',
          mockFile,
        );
        expect(mockFormDataInstance.append).toHaveBeenNthCalledWith(
          2,
          'title',
          'Test',
        );
        expect(mockFormDataInstance.append).toHaveBeenNthCalledWith(
          3,
          'tags',
          'pdf',
        );
      });

      it('should create new FormData instance for each upload', async () => {
        const mockFile = createMockFile('test.pdf');

        mockPost.mockResolvedValueOnce({ status: 201 });
        await uploadMaterial('classroom123', {
          title: 'First',
          file: mockFile,
        });

        mockPost.mockResolvedValueOnce({ status: 201 });
        await uploadMaterial('classroom456', {
          title: 'Second',
          file: mockFile,
        });

        expect(global.FormData).toHaveBeenCalledTimes(2);
      });
    });

    describe('headers validation', () => {
      it('should set correct Content-Type header', async () => {
        const mockFile = createMockFile('test.pdf');
        mockPost.mockResolvedValueOnce({ status: 201 });

        await uploadMaterial('classroom123', { title: 'Test', file: mockFile });

        expect(mockPost).toHaveBeenCalledWith(
          expect.any(String),
          expect.any(Object),
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        );
      });
    });

    describe('error handling', () => {
      it('should throw provided error message for file too large', async () => {
        const mockFile = createMockFile('large-file.pdf');
        const errorResponse = {
          response: {
            data: { error: 'File size exceeds maximum allowed limit' },
          },
        };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          uploadMaterial('classroom123', {
            title: 'Large File',
            file: mockFile,
          }),
        ).rejects.toThrow('File size exceeds maximum allowed limit');
      });

      it('should throw provided error message for unsupported file type', async () => {
        const mockFile = createMockFile(
          'virus.exe',
          1024,
          'application/x-msdownload',
        );
        const errorResponse = {
          response: { data: { error: 'File type not supported' } },
        };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          uploadMaterial('classroom123', { title: 'Virus', file: mockFile }),
        ).rejects.toThrow('File type not supported');
      });

      it('should throw provided error message for storage quota exceeded', async () => {
        const mockFile = createMockFile('test.pdf');
        const errorResponse = {
          response: {
            data: { error: 'Storage quota exceeded for this classroom' },
          },
        };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          uploadMaterial('classroom123', { title: 'Test', file: mockFile }),
        ).rejects.toThrow('Storage quota exceeded for this classroom');
      });

      it('should throw provided error message for classroom not found', async () => {
        const mockFile = createMockFile('test.pdf');
        const errorResponse = {
          response: { data: { error: 'Classroom not found' } },
        };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          uploadMaterial('nonexistent-classroom', {
            title: 'Test',
            file: mockFile,
          }),
        ).rejects.toThrow('Classroom not found');
      });

      it('should throw provided error message for insufficient permissions', async () => {
        const mockFile = createMockFile('test.pdf');
        const errorResponse = {
          response: {
            data: { error: 'Insufficient permissions to upload materials' },
          },
        };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          uploadMaterial('classroom123', { title: 'Test', file: mockFile }),
        ).rejects.toThrow('Insufficient permissions to upload materials');
      });

      it('should throw provided error message for duplicate file', async () => {
        const mockFile = createMockFile('existing-file.pdf');
        const errorResponse = {
          response: {
            data: { error: 'A material with this name already exists' },
          },
        };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          uploadMaterial('classroom123', {
            title: 'Existing File',
            file: mockFile,
          }),
        ).rejects.toThrow('A material with this name already exists');
      });

      it('should throw provided error message for virus detected', async () => {
        const mockFile = createMockFile('suspicious.pdf');
        const errorResponse = {
          response: { data: { error: 'File failed security scan' } },
        };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          uploadMaterial('classroom123', {
            title: 'Suspicious',
            file: mockFile,
          }),
        ).rejects.toThrow('File failed security scan');
      });

      // Note: The error message in the function says "Failed to delete material!" which seems incorrect for upload
      it('should throw default message when API error has no error field', async () => {
        const mockFile = createMockFile('test.pdf');
        const errorResponse = { response: { data: {} } };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          uploadMaterial('classroom123', { title: 'Test', file: mockFile }),
        ).rejects.toThrow('Failed to delete material!'); // This seems to be a bug in the original function
      });

      it('should throw default message when API error has null error', async () => {
        const mockFile = createMockFile('test.pdf');
        const errorResponse = { response: { data: { error: null } } };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          uploadMaterial('classroom123', { title: 'Test', file: mockFile }),
        ).rejects.toThrow('Failed to delete material!');
      });

      it('should throw default message when API error has undefined error', async () => {
        const mockFile = createMockFile('test.pdf');
        const errorResponse = { response: { data: { error: undefined } } };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          uploadMaterial('classroom123', { title: 'Test', file: mockFile }),
        ).rejects.toThrow('Failed to delete material!');
      });

      it('should throw default message when API error has empty string error', async () => {
        const mockFile = createMockFile('test.pdf');
        const errorResponse = { response: { data: { error: '' } } };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          uploadMaterial('classroom123', { title: 'Test', file: mockFile }),
        ).rejects.toThrow('Failed to delete material!');
      });

      it('should throw default message when API error has no response', async () => {
        const mockFile = createMockFile('test.pdf');
        const errorResponse = { response: undefined };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          uploadMaterial('classroom123', { title: 'Test', file: mockFile }),
        ).rejects.toThrow('Failed to delete material!');
      });

      it('should throw default message when error is not axios error', async () => {
        const mockFile = createMockFile('test.pdf');
        isAxiosErrorSpy.mockReturnValueOnce(false);
        mockPost.mockRejectedValueOnce(new Error('Network failure'));

        await expect(
          uploadMaterial('classroom123', { title: 'Test', file: mockFile }),
        ).rejects.toThrow('Failed to delete material!');
      });

      it('should handle generic error correctly', async () => {
        const mockFile = createMockFile('test.pdf');
        mockPost.mockRejectedValueOnce(new Error('Some generic error'));

        await expect(
          uploadMaterial('classroom123', { title: 'Test', file: mockFile }),
        ).rejects.toThrow('Failed to delete material!');
      });

      it('should handle timeout error', async () => {
        const mockFile = createMockFile('test.pdf');
        const timeoutError = {
          response: { data: { error: 'Upload timeout - please try again' } },
        };
        mockPost.mockRejectedValueOnce(timeoutError as AxiosError);

        await expect(
          uploadMaterial('classroom123', { title: 'Test', file: mockFile }),
        ).rejects.toThrow('Upload timeout - please try again');
      });

      it('should handle server error during upload', async () => {
        const mockFile = createMockFile('test.pdf');
        const serverError = {
          response: {
            data: { error: 'Internal server error during file processing' },
          },
        };
        mockPost.mockRejectedValueOnce(serverError as AxiosError);

        await expect(
          uploadMaterial('classroom123', { title: 'Test', file: mockFile }),
        ).rejects.toThrow('Internal server error during file processing');
      });
    });

    describe('edge cases', () => {
      it('should handle very long filenames', async () => {
        const longFileName = 'a'.repeat(255) + '.pdf';
        const mockFile = createMockFile(longFileName);
        mockPost.mockResolvedValueOnce({ status: 201 });

        await uploadMaterial('classroom123', {
          title: 'Long filename',
          file: mockFile,
        });

        expect(mockFormDataInstance.append).toHaveBeenCalledWith(
          'file',
          mockFile,
        );
      });

      it('should handle files with no extension', async () => {
        const mockFile = createMockFile('filename_without_extension');
        mockPost.mockResolvedValueOnce({ status: 201 });

        await uploadMaterial('classroom123', {
          title: 'No extension',
          file: mockFile,
        });

        expect(mockFormDataInstance.append).toHaveBeenCalledWith(
          'file',
          mockFile,
        );
        expect(mockFormDataInstance.append).toHaveBeenCalledWith('tags', 'pdf'); // Still tags as PDF
      });

      it('should handle zero-byte files', async () => {
        const mockFile = createMockFile('empty.pdf', 0);
        mockPost.mockResolvedValueOnce({ status: 201 });

        await uploadMaterial('classroom123', {
          title: 'Empty file',
          file: mockFile,
        });

        expect(mockFormDataInstance.append).toHaveBeenCalledWith(
          'file',
          mockFile,
        );
      });

      it('should handle multiple uploads concurrently', async () => {
        const files = [
          { title: 'File 1', file: createMockFile('file1.pdf') },
          { title: 'File 2', file: createMockFile('file2.pdf') },
          { title: 'File 3', file: createMockFile('file3.pdf') },
        ];

        files.forEach(() => {
          mockPost.mockResolvedValueOnce({ status: 201 });
        });

        const uploadPromises = files.map((data) =>
          uploadMaterial('classroom123', data),
        );

        await expect(Promise.all(uploadPromises)).resolves.toEqual([
          undefined,
          undefined,
          undefined,
        ]);

        expect(mockPost).toHaveBeenCalledTimes(3);
        expect(global.FormData).toHaveBeenCalledTimes(3);
      });
    });
  });
});
