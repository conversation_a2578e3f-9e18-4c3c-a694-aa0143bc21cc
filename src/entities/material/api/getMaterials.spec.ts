import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { getMaterials } from './getMaterials';
import type { Material } from '../types';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { get: vi.fn() },
}));

describe('Materials API', () => {
  let mockGet: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockGet = api.get as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' && error !== null && 'response' in error,
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  describe('getMaterials', () => {
    const mockMaterials: Material[] = [
      {
        id: 'material-1',
        title: 'Introduction to React',
        description: 'Learn the basics of React',
        fileName: 'react-intro.pdf',
        fileSize: 1024000,
        fileSizeMB: 1.0,
        pageCount: 25,
        tags: ['react', 'javascript', 'tutorial'],
        createdAt: 1704067200000, // 2024-01-01T00:00:00Z
        updatedAt: 1704067200000,
        uploadedBy: 'user-1',
      },
      {
        id: 'material-2',
        title: 'JavaScript Fundamentals',
        description: 'Core JavaScript concepts',
        fileName: 'js-fundamentals.mp4',
        fileSize: 50240000,
        fileSizeMB: 47.9,
        tags: ['javascript', 'fundamentals'],
        createdAt: 1704153600000, // 2024-01-02T00:00:00Z
        updatedAt: 1704153600000,
        uploadedBy: 'user-1',
      },
    ];

    const mockPagination = {
      page: 1,
      limit: 10,
      total: 25,
      total_pages: 3,
      has_more: true,
    };

    const mockResponse = {
      data: {
        items: mockMaterials,
        pagination: mockPagination,
      },
    };

    it('should return materials with pagination when API call succeeds', async () => {
      mockGet.mockResolvedValueOnce({ data: mockResponse });

      const result = await getMaterials('classroom123', { page: 1, limit: 10 });

      expect(mockGet).toHaveBeenCalledWith(
        '/classrooms/classroom123/materials?page=1&limit=10&sort_by=createdAt_desc',
      );
      expect(result).toEqual(mockResponse);
      expect(result.data.items).toHaveLength(2);
      expect(result.data.pagination.total).toBe(25);
    });

    it('should handle empty materials list', async () => {
      const emptyResponse = {
        data: {
          items: [],
          pagination: {
            page: 1,
            limit: 10,
            total: 0,
            total_pages: 0,
            has_more: false,
          },
        },
      };

      mockGet.mockResolvedValueOnce({ data: emptyResponse });

      const result = await getMaterials('classroom456', { page: 1, limit: 10 });

      expect(result.data.items).toEqual([]);
      expect(result.data.pagination.total).toBe(0);
      expect(result.data.pagination.has_more).toBe(false);
    });

    describe('pagination parameters', () => {
      it('should handle different page numbers', async () => {
        mockGet.mockResolvedValueOnce({ data: mockResponse });

        await getMaterials('classroom123', { page: 3, limit: 5 });

        expect(mockGet).toHaveBeenCalledWith(
          '/classrooms/classroom123/materials?page=3&limit=5&sort_by=createdAt_desc',
        );
      });

      it('should handle large page numbers', async () => {
        mockGet.mockResolvedValueOnce({ data: mockResponse });

        await getMaterials('classroom123', { page: 100, limit: 50 });

        expect(mockGet).toHaveBeenCalledWith(
          '/classrooms/classroom123/materials?page=100&limit=50&sort_by=createdAt_desc',
        );
      });

      it('should handle page 0 (edge case - not included in query)', async () => {
        mockGet.mockResolvedValueOnce({ data: mockResponse });

        await getMaterials('classroom123', { page: 0, limit: 10 });

        // Page 0 is falsy, so it won't be included in query string
        expect(mockGet).toHaveBeenCalledWith(
          '/classrooms/classroom123/materials?limit=10&sort_by=createdAt_desc',
        );
      });

      it('should handle limit of 1', async () => {
        mockGet.mockResolvedValueOnce({ data: mockResponse });

        await getMaterials('classroom123', { page: 1, limit: 1 });

        expect(mockGet).toHaveBeenCalledWith(
          '/classrooms/classroom123/materials?page=1&limit=1&sort_by=createdAt_desc',
        );
      });

      it('should handle large limit values', async () => {
        mockGet.mockResolvedValueOnce({ data: mockResponse });

        await getMaterials('classroom123', { page: 1, limit: 1000 });

        expect(mockGet).toHaveBeenCalledWith(
          '/classrooms/classroom123/materials?page=1&limit=1000&sort_by=createdAt_desc',
        );
      });
    });

    describe('URL construction', () => {
      it('should work with different classroom IDs', async () => {
        const testCases = [
          'classroom-abc-123',
          '12345',
          'special-chars_classroom',
          'uuid-f47ac10b-58cc-4372-a567-0e02b2c3d479',
        ];

        for (const classroomId of testCases) {
          mockGet.mockResolvedValueOnce({ data: mockResponse });

          await getMaterials(classroomId, { page: 1, limit: 10 });

          expect(mockGet).toHaveBeenCalledWith(
            `/classrooms/${classroomId}/materials?page=1&limit=10&sort_by=createdAt_desc`,
          );
        }
      });

      it('should construct query string correctly with sort_by parameter', async () => {
        mockGet.mockResolvedValueOnce({ data: mockResponse });

        await getMaterials('classroom123', { page: 2, limit: 20 });

        expect(mockGet).toHaveBeenCalledWith(
          '/classrooms/classroom123/materials?page=2&limit=20&sort_by=createdAt_desc',
        );
      });

      it('should handle query parameters when page is undefined', async () => {
        mockGet.mockResolvedValueOnce({ data: mockResponse });

        await getMaterials('classroom123', { page: undefined!, limit: 10 });

        // Should only include limit and sort_by parameters
        expect(mockGet).toHaveBeenCalledWith(
          '/classrooms/classroom123/materials?limit=10&sort_by=createdAt_desc',
        );
      });

      it('should handle query parameters when limit is undefined', async () => {
        mockGet.mockResolvedValueOnce({ data: mockResponse });

        await getMaterials('classroom123', { page: 1, limit: undefined! });

        // Should only include page and sort_by parameters
        expect(mockGet).toHaveBeenCalledWith(
          '/classrooms/classroom123/materials?page=1&sort_by=createdAt_desc',
        );
      });

      it('should handle when both parameters are undefined', async () => {
        mockGet.mockResolvedValueOnce({ data: mockResponse });

        await getMaterials('classroom123', {
          page: undefined!,
          limit: undefined!,
        });

        // Should only have sort_by parameter
        expect(mockGet).toHaveBeenCalledWith(
          '/classrooms/classroom123/materials?sort_by=createdAt_desc',
        );
      });

      it('should always include sort_by parameter', async () => {
        mockGet.mockResolvedValueOnce({ data: mockResponse });

        await getMaterials('classroom123', { page: 1, limit: 10 });

        expect(mockGet).toHaveBeenCalledWith(
          expect.stringContaining('sort_by=createdAt_desc'),
        );
      });
    });

    describe('response data structure validation', () => {
      it('should preserve material properties', async () => {
        mockGet.mockResolvedValueOnce({ data: mockResponse });

        const result = await getMaterials('classroom123', {
          page: 1,
          limit: 10,
        });

        const firstMaterial = result.data.items[0];
        expect(firstMaterial).toHaveProperty('id', 'material-1');
        expect(firstMaterial).toHaveProperty('title', 'Introduction to React');
        expect(firstMaterial).toHaveProperty(
          'description',
          'Learn the basics of React',
        );
        expect(firstMaterial).toHaveProperty('fileName', 'react-intro.pdf');
        expect(firstMaterial).toHaveProperty('fileSize', 1024000);
        expect(firstMaterial).toHaveProperty('fileSizeMB', 1.0);
        expect(firstMaterial).toHaveProperty('pageCount', 25);
        expect(firstMaterial).toHaveProperty('tags');
        expect(firstMaterial.tags).toEqual(['react', 'javascript', 'tutorial']);
        expect(firstMaterial).toHaveProperty('createdAt', 1704067200000);
        expect(firstMaterial).toHaveProperty('updatedAt', 1704067200000);
      });

      it('should preserve pagination properties', async () => {
        mockGet.mockResolvedValueOnce({ data: mockResponse });

        const result = await getMaterials('classroom123', {
          page: 1,
          limit: 10,
        });

        expect(result.data.pagination).toHaveProperty('page', 1);
        expect(result.data.pagination).toHaveProperty('limit', 10);
        expect(result.data.pagination).toHaveProperty('total', 25);
        expect(result.data.pagination).toHaveProperty('total_pages', 3);
        expect(result.data.pagination).toHaveProperty('has_more', true);
      });

      it('should handle different material types', async () => {
        const diverseMaterials: Material[] = [
          {
            id: 'doc-1',
            title: 'PDF Document',
            fileName: 'document.pdf',
            fileSize: 1024000,
            fileSizeMB: 1.0,
            pageCount: 15,
            tags: ['pdf', 'document'],
            createdAt: 1704067200000,
            updatedAt: 1704067200000,
            uploadedBy: 'user-1',
          },
          {
            id: 'vid-1',
            title: 'Video Lecture',
            fileName: 'lecture.mp4',
            fileSize: 50240000,
            fileSizeMB: 47.9,
            tags: ['video', 'lecture'],
            createdAt: 1704153600000,
            updatedAt: 1704153600000,
            uploadedBy: 'user-1',
          },
          {
            id: 'img-1',
            title: 'Diagram Image',
            fileName: 'diagram.png',
            fileSize: 512000,
            fileSizeMB: 0.5,
            tags: ['image', 'diagram'],
            createdAt: 1704240000000,
            updatedAt: 1704240000000,
            uploadedBy: 'user-1',
          },
        ];

        const diverseResponse = {
          data: {
            items: diverseMaterials,
            pagination: mockPagination,
          },
        };

        mockGet.mockResolvedValueOnce({ data: diverseResponse });

        const result = await getMaterials('classroom123', {
          page: 1,
          limit: 10,
        });

        expect(result.data.items[0].tags).toEqual(['pdf', 'document']);
        expect(result.data.items[1].tags).toEqual(['video', 'lecture']);
        expect(result.data.items[2].tags).toEqual(['image', 'diagram']);
      });
    });

    describe('error handling', () => {
      it('should throw provided error message on API error with string error', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Materials not found for this classroom' },
          },
        };
        mockGet.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          getMaterials('classroom123', { page: 1, limit: 10 }),
        ).rejects.toThrow('Materials not found for this classroom');
      });

      it('should throw provided error for access denied', async () => {
        const errorResponse = {
          response: { data: { error: 'Access denied to classroom materials' } },
        };
        mockGet.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          getMaterials('classroom123', { page: 1, limit: 10 }),
        ).rejects.toThrow('Access denied to classroom materials');
      });

      it('should throw provided error for invalid pagination', async () => {
        const errorResponse = {
          response: { data: { error: 'Invalid pagination parameters' } },
        };
        mockGet.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          getMaterials('classroom123', { page: -1, limit: 0 }),
        ).rejects.toThrow('Invalid pagination parameters');
      });

      it('should throw default message when API error has no error field', async () => {
        const errorResponse = { response: { data: {} } };
        mockGet.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          getMaterials('classroom123', { page: 1, limit: 10 }),
        ).rejects.toThrow('Failed to get materials!');
      });

      it('should throw default message when API error has null error', async () => {
        const errorResponse = { response: { data: { error: null } } };
        mockGet.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          getMaterials('classroom123', { page: 1, limit: 10 }),
        ).rejects.toThrow('Failed to get materials!');
      });

      it('should throw default message when API error has undefined error', async () => {
        const errorResponse = { response: { data: { error: undefined } } };
        mockGet.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          getMaterials('classroom123', { page: 1, limit: 10 }),
        ).rejects.toThrow('Failed to get materials!');
      });

      it('should throw default message when API error has no response', async () => {
        const errorResponse = { response: undefined };
        mockGet.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          getMaterials('classroom123', { page: 1, limit: 10 }),
        ).rejects.toThrow('Failed to get materials!');
      });

      it('should throw default message when error is not axios error', async () => {
        isAxiosErrorSpy.mockReturnValueOnce(false);
        mockGet.mockRejectedValueOnce(new Error('Network failure'));

        await expect(
          getMaterials('classroom123', { page: 1, limit: 10 }),
        ).rejects.toThrow('Failed to get materials!');
      });

      it('should handle generic error correctly', async () => {
        mockGet.mockRejectedValueOnce(new Error('Some generic error'));

        await expect(
          getMaterials('classroom123', { page: 1, limit: 10 }),
        ).rejects.toThrow('Failed to get materials!');
      });

      it('should handle timeout error', async () => {
        const timeoutError = {
          response: {
            data: { error: 'Request timeout while fetching materials' },
          },
        };
        mockGet.mockRejectedValueOnce(timeoutError as AxiosError);

        await expect(
          getMaterials('classroom123', { page: 1, limit: 10 }),
        ).rejects.toThrow('Request timeout while fetching materials');
      });

      it('should handle server error', async () => {
        const serverError = {
          response: { data: { error: 'Internal server error' } },
        };
        mockGet.mockRejectedValueOnce(serverError as AxiosError);

        await expect(
          getMaterials('classroom123', { page: 1, limit: 10 }),
        ).rejects.toThrow('Internal server error');
      });
    });

    describe('edge cases', () => {
      it('should handle very large file sizes', async () => {
        const largeMaterials: Material[] = [
          {
            id: 'large-file',
            title: 'Large Video File',
            fileName: 'large-video.mp4',
            fileSize: 5368709120, // 5GB
            fileSizeMB: 5120.0,
            tags: ['video', 'large'],
            createdAt: 1704067200000,
            updatedAt: 1704067200000,
            uploadedBy: 'user-1',
          },
        ];

        const largeFileResponse = {
          data: {
            items: largeMaterials,
            pagination: {
              page: 1,
              limit: 1,
              total: 1,
              total_pages: 1,
              has_more: false,
            },
          },
        };

        mockGet.mockResolvedValueOnce({ data: largeFileResponse });

        const result = await getMaterials('classroom123', {
          page: 1,
          limit: 1,
        });

        expect(result.data.items[0].fileSize).toBe(5368709120);
        expect(result.data.items[0].fileSizeMB).toBe(5120.0);
      });

      it('should handle materials with optional fields', async () => {
        const materialWithOptionalFields: Material[] = [
          {
            id: 'optional-1',
            title: 'Material with All Optional Fields',
            description: 'This has all optional fields',
            fileName: 'complete.pdf',
            fileSize: 2048000,
            fileSizeMB: 2.0,
            pageCount: 50,
            tags: ['complete', 'example', 'optional'],
            createdAt: 1704067200000,
            updatedAt: 1704153600000, // Different from createdAt
            uploadedBy: 'user-1',
          },
          {
            id: 'optional-2',
            title: 'Material with No Optional Fields',
            fileName: 'minimal.txt',
            fileSize: 1024,
            fileSizeMB: 0.001,
            createdAt: 1704240000000,
            updatedAt: 1704240000000,
            uploadedBy: 'user-1',
          },
        ];

        const optionalFieldsResponse = {
          data: {
            items: materialWithOptionalFields,
            pagination: mockPagination,
          },
        };

        mockGet.mockResolvedValueOnce({ data: optionalFieldsResponse });

        const result = await getMaterials('classroom123', {
          page: 1,
          limit: 10,
        });

        // First material has all optional fields
        expect(result.data.items[0].description).toBe(
          'This has all optional fields',
        );
        expect(result.data.items[0].pageCount).toBe(50);
        expect(result.data.items[0].tags).toEqual([
          'complete',
          'example',
          'optional',
        ]);
        expect(result.data.items[0].updatedAt).not.toBe(
          result.data.items[0].createdAt,
        );

        // Second material has no optional fields
        expect(result.data.items[1].description).toBeUndefined();
        expect(result.data.items[1].pageCount).toBeUndefined();
        expect(result.data.items[1].tags).toBeUndefined();
        expect(result.data.items[1].updatedAt).toBe(
          result.data.items[1].createdAt,
        );
      });

      it('should handle last page with has_more: false', async () => {
        const lastPageResponse = {
          data: {
            items: [mockMaterials[0]], // Only one item
            pagination: {
              page: 3,
              limit: 10,
              total: 21,
              total_pages: 3,
              has_more: false,
            },
          },
        };

        mockGet.mockResolvedValueOnce({ data: lastPageResponse });

        const result = await getMaterials('classroom123', {
          page: 3,
          limit: 10,
        });

        expect(result.data.pagination.has_more).toBe(false);
        expect(result.data.pagination.page).toBe(3);
        expect(result.data.pagination.total_pages).toBe(3);
      });
    });
  });
});
