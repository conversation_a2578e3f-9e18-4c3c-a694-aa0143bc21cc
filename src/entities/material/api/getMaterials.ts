import { api } from '@/shared';
import { Material } from '../types';
import axios from 'axios';

type MaterialsResponse = {
  data: {
    items: Material[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      total_pages: number;
      has_more: boolean;
    };
  };
};

export const getMaterials = async (
  classroomId: string,
  params: {
    page: number;
    limit: number;
  },
): Promise<MaterialsResponse> => {
  try {
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    queryParams.set('sort_by', 'createdAt_desc');

    const queryString = queryParams.toString();

    const response = await api.get<MaterialsResponse>(
      `/classrooms/${classroomId}/materials?${queryString}`,
    );

    return response.data;
  } catch (error) {
    let errorMessage = 'Failed to get materials!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
