import { api } from '@/shared';
import axios from 'axios';

export const deleteMaterial = async (
  classroomId: string,
  materialId: string,
) => {
  try {
    await api.delete(`/classrooms/${classroomId}/materials/${materialId}`);
  } catch (error) {
    let errorMessage = 'Failed to delete material!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
