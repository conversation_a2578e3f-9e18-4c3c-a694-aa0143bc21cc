import { api } from '@/shared';
import axios from 'axios';

export const uploadMaterial = async (
  classroomId: string,
  data: {
    title: string;
    file: File;
  },
) => {
  try {
    const formData = new FormData();

    formData.append('file', data.file);
    formData.append('title', data.title);
    formData.append('tags', 'pdf');

    await api.post(`/classrooms/${classroomId}/materials/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  } catch (error) {
    let errorMessage = 'Failed to delete material!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
