import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteMaterial } from '../api';
import { useNotifications } from '@/shared';

export const useDeleteMaterial = (classroomId: string, materialId: string) => {
  const { addNotification, addToast } = useNotifications();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => deleteMaterial(classroomId, materialId),
    onSuccess: () => {
      addToast?.({ text: 'notifications.success.materialDeleted' });
      queryClient.invalidateQueries({ queryKey: ['materials', classroomId] });
    },
    onError: () => {
      addNotification?.({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
