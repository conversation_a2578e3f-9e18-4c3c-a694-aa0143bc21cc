import { useQuery } from '@tanstack/react-query';
import { getMaterialDownloadLink } from '../api';

export const useDownloadMaterial = (
  classroomId: string,
  materialId: string,
  enabled: boolean,
) => {
  return useQuery({
    queryKey: ['materials', classroomId, materialId],
    queryFn: () => getMaterialDownloadLink(classroomId, materialId),
    enabled,
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 10,
    retry: 0,
  });
};
