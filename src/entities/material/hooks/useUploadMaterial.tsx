import { useMutation, useQueryClient } from '@tanstack/react-query';
import { uploadMaterial } from '../api';
import { useNotifications } from '@/shared';

export const useUploadMaterial = (
  classroomId: string,
  close: (show: boolean) => void,
) => {
  const { addNotification, addToast } = useNotifications();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ file, title }: { file: File; title: string }) =>
      uploadMaterial(classroomId, { file, title }),
    onSuccess: () => {
      addToast?.({ text: 'notifications.success.materialUploaded' });
      queryClient.invalidateQueries({ queryKey: ['materials', classroomId] });
      close(false);
    },
    onError: () => {
      addNotification?.({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
