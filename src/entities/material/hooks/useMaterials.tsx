import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router';
import { useQuery } from '@tanstack/react-query';
import { getMaterials } from '../api';

export const useMaterials = (
  id: string,
  { limit }: { limit: number },
  enabled: boolean,
) => {
  const location = useLocation();
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);

  const query = useQuery({
    queryKey: ['materials', id, currentPage],
    queryFn: () => getMaterials(id, { page: currentPage, limit }),
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 10,
    enabled,
  });

  const handlePageChange = (newPage: number) => {
    const totalPages = query.data?.data?.pagination?.total_pages || 1;
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  useEffect(() => {
    if (query.data && !query.isPending) {
      const totalPages = query.data.data?.pagination?.total_pages || 1;
      const hasItems = query.data.data?.items?.length > 0;

      if (!hasItems && currentPage > 1) {
        const targetPage = Math.min(currentPage - 1, totalPages);
        setCurrentPage(targetPage);
      } else if (currentPage > totalPages && totalPages > 0) {
        setCurrentPage(totalPages);
      }
    }
  }, [query.data, query.isPending, currentPage]);

  useEffect(() => {
    queryClient.invalidateQueries({ queryKey: ['materials'] });
    setCurrentPage(1);
  }, [location.pathname, queryClient]);

  useEffect(() => {
    setCurrentPage(1);
  }, [limit]);

  return {
    data: query.data,
    materials: query.data?.data?.items || [],
    totalPages: query.data?.data?.pagination?.total_pages || 1,
    totalItems: query.data?.data?.pagination?.total || 0,
    currentPage,
    handlePageChange,
    isPending: query.isPending,
  };
};
