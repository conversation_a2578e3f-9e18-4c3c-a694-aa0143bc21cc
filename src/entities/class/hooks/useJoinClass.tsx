import { useMutation } from '@tanstack/react-query';
import { joinClass } from '../api';
import { useNotifications } from '@/shared';
import { useNavigate } from 'react-router';
import { useCookies } from 'react-cookie';

export const useJoinClass = () => {
  const { addNotification } = useNotifications();
  const [, setCookie] = useCookies(['livekit-token', 'livekit-url']);
  const navigate = useNavigate();

  return useMutation({
    mutationFn: async ({
      classroomId,
      classId,
    }: {
      classroomId: string;
      classId: string;
    }) => {
      return await joinClass(classroomId, classId);
    },
    onSuccess: (data) => {
      setCookie('livekit-token', data.token.token, {
        path: '/',
        secure: true,
        sameSite: 'lax',
      });
      setCookie('livekit-url', data.token.url, {
        path: '/',
        secure: true,
        sameSite: 'lax',
      });

      navigate(`/classrooms/${data.classroomId}/class/${data.class.id}`);
    },
    onError: () => {
      addNotification?.({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
