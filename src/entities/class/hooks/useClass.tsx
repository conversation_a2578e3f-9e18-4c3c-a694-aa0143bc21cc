import { useQuery } from '@tanstack/react-query';
import { getClass } from '../api';
import { useEffect } from 'react';
import { useUser } from '@/entities/user';
import { useACLContext, useClassroomSettingsContext } from '@/shared';

export const useClass = (id: string, classroomId: string) => {
  const { user } = useUser();
  const { updateACL } = useACLContext();
  const { updateClassroomSettings } = useClassroomSettingsContext();
  const query = useQuery({
    queryKey: ['class', id, classroomId],
    queryFn: () => getClass(id, classroomId),
    staleTime: 10 * (60 * 1000),
    gcTime: 15 * (60 * 1000),
  });

  useEffect(() => {
    if (query.data) {
      query.data.participants.map((participant) => {
        if (participant.userId === user!.id) {
          updateACL(query.data.roomId, participant.acl);
        }
      });

      if (query.data.settings && query.data.classroomSettings) {
        updateClassroomSettings(query.data.roomId, {
          canAnonymousJoin: query.data.classroomSettings.canAnonymousJoin,
          ...query.data.settings,
        });
      }
    }
  }, [query.data]);

  return query;
};
