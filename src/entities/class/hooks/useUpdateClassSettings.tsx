import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateClassSettings } from '../api';
import { useNotifications } from '@/shared';

export const useUpdateClassSettings = (
  classroomId: string,
  classId: string,
) => {
  const { addNotification } = useNotifications();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      settings,
    }: {
      settings: {
        allowSharing: boolean;
        allowChat: boolean;
      };
    }) => {
      await updateClassSettings(classroomId, classId, settings);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['class', classId, classroomId],
      });
    },
    onError: () => {
      addNotification({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
