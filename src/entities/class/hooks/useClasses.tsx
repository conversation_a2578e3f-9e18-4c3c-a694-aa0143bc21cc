import { useQuery } from '@tanstack/react-query';
import { getClasses } from '../api';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router';

export const useClasses = (id: string, { limit }: { limit: number }) => {
  const location = useLocation();
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);

  const query = useQuery({
    queryKey: ['classes', id, currentPage],
    queryFn: () =>
      getClasses(id, {
        page: currentPage,
        limit,
      }),
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 10,
  });

  const handlePageChange = (newPage: number) => {
    const totalPages = query.data?.data?.pagination?.total_pages || 1;
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  useEffect(() => {
    queryClient.invalidateQueries({ queryKey: ['classes'] });
    setCurrentPage(1);
  }, [location.pathname, queryClient]);

  useEffect(() => {
    setCurrentPage(1);
  }, [limit]);

  return {
    data: query.data,
    classes: query.data?.data?.items || [],
    totalPages: query.data?.data?.pagination?.total_pages || 1,
    currentPage,
    handlePageChange,
    isPending: query.isPending,
  };
};
