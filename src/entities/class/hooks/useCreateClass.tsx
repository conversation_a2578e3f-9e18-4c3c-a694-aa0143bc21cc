import { useMutation } from '@tanstack/react-query';
import { useNavigate, useParams } from 'react-router';
import { createClass } from '../api';
import { useNotifications } from '@/shared';
import { useJoinClass } from './useJoinClass';

export const useCreateClass = (join: boolean = false) => {
  const navigate = useNavigate();
  const { addNotification } = useNotifications();
  const { id: classroomId } = useParams<{
    id: string;
  }>();
  const { mutate: joinClass } = useJoinClass();

  return useMutation({
    mutationFn: ({ id, title }: { id: string; title: string }) =>
      createClass(id, title),
    onSuccess: (data) => {
      if (join) {
        joinClass({
          classroomId: data.classroomId,
          classId: data.class.id,
        });
      }
    },
    onError: () => {
      addNotification?.({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });

      if (classroomId) navigate(`/classrooms/${classroomId}`);
    },
  });
};
