import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateParticipantSettings } from '../api';
import { useUser } from '@/entities/user';
import { useNotifications } from '@/shared';

export const useUpdateParticipantSettings = (
  classroomId: string,
  classId: string,
) => {
  const { user } = useUser();
  const { addNotification } = useNotifications();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      settings,
    }: {
      settings: {
        translationLanguage?: string;
        sharingConsent?: boolean;
        captionEnabled?: boolean;
      };
    }) => {
      await updateParticipantSettings(
        classroomId,
        classId,
        user!.id ?? '',
        settings,
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['class', classId, classroomId],
      });
    },
    onError: () => {
      addNotification({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
