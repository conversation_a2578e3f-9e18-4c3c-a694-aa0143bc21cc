import { Role } from '../../../shared';

export type ClassParticipant = {
  userId: string;
  displayName: string;
  firstName: string;
  lastName: string;
  profileImage: string;
  role: Role;
  status: string;
  isAnonymous: boolean;
  joinedAt: number;
  leftAt: number;
  isConnected: boolean;
  settings: {
    translationLanguage: string;
    sharingConsent: boolean;
    captionEnabled: boolean;
  };
  acl: {
    canCommentOnRecordings: boolean;
    canDownloadMaterials: boolean;
    canGuestInvite: boolean;
    canKickParticipant: boolean;
    canParticipantInvite: boolean;
    canStartClass: boolean;
    canUpdateClass: boolean;
    canUpdateParticipant: boolean;
    canUploadMaterials: boolean;
    canViewRecordings: boolean;
  };
};
