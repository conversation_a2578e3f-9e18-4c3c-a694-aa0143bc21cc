import { ClassParticipant } from './ClassParticipant';

export interface Class {
  id: string;
  roomId: string;
  title: string;
  status: 'active' | 'completed';
  startTime: number;
  endTime: number;
  participants: ClassParticipant[];
  summary: string;
  feedback: string;
  recordings: string[];
  createdAt: number;
  updatedAt: number;
  keyMoments: { timestamp: string; description: Record<string, string> }[];
  settings: {
    allowRecording: boolean;
    allowTranscription: boolean;
    allowTranslation: boolean;
    allowCaptions: boolean;
    allowSharing: boolean;
    allowMaterialsUpload: boolean;
    allowMaterialsDownload: boolean;
    allowChat: boolean;
    allowAnonymousJoin: boolean;
    allowInviteGuests: boolean;
    allowMultipleTeachers: boolean;
    allowMultipleStudents: boolean;
    allowComments: boolean;
  };
  classroomSettings: {
    canAnonymousJoin: boolean;
  };
}
