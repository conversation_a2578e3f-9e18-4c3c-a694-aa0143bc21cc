import { api } from '@/shared';
import { Class } from '../types';
import axios from 'axios';

interface GetClassesParams {
  page?: number;
  limit?: number;
}

interface ClassesApiResponse {
  data: {
    items: Class[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      total_pages: number;
      has_more: boolean;
    };
  };
  meta: {
    request_id: string;
    timestamp: string;
  };
}

export const getClasses = async (
  id: string,
  params?: GetClassesParams,
): Promise<ClassesApiResponse> => {
  try {
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    queryParams.set('status', 'completed');

    const queryString = queryParams.toString();
    const url = queryString
      ? `/classrooms/${id}/classes?${queryString}`
      : `/classrooms/${id}/classes`;

    const response = await api.get<ClassesApiResponse>(url, {
      headers: { 'Content-Type': 'application/json' },
    });

    return response.data;
  } catch (error) {
    let errorMessage = 'Failed to get all classes!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
