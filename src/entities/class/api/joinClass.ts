import { api } from '@/shared';
import axios from 'axios';
import { JoinClassResponse } from '../types';

export const joinClass = async (classroomId: string, classId: string) => {
  try {
    const response = await api.post<JoinClassResponse>(
      `/classrooms/${classroomId}/classes/${classId}/join`,
      { title: ' ' },
    );
    return { ...response.data.data, classroomId };
  } catch (error) {
    let errorMessage = 'Failed to join the class!';
    if (axios.isAxiosError(error) && error.response && error.response.data) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
