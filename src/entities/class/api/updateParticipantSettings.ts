import { api } from '@/shared';
import axios from 'axios';

export const updateParticipantSettings = async (
  classroomId: string,
  classId: string,
  userId: string,
  settings: {
    translationLanguage?: string;
    sharingConsent?: boolean;
    captionEnabled?: boolean;
  },
) => {
  try {
    await api.put(
      `/classrooms/${classroomId}/classes/${classId}/participants/${userId}/settings`,
      { ...settings },
    );
  } catch (error) {
    let errorMessage = 'Failed to update class participant settings!';
    if (axios.isAxiosError(error) && error.response && error.response.data) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
