import { api } from '@/shared';
import axios from 'axios';
import { Class } from '../types';

export const getClass = async (
  id: string,
  classroomId: string,
): Promise<Class> => {
  try {
    const response = await api.get<{ data: Class }>(
      `/classrooms/${classroomId}/classes/${id}`,
    );
    return response.data.data;
  } catch (error) {
    let errorMessage = 'Failed to get class!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
