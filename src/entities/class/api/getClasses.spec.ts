import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { Class } from '../types';
import { getClasses } from './getClasses';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { get: vi.fn() },
}));

describe('Class Service API', () => {
  let mockGet: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockGet = api.get as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' &&
          error !== null &&
          'response' in (error as object),
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  describe('getClasses', () => {
    const mockClassesResponse = {
      data: {
        items: [
          {
            id: 'a',
            roomId: 'room1',
            title: 'Class A',
            status: 'completed',
            startTime: 1620000000000,
            endTime: 1620003600000,
            participants: [],
            summary: 'Summary A',
            feedback: 'Feedback A',
            recordings: [],
            createdAt: 1620000000000,
            updatedAt: 1620003600000,
            keyMoments: [
              {
                timestamp: '1620000100000',
                description: { en: 'Introduction', es: 'Introducción' },
              },
            ],
            settings: {
              allowRecording: true,
              allowTranscription: true,
              allowTranslation: true,
              allowCaptions: true,
              allowSharing: true,
              allowMaterialsUpload: true,
              allowMaterialsDownload: true,
              allowChat: true,
              allowAnonymousJoin: true,
              allowInviteGuests: true,
              allowMultipleTeachers: true,
              allowMultipleStudents: true,
              allowComments: true,
            },
            classroomSettings: {
              canAnonymousJoin: true,
            },
          },
          {
            id: 'b',
            roomId: 'room1',
            title: 'Class B',
            status: 'completed',
            startTime: 1620010000000,
            endTime: 1620013600000,
            participants: [],
            summary: 'Summary B',
            feedback: 'Feedback B',
            recordings: ['rec1'],
            createdAt: 1620010000000,
            updatedAt: 1620013600000,
            keyMoments: [
              {
                timestamp: '1620010100000',
                description: { en: 'Main topic', fr: 'Sujet principal' },
              },
            ],
            settings: {
              allowRecording: true,
              allowTranscription: true,
              allowTranslation: true,
              allowCaptions: true,
              allowSharing: true,
              allowMaterialsUpload: true,
              allowMaterialsDownload: true,
              allowChat: true,
              allowAnonymousJoin: true,
              allowInviteGuests: true,
              allowMultipleTeachers: true,
              allowMultipleStudents: true,
              allowComments: true,
            },
            classroomSettings: {
              canAnonymousJoin: true,
            },
          },
        ] as Class[],
        pagination: {
          page: 1,
          limit: 10,
          total: 2,
          total_pages: 1,
          has_more: false,
        },
      },
      meta: {
        request_id: 'req-123',
        timestamp: '2023-01-01T00:00:00Z',
      },
    };

    it('should return full API response on success', async () => {
      mockGet.mockResolvedValue({ data: mockClassesResponse });

      const result = await getClasses('room1');

      expect(mockGet).toHaveBeenCalledWith(
        '/classrooms/room1/classes?status=completed',
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      expect(result).toEqual(mockClassesResponse);
    });

    it('should handle pagination parameters', async () => {
      mockGet.mockResolvedValue({ data: mockClassesResponse });

      await getClasses('room1', { page: 2, limit: 5 });

      expect(mockGet).toHaveBeenCalledWith(
        '/classrooms/room1/classes?page=2&limit=5&status=completed',
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
    });

    it('should handle only page parameter', async () => {
      mockGet.mockResolvedValue({ data: mockClassesResponse });

      await getClasses('room1', { page: 3 });

      expect(mockGet).toHaveBeenCalledWith(
        '/classrooms/room1/classes?page=3&status=completed',
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
    });

    it('should handle only limit parameter', async () => {
      mockGet.mockResolvedValue({ data: mockClassesResponse });

      await getClasses('room1', { limit: 20 });

      expect(mockGet).toHaveBeenCalledWith(
        '/classrooms/room1/classes?limit=20&status=completed',
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
    });

    it('should always include status=completed in query params', async () => {
      mockGet.mockResolvedValue({ data: mockClassesResponse });

      await getClasses('room1');

      expect(mockGet).toHaveBeenCalledWith(
        '/classrooms/room1/classes?status=completed',
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
    });

    it('should throw provided error message when API error with response', async () => {
      const errorResponse = {
        response: { data: { error: 'Fetch error' } },
      } as AxiosError;
      mockGet.mockRejectedValue(errorResponse);

      await expect(getClasses('room1')).rejects.toThrow('Fetch error');
    });

    it('should throw default message when response has no error message', async () => {
      const errorResponse = { response: { data: {} } } as AxiosError;
      mockGet.mockRejectedValue(errorResponse);

      await expect(getClasses('room1')).rejects.toThrow(
        'Failed to get all classes!',
      );
    });

    it('should throw default message on non-Axios error', async () => {
      mockGet.mockRejectedValue(new Error('Network fail'));
      isAxiosErrorSpy.mockReturnValue(false);

      await expect(getClasses('room1')).rejects.toThrow(
        'Failed to get all classes!',
      );
    });

    it('should handle empty parameters object', async () => {
      mockGet.mockResolvedValue({ data: mockClassesResponse });

      await getClasses('room1', {});

      expect(mockGet).toHaveBeenCalledWith(
        '/classrooms/room1/classes?status=completed',
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
    });
  });
});
