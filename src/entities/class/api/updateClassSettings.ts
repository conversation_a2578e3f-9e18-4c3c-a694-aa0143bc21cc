import { api } from '@/shared';
import axios from 'axios';

export const updateClassSettings = async (
  classroomId: string,
  classId: string,
  settings: {
    allowSharing: boolean;
    allowChat: boolean;
  },
) => {
  try {
    const response = await api.put(
      `/classrooms/${classroomId}/classes/${classId}`,
      { settings },
    );
    return { ...response.data.data, classroomId };
  } catch (error) {
    let errorMessage = 'Failed to update class settings!';
    if (axios.isAxiosError(error) && error.response && error.response.data) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
