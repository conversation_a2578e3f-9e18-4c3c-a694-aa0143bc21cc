import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { JoinClassResponse, Class } from '../types';
import { joinClass } from './joinClass';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { post: vi.fn() },
}));

describe('joinClass', () => {
  let mockPost: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockPost = api.post as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' &&
          error !== null &&
          'response' in (error as object),
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  const mockClass: Class = {
    id: 'class-123',
    roomId: 'room-456',
    title: 'Test Class',
    status: 'active',
    startTime: 1620000000000,
    endTime: 1620003600000,
    participants: [],
    summary: 'Test class summary',
    feedback: 'Initial feedback text',
    recordings: [],
    createdAt: 1620000000000,
    updatedAt: 1620003600000,
    keyMoments: [
      {
        timestamp: '1620000100000',
        description: { en: 'Class started', es: 'Clase iniciada' },
      },
    ],
    settings: {
      allowRecording: true,
      allowTranscription: true,
      allowTranslation: true,
      allowCaptions: true,
      allowSharing: true,
      allowMaterialsUpload: true,
      allowMaterialsDownload: true,
      allowChat: true,
      allowAnonymousJoin: true,
      allowInviteGuests: true,
      allowMultipleTeachers: true,
      allowMultipleStudents: true,
      allowComments: true,
    },
    classroomSettings: {
      canAnonymousJoin: true,
    },
  };

  const mockJoinResponse: JoinClassResponse = {
    data: {
      class: mockClass,
      token: {
        token: 'token-abc-123',
        url: 'https://meet.example.com/room123',
        expiresAt: 1620003600000,
        roomId: 'room-456',
        role: 'student',
        partyType: 'participant',
      },
    },
  };

  it('should successfully join a class and return data with classroomId', async () => {
    mockPost.mockResolvedValue({ data: mockJoinResponse });

    const result = await joinClass('classroom-1', 'class-1');

    expect(mockPost).toHaveBeenCalledWith(
      '/classrooms/classroom-1/classes/class-1/join',
      { title: ' ' },
    );

    expect(result).toEqual({
      ...mockJoinResponse.data,
      classroomId: 'classroom-1',
    });
  });

  it('should include classroomId in the returned data', async () => {
    mockPost.mockResolvedValue({ data: mockJoinResponse });

    const result = await joinClass('test-classroom', 'test-class');

    expect(result).toHaveProperty('classroomId', 'test-classroom');
    expect(result).toHaveProperty('class');
    expect(result).toHaveProperty('token');
    expect(result.class).toEqual(mockClass);
    expect(result.token).toEqual(mockJoinResponse.data.token);
  });

  it('should send correct request body with title field', async () => {
    mockPost.mockResolvedValue({ data: mockJoinResponse });

    await joinClass('classroom-1', 'class-1');

    expect(mockPost).toHaveBeenCalledWith(expect.any(String), { title: ' ' });
  });

  it('should construct correct URL with parameters', async () => {
    mockPost.mockResolvedValue({ data: mockJoinResponse });

    await joinClass('my-classroom', 'my-class');

    expect(mockPost).toHaveBeenCalledWith(
      '/classrooms/my-classroom/classes/my-class/join',
      expect.any(Object),
    );
  });

  it('should handle different classroom and class ID formats', async () => {
    mockPost.mockResolvedValue({ data: mockJoinResponse });

    await joinClass('classroom_with_underscores', 'class-with-dashes-123');

    expect(mockPost).toHaveBeenCalledWith(
      '/classrooms/classroom_with_underscores/classes/class-with-dashes-123/join',
      { title: ' ' },
    );
  });

  it('should throw provided error message when API error with response', async () => {
    const errorResponse = {
      response: { data: { error: 'Class is full' } },
    } as AxiosError;
    mockPost.mockRejectedValue(errorResponse);

    await expect(joinClass('classroom-1', 'class-1')).rejects.toThrow(
      'Class is full',
    );
  });

  it('should throw specific error for access denied', async () => {
    const errorResponse = {
      response: { data: { error: 'Access denied to this class' } },
    } as AxiosError;
    mockPost.mockRejectedValue(errorResponse);

    await expect(joinClass('classroom-1', 'class-1')).rejects.toThrow(
      'Access denied to this class',
    );
  });

  it('should throw default message when response has no error message', async () => {
    const errorResponse = {
      response: { data: {} },
    } as AxiosError;
    mockPost.mockRejectedValue(errorResponse);

    await expect(joinClass('classroom-1', 'class-1')).rejects.toThrow(
      'Failed to join the class!',
    );
  });

  it('should throw default message when response data is null', async () => {
    const errorResponse = {
      response: { data: null },
    } as AxiosError;
    mockPost.mockRejectedValue(errorResponse);

    await expect(joinClass('classroom-1', 'class-1')).rejects.toThrow(
      'Failed to join the class!',
    );
  });

  it('should throw default message on non-Axios error', async () => {
    mockPost.mockRejectedValue(new Error('Network connection failed'));
    isAxiosErrorSpy.mockReturnValue(false);

    await expect(joinClass('classroom-1', 'class-1')).rejects.toThrow(
      'Failed to join the class!',
    );
  });

  it('should handle empty string parameters', async () => {
    mockPost.mockResolvedValue({ data: mockJoinResponse });

    await joinClass('', '');

    expect(mockPost).toHaveBeenCalledWith('/classrooms//classes//join', {
      title: ' ',
    });
  });

  it('should preserve all original response data properties', async () => {
    const customClass: Class = {
      ...mockClass,
      id: 'custom-class-456',
      title: 'Custom Class Title',
      feedback: 'Custom feedback text',
      keyMoments: [
        {
          timestamp: '1620000200000',
          description: { en: 'Custom moment' },
        },
      ],
    };

    const extendedResponse: JoinClassResponse = {
      data: {
        class: customClass,
        token: {
          token: 'extended-token-789',
          url: 'https://custom.meet.example.com/room789',
          expiresAt: 1620007200000,
          roomId: 'room-789',
          role: 'teacher',
          partyType: 'moderator',
        },
      },
    };

    mockPost.mockResolvedValue({ data: extendedResponse });

    const result = await joinClass('classroom-1', 'class-1');

    expect(result).toEqual({
      ...extendedResponse.data,
      classroomId: 'classroom-1',
    });
    expect(result.class).toEqual(customClass);
    expect(result.token.role).toBe('teacher');
    expect(result.token.partyType).toBe('moderator');
  });

  it('should verify token properties are correctly returned', async () => {
    mockPost.mockResolvedValue({ data: mockJoinResponse });

    const result = await joinClass('classroom-1', 'class-1');

    expect(result.token).toHaveProperty('token', 'token-abc-123');
    expect(result.token).toHaveProperty(
      'url',
      'https://meet.example.com/room123',
    );
    expect(result.token).toHaveProperty('expiresAt', 1620003600000);
    expect(result.token).toHaveProperty('roomId', 'room-456');
    expect(result.token).toHaveProperty('role', 'student');
    expect(result.token).toHaveProperty('partyType', 'participant');
  });

  it('should verify class properties are correctly returned', async () => {
    mockPost.mockResolvedValue({ data: mockJoinResponse });

    const result = await joinClass('classroom-1', 'class-1');

    expect(result.class).toEqual(mockClass);
    expect(result.class).toHaveProperty('id', 'class-123');
    expect(result.class).toHaveProperty('title', 'Test Class');
    expect(result.class).toHaveProperty('status', 'active');
    expect(result.class).toHaveProperty('feedback', 'Initial feedback text');
    expect(result.class.keyMoments[0]).toHaveProperty(
      'description',
      expect.any(Object),
    );
  });
});
