/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types */
import axios, { type AxiosError } from 'axios';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { api } from '@/shared';
import { createClass } from './createClass';

describe('createClass()', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('createClass()', () => {
    it('resolves with create response and classroomId on success', async () => {
      const fakeResponse = {
        id: 'cls2',
        name: 'Lesson 2',
      };
      vi.spyOn(api, 'post').mockResolvedValueOnce({ data: { data: fakeResponse } });

      const result = await createClass('room123', 'Lesson 2');
      expect(result).toEqual({ class: fakeResponse, classroomId: 'room123' });
      expect(api.post).toHaveBeenCalledWith('/classrooms/room123/classes', {
        name: 'Lesson 2',
      });
    });

    it('throws generic message on non-Axios error', async () => {
      vi.spyOn(api, 'post').mockRejectedValueOnce(new Error('oops'));

      await expect(createClass('room123', 'Lesson')).rejects.toThrow(
        'Failed to create a classroom!',
      );
    });

    it('throws server-provided error when AxiosError with response.data.error', async () => {
      const err = {
        isAxiosError: true,
        response: { data: { error: 'Bad name' } },
      };
      vi.spyOn(axios, 'isAxiosError').mockImplementation((e): e is AxiosError =>
        Boolean((e as any).isAxiosError),
      );
      vi.spyOn(api, 'post').mockRejectedValueOnce(err);

      await expect(createClass('room123', 'Lesson')).rejects.toThrow(
        'Bad name',
      );
    });

    it('falls back to generic when AxiosError without response error message', async () => {
      const err = { isAxiosError: true };
      vi.spyOn(axios, 'isAxiosError').mockImplementation((e): e is AxiosError =>
        Boolean((e as any).isAxiosError),
      );
      vi.spyOn(api, 'post').mockRejectedValueOnce(err);

      await expect(createClass('room123', 'Lesson')).rejects.toThrow(
        'Failed to create a classroom!',
      );
    });
  });
});
