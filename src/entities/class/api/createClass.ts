import { api } from '@/shared';
import axios from 'axios';
import { Class, CreateClassResponse } from '../types';

export const createClass = async (
  id: string,
  name = '',
): Promise<{ class: Class } & { classroomId: string }> => {
  try {
    const response = await api.post<CreateClassResponse>(
      `/classrooms/${id}/classes`,
      { name },
    );
    return { class: response.data.data, classroomId: id };
  } catch (error) {
    let errorMessage = 'Failed to create a classroom!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
