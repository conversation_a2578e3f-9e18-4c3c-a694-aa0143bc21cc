import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { updateParticipantSettings } from './updateParticipantSettings';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { put: vi.fn() },
}));

describe('Update Participant Settings API', () => {
  let mockPut: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockPut = api.put as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' && error !== null && 'response' in error,
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  describe('updateParticipantSettings', () => {
    it('should update participant settings successfully', async () => {
      mockPut.mockResolvedValueOnce({ status: 200 });

      const settings = {
        translationLanguage: 'es',
        sharingConsent: true,
      };

      await updateParticipantSettings(
        'classroom123',
        'class456',
        'user789',
        settings,
      );

      expect(mockPut).toHaveBeenCalledWith(
        '/classrooms/classroom123/classes/class456/participants/user789/settings',
        { translationLanguage: 'es', sharingConsent: true },
      );
    });

    it('should complete without returning anything', async () => {
      mockPut.mockResolvedValueOnce({ status: 204 });

      const settings = { translationLanguage: 'fr' };

      const result = await updateParticipantSettings(
        'classroom123',
        'class456',
        'user789',
        settings,
      );

      expect(result).toBeUndefined();
    });

    it('should work with different IDs', async () => {
      const testCases = [
        {
          classroomId: 'abc-123',
          classId: 'class-001',
          userId: 'user-001',
        },
        {
          classroomId: 'classroom-xyz-789',
          classId: 'class-session-456',
          userId: 'participant-123',
        },
        {
          classroomId: '12345',
          classId: '67890',
          userId: 'guest-999',
        },
        {
          classroomId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
          classId: '550e8400-e29b-41d4-a716-446655440000',
          userId: '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
        },
      ];

      for (const { classroomId, classId, userId } of testCases) {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings(classroomId, classId, userId, {
          translationLanguage: 'en',
        });

        expect(mockPut).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/classes/${classId}/participants/${userId}/settings`,
          expect.any(Object),
        );
      }
    });

    describe('translationLanguage setting', () => {
      it('should handle different language codes', async () => {
        const languages = [
          'en',
          'es',
          'fr',
          'de',
          'it',
          'pt',
          'ru',
          'zh',
          'ja',
          'ko',
        ];

        for (const language of languages) {
          mockPut.mockResolvedValueOnce({ status: 200 });

          await updateParticipantSettings(
            'classroom123',
            'class456',
            'user789',
            {
              translationLanguage: language,
            },
          );

          expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
            translationLanguage: language,
          });
        }
      });

      it('should handle full locale codes', async () => {
        const locales = [
          'en-US',
          'es-ES',
          'fr-FR',
          'de-DE',
          'pt-BR',
          'zh-CN',
          'en-GB',
        ];

        for (const locale of locales) {
          mockPut.mockResolvedValueOnce({ status: 200 });

          await updateParticipantSettings(
            'classroom123',
            'class456',
            'user789',
            {
              translationLanguage: locale,
            },
          );

          expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
            translationLanguage: locale,
          });
        }
      });

      it('should handle undefined translationLanguage', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings('classroom123', 'class456', 'user789', {
          sharingConsent: true,
        });

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          sharingConsent: true,
        });
      });
    });

    describe('sharingConsent setting', () => {
      it('should handle sharingConsent: true', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings('classroom123', 'class456', 'user789', {
          sharingConsent: true,
        });

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          sharingConsent: true,
        });
      });

      it('should handle sharingConsent: false', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings('classroom123', 'class456', 'user789', {
          sharingConsent: false,
        });

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          sharingConsent: false,
        });
      });

      it('should handle undefined sharingConsent', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings('classroom123', 'class456', 'user789', {
          translationLanguage: 'en',
        });

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          translationLanguage: 'en',
        });
      });
    });

    describe('settings combinations', () => {
      it('should handle both settings together', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        const settings = {
          translationLanguage: 'es',
          sharingConsent: true,
        };

        await updateParticipantSettings(
          'classroom123',
          'class456',
          'user789',
          settings,
        );

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          translationLanguage: 'es',
          sharingConsent: true,
        });
      });

      it('should handle empty settings object', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings(
          'classroom123',
          'class456',
          'user789',
          {},
        );

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {});
      });

      it('should handle only translationLanguage', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings('classroom123', 'class456', 'user789', {
          translationLanguage: 'fr',
        });

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          translationLanguage: 'fr',
        });
      });

      it('should handle only sharingConsent', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings('classroom123', 'class456', 'user789', {
          sharingConsent: false,
        });

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          sharingConsent: false,
        });
      });
    });

    describe('URL construction', () => {
      it('should construct correct endpoint path', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings(
          'classroom123',
          'class456',
          'user789',
          {},
        );

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom123/classes/class456/participants/user789/settings',
          expect.any(Object),
        );
      });

      it('should handle special characters in IDs', async () => {
        const classroomId = 'classroom-with-dashes_and_underscores';
        const classId = 'class-session_with_special-chars';
        const userId = 'user-with-special_chars_123';

        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings(classroomId, classId, userId, {});

        expect(mockPut).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/classes/${classId}/participants/${userId}/settings`,
          expect.any(Object),
        );
      });

      it('should handle numeric IDs', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings('12345', '67890', '11111', {});

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/12345/classes/67890/participants/11111/settings',
          expect.any(Object),
        );
      });
    });

    describe('response handling', () => {
      it('should handle 200 OK response', async () => {
        mockPut.mockResolvedValueOnce({
          status: 200,
          data: { message: 'Settings updated' },
        });

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {
            translationLanguage: 'en',
          }),
        ).resolves.toBeUndefined();
      });

      it('should handle 204 No Content response', async () => {
        mockPut.mockResolvedValueOnce({ status: 204 });

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {
            sharingConsent: true,
          }),
        ).resolves.toBeUndefined();
      });

      it('should handle response with metadata', async () => {
        mockPut.mockResolvedValueOnce({
          status: 200,
          data: {
            message: 'Settings updated successfully',
            updatedAt: '2024-01-15T12:00:00Z',
            settings: { translationLanguage: 'es', sharingConsent: true },
          },
        });

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {
            translationLanguage: 'es',
          }),
        ).resolves.toBeUndefined();
      });
    });

    describe('error handling', () => {
      it('should throw provided error message for participant not found', async () => {
        const errorResponse = {
          response: { data: { error: 'Participant not found' } },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateParticipantSettings(
            'classroom123',
            'class456',
            'nonexistent-user',
            {},
          ),
        ).rejects.toThrow('Participant not found');
      });

      it('should throw provided error message for class not found', async () => {
        const errorResponse = {
          response: { data: { error: 'Class not found' } },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateParticipantSettings(
            'classroom123',
            'nonexistent-class',
            'user789',
            {},
          ),
        ).rejects.toThrow('Class not found');
      });

      it('should throw provided error message for classroom not found', async () => {
        const errorResponse = {
          response: { data: { error: 'Classroom not found' } },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateParticipantSettings(
            'nonexistent-classroom',
            'class456',
            'user789',
            {},
          ),
        ).rejects.toThrow('Classroom not found');
      });

      it('should throw provided error message for insufficient permissions', async () => {
        const errorResponse = {
          response: {
            data: {
              error: 'Insufficient permissions to update participant settings',
            },
          },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {}),
        ).rejects.toThrow(
          'Insufficient permissions to update participant settings',
        );
      });

      it('should throw provided error message for invalid language code', async () => {
        const errorResponse = {
          response: { data: { error: 'Invalid translation language code' } },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {
            translationLanguage: 'invalid',
          }),
        ).rejects.toThrow('Invalid translation language code');
      });

      it('should throw provided error message for class not active', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Cannot update settings for inactive class' },
          },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateParticipantSettings(
            'classroom123',
            'inactive-class',
            'user789',
            {},
          ),
        ).rejects.toThrow('Cannot update settings for inactive class');
      });

      it('should throw default message when API error has no error field', async () => {
        const errorResponse = { response: { data: {} } };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {}),
        ).rejects.toThrow('Failed to update class participant settings!');
      });

      it('should throw default message when API error has null error', async () => {
        const errorResponse = { response: { data: { error: null } } };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {}),
        ).rejects.toThrow('Failed to update class participant settings!');
      });

      it('should throw default message when API error has undefined error', async () => {
        const errorResponse = { response: { data: { error: undefined } } };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {}),
        ).rejects.toThrow('Failed to update class participant settings!');
      });

      it('should throw default message when API error has no response data', async () => {
        const errorResponse = { response: { data: undefined } };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {}),
        ).rejects.toThrow('Failed to update class participant settings!');
      });

      it('should throw default message when API error has no response', async () => {
        const errorResponse = { response: undefined };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {}),
        ).rejects.toThrow('Failed to update class participant settings!');
      });

      it('should throw default message when error is not axios error', async () => {
        isAxiosErrorSpy.mockReturnValueOnce(false);
        mockPut.mockRejectedValueOnce(new Error('Network failure'));

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {}),
        ).rejects.toThrow('Failed to update class participant settings!');
      });

      it('should handle generic error correctly', async () => {
        mockPut.mockRejectedValueOnce(new Error('Some generic error'));

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {}),
        ).rejects.toThrow('Failed to update class participant settings!');
      });

      it('should handle timeout error', async () => {
        const timeoutError = {
          response: {
            data: {
              error: 'Request timeout while updating participant settings',
            },
          },
        };
        mockPut.mockRejectedValueOnce(timeoutError as AxiosError);

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {}),
        ).rejects.toThrow(
          'Request timeout while updating participant settings',
        );
      });

      it('should handle server error', async () => {
        const serverError = {
          response: {
            data: { error: 'Internal server error during settings update' },
          },
        };
        mockPut.mockRejectedValueOnce(serverError as AxiosError);

        await expect(
          updateParticipantSettings('classroom123', 'class456', 'user789', {}),
        ).rejects.toThrow('Internal server error during settings update');
      });
    });

    describe('edge cases', () => {
      it('should handle very long language codes', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        const longLanguageCode = 'en-US-x-very-long-custom-variant-code';

        await updateParticipantSettings('classroom123', 'class456', 'user789', {
          translationLanguage: longLanguageCode,
        });

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          translationLanguage: longLanguageCode,
        });
      });

      it('should handle multiple rapid updates', async () => {
        const updates = [
          { translationLanguage: 'en' },
          { sharingConsent: true },
          { translationLanguage: 'es', sharingConsent: false },
        ];

        updates.forEach(() => {
          mockPut.mockResolvedValueOnce({ status: 200 });
        });

        for (const settings of updates) {
          await updateParticipantSettings(
            'classroom123',
            'class456',
            'user789',
            settings,
          );
        }

        expect(mockPut).toHaveBeenCalledTimes(3);
      });

      it('should handle settings for different participants in same class', async () => {
        const participants = ['user1', 'user2', 'user3'];
        const settingsMap = [
          { translationLanguage: 'en' },
          { translationLanguage: 'es' },
          { translationLanguage: 'fr', sharingConsent: true },
        ];

        participants.forEach(() => {
          mockPut.mockResolvedValueOnce({ status: 200 });
        });

        for (let i = 0; i < participants.length; i++) {
          await updateParticipantSettings(
            'classroom123',
            'class456',
            participants[i],
            settingsMap[i],
          );
        }

        expect(mockPut).toHaveBeenCalledTimes(3);
        expect(mockPut).toHaveBeenNthCalledWith(
          1,
          expect.stringContaining('user1'),
          { translationLanguage: 'en' },
        );
        expect(mockPut).toHaveBeenNthCalledWith(
          2,
          expect.stringContaining('user2'),
          { translationLanguage: 'es' },
        );
        expect(mockPut).toHaveBeenNthCalledWith(
          3,
          expect.stringContaining('user3'),
          { translationLanguage: 'fr', sharingConsent: true },
        );
      });

      it('should handle settings with undefined values explicitly passed', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        const settingsWithUndefined = {
          translationLanguage: undefined,
          sharingConsent: undefined,
        };

        await updateParticipantSettings(
          'classroom123',
          'class456',
          'user789',
          settingsWithUndefined,
        );

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          translationLanguage: undefined,
          sharingConsent: undefined,
        });
      });
    });

    describe('business logic scenarios', () => {
      it('should handle enabling translation for participant', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings('classroom123', 'class456', 'user789', {
          translationLanguage: 'es',
          sharingConsent: true,
        });

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          translationLanguage: 'es',
          sharingConsent: true,
        });
      });

      it('should handle disabling sharing consent', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings('classroom123', 'class456', 'user789', {
          sharingConsent: false,
        });

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          sharingConsent: false,
        });
      });

      it('should handle switching translation language mid-class', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings(
          'classroom123',
          'active-class-789',
          'user789',
          {
            translationLanguage: 'zh-CN',
          },
        );

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom123/classes/active-class-789/participants/user789/settings',
          { translationLanguage: 'zh-CN' },
        );
      });
    });

    describe('captionEnabled setting', () => {
      it('should handle captionEnabled: true', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings('classroom123', 'class456', 'user789', {
          captionEnabled: true,
        });

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          captionEnabled: true,
        });
      });

      it('should handle captionEnabled: false', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings('classroom123', 'class456', 'user789', {
          captionEnabled: false,
        });

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          captionEnabled: false,
        });
      });

      it('should handle undefined captionEnabled', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings('classroom123', 'class456', 'user789', {
          translationLanguage: 'en',
        });

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          translationLanguage: 'en',
        });
      });
    });

    describe('settings combinations', () => {
      it('should handle all three settings together', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        const settings = {
          translationLanguage: 'es',
          sharingConsent: true,
          captionEnabled: true,
        };

        await updateParticipantSettings(
          'classroom123',
          'class456',
          'user789',
          settings,
        );

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          translationLanguage: 'es',
          sharingConsent: true,
          captionEnabled: true,
        });
      });

      it('should handle only captionEnabled', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        await updateParticipantSettings('classroom123', 'class456', 'user789', {
          captionEnabled: true,
        });

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          captionEnabled: true,
        });
      });
    });

    describe('edge cases', () => {
      it('should handle settings with undefined captionEnabled explicitly passed', async () => {
        mockPut.mockResolvedValueOnce({ status: 200 });

        const settingsWithUndefined = {
          translationLanguage: undefined,
          sharingConsent: undefined,
          captionEnabled: undefined,
        };

        await updateParticipantSettings(
          'classroom123',
          'class456',
          'user789',
          settingsWithUndefined,
        );

        expect(mockPut).toHaveBeenCalledWith(expect.any(String), {
          translationLanguage: undefined,
          sharingConsent: undefined,
          captionEnabled: undefined,
        });
      });
    });
  });
});
