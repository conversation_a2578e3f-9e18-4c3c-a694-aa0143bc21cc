import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { Class } from '../types';
import { getActiveClass } from './getActiveClass';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { get: vi.fn() },
}));

describe('getActiveClass', () => {
  let mockGet: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockGet = api.get as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' &&
          error !== null &&
          'response' in (error as object),
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  const mockActiveClass: Class = {
    id: 'active-class-1',
    roomId: 'room1',
    title: 'Active Class',
    status: 'active',
    startTime: 1620000000000,
    endTime: 1620003600000,
    participants: [],
    summary: 'Active class summary',
    feedback: 'Overall class feedback text',
    recordings: [],
    createdAt: 1620000000000,
    updatedAt: 1620003600000,
    keyMoments: [
      {
        timestamp: '1620000100000',
        description: { en: 'Introductions', es: 'Presentaciones' },
      },
      {
        timestamp: '1620001000000',
        description: { en: 'Main Topic', fr: 'Sujet principal' },
      },
    ],
    settings: {
      allowRecording: true,
      allowTranscription: true,
      allowTranslation: true,
      allowCaptions: true,
      allowSharing: true,
      allowMaterialsUpload: true,
      allowMaterialsDownload: true,
      allowChat: true,
      allowAnonymousJoin: true,
      allowInviteGuests: true,
      allowMultipleTeachers: true,
      allowMultipleStudents: true,
      allowComments: true,
    },
    classroomSettings: {
      canAnonymousJoin: true,
    },
  };

  it('should return active class when one exists', async () => {
    const mockResponse = {
      data: {
        data: {
          items: [mockActiveClass],
        },
      },
    };

    mockGet.mockResolvedValue(mockResponse);

    const result = await getActiveClass('room1');

    expect(mockGet).toHaveBeenCalledWith(
      '/classrooms/room1/classes?page=1&limit=1&status=active',
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );
    expect(result).toEqual(mockActiveClass);
  });

  it('should return null when no active class exists', async () => {
    const mockResponse = {
      data: {
        data: {
          items: [],
        },
      },
    };

    mockGet.mockResolvedValue(mockResponse);

    const result = await getActiveClass('room1');

    expect(mockGet).toHaveBeenCalledWith(
      '/classrooms/room1/classes?page=1&limit=1&status=active',
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );
    expect(result).toBeNull();
  });

  it('should return first class when multiple active classes exist', async () => {
    const secondClass: Class = {
      ...mockActiveClass,
      id: 'active-class-2',
      title: 'Second Active Class',
    };

    const mockResponse = {
      data: {
        data: {
          items: [mockActiveClass, secondClass],
        },
      },
    };

    mockGet.mockResolvedValue(mockResponse);

    const result = await getActiveClass('room1');

    expect(result).toEqual(mockActiveClass);
    expect(result).not.toEqual(secondClass);
  });

  it('should use correct query parameters', async () => {
    const mockResponse = {
      data: {
        data: {
          items: [mockActiveClass],
        },
      },
    };

    mockGet.mockResolvedValue(mockResponse);

    await getActiveClass('classroom-123');

    expect(mockGet).toHaveBeenCalledWith(
      '/classrooms/classroom-123/classes?page=1&limit=1&status=active',
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );
  });

  it('should throw provided error message when API error with response', async () => {
    const errorResponse = {
      response: { data: { error: 'Access denied' } },
    } as AxiosError;
    mockGet.mockRejectedValue(errorResponse);

    await expect(getActiveClass('room1')).rejects.toThrow('Access denied');
  });

  it('should throw default message when response has no error message', async () => {
    const errorResponse = {
      response: { data: {} },
    } as AxiosError;
    mockGet.mockRejectedValue(errorResponse);

    await expect(getActiveClass('room1')).rejects.toThrow(
      'Failed to get all classes!',
    );
  });

  it('should throw default message when response data is null', async () => {
    const errorResponse = {
      response: { data: null },
    } as AxiosError;
    mockGet.mockRejectedValue(errorResponse);

    await expect(getActiveClass('room1')).rejects.toThrow(
      'Failed to get all classes!',
    );
  });

  it('should throw default message on non-Axios error', async () => {
    mockGet.mockRejectedValue(new Error('Network timeout'));
    isAxiosErrorSpy.mockReturnValue(false);

    await expect(getActiveClass('room1')).rejects.toThrow(
      'Failed to get all classes!',
    );
  });

  it('should handle undefined items array gracefully', async () => {
    const mockResponse = {
      data: {
        data: {
          items: undefined,
        },
      },
    };

    mockGet.mockResolvedValue(mockResponse);

    // With updated service using defensive programming, this now returns null
    const result = await getActiveClass('room1');

    expect(result).toBeNull();
  });

  it('should handle response with missing data structure', async () => {
    const mockResponse = {
      data: {},
    };

    mockGet.mockResolvedValue(mockResponse);

    // With updated service using defensive programming, this now returns null
    const result = await getActiveClass('room1');

    expect(result).toBeNull();
  });
});
