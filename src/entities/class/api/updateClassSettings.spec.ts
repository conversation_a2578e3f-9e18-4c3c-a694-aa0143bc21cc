import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { updateClassSettings } from './updateClassSettings';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { put: vi.fn() },
}));

describe('Update Class Settings API', () => {
  let mockPut: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockPut = api.put as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' && error !== null && 'response' in error,
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  describe('updateClassSettings', () => {
    const mockClassData = {
      id: 'class-456',
      title: 'Math Class',
      status: 'active',
      startedAt: 1704067200000,
      settings: {
        allowSharing: true,
        allowChat: true,
      },
      participants: [],
      createdAt: 1704067200000,
      updatedAt: 1704153600000,
    };

    it('should update class settings successfully and return data with classroomId', async () => {
      mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

      const settings = {
        allowSharing: true,
        allowChat: false,
      };

      const result = await updateClassSettings(
        'classroom123',
        'class456',
        settings,
      );

      expect(mockPut).toHaveBeenCalledWith(
        '/classrooms/classroom123/classes/class456',
        { settings: { allowSharing: true, allowChat: false } },
      );
      expect(result).toEqual({
        ...mockClassData,
        classroomId: 'classroom123',
      });
    });

    it('should work with different classroom and class IDs', async () => {
      const testCases = [
        {
          classroomId: 'abc-123',
          classId: 'class-001',
        },
        {
          classroomId: 'classroom-xyz-789',
          classId: 'session-456',
        },
        {
          classroomId: '12345',
          classId: '67890',
        },
        {
          classroomId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
          classId: '550e8400-e29b-41d4-a716-************',
        },
      ];

      for (const { classroomId, classId } of testCases) {
        const responseData = { ...mockClassData, id: classId };
        mockPut.mockResolvedValueOnce({ data: { data: responseData } });

        const result = await updateClassSettings(classroomId, classId, {
          allowSharing: true,
          allowChat: true,
        });

        expect(mockPut).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/classes/${classId}`,
          expect.any(Object),
        );
        expect(result.classroomId).toBe(classroomId);
        expect(result.id).toBe(classId);
      }
    });

    describe('allowSharing setting', () => {
      it('should handle allowSharing: true', async () => {
        mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

        const settings = {
          allowSharing: true,
          allowChat: false,
        };

        await updateClassSettings('classroom123', 'class456', settings);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom123/classes/class456',
          { settings: { allowSharing: true, allowChat: false } },
        );
      });

      it('should handle allowSharing: false', async () => {
        mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

        const settings = {
          allowSharing: false,
          allowChat: true,
        };

        await updateClassSettings('classroom123', 'class456', settings);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom123/classes/class456',
          { settings: { allowSharing: false, allowChat: true } },
        );
      });
    });

    describe('allowChat setting', () => {
      it('should handle allowChat: true', async () => {
        mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

        const settings = {
          allowSharing: false,
          allowChat: true,
        };

        await updateClassSettings('classroom123', 'class456', settings);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom123/classes/class456',
          { settings: { allowSharing: false, allowChat: true } },
        );
      });

      it('should handle allowChat: false', async () => {
        mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

        const settings = {
          allowSharing: true,
          allowChat: false,
        };

        await updateClassSettings('classroom123', 'class456', settings);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom123/classes/class456',
          { settings: { allowSharing: true, allowChat: false } },
        );
      });
    });

    describe('settings combinations', () => {
      it('should handle both settings enabled', async () => {
        mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

        const settings = {
          allowSharing: true,
          allowChat: true,
        };

        await updateClassSettings('classroom123', 'class456', settings);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom123/classes/class456',
          { settings: { allowSharing: true, allowChat: true } },
        );
      });

      it('should handle both settings disabled', async () => {
        mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

        const settings = {
          allowSharing: false,
          allowChat: false,
        };

        await updateClassSettings('classroom123', 'class456', settings);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom123/classes/class456',
          { settings: { allowSharing: false, allowChat: false } },
        );
      });

      it('should handle mixed settings', async () => {
        mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

        const settings = {
          allowSharing: true,
          allowChat: false,
        };

        await updateClassSettings('classroom123', 'class456', settings);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom123/classes/class456',
          { settings: { allowSharing: true, allowChat: false } },
        );
      });
    });

    describe('request structure', () => {
      it('should wrap settings in settings object', async () => {
        mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

        const settings = {
          allowSharing: true,
          allowChat: true,
        };

        await updateClassSettings('classroom123', 'class456', settings);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom123/classes/class456',
          { settings },
        );
      });

      it('should construct correct endpoint URL', async () => {
        mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

        await updateClassSettings('classroom123', 'class456', {
          allowSharing: true,
          allowChat: false,
        });

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom123/classes/class456',
          expect.any(Object),
        );
      });
    });

    describe('response handling', () => {
      it('should return response data with added classroomId', async () => {
        const responseData = {
          id: 'class-789',
          title: 'Science Class',
          status: 'active',
          settings: {
            allowSharing: false,
            allowChat: true,
          },
          participants: [{ userId: 'user1', role: 'student' }],
          createdAt: 1704067200000,
          updatedAt: 1704153600000,
        };

        mockPut.mockResolvedValueOnce({ data: { data: responseData } });

        const result = await updateClassSettings('classroom123', 'class789', {
          allowSharing: false,
          allowChat: true,
        });

        expect(result).toEqual({
          ...responseData,
          classroomId: 'classroom123',
        });
        expect(result.classroomId).toBe('classroom123');
        expect(result.id).toBe('class-789');
        expect(result.title).toBe('Science Class');
      });

      it('should preserve all original response properties', async () => {
        const complexResponseData = {
          id: 'class-complex',
          title: 'Complex Class',
          description: 'A complex class with many properties',
          status: 'active',
          startedAt: 1704067200000,
          endedAt: null,
          settings: {
            allowSharing: true,
            allowChat: false,
            maxParticipants: 50,
            language: 'en',
          },
          participants: [
            { userId: 'user1', role: 'teacher' },
            { userId: 'user2', role: 'student' },
          ],
          materials: ['material1', 'material2'],
          recordings: [],
          createdAt: 1704067200000,
          updatedAt: 1704153600000,
          metadata: {
            source: 'web',
            version: '1.0',
          },
        };

        mockPut.mockResolvedValueOnce({ data: { data: complexResponseData } });

        const result = await updateClassSettings(
          'classroom123',
          'class-complex',
          {
            allowSharing: true,
            allowChat: false,
          },
        );

        expect(result).toEqual({
          ...complexResponseData,
          classroomId: 'classroom123',
        });
        expect(result.description).toBe('A complex class with many properties');
        expect(result.participants).toHaveLength(2);
        expect(result.metadata).toEqual({ source: 'web', version: '1.0' });
      });

      it('should handle response with minimal data', async () => {
        const minimalData = {
          id: 'class-minimal',
          settings: {
            allowSharing: false,
            allowChat: false,
          },
        };

        mockPut.mockResolvedValueOnce({ data: { data: minimalData } });

        const result = await updateClassSettings(
          'classroom123',
          'class-minimal',
          {
            allowSharing: false,
            allowChat: false,
          },
        );

        expect(result).toEqual({
          ...minimalData,
          classroomId: 'classroom123',
        });
      });
    });

    describe('error handling', () => {
      it('should throw provided error message for class not found', async () => {
        const errorResponse = {
          response: { data: { error: 'Class not found' } },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassSettings('classroom123', 'nonexistent-class', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Class not found');
      });

      it('should throw provided error message for classroom not found', async () => {
        const errorResponse = {
          response: { data: { error: 'Classroom not found' } },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassSettings('nonexistent-classroom', 'class456', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Classroom not found');
      });

      it('should throw provided error message for insufficient permissions', async () => {
        const errorResponse = {
          response: {
            data: {
              error: 'Insufficient permissions to update class settings',
            },
          },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassSettings('classroom123', 'class456', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Insufficient permissions to update class settings');
      });

      it('should throw provided error message for class not active', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Cannot update settings for inactive class' },
          },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassSettings('classroom123', 'inactive-class', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Cannot update settings for inactive class');
      });

      it('should throw provided error message for invalid settings', async () => {
        const errorResponse = {
          response: { data: { error: 'Invalid class settings provided' } },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassSettings('classroom123', 'class456', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Invalid class settings provided');
      });

      it('should throw default message when API error has no error field', async () => {
        const errorResponse = { response: { data: {} } };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassSettings('classroom123', 'class456', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Failed to update class settings!');
      });

      it('should throw default message when API error has null error', async () => {
        const errorResponse = { response: { data: { error: null } } };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassSettings('classroom123', 'class456', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Failed to update class settings!');
      });

      it('should throw default message when API error has undefined error', async () => {
        const errorResponse = { response: { data: { error: undefined } } };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassSettings('classroom123', 'class456', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Failed to update class settings!');
      });

      it('should throw default message when API error has no response data', async () => {
        const errorResponse = { response: { data: undefined } };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassSettings('classroom123', 'class456', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Failed to update class settings!');
      });

      it('should throw default message when API error has no response', async () => {
        const errorResponse = { response: undefined };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassSettings('classroom123', 'class456', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Failed to update class settings!');
      });

      it('should throw default message when error is not axios error', async () => {
        isAxiosErrorSpy.mockReturnValueOnce(false);
        mockPut.mockRejectedValueOnce(new Error('Network failure'));

        await expect(
          updateClassSettings('classroom123', 'class456', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Failed to update class settings!');
      });

      it('should handle generic error correctly', async () => {
        mockPut.mockRejectedValueOnce(new Error('Some generic error'));

        await expect(
          updateClassSettings('classroom123', 'class456', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Failed to update class settings!');
      });

      it('should handle timeout error', async () => {
        const timeoutError = {
          response: {
            data: { error: 'Request timeout while updating class settings' },
          },
        };
        mockPut.mockRejectedValueOnce(timeoutError as AxiosError);

        await expect(
          updateClassSettings('classroom123', 'class456', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Request timeout while updating class settings');
      });

      it('should handle server error', async () => {
        const serverError = {
          response: {
            data: {
              error: 'Internal server error during class settings update',
            },
          },
        };
        mockPut.mockRejectedValueOnce(serverError as AxiosError);

        await expect(
          updateClassSettings('classroom123', 'class456', {
            allowSharing: true,
            allowChat: true,
          }),
        ).rejects.toThrow('Internal server error during class settings update');
      });
    });

    describe('edge cases', () => {
      it('should handle special characters in IDs', async () => {
        mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

        const classroomId = 'classroom-with-special_chars-123';
        const classId = 'class-session_with-dashes_456';

        await updateClassSettings(classroomId, classId, {
          allowSharing: true,
          allowChat: false,
        });

        expect(mockPut).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/classes/${classId}`,
          expect.any(Object),
        );
      });

      it('should handle UUID format IDs', async () => {
        mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

        const classroomId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
        const classId = '550e8400-e29b-41d4-a716-************';

        await updateClassSettings(classroomId, classId, {
          allowSharing: false,
          allowChat: true,
        });

        expect(mockPut).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/classes/${classId}`,
          expect.any(Object),
        );
      });

      it('should handle multiple rapid updates', async () => {
        const updates = [
          { allowSharing: true, allowChat: true },
          { allowSharing: false, allowChat: true },
          { allowSharing: true, allowChat: false },
          { allowSharing: false, allowChat: false },
        ];

        updates.forEach(() => {
          mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });
        });

        for (const settings of updates) {
          await updateClassSettings('classroom123', 'class456', settings);
        }

        expect(mockPut).toHaveBeenCalledTimes(4);
      });

      it('should handle response with null or missing properties', async () => {
        const responseWithNulls = {
          id: 'class-nulls',
          title: null,
          description: undefined,
          settings: {
            allowSharing: true,
            allowChat: false,
          },
          participants: null,
        };

        mockPut.mockResolvedValueOnce({ data: { data: responseWithNulls } });

        const result = await updateClassSettings(
          'classroom123',
          'class-nulls',
          {
            allowSharing: true,
            allowChat: false,
          },
        );

        expect(result).toEqual({
          ...responseWithNulls,
          classroomId: 'classroom123',
        });
        expect(result.title).toBeNull();
        expect(result.participants).toBeNull();
      });
    });

    describe('business logic scenarios', () => {
      it('should handle enabling sharing for active class', async () => {
        mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

        const result = await updateClassSettings(
          'classroom123',
          'active-class-789',
          {
            allowSharing: true,
            allowChat: true,
          },
        );

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom123/classes/active-class-789',
          { settings: { allowSharing: true, allowChat: true } },
        );
        expect(result.classroomId).toBe('classroom123');
      });

      it('should handle disabling chat during class', async () => {
        mockPut.mockResolvedValueOnce({ data: { data: mockClassData } });

        const result = await updateClassSettings('classroom123', 'class456', {
          allowSharing: true,
          allowChat: false,
        });

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom123/classes/class456',
          { settings: { allowSharing: true, allowChat: false } },
        );
        expect(result.classroomId).toBe('classroom123');
      });

      it('should handle restrictive class settings', async () => {
        const restrictiveClass = {
          ...mockClassData,
          settings: {
            allowSharing: false,
            allowChat: false,
          },
        };

        mockPut.mockResolvedValueOnce({ data: { data: restrictiveClass } });

        const result = await updateClassSettings(
          'classroom123',
          'restricted-class',
          {
            allowSharing: false,
            allowChat: false,
          },
        );

        expect(result.settings.allowSharing).toBe(false);
        expect(result.settings.allowChat).toBe(false);
        expect(result.classroomId).toBe('classroom123');
      });

      it('should handle open class settings', async () => {
        const openClass = {
          ...mockClassData,
          settings: {
            allowSharing: true,
            allowChat: true,
          },
        };

        mockPut.mockResolvedValueOnce({ data: { data: openClass } });

        const result = await updateClassSettings('classroom123', 'open-class', {
          allowSharing: true,
          allowChat: true,
        });

        expect(result.settings.allowSharing).toBe(true);
        expect(result.settings.allowChat).toBe(true);
        expect(result.classroomId).toBe('classroom123');
      });
    });
  });
});
