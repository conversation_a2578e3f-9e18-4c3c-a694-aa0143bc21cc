/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types */
import axios, { type AxiosError } from 'axios';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { api } from '@/shared';
import { getClass } from './getClass';
import type { Class } from '../types';

describe('getClass()', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('getClass()', () => {
    it('resolves with class data on success', async () => {
      const fakeClass: Class = {
        id: 'cls1',
        roomId: 'room123',
        title: 'Lesson 1',
        status: 'active',
        startTime: 1620000000000,
        endTime: 1620003600000,
        participants: [],
        summary: 'Intro lesson',
        feedback: 'Great session overall',
        recordings: ['rec1.m4a'],
        createdAt: 1620000000000,
        updatedAt: 1620003600000,
        keyMoments: [
          {
            timestamp: '1620000100000',
            description: { en: 'Warm-up exercise', es: 'Ejercicio inicial' },
          },
          {
            timestamp: '1620000200000',
            description: { en: 'Main topic', fr: 'Sujet principal' },
          },
        ],
        settings: {
          allowRecording: true,
          allowTranscription: true,
          allowTranslation: true,
          allowCaptions: true,
          allowSharing: true,
          allowMaterialsUpload: true,
          allowMaterialsDownload: true,
          allowChat: true,
          allowAnonymousJoin: true,
          allowInviteGuests: true,
          allowMultipleTeachers: true,
          allowMultipleStudents: true,
          allowComments: true,
        },
        classroomSettings: {
          canAnonymousJoin: true,
        },
      };

      vi.spyOn(api, 'get').mockResolvedValueOnce({ data: { data: fakeClass } });

      const result = await getClass('cls1', 'room123');
      expect(result).toEqual(fakeClass);
      expect(api.get).toHaveBeenCalledWith('/classrooms/room123/classes/cls1');
    });

    it('throws generic message on non-Axios error', async () => {
      vi.spyOn(api, 'get').mockRejectedValueOnce(new Error('oops'));

      await expect(getClass('cls1', 'room123')).rejects.toThrow(
        'Failed to get class!',
      );
    });

    it('throws server-provided error when AxiosError with response.data.error', async () => {
      const err = {
        isAxiosError: true,
        response: { data: { error: 'Not found' } },
      };
      vi.spyOn(axios, 'isAxiosError').mockImplementation((e): e is AxiosError =>
        Boolean((e as any).isAxiosError),
      );
      vi.spyOn(api, 'get').mockRejectedValueOnce(err);

      await expect(getClass('cls1', 'room123')).rejects.toThrow('Not found');
    });

    it('falls back to generic when AxiosError without response error message', async () => {
      const err = { isAxiosError: true };
      vi.spyOn(axios, 'isAxiosError').mockImplementation((e): e is AxiosError =>
        Boolean((e as any).isAxiosError),
      );
      vi.spyOn(api, 'get').mockRejectedValueOnce(err);

      await expect(getClass('cls1', 'room123')).rejects.toThrow(
        'Failed to get class!',
      );
    });
  });
});
