import { api } from '@/shared';
import { Class } from '../types';
import axios from 'axios';

export const getActiveClass = async (id: string): Promise<Class | null> => {
  try {
    const queryParams = new URLSearchParams();
    queryParams.set('page', '1');
    queryParams.set('limit', '1');
    queryParams.set('status', 'active');

    const queryString = queryParams.toString();
    const url = `/classrooms/${id}/classes?${queryString}`;

    const response = await api.get<{ data: { items: Class[] } }>(url, {
      headers: { 'Content-Type': 'application/json' },
    });

    const items = response.data?.data?.items;
    return items && items.length > 0 ? items[0] : null;
  } catch (error) {
    let errorMessage = 'Failed to get all classes!';
    if (axios.isAxiosError(error) && error.response?.data?.error) {
      errorMessage = error.response.data.error;
    }
    throw new Error(errorMessage);
  }
};
