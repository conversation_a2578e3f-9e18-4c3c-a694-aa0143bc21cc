import { useCookies } from 'react-cookie';
import { useNavigate } from 'react-router';
import { useSidebar } from '@/shadcn/components/ui/sidebar';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { logoutUser } from '../api';

export const useLogoutUser = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { toggleSidebar } = useSidebar();

  const [, , removeCookie] = useCookies([
    'token',
    'refreshToken',
    'anonymousDisplayName',
    'anonymousUserId',
  ]);

  const cleanUp = () => {
    removeCookie('token', { path: '/' });
    removeCookie('refreshToken', { path: '/' });
    removeCookie('anonymousDisplayName', { path: '/' });
    removeCookie('anonymousUserId', { path: '/' });

    queryClient.clear();

    toggleSidebar();
    navigate('/', { replace: true });
  };

  const { mutate: logout } = useMutation({
    mutationFn: async () => logoutUser(),
    onSuccess: () => {
      cleanUp();
    },
    onError: () => {
      cleanUp();
    },
  });

  return { logout };
};
