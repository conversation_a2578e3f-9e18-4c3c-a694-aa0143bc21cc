import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateUser } from '../api';
import i18n from '@/app/i18n';
import { useAppDispatch, useNotifications } from '@/shared';
import { User } from '../types';
import { setUser } from '../model';
import { DeepPartial } from 'react-hook-form';

export const useUpdateUser = () => {
  const { addNotification, addToast } = useNotifications();
  const queryClient = useQueryClient();
  const dispatch = useAppDispatch();

  return useMutation({
    mutationFn: (user: DeepPartial<User>) => updateUser(user),
    onSuccess: (data) => {
      i18n.changeLanguage(data.preferences.locale || 'en');
      dispatch(setUser(data));
      queryClient.invalidateQueries({
        queryKey: ['classrooms'],
      });
      queryClient.invalidateQueries({
        queryKey: ['classroom'],
      });
      queryClient.invalidateQueries({
        queryKey: ['class'],
      });
      addToast?.({ text: 'notifications.success.settingsUpdated' });
    },
    onError: () => {
      addNotification?.({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
