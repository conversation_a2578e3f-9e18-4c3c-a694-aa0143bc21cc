import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteUser } from '../api';
import { useAppDispatch, useNotifications } from '@/shared';
import { setUser } from '../model';
import { useCookies } from 'react-cookie';
import { useNavigate } from 'react-router';

export const useDeleteUser = () => {
  const { addNotification } = useNotifications();
  const queryClient = useQueryClient();
  const [, , removeCookie] = useCookies([
    'token',
    'refreshToken',
    'anonymousDisplayName',
    'anonymousUserId',
  ]);
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  return useMutation({
    mutationFn: (confirmation: string) => deleteUser(confirmation),
    onSuccess: () => {
      removeCookie('token', { path: '/' });
      removeCookie('refreshToken', { path: '/' });
      removeCookie('anonymousDisplayName', { path: '/' });
      removeCookie('anonymousUserId', { path: '/' });

      dispatch(setUser(null));

      queryClient.clear();

      navigate('/', { replace: true });

      addNotification?.({
        title: 'notifications.success.userDeleted.title',
        description: 'notifications.success.userDeleted.description',
      });
    },
    onError: (error) => {
      if (error.message === 'wrong_confirmation') {
        addNotification?.({
          title: 'notifications.errors.wrongConfirmation.title',
          description: 'notifications.errors.wrongConfirmation.description',
        });
      } else
        addNotification?.({
          title: 'notifications.errors.serverError.title',
          description: 'notifications.errors.serverError.description',
        });
    },
  });
};
