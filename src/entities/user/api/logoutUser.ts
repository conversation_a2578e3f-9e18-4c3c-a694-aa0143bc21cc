import axios from 'axios';
import { api } from '@/shared';

export const logoutUser = async (): Promise<void> => {
  try {
    await api.post(
      '/auth/logout',
      {},
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );
  } catch (error) {
    let errorMessage = 'Failed to logout user!';

    if (axios.isAxiosError(error) && error.response) {
      const responseError = error.response.data?.error;
      if (
        responseError &&
        typeof responseError === 'string' &&
        responseError.trim()
      ) {
        errorMessage = responseError;
      }
    }

    throw new Error(errorMessage);
  }
};
