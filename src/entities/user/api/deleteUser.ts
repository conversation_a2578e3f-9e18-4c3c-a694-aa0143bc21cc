import { User } from '@/entities';
import { api } from '@/shared';
import axios from 'axios';

export const deleteUser = async (confirmation: string): Promise<void> => {
  try {
    await api.delete<{ data: User }>('/users/me', {
      data: {
        confirmation,
      },
    });
  } catch (error) {
    let errorMessage = 'Failed to delete user!';
    if (axios.isAxiosError(error) && error.response?.data?.error) {
      errorMessage = error.response.data.error;
      if (error.status === 403) {
        errorMessage = 'wrong_confirmation';
      }
    }
    throw new Error(errorMessage);
  }
};
