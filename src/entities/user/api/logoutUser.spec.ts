import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { AxiosError } from 'axios';
import { api } from '@/shared';
import { logoutUser } from './logoutUser';

describe('logoutUser', () => {
  let originalPost: typeof api.post;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    originalPost = api.post;
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' &&
          error !== null &&
          'response' in (error as object),
      );
    vi.clearAllMocks();
  });

  afterEach(() => {
    api.post = originalPost;
    isAxiosErrorSpy.mockRestore();
  });

  it('should successfully logout user', async () => {
    const mockResponse = {
      data: {
        message: 'Logged out successfully',
        meta: {
          request_id: 'dcabdaed-737b-4e32-bbdd-97f658c36d1f',
          timestamp: '2025-07-30T15:36:43.456889567Z',
        },
      },
    };

    api.post = vi.fn().mockResolvedValue(mockResponse);

    await expect(logoutUser()).resolves.not.toThrow();

    expect(api.post).toHaveBeenCalledWith(
      '/auth/logout',
      {},
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );
    expect(api.post).toHaveBeenCalledTimes(1);
  });

  it('should throw provided error message from response', async () => {
    const errorResponse = {
      response: {
        data: { error: 'Session already expired' },
        status: 401,
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(logoutUser()).rejects.toThrow('Session already expired');
  });

  it('should throw default error message when response has no error string', async () => {
    const errorResponse = {
      response: {
        data: {},
        status: 500,
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(logoutUser()).rejects.toThrow('Failed to logout user!');
  });

  it('should throw default error message when response data is null', async () => {
    const errorResponse = {
      response: {
        data: null,
        status: 500,
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(logoutUser()).rejects.toThrow('Failed to logout user!');
  });

  it('should throw default error message when response has empty error string', async () => {
    const errorResponse = {
      response: {
        data: { error: '' },
        status: 500,
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(logoutUser()).rejects.toThrow('Failed to logout user!');
  });

  it('should throw default error message for Axios error without response', async () => {
    const errorResponse = {
      message: 'Network Error',
      request: {},
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(logoutUser()).rejects.toThrow('Failed to logout user!');
  });

  it('should throw default error message for non-Axios error', async () => {
    isAxiosErrorSpy.mockReturnValue(false);
    api.post = vi.fn().mockRejectedValue(new Error('Random network failure'));

    await expect(logoutUser()).rejects.toThrow('Failed to logout user!');
  });

  it('should throw default error message for non-Error objects', async () => {
    isAxiosErrorSpy.mockReturnValue(false);
    api.post = vi.fn().mockRejectedValue('String error');

    await expect(logoutUser()).rejects.toThrow('Failed to logout user!');
  });

  it('should handle server timeout errors', async () => {
    const timeoutError = {
      response: {
        data: { error: 'Request timeout' },
        status: 408,
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(timeoutError);

    await expect(logoutUser()).rejects.toThrow('Request timeout');
  });

  it('should handle server internal errors', async () => {
    const serverError = {
      response: {
        data: { error: 'Internal server error occurred' },
        status: 500,
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(serverError);

    await expect(logoutUser()).rejects.toThrow(
      'Internal server error occurred',
    );
  });
});
