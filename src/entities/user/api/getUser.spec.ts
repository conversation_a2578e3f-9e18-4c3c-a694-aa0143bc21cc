import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { AxiosError } from 'axios';
import { api } from '@/shared';
import { getUser } from './getUser';
import { User } from '../types';

describe('getUser', () => {
  let originalGet: typeof api.get;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    originalGet = api.get;
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' &&
          error !== null &&
          'response' in (error as object),
      );
    vi.clearAllMocks();
  });

  afterEach(() => {
    api.get = originalGet;
    isAxiosErrorSpy.mockRestore();
  });

  it('should return user data on success', async () => {
    const fakeUser: User = {
      id: '688a3c04e1b805ca18b53e0c',
      email: '<EMAIL>',
      displayName: 'Je<PERSON><PERSON> Dorofejev<PERSON>',
      displayNameWithTitle: 'Jegors Dorofejevs',
      firstName: 'Jegors',
      lastName: 'Dorofejevs',
      preferences: {
        locale: 'en',
        defaultClassroomSettings: {
          speakingLanguage: 'en',
          translationLanguage: 'en',
          autoRecording: false,
          autoTranscribe: false,
          autoTranslateOtherLanguages: false,
          preferedAudioInputDevices: null,
          preferedVideoInputDevices: null,
          preferedAudioOutputDevices: null,
          autoShowCaptions: false,
        },
        receiveNotifications: false,
        participantConsent: {
          canRecord: true,
          canShareClassRecordings: true,
          canShareLiveStream: true,
          canAIProcess: true,
          canTeacherInviteGuests: true,
          canStudentInviteGuests: true,
          canAnonymousJoin: true,
          canPartyChat: true,
          canInviteMultipleTeachers: false,
          canInviteMultipleStudents: false,
        },
      },
      isAnonymous: false,
      createdAt: '2025-07-30T15:36:36.478Z',
      emailVerified: true,
      hasPassword: true,
    };

    // Mock the full response structure that matches the API endpoint
    const mockResponse = {
      data: {
        data: fakeUser,
        meta: {
          request_id: 'dcabdaed-737b-4e32-bbdd-97f658c36d1f',
          timestamp: '2025-07-30T15:36:43.456889567Z',
        },
      },
    };

    api.get = vi.fn().mockResolvedValue(mockResponse);

    const result = await getUser();
    expect(result).toEqual(fakeUser);
    expect(api.get).toHaveBeenCalledWith('/users/me', {
      headers: { 'Content-Type': 'application/json' },
    });
  });

  it('should throw provided error message from response', async () => {
    const errorResponse = {
      response: { data: { error: 'Unauthorized access' } },
    } as AxiosError;

    api.get = vi.fn().mockRejectedValue(errorResponse);

    await expect(getUser()).rejects.toThrow('Unauthorized access');
  });

  it('should throw default error message when response has no error string', async () => {
    const errorResponse = {
      response: { data: {} },
    } as AxiosError;

    api.get = vi.fn().mockRejectedValue(errorResponse);

    await expect(getUser()).rejects.toThrow('Failed to get user!');
  });

  it('should throw default error message for non-Axios error', async () => {
    isAxiosErrorSpy.mockReturnValue(false);
    api.get = vi.fn().mockRejectedValue(new Error('Random failure'));

    await expect(getUser()).rejects.toThrow('Failed to get user!');
  });
});
