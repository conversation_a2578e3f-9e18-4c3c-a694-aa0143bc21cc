import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { deleteUser } from './deleteUser';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { delete: vi.fn() },
}));

describe('Delete User API', () => {
  let mockDelete: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockDelete = api.delete as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' && error !== null && 'response' in error,
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  describe('deleteUser', () => {
    it('should delete user successfully with correct confirmation', async () => {
      const confirmationText = 'DELETE';
      mockDelete.mockResolvedValueOnce({
        status: 200,
        data: { data: { id: 'user123', name: 'Test User' } },
      });

      await deleteUser(confirmationText);

      expect(mockDelete).toHaveBeenCalledWith('/users/me', {
        data: {
          confirmation: confirmationText,
        },
      });
    });

    it('should complete without returning anything', async () => {
      mockDelete.mockResolvedValueOnce({ status: 204 });

      const result = await deleteUser('DELETE');

      expect(result).toBeUndefined();
    });

    it('should work with different confirmation strings', async () => {
      const confirmationCases = [
        'DELETE',
        'delete',
        'CONFIRM',
        'yes',
        'I understand the consequences',
        'delete my account',
        'permanently delete',
        '确认删除', // Chinese
        'confirmar', // Spanish
      ];

      for (const confirmation of confirmationCases) {
        mockDelete.mockResolvedValueOnce({ status: 200 });

        await deleteUser(confirmation);

        expect(mockDelete).toHaveBeenCalledWith('/users/me', {
          data: {
            confirmation,
          },
        });
      }
    });

    describe('request structure validation', () => {
      it('should send DELETE request to correct endpoint', async () => {
        mockDelete.mockResolvedValueOnce({ status: 200 });

        await deleteUser('DELETE');

        expect(mockDelete).toHaveBeenCalledWith(
          '/users/me',
          expect.any(Object),
        );
      });

      it('should include confirmation in request body data', async () => {
        const confirmation = 'CONFIRM_DELETE';
        mockDelete.mockResolvedValueOnce({ status: 200 });

        await deleteUser(confirmation);

        expect(mockDelete).toHaveBeenCalledWith('/users/me', {
          data: {
            confirmation,
          },
        });
      });

      it('should handle empty confirmation string', async () => {
        mockDelete.mockResolvedValueOnce({ status: 200 });

        await deleteUser('');

        expect(mockDelete).toHaveBeenCalledWith('/users/me', {
          data: {
            confirmation: '',
          },
        });
      });

      it('should handle confirmation with whitespace', async () => {
        const confirmation = '  DELETE  ';
        mockDelete.mockResolvedValueOnce({ status: 200 });

        await deleteUser(confirmation);

        expect(mockDelete).toHaveBeenCalledWith('/users/me', {
          data: {
            confirmation,
          },
        });
      });
    });

    describe('response handling', () => {
      it('should handle 200 OK response', async () => {
        mockDelete.mockResolvedValueOnce({
          status: 200,
          data: { data: { id: 'user123', deleted: true } },
        });

        await expect(deleteUser('DELETE')).resolves.toBeUndefined();
      });

      it('should handle 204 No Content response', async () => {
        mockDelete.mockResolvedValueOnce({ status: 204 });

        await expect(deleteUser('DELETE')).resolves.toBeUndefined();
      });

      it('should handle response with user data', async () => {
        mockDelete.mockResolvedValueOnce({
          status: 200,
          data: {
            data: {
              id: 'user123',
              email: '<EMAIL>',
              name: 'Test User',
              deletedAt: '2024-01-01T00:00:00Z',
            },
          },
        });

        await expect(deleteUser('CONFIRM')).resolves.toBeUndefined();
      });
    });

    describe('error handling', () => {
      it('should throw "wrong_confirmation" for 403 status regardless of error message', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Invalid confirmation text' },
            status: 403,
          },
          status: 403,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(deleteUser('WRONG')).rejects.toThrow('wrong_confirmation');
      });

      it('should throw "wrong_confirmation" for 403 even with no error message', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Some error message' }, // Need truthy error for the condition to work
            status: 403,
          },
          status: 403,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(deleteUser('INVALID')).rejects.toThrow(
          'wrong_confirmation',
        );
      });

      it('should throw provided error message for non-403 errors with valid error', async () => {
        const errorResponse = {
          response: {
            data: { error: 'User account is locked and cannot be deleted' },
            status: 400,
          },
          status: 400,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(deleteUser('DELETE')).rejects.toThrow(
          'User account is locked and cannot be deleted',
        );
      });

      it('should throw provided error message for 500 server error', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Internal server error during account deletion' },
            status: 500,
          },
          status: 500,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(deleteUser('DELETE')).rejects.toThrow(
          'Internal server error during account deletion',
        );
      });

      it('should throw provided error message for 404 user not found', async () => {
        const errorResponse = {
          response: {
            data: { error: 'User not found' },
            status: 404,
          },
          status: 404,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(deleteUser('DELETE')).rejects.toThrow('User not found');
      });

      it('should throw provided error message for conflict error', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Cannot delete user with active subscriptions' },
            status: 409,
          },
          status: 409,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(deleteUser('DELETE')).rejects.toThrow(
          'Cannot delete user with active subscriptions',
        );
      });

      it('should throw default message when API error has no error field', async () => {
        const errorResponse = {
          response: {
            data: {},
            status: 400,
          },
          status: 400,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(deleteUser('DELETE')).rejects.toThrow(
          'Failed to delete user!',
        );
      });

      it('should throw default message when API error has null error', async () => {
        const errorResponse = {
          response: {
            data: { error: null },
            status: 400,
          },
          status: 400,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(deleteUser('DELETE')).rejects.toThrow(
          'Failed to delete user!',
        );
      });

      it('should throw default message when API error has undefined error', async () => {
        const errorResponse = {
          response: {
            data: { error: undefined },
            status: 400,
          },
          status: 400,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(deleteUser('DELETE')).rejects.toThrow(
          'Failed to delete user!',
        );
      });

      it('should throw default message when API error has empty string error', async () => {
        const errorResponse = {
          response: {
            data: { error: '' },
            status: 400,
          },
          status: 400,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(deleteUser('DELETE')).rejects.toThrow(
          'Failed to delete user!',
        );
      });

      it('should throw default message when no response data', async () => {
        const errorResponse = {
          response: undefined,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(deleteUser('DELETE')).rejects.toThrow(
          'Failed to delete user!',
        );
      });

      it('should throw default message when error is not axios error', async () => {
        isAxiosErrorSpy.mockReturnValueOnce(false);
        mockDelete.mockRejectedValueOnce(new Error('Network failure'));

        await expect(deleteUser('DELETE')).rejects.toThrow(
          'Failed to delete user!',
        );
      });

      it('should handle generic error correctly', async () => {
        mockDelete.mockRejectedValueOnce(new Error('Some generic error'));

        await expect(deleteUser('DELETE')).rejects.toThrow(
          'Failed to delete user!',
        );
      });

      it('should handle timeout error', async () => {
        const timeoutError = {
          response: {
            data: { error: 'Request timeout during account deletion' },
            status: 408,
          },
          status: 408,
        };
        mockDelete.mockRejectedValueOnce(timeoutError as AxiosError);

        await expect(deleteUser('DELETE')).rejects.toThrow(
          'Request timeout during account deletion',
        );
      });

      it('should prioritize 403 status over specific error message', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Some other specific error message' },
            status: 403,
          },
          status: 403,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        // Should throw 'wrong_confirmation' despite having a specific error message
        await expect(deleteUser('WRONG_TEXT')).rejects.toThrow(
          'wrong_confirmation',
        );
      });
    });

    describe('edge cases', () => {
      it('should handle very long confirmation strings', async () => {
        const longConfirmation =
          'I understand that this action will permanently delete my account and all associated data including but not limited to my profile information, uploaded files, messages, and any other content I have created on this platform. This action cannot be undone and I accept full responsibility for this decision.';
        mockDelete.mockResolvedValueOnce({ status: 200 });

        await deleteUser(longConfirmation);

        expect(mockDelete).toHaveBeenCalledWith('/users/me', {
          data: {
            confirmation: longConfirmation,
          },
        });
      });

      it('should handle confirmation with special characters', async () => {
        const specialConfirmation = 'DELETE@#$%^&*()_+-=[]{}|;:,.<>?/~`';
        mockDelete.mockResolvedValueOnce({ status: 200 });

        await deleteUser(specialConfirmation);

        expect(mockDelete).toHaveBeenCalledWith('/users/me', {
          data: {
            confirmation: specialConfirmation,
          },
        });
      });

      it('should handle confirmation with unicode characters', async () => {
        const unicodeConfirmation =
          'DELETE 删除 مسح حذف διαγραφή удалить 削除 삭제';
        mockDelete.mockResolvedValueOnce({ status: 200 });

        await deleteUser(unicodeConfirmation);

        expect(mockDelete).toHaveBeenCalledWith('/users/me', {
          data: {
            confirmation: unicodeConfirmation,
          },
        });
      });

      it('should handle confirmation with newlines and tabs', async () => {
        const confirmationWithWhitespace = 'DELETE\n\tCONFIRM\n\tMY\n\tACCOUNT';
        mockDelete.mockResolvedValueOnce({ status: 200 });

        await deleteUser(confirmationWithWhitespace);

        expect(mockDelete).toHaveBeenCalledWith('/users/me', {
          data: {
            confirmation: confirmationWithWhitespace,
          },
        });
      });

      it('should handle multiple rapid deletion attempts', async () => {
        const confirmations = ['DELETE1', 'DELETE2', 'DELETE3'];

        // First two should fail with 403
        confirmations.slice(0, 2).forEach(() => {
          const errorResponse = {
            response: { data: { error: 'Wrong confirmation' }, status: 403 },
            status: 403,
          };
          mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);
        });

        // Last one should succeed
        mockDelete.mockResolvedValueOnce({ status: 200 });

        // Test first two failures
        await expect(deleteUser(confirmations[0])).rejects.toThrow(
          'wrong_confirmation',
        );
        await expect(deleteUser(confirmations[1])).rejects.toThrow(
          'wrong_confirmation',
        );

        // Test final success
        await expect(deleteUser(confirmations[2])).resolves.toBeUndefined();

        expect(mockDelete).toHaveBeenCalledTimes(3);
      });
    });

    describe('security scenarios', () => {
      it('should handle attempted injection attacks in confirmation', async () => {
        const maliciousConfirmations = [
          '<script>alert("xss")</script>',
          'DELETE"; DROP TABLE users; --',
          '../../etc/passwd',
          '${process.env.SECRET}',
          'eval("malicious code")',
        ];

        for (const confirmation of maliciousConfirmations) {
          mockDelete.mockResolvedValueOnce({ status: 200 });

          await deleteUser(confirmation);

          expect(mockDelete).toHaveBeenCalledWith('/users/me', {
            data: {
              confirmation,
            },
          });
        }
      });

      it('should handle confirmation text that might cause encoding issues', async () => {
        const encodingTestCases = [
          'DELETE%20USER',
          'DELETE+USER',
          'DELETE USER',
          'DELETE&amp;USER',
          'DELETE<USER>',
          'DELETE"USER"',
          "DELETE'USER'",
        ];

        for (const confirmation of encodingTestCases) {
          mockDelete.mockResolvedValueOnce({ status: 200 });

          await deleteUser(confirmation);

          expect(mockDelete).toHaveBeenCalledWith('/users/me', {
            data: {
              confirmation,
            },
          });
        }
      });
    });

    describe('business logic validation', () => {
      it('should handle different HTTP status codes for 403 consistently', async () => {
        const forbidden403 = {
          response: { data: { error: 'Forbidden' }, status: 403 },
          status: 403,
        };
        mockDelete.mockRejectedValueOnce(forbidden403 as AxiosError);

        await expect(deleteUser('INVALID')).rejects.toThrow(
          'wrong_confirmation',
        );
      });

      it('should not override 403 status behavior even with truthy error message', async () => {
        const errorResponse = {
          response: {
            data: {
              error: 'Very specific error message that should be ignored',
            },
            status: 403,
          },
          status: 403,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(deleteUser('WRONG')).rejects.toThrow('wrong_confirmation');
      });

      it('should handle case where response.data exists with truthy error for 403', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Any truthy error message' }, // Need truthy error for 403 logic to trigger
            status: 403,
          },
          status: 403,
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(deleteUser('INVALID')).rejects.toThrow(
          'wrong_confirmation',
        );
      });
    });
  });
});
