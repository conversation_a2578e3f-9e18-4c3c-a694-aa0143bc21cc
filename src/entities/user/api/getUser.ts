import axios from 'axios';
import { api } from '@/shared';
import { User } from '../types';

export const getUser = async (): Promise<User> => {
  try {
    const response = await api.get<{ data: User }>('/users/me', {
      headers: { 'Content-Type': 'application/json' },
    });
    return response.data.data;
  } catch (error) {
    let errorMessage = 'Failed to get user!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
