import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { AxiosError } from 'axios';
import { api, DeepPartial } from '@/shared';
import { updateUser } from './updateUser';
import { User } from '@/entities';

describe('updateUser', () => {
  let originalPut: typeof api.put;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    originalPut = api.put;
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' &&
          error !== null &&
          'response' in (error as object),
      );
    vi.clearAllMocks();
  });

  afterEach(() => {
    api.put = originalPut;
    isAxiosErrorSpy.mockRestore();
  });

  it('should successfully update user with full user data', async () => {
    const updatedUser: User = {
      id: '688a3c04e1b805ca18b53e0c',
      email: '<EMAIL>',
      displayName: 'Updated Name',
      displayNameWithTitle: 'Mr. Updated Name',
      firstName: 'Updated',
      lastName: 'Name',
      preferences: {
        locale: 'fr',
        defaultClassroomSettings: {
          speakingLanguage: 'fr',
          translationLanguage: 'en',
          autoRecording: true,
          autoTranscribe: true,
          autoTranslateOtherLanguages: true,
          preferedAudioInputDevices: null,
          preferedVideoInputDevices: null,
          preferedAudioOutputDevices: null,
          autoShowCaptions: true,
        },
        receiveNotifications: true,
        participantConsent: {
          canRecord: false,
          canShareClassRecordings: false,
          canShareLiveStream: false,
          canAIProcess: false,
          canTeacherInviteGuests: false,
          canStudentInviteGuests: false,
          canAnonymousJoin: false,
          canPartyChat: false,
          canInviteMultipleTeachers: true,
          canInviteMultipleStudents: true,
        },
      },
      isAnonymous: false,
      createdAt: '2025-07-30T15:36:36.478Z',
      emailVerified: true,
      hasPassword: true,
    };

    const mockResponse = {
      data: {
        data: updatedUser,
      },
    };

    api.put = vi.fn().mockResolvedValue(mockResponse);

    const userUpdate: DeepPartial<User> = {
      displayName: 'Updated Name',
      preferences: {
        locale: 'fr',
        receiveNotifications: true,
      },
    };

    const result = await updateUser(userUpdate);

    expect(result).toEqual(updatedUser);
    expect(api.put).toHaveBeenCalledWith('/users/me', userUpdate);
    expect(api.put).toHaveBeenCalledTimes(1);
  });

  it('should successfully update user with partial data', async () => {
    const updatedUser: User = {
      id: '688a3c04e1b805ca18b53e0c',
      email: '<EMAIL>',
      displayName: 'New Display Name',
      displayNameWithTitle: 'New Display Name',
      firstName: 'John',
      lastName: 'Doe',
      preferences: {
        locale: 'en',
        defaultClassroomSettings: {
          speakingLanguage: 'en',
          translationLanguage: 'en',
          autoRecording: false,
          autoTranscribe: false,
          autoTranslateOtherLanguages: false,
          preferedAudioInputDevices: null,
          preferedVideoInputDevices: null,
          preferedAudioOutputDevices: null,
          autoShowCaptions: false,
        },
        receiveNotifications: false,
        participantConsent: {
          canRecord: true,
          canShareClassRecordings: true,
          canShareLiveStream: true,
          canAIProcess: true,
          canTeacherInviteGuests: true,
          canStudentInviteGuests: true,
          canAnonymousJoin: true,
          canPartyChat: true,
          canInviteMultipleTeachers: false,
          canInviteMultipleStudents: false,
        },
      },
      isAnonymous: false,
      createdAt: '2025-07-30T15:36:36.478Z',
      emailVerified: true,
      hasPassword: true,
    };

    const mockResponse = {
      data: {
        data: updatedUser,
      },
    };

    api.put = vi.fn().mockResolvedValue(mockResponse);

    const userUpdate: DeepPartial<User> = {
      displayName: 'New Display Name',
    };

    const result = await updateUser(userUpdate);

    expect(result).toEqual(updatedUser);
    expect(api.put).toHaveBeenCalledWith('/users/me', userUpdate);
  });

  it('should successfully update nested preferences', async () => {
    const updatedUser: User = {
      id: '688a3c04e1b805ca18b53e0c',
      email: '<EMAIL>',
      displayName: 'John Doe',
      displayNameWithTitle: 'John Doe',
      firstName: 'John',
      lastName: 'Doe',
      preferences: {
        locale: 'es',
        defaultClassroomSettings: {
          speakingLanguage: 'es',
          translationLanguage: 'en',
          autoRecording: true,
          autoTranscribe: true,
          autoTranslateOtherLanguages: false,
          preferedAudioInputDevices: null,
          preferedVideoInputDevices: null,
          preferedAudioOutputDevices: null,
          autoShowCaptions: true,
        },
        receiveNotifications: true,
        participantConsent: {
          canRecord: true,
          canShareClassRecordings: true,
          canShareLiveStream: true,
          canAIProcess: true,
          canTeacherInviteGuests: true,
          canStudentInviteGuests: true,
          canAnonymousJoin: true,
          canPartyChat: true,
          canInviteMultipleTeachers: false,
          canInviteMultipleStudents: false,
        },
      },
      isAnonymous: false,
      createdAt: '2025-07-30T15:36:36.478Z',
      emailVerified: true,
      hasPassword: true,
    };

    const mockResponse = {
      data: {
        data: updatedUser,
      },
    };

    api.put = vi.fn().mockResolvedValue(mockResponse);

    const userUpdate: DeepPartial<User> = {
      preferences: {
        locale: 'es',
        defaultClassroomSettings: {
          speakingLanguage: 'es',
          autoRecording: true,
          autoTranscribe: true,
        },
        receiveNotifications: true,
      },
    };

    const result = await updateUser(userUpdate);

    expect(result).toEqual(updatedUser);
    expect(api.put).toHaveBeenCalledWith('/users/me', userUpdate);
  });

  it('should handle empty update object', async () => {
    const existingUser: User = {
      id: '688a3c04e1b805ca18b53e0c',
      email: '<EMAIL>',
      displayName: 'John Doe',
      displayNameWithTitle: 'John Doe',
      firstName: 'John',
      lastName: 'Doe',
      preferences: {
        locale: 'en',
        defaultClassroomSettings: {
          speakingLanguage: 'en',
          translationLanguage: 'en',
          autoRecording: false,
          autoTranscribe: false,
          autoTranslateOtherLanguages: false,
          preferedAudioInputDevices: null,
          preferedVideoInputDevices: null,
          preferedAudioOutputDevices: null,
          autoShowCaptions: false,
        },
        receiveNotifications: false,
        participantConsent: {
          canRecord: true,
          canShareClassRecordings: true,
          canShareLiveStream: true,
          canAIProcess: true,
          canTeacherInviteGuests: true,
          canStudentInviteGuests: true,
          canAnonymousJoin: true,
          canPartyChat: true,
          canInviteMultipleTeachers: false,
          canInviteMultipleStudents: false,
        },
      },
      isAnonymous: false,
      createdAt: '2025-07-30T15:36:36.478Z',
      emailVerified: true,
      hasPassword: true,
    };

    const mockResponse = {
      data: {
        data: existingUser,
      },
    };

    api.put = vi.fn().mockResolvedValue(mockResponse);

    const userUpdate: DeepPartial<User> = {};

    const result = await updateUser(userUpdate);

    expect(result).toEqual(existingUser);
    expect(api.put).toHaveBeenCalledWith('/users/me', {});
  });

  it('should throw provided error message from response', async () => {
    const userUpdate: DeepPartial<User> = {
      displayName: 'Invalid Name',
    };

    const errorResponse = {
      response: {
        data: {
          error: 'Display name contains invalid characters',
        },
      },
    } as AxiosError;

    api.put = vi.fn().mockRejectedValue(errorResponse);

    await expect(updateUser(userUpdate)).rejects.toThrow(
      'Display name contains invalid characters',
    );
  });

  it('should throw default error message when response has no error string', async () => {
    const userUpdate: DeepPartial<User> = {
      email: 'invalid-email',
    };

    const errorResponse = {
      response: {
        data: {},
      },
    } as AxiosError;

    api.put = vi.fn().mockRejectedValue(errorResponse);

    await expect(updateUser(userUpdate)).rejects.toThrow(
      'Failed to update user settings!',
    );
  });

  it('should throw default error message when error is null', async () => {
    const userUpdate: DeepPartial<User> = {
      displayName: 'Test Name',
    };

    const errorResponse = {
      response: {
        data: {
          error: null,
        },
      },
    } as AxiosError;

    api.put = vi.fn().mockRejectedValue(errorResponse);

    await expect(updateUser(userUpdate)).rejects.toThrow(
      'Failed to update user settings!',
    );
  });

  it('should throw default error message when response data is undefined', async () => {
    const userUpdate: DeepPartial<User> = {
      displayName: 'Test Name',
    };

    const errorResponse = {
      response: {}, // no data property
    } as AxiosError;

    api.put = vi.fn().mockRejectedValue(errorResponse);

    await expect(updateUser(userUpdate)).rejects.toThrow(
      'Failed to update user settings!',
    );
  });

  it('should throw default error message when response is undefined', async () => {
    const userUpdate: DeepPartial<User> = {
      displayName: 'Test Name',
    };

    const errorResponse = {} as AxiosError; // no response property

    api.put = vi.fn().mockRejectedValue(errorResponse);

    await expect(updateUser(userUpdate)).rejects.toThrow(
      'Failed to update user settings!',
    );
  });

  it('should throw default error message for non-Axios error', async () => {
    const userUpdate: DeepPartial<User> = {
      displayName: 'Test Name',
    };

    isAxiosErrorSpy.mockReturnValue(false);
    api.put = vi.fn().mockRejectedValue(new Error('Network failure'));

    await expect(updateUser(userUpdate)).rejects.toThrow(
      'Failed to update user settings!',
    );
  });
});
