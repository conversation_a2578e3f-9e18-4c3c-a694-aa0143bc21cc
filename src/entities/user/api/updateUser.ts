import { User } from '@/entities';
import { api, DeepPartial } from '@/shared';
import axios from 'axios';

export const updateUser = async (user: DeepPartial<User>): Promise<User> => {
  try {
    const response = await api.put<{ data: User }>('/users/me', {
      ...user,
    });

    return response.data.data;
  } catch (error) {
    let errorMessage = 'Failed to update user settings!';
    if (axios.isAxiosError(error) && error.response?.data?.error) {
      errorMessage = error.response.data.error;
    }
    throw new Error(errorMessage);
  }
};
