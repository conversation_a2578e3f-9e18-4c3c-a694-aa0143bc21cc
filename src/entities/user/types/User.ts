export type User = {
  id: string;
  email: string;
  displayName: string;
  displayNameWithTitle: string;
  firstName: string;
  lastName: string;
  preferences: {
    locale: string;
    defaultClassroomSettings: {
      speakingLanguage: string;
      translationLanguage: string;
      autoRecording: boolean;
      autoTranscribe: boolean;
      autoTranslateOtherLanguages: boolean;
      preferedAudioInputDevices: string | null;
      preferedVideoInputDevices: string | null;
      preferedAudioOutputDevices: string | null;
      autoShowCaptions: boolean;
    };
    receiveNotifications: boolean;
    participantConsent: {
      canRecord: boolean;
      canShareClassRecordings: boolean;
      canShareLiveStream: boolean;
      canAIProcess: boolean;
      canTeacherInviteGuests: boolean;
      canStudentInviteGuests: boolean;
      canAnonymousJoin: boolean;
      canPartyChat: boolean;
      canInviteMultipleTeachers: boolean;
      canInviteMultipleStudents: boolean;
    };
  };
  createdAt: string;
  isAnonymous: boolean;
  emailVerified: boolean;
  hasPassword: boolean;
};
