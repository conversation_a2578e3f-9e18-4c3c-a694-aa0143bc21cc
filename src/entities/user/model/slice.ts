import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User } from '../types';
import { Role } from '../../../shared';

interface IInitialState {
  token: string | null;
  refreshToken: string | null;
  user: User | null;
  isLoading: boolean;
  role: Role | null;
  roleType: 'watcher' | 'party' | null;
}

const initialState: IInitialState = {
  token: null,
  refreshToken: null,
  user: null,
  isLoading: true,
  role: null,
  roleType: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User | null>) => {
      state.user = action.payload;
      state.isLoading = false;
    },
    setTokens: (
      state,
      action: PayloadAction<{
        token: string | null;
        refreshToken: string | null;
      }>,
    ) => {
      state.token = action.payload.token;
      state.refreshToken = action.payload.refreshToken;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setLanguage: (state, action: PayloadAction<string>) => {
      if (state.user) {
        state.user.preferences!.locale = action.payload;
      }
    },
    setUserRole: (state, action: PayloadAction<Role | null>) => {
      state.role = action.payload;
    },
    setUserRoleType: (
      state,
      action: PayloadAction<'watcher' | 'party' | null>,
    ) => {
      state.roleType = action.payload;
    },
  },
});

export const {
  setUser,
  setTokens,
  setLoading,
  setLanguage,
  setUserRole,
  setUserRoleType,
} = userSlice.actions;
export default userSlice.reducer;
