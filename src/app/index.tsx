import { useEffect, useLayoutEffect } from 'react';
import { useLocation } from 'react-router';
import { useTranslation } from 'react-i18next';
import { AppSidebar } from '@/widgets';
import {
  AnonymousWarning,
  AuthProvider,
  DigitalConsentProvider,
} from '@/features';
import { initAnalytics, Notifications } from '@/shared';
import { SidebarProvider } from '@/shadcn/components/ui/sidebar';
import './i18n';
import '@main/ui-kit/dist/index.css';
import './index.css';
import * as pdfjsLib from 'pdfjs-dist';
import pdfWorker from '/pdf.worker.min.mjs?url';
import { Router } from './router';

// Locales for dates
import 'moment/dist/locale/ru';
import 'moment/dist/locale/zh-cn';

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfWorker;

export const App = () => {
  const { i18n, t } = useTranslation();
  const currentLanguage = i18n.language;
  const location = useLocation();

  useEffect(() => {
    document.title = t('title');
  }, [currentLanguage]);

  useEffect(() => {
    initAnalytics();
  }, []);

  useLayoutEffect(() => {
    if (window) {
      document.documentElement.scrollTo(0, 0);
    }
  }, [location.pathname]);

  return (
    <AuthProvider>
      <DigitalConsentProvider>
        <SidebarProvider defaultChecked={false} defaultOpen={false}>
          <AppSidebar />

          <main className="w-full flex flex-col">
            <Router />
            <AnonymousWarning />
            <Notifications />
          </main>
        </SidebarProvider>
      </DigitalConsentProvider>
    </AuthProvider>
  );
};
