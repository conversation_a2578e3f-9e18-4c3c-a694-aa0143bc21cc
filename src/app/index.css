@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+HK:wght@100..900&family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Noto+Serif:ital,wght@0,100..900;1,100..900&display=swap&subset=latin,latin-ext,cyrillic,cyrillic-ext');
@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
}

:root {
  /* Border radii */
  --radius: 0.75rem; /* base borderRadius (12px) */

  /* Backgrounds */
  --background: #f8e9d6; /* background.default */
  --foreground: #484041; /* text.primary */

  /* Card & Popover */
  --card: #fffaf1; /* background.card */
  --card-foreground: #484041; /* text.primary */
  --popover: #fffaf1; /* background.card */
  --popover-foreground: #484041; /* text.primary */

  /* Primary / Secondary */
  --primary: #3f2c21; /* primary.main */
  --primary-foreground: #ffffff; /* primary.contrastText */
  --secondary: #f2834f; /* secondary.main */
  --secondary-foreground: #ffffff; /* secondary.contrastText */

  /* Muted (Tertiary) */
  --muted: #b39cd0; /* tertiary.main */
  --muted-foreground: #8a6bb5; /* tertiary.dark */

  /* Accent */
  --accent: #f2834f; /* accent.main */
  --accent-foreground: #ffffff; /* use white for contrast */

  /* Feedback */
  --destructive: #e76f51; /* error.main */

  /* Borders and Inputs */
  --border: #e5ddd5; /* divider */
  --input: #efe9e2; /* divider */

  /* Ring (focus) */
  --ring: #5c4336; /* primary.light */

  /* Chart Colors */
  --chart-1: #7fb069; /* success.main */
  --chart-2: #e76f51; /* error.main */
  --chart-3: #f8bc5a; /* warning.main */
  --chart-4: #9ec1cc; /* info.main */
  --chart-5: #ffd580; /* accent.main */

  /* Sidebar */
  --sidebar: #fffcf5; /* background.paper */
  --sidebar-foreground: #3f2c21; /* primary.main */
  --sidebar-primary: #3f2c21; /* primary.main */
  --sidebar-primary-foreground: #ffffff; /* primary.contrastText */
  --sidebar-accent: #f2834f; /* primary.main alpha 5% */
  --sidebar-accent-foreground: #ffffff; /* primary.main */
  --sidebar-border: #efe9e2; /* divider */
  --sidebar-ring: #5c4336; /* primary.light */

  --safe-padding-x: max(env(safe-area-inset-left), 4rem);
}

@media (max-width: 500px) {
  :root {
    --safe-padding-x: max(env(safe-area-inset-left), 1rem);
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

.dark {
  --foreground: oklch(0.145 0 0);
  --background: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: #fffcf5;
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: #f2834f;
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  html {
    /* font-family: 'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif; */
    font-family: 'Noto Sans', 'Noto Sans HK', sans-serif;
  }
}

@layer base {
  :root {
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer utilities {
  .h-screen {
    height: 100dvh;
  }

  .max-h-screen {
    max-height: 100dvh;
  }

  .w-screen {
    width: 100dvw;
  }

  .max-w-screen {
    max-width: 100dvw;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;

    line-height: normal;
    min-height: 55px;
  }

  .themed-scrollbar {
    scrollbar-width: 8px;
    scrollbar-color: #3f2c21 transparent;
  }
  .themed-scrollbar::-webkit-scrollbar {
    width: 8px;
  }
  .themed-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }
  .themed-scrollbar::-webkit-scrollbar-thumb {
    background-color: #3f2c21;
    border-radius: 8px;
    border: 2px solid transparent;
  }
  .themed-scrollbar:hover::-webkit-scrollbar-thumb {
    background-color: #3f2c21;
  }

  .optimized-blur {
    @apply transform;
    transform: translate3d(0, 0, 0);
    transform: translateZ(0);
  }

  .transparent-scrollbar {
    scrollbar-color: white transparent;
  }
  .transparent-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  .transparent-scrollbar::-webkit-scrollbar-thumb {
    background-color: white;
    border-radius: 8px;
    border: 2px solid transparent;
  }

  .scrollbar_primary {
    &::-webkit-scrollbar-thumb:hover {
      background: var(--primary) !important;
    }
    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px
        color-mix(in oklab, var(--primary) 10%, transparent);
      background-color: color-mix(in oklab, var(--primary) 10%, transparent);
      border-radius: 1000px;
    }
    &::-webkit-scrollbar {
      width: 7px;
      background-color: color-mix(in oklab, var(--primary) 10%, transparent);
      border-radius: 1000px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: var(--primary);
      border-radius: 1000px;
    }
  }

  .slider-accent {
    background: #ffffff30;
  }

  .slider-accent > span[data-slot='slider-track'] {
    background: #ffffff30;
  }

  .slider-accent > span[data-slot='slider-track'] > span {
    background: var(--accent);
  }

  .slider-accent > span > span[data-slot='slider-thumb'] {
    background: var(--accent) !important;
    border: unset !important;
    box-shadow: none !important;
  }
}

.action-bar {
  padding: 0.875rem;
  padding-bottom: max(env(safe-area-inset-bottom), 0.875rem);
}

@media (width < 1200px) {
  .action-bar {
    padding: 0.625rem;
    padding-bottom: max(env(safe-area-inset-bottom), 0.625rem);
  }
}

@media (width < 1000px) {
  @media (orientation: landscape) {
    .action-bar {
      padding: 0.375rem;
      padding-bottom: max(env(safe-area-inset-bottom), 0.375rem);
    }
  }
}

@media (width < 600px) {
  .action-bar {
    padding: 0.375rem;
    padding-bottom: max(env(safe-area-inset-bottom), 0.375rem);
  }
}
