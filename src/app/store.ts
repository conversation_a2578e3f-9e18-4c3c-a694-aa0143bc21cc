import { configureStore } from '@reduxjs/toolkit';
import { userIdListener, notificationsReducer } from '@/shared';
import {
  livekitConnectionReducer,
  localMediaReducer,
  chatReducer,
  conferenceReducer,
  classLoggerReducer,
} from '@/features';
import { userReducer } from '@/entities';

export const store = configureStore({
  reducer: {
    user: userReducer,
    notifications: notificationsReducer,
    conference: conferenceReducer,
    livekit: livekitConnectionReducer,
    localMedia: localMediaReducer,
    chat: chatReducer,
    classLogger: classLoggerReducer,
  },
  middleware: (getDefault) => getDefault().prepend(userIdListener.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
