import { Route, Routes } from 'react-router';
import {
  ClassPage,
  ClassroomPage,
  ClassroomsPage,
  HomePage,
  ProfilePage,
  VerifyEmailPage,
  OAuth2Page,
  OAuth2ErrorPage,
  NotFoundPage,
  VerifyEmailErrorPage,
  AcceptInvitePage,
  PrivacyPolicyPage,
  TermsAndConditionsPage,
  CodeOfConductPage,
  CopyrightPage,
  LicensePage,
  ReportPage,
} from '@/pages';
import { AuthGuard } from '@/features';
import { ACLProvider, ClassroomSettingsProvider } from '@/shared';

export const Router = () => {
  return (
    <Routes>
      <Route
        path="/"
        element={
          <AuthGuard>
            <HomePage />
          </AuthGuard>
        }
      />
      <Route
        path="/classrooms"
        element={
          <AuthGuard requireAuth>
            <ClassroomsPage />
          </AuthGuard>
        }
      />

      <Route
        path="/classrooms/*"
        element={
          <ClassroomSettingsProvider>
            <ACLProvider>
              <Routes>
                <Route
                  path=":id"
                  element={
                    <AuthGuard requireAuth>
                      <ClassroomPage />
                    </AuthGuard>
                  }
                />
                <Route
                  path=":id/class/:classId"
                  element={
                    <AuthGuard requireAuth>
                      <ClassPage />
                    </AuthGuard>
                  }
                />
              </Routes>
            </ACLProvider>
          </ClassroomSettingsProvider>
        }
      />

      <Route
        path="/profile"
        element={
          <AuthGuard requireAuth>
            <ProfilePage />
          </AuthGuard>
        }
      />

      <Route path="/oauth2" element={<OAuth2Page />} />
      <Route path="/oauth2/error" element={<OAuth2ErrorPage />} />

      <Route path="/verify-email" element={<VerifyEmailPage />} />
      <Route path="/verify-email/error" element={<VerifyEmailErrorPage />} />

      <Route path="/join/:token" element={<AcceptInvitePage />} />

      <Route path="/legal/privacy-policy" element={<PrivacyPolicyPage />} />
      <Route
        path="/legal/terms-and-conditions"
        element={<TermsAndConditionsPage />}
      />
      <Route path="/legal/code-of-conduct" element={<CodeOfConductPage />} />
      <Route path="/legal/copyright" element={<CopyrightPage />} />
      <Route path="/legal/license" element={<LicensePage />} />
      <Route path="/legal/report" element={<ReportPage />} />

      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};
