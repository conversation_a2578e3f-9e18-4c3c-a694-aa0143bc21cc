import { Suspense } from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router';
import { Provider } from 'react-redux';
import { CookiesProvider } from 'react-cookie';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { App } from './app';
import { store } from './app/store';

const queryClient = new QueryClient();

createRoot(document.getElementById('root')!).render(
    <BrowserRouter>
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          <CookiesProvider>
            <Suspense>
              <App />
            </Suspense>
          </CookiesProvider>
        </QueryClientProvider>
      </Provider>
    </BrowserRouter>
);
