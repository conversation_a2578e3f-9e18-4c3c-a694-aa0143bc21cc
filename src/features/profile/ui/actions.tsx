import { Link2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/shadcn/components/ui/accordion';
import { Control, useForm } from 'react-hook-form';
import { useUpdateUser, useUser } from '@/entities';
import { ConsentCheckboxList, Consents } from '@/shared';
import { DeleteAccountButton } from '@/features/delete-account';

type FormValues = Consents;

export const Actions = () => {
  const { t } = useTranslation();
  const { user } = useUser();
  const { mutate: updateUser } = useUpdateUser();

  const { control } = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: user?.preferences.participantConsent,
  });

  return (
    <div className="w-full h-fit flex flex-col items-start justify-start gap-6 p-6 rounded-[40px] bg-card duration-300 transition-colors hover:border-primary/20 border-1 border-primary/10">
      <h1 className="text-2xl font-bold text-primary">
        {t('profile.actions.title')}
      </h1>
      <div className="w-full flex flex-col gap-4">
        <Link
          data-testid="profile-actions-privacy-link"
          target="_blank"
          to="/legal/privacy-policy"
          className="w-fit flex items-center justify-center gap-2 group"
        >
          <Link2 className="size-5 text-primary/80 group-hover:text-primary transition-colors duration-300" />
          <h1 className="text-primary/80 font-semibold group-hover:text-primary transition-colors duration-300">
            {t('profile.actions.privacy')}
          </h1>
        </Link>

        <Link
          data-testid="profile-actions-terms-link"
          target="_blank"
          to="/legal/terms-and-conditions"
          className="w-fit flex items-center justify-center gap-2 group"
        >
          <Link2 className="size-5 text-primary/80 group-hover:text-primary transition-colors duration-300" />
          <h1 className="text-primary/80 font-semibold group-hover:text-primary transition-colors duration-300">
            {t('profile.actions.terms')}
          </h1>
        </Link>

        <Link
          data-testid="profile-actions-code-of-conduct-link"
          target="_blank"
          to="/legal/code-of-conduct"
          className="w-fit flex items-center justify-center gap-2 group"
        >
          <Link2 className="size-5 text-primary/80 group-hover:text-primary transition-colors duration-300" />
          <h1 className="text-primary/80 font-semibold group-hover:text-primary transition-colors duration-300">
            {t('profile.actions.codeOfConduct')}
          </h1>
        </Link>

        <Link
          data-testid="profile-actions-copyright-link"
          target="_blank"
          to="/legal/copyright"
          className="w-fit flex items-center justify-center gap-2 group"
        >
          <Link2 className="size-5 text-primary/80 group-hover:text-primary transition-colors duration-300" />
          <h1 className="text-primary/80 font-semibold group-hover:text-primary transition-colors duration-300">
            {t('profile.actions.copyright')}
          </h1>
        </Link>

        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="consents">
            <AccordionTrigger
              data-testid="profile-actions-consents-trigger"
              className="text-primary/80 font-semibold text-base py-0 hover:text-primary transition-colors duration-300 cursor-pointer"
            >
              {t('profile.actions.consents')}
            </AccordionTrigger>
            <AccordionContent
              data-testid="profile-actions-consents-content"
              className="pt-4 pb-0"
            >
              <ConsentCheckboxList
                control={
                  control as unknown as Control<{
                    [K in keyof Consents]?: boolean;
                  }>
                }
                onConsentChange={(name, value) => {
                  updateUser({
                    preferences: {
                      ...user?.preferences,
                      participantConsent: {
                        ...user?.preferences.participantConsent,
                        [name]: value,
                      },
                    },
                  });
                }}
              />
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        <DeleteAccountButton />
      </div>
    </div>
  );
};
