import { useUser } from '@/entities';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/shadcn/components/ui/avatar';
import { getInitials } from '@/shared';
import { CircleUserRound, Mail } from 'lucide-react';

export const BasicInformation = () => {
  const { user } = useUser();

  return (
    <div className="h-fit flex flex-col items-center justify-start gap-6 p-6 rounded-[40px] bg-card duration-300 transition-colors hover:border-primary/20 border-1 border-primary/10">
      <div className="w-full flex items-center gap-4">
        <Avatar className="size-16">
          <AvatarImage src={''} />
          <AvatarFallback className="text-base font-semibold">
            {getInitials(user!.displayName)}
          </AvatarFallback>
        </Avatar>

        <h1
          data-testid="profile-basic-information-display-name"
          className="text-primary/80 font-bold text-xl break-all text-right"
        >
          {user!.displayName}
        </h1>
      </div>

      <div className="w-full h-px bg-black/10" />

      <div className="w-full grid grid-cols-[24px_1fr] gap-6 items-center text-primary">
        {user!.isAnonymous ? (
          <CircleUserRound className="size-6 text-primary" />
        ) : (
          <Mail className="size-6 text-primary" />
        )}
        <h3
          data-testid="profile-basic-information-email"
          className="w-full font-medium text-wrap text-right break-all"
        >
          {user!.isAnonymous ? 'Guest Access' : user!.email}
        </h3>
      </div>
    </div>
  );
};
