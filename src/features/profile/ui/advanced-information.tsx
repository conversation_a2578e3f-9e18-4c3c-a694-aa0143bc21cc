import { useUser } from '@/entities';
import { LANGUAGES } from '@/shared';
import {
  CalendarDays,
  Languages,
  UserRound,
  UserRoundCheck,
} from 'lucide-react';
import moment from 'moment';
import { useTranslation } from 'react-i18next';

export const AdvancedInformation = () => {
  const { user } = useUser();
  const { t } = useTranslation();
  const information = [
    {
      dataTestId: 'profile-advanced-information-display-name',
      label: t('globalFields.displayName.label'),
      value: user!.displayName,
      icon: UserRound,
    },
    {
      dataTestId: 'profile-advanced-information-registred-at',
      label: t('profile.details.labels.registredAt'),
      value: moment(user!.createdAt).format('DD.MM.YYYY'),
      icon: CalendarDays,
    },
    {
      dataTestId: 'profile-advanced-information-type',
      label: t('profile.details.labels.type.title'),
      value: user!.isAnonymous
        ? t('profile.details.labels.type.anonymous')
        : t('profile.details.labels.type.registred'),
      icon: UserRound<PERSON>he<PERSON>,
    },
    {
      dataTestId: 'profile-advanced-information-language-locale',
      label: t('globalFields.languages.locale.label'),
      value:
        user && user.preferences.locale
          ? (Object.entries(LANGUAGES).find(
              ([key]) => key === user.preferences.locale,
            )?.[1] ?? 'English')
          : 'English',
      icon: Languages,
    },
    {
      dataTestId: 'profile-advanced-information-speaking-language-speaking',
      label: t('globalFields.languages.speakingLanguage.label'),
      value:
        user && user.preferences.locale
          ? (Object.entries(LANGUAGES).find(
              ([key]) =>
                key ===
                user.preferences.defaultClassroomSettings.speakingLanguage,
            )?.[1] ?? 'English')
          : 'English',
      icon: Languages,
    },
    {
      dataTestId: 'profile-advanced-information-translation-language-translation',
      label: t('globalFields.languages.translationLanguage.label'),
      value:
        user && user.preferences.locale
          ? (Object.entries(LANGUAGES).find(
              ([key]) =>
                key ===
                user.preferences.defaultClassroomSettings.translationLanguage,
            )?.[1] ?? 'English')
          : 'English',
      icon: Languages,
    },
  ];

  return (
    <div className="h-fit p-6 rounded-[40px] bg-card flex flex-col gap-6 duration-300 transition-colors hover:border-primary/20 border-1 border-primary/10">
      <h1 className="text-2xl font-bold text-primary">
        {t('profile.details.title')}
      </h1>
      <div className="flex flex-col gap-6">
        {information.map(({ icon: Icon, label, value, dataTestId }, index) => (
          <div
            className="w-full grid grid-cols-[auto_1fr] items-start gap-4 text-primary"
            key={index}
          >
            <div className="flex items-center gap-2">
              <Icon className="size-5 text-primary/70" />
              <h3 className="font-semibold text-sm text-primary/70 whitespace-nowrap">
                {label}
              </h3>
            </div>
            <h3
              className="font-medium text-right break-all"
              data-testid={dataTestId}
            >
              {value}
            </h3>
          </div>
        ))}
      </div>
    </div>
  );
};
