import { FC, PropsWithChildren, ReactNode, useEffect } from 'react';
import { useCookies } from 'react-cookie';
import { setLoading, setTokens, setUser, getUser, useUser } from '@/entities';
import { Navigate } from 'react-router';
import { Loader, logger, useAppDispatch } from '@/shared';
import { useTranslation } from 'react-i18next';

export const AuthProvider: FC<PropsWithChildren> = ({ children }) => {
  const dispatch = useAppDispatch();
  const [cookies] = useCookies(['token', 'refreshToken']);
  const { i18n } = useTranslation();

  useEffect(() => {
    if (cookies.token && cookies.refreshToken) {
      const { token, refreshToken } = cookies;
      dispatch(setTokens({ token, refreshToken }));

      (async () => {
        try {
          const user = await getUser();
          dispatch(setUser(user));
          logger.setUserId(user.id);
          i18n.changeLanguage(user.preferences.locale || 'en');
        } catch {
          dispatch(setLoading(false));
          dispatch(setTokens({ token: null, refreshToken: null }));
        }
      })();
    } else {
      dispatch(setTokens({ token: null, refreshToken: null }));
      dispatch(setUser(null));
      dispatch(setLoading(false));
    }
  }, [cookies]);

  return children;
};

interface IAuthGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
}

export const AuthGuard = ({
  children,
  requireAuth = false,
}: IAuthGuardProps) => {
  const { isLoading, user } = useUser();

  if (isLoading) {
    return <Loader />;
  }

  if (
    !isLoading &&
    user &&
    !user.isAnonymous &&
    location.pathname === '/' &&
    location.search !== '?onboarding=true'
  ) {
    return <Navigate to="/classrooms" replace />;
  }

  if (
    !isLoading &&
    user &&
    user.isAnonymous &&
    location.pathname === '/' &&
    (!location.search || location.search.includes('reset-password'))
  ) {
    return <Navigate to="/classrooms" replace />;
  }

  if (!isLoading && requireAuth && user === null) {
    return <Navigate to="/" replace />;
  }

  return children;
};
