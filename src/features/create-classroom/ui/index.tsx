import { Button } from '@/shadcn/components/ui/button';
import { Loader2, Plus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useCreateClassroom, useUser } from '@/entities';
import { FC } from 'react';
import { cn } from '@/shadcn/lib/utils';

export const CreateClassroomButton: FC<{ responsive?: boolean }> = ({
  responsive = true,
}) => {
  const { user } = useUser();
  const { t } = useTranslation();
  const { mutate: createClassroom, isPending } = useCreateClassroom();

  const handleCreateClassroom = () => {
    const defaultData = {
      title: t('classrooms.dialogs.create.titlePlaceholder', {
        username: user?.displayName,
      }),
      description: ' ',
    };

    createClassroom(defaultData);
  };

  return (
    <Button
      data-testid="create-classroom-button"
      variant="default"
      size="lg"
      className="rounded-xl"
      onClick={handleCreateClassroom}
      disabled={isPending}
    >
      {isPending ? (
        <Loader2 className="animate-spin size-5 text-accent-foreground" />
      ) : (
        <Plus className="size-5 text-accent-foreground" />
      )}
      <p className={cn(responsive && 'max-[420px]:hidden')}>
        {t('classrooms.actions.create')}
      </p>
    </Button>
  );
};
