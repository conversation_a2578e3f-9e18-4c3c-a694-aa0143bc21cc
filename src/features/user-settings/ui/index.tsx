import { supportedLanguages } from '@/app/i18n';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { Input } from '@/shadcn/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shadcn/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { LANGUAGES } from '@/shared';
import { Info, Loader2 } from 'lucide-react';
import { FC, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Button } from '@/shadcn/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useUpdateUser, useUser } from '@/entities';

interface IUserSettingsProps {
  show: boolean;
  setShow: (show: boolean) => void;
}

interface FormValues {
  displayName: string;
  email: string;
  preferences: {
    locale: string;
    defaultClassroomSettings: {
      speakingLanguage: string;
      translationLanguage: string;
    };
  };
}

export const UserSettings: FC<IUserSettingsProps> = ({ show, setShow }) => {
  const { user } = useUser();
  const {
    handleSubmit,
    control,
    register,
    setValue,
    reset,
    formState: { errors, isSubmitting, isValid },
  } = useForm<FormValues>({
    defaultValues: {
      displayName: user?.displayName || '',
      email: user?.email || '',
      preferences: {
        locale: user?.preferences.locale || 'en',
        defaultClassroomSettings: {
          speakingLanguage:
            user?.preferences.defaultClassroomSettings.speakingLanguage || 'en',
          translationLanguage:
            user?.preferences.defaultClassroomSettings.translationLanguage ||
            'en',
        },
      },
    },
    mode: 'onChange',
  });
  const { mutate: updateUser, isPending } = useUpdateUser();
  const { t } = useTranslation();
  const [isLocaleTooltipOpen, setIsLocaleTooltipOpen] = useState(false);
  const [isTranslationTooltipOpen, setIsTranslationTooltipOpen] =
    useState(false);
  const [isSpeakingTooltipOpen, setIsSpeakingTooltipOpen] = useState(false);

  const onSubmit = async (data: FormValues) => {
    updateUser(data);
    setShow(false);
  };

  useEffect(() => {
    if (show) {
      reset({
        displayName: user?.displayName || '',
        email: user?.email || '',
        preferences: {
          locale: user?.preferences.locale || 'en',
          defaultClassroomSettings: {
            speakingLanguage:
              user?.preferences.defaultClassroomSettings.speakingLanguage ||
              'en',
            translationLanguage:
              user?.preferences.defaultClassroomSettings.translationLanguage ||
              'en',
          },
        },
      });
    }
  }, [show, reset]);

  return (
    <Dialog open={show} onOpenChange={setShow}>
      <DialogTrigger asChild />
      <DialogContent
        data-testid="settings-dialog"
        className="sm:max-w-[500px] bg-card"
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <DialogHeader className="mb-4">
          <DialogTitle>{t('settings.title')}</DialogTitle>
          <DialogDescription>{t('settings.description')}</DialogDescription>
        </DialogHeader>

        <form className="grid gap-4" onSubmit={handleSubmit(onSubmit)}>
          {!user!.isAnonymous && (
            <div className="flex flex-col gap-2">
              <p className="text-xs text-card-foreground/80 px-1">
                {t('globalFields.email.label')}
              </p>
              <Input
                data-testid="settings-email-input"
                id="email"
                placeholder={t('globalFields.email.label')}
                {...register('email', {
                  required: t('globalFields.email.validation.required'),
                  pattern: {
                    value: /^[^@\s]+@(?:[A-Za-z0-9-]+\.)+[A-Za-z0-9-]+$/,
                    message: t('globalFields.email.validation.invalid'),
                  },
                  minLength: {
                    value: 3,
                    message: t('globalFields.email.validation.minLength'),
                  },
                  maxLength: {
                    value: 50,
                    message: t('globalFields.email.validation.maxLength'),
                  },
                  onChange: (e) => {
                    setValue('email', e.target.value.trim());
                  },
                })}
                className={
                  errors.email ? 'border-destructive' : 'border-[#00000030]'
                }
                inputMode="email"
              />
              {errors.email && (
                <p
                  data-testid="settings-email-error"
                  className="text-xs text-destructive px-1"
                >
                  {errors.email.message}
                </p>
              )}
            </div>
          )}

          <div className="flex flex-col gap-2">
            <p className="text-xs text-card-foreground/80 px-1">
              {t('globalFields.displayName.label')}
            </p>

            <Input
              data-testid="settings-display-name-input"
              id="displayName"
              placeholder={t('globalFields.displayName.label')}
              {...register('displayName', {
                required: t('globalFields.displayName.validation.required'),
                minLength: {
                  value: 3,
                  message: t('globalFields.displayName.validation.minLength'),
                },
                maxLength: {
                  value: 20,
                  message: t('globalFields.displayName.validation.maxLength'),
                },
              })}
              className={
                errors.displayName ? 'border-destructive' : 'border-[#00000030]'
              }
            />
            {errors.displayName && (
              <p
                data-testid="settings-display-name-error"
                className="text-xs text-destructive px-1"
              >
                {errors.displayName.message}
              </p>
            )}
          </div>

          <div className="flex flex-col gap-2">
            <TooltipProvider>
              <Tooltip
                open={isLocaleTooltipOpen}
                onOpenChange={setIsLocaleTooltipOpen}
              >
                <TooltipTrigger
                  asChild
                  onClick={() => setIsLocaleTooltipOpen((prev) => !prev)}
                >
                  <div className="w-fit flex items-center justify-start gap-1.5 px-1 cursor-default">
                    <Info className="size-4 text-card-foreground/50" />
                    <p className="text-xs text-card-foreground/80">
                      {t('globalFields.languages.locale.label')}
                    </p>
                  </div>
                </TooltipTrigger>

                <TooltipContent
                  align="start"
                  className="w-fit max-w-[80dvw] md:max-w-[600px]"
                >
                  <p>{t('globalFields.languages.locale.tooltip')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <Controller
              control={control}
              name="preferences.locale"
              rules={{ required: true }}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger
                    data-testid="settings-locale-trigger"
                    className="w-full"
                  >
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent data-testid="settings-locale-content">
                    {Object.entries(LANGUAGES).map(([key, label]) => {
                      if (supportedLanguages.includes(key))
                        return (
                          <SelectItem
                            key={key}
                            value={key}
                            data-testid={`settings-locale-option-${key}`}
                          >
                            {label}
                          </SelectItem>
                        );
                    })}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          <div className="flex flex-col gap-2">
            <TooltipProvider>
              <Tooltip
                open={isSpeakingTooltipOpen}
                onOpenChange={setIsSpeakingTooltipOpen}
              >
                <TooltipTrigger
                  asChild
                  onClick={() => setIsSpeakingTooltipOpen((prev) => !prev)}
                >
                  <div className="w-fit flex items-center justify-start gap-1.5 px-1 cursor-default">
                    <Info className="size-4 text-card-foreground/50" />
                    <p className="text-xs text-card-foreground/80">
                      {t('globalFields.languages.speakingLanguage.label')}
                    </p>
                  </div>
                </TooltipTrigger>

                <TooltipContent
                  align="start"
                  className="w-fit max-w-[80dvw] md:max-w-[600px]"
                >
                  <p>{t('globalFields.languages.speakingLanguage.tooltip')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <Controller
              control={control}
              name="preferences.defaultClassroomSettings.speakingLanguage"
              rules={{ required: true }}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger
                    data-testid="settings-speaking-trigger"
                    className="w-full"
                  >
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent data-testid="settings-speaking-content">
                    {Object.entries(LANGUAGES).map(([key, label]) => {
                      return (
                        <SelectItem
                          key={key}
                          value={key}
                          data-testid={`settings-speaking-option-${key}`}
                        >
                          {label}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          <div className="flex flex-col gap-2">
            <TooltipProvider>
              <Tooltip
                open={isTranslationTooltipOpen}
                onOpenChange={setIsTranslationTooltipOpen}
              >
                <TooltipTrigger
                  asChild
                  onClick={() => setIsTranslationTooltipOpen((prev) => !prev)}
                >
                  <div className="w-fit flex items-center justify-start gap-1.5 px-1 cursor-default">
                    <Info className="size-4 text-card-foreground/50" />
                    <p className="text-xs text-card-foreground/80">
                      {t('globalFields.languages.translationLanguage.label')}
                    </p>
                  </div>
                </TooltipTrigger>

                <TooltipContent
                  align="start"
                  className="w-fit max-w-[80dvw] md:max-w-[600px]"
                >
                  <p>
                    {t('globalFields.languages.translationLanguage.tooltip')}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <Controller
              control={control}
              name="preferences.defaultClassroomSettings.translationLanguage"
              rules={{ required: true }}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger
                    data-testid="settings-translation-trigger"
                    className="w-full"
                  >
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent data-testid="settings-translation-content">
                    {Object.entries(LANGUAGES).map(([key, label]) => {
                      return (
                        <SelectItem
                          key={key}
                          value={key}
                          data-testid={`settings-translation-option-${key}`}
                        >
                          {label}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          <DialogFooter className="mt-4 flex justify-center">
            <Button
              data-testid="settings-save-button"
              type="submit"
              variant="default"
              size="lg"
              disabled={!isValid || isSubmitting || isPending}
              className="mx-auto"
            >
              {isSubmitting || isPending ? (
                <Loader2 className="animate-spin h-5 w-5" />
              ) : (
                t('settings.buttons.save')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
