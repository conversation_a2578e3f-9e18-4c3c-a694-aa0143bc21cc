import {
  Tooltip,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { cn } from '@/shadcn/lib/utils';
import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface IDisabledTooltipProps {
  children: React.ReactNode;
  tooltipText: string;
  className?: string;
  disabled?: boolean;
}

export const DisabledTooltip: FC<IDisabledTooltipProps> = ({
  children,
  tooltipText,
  className,
  disabled = true,
}) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);

  if (!disabled) return <>{children}</>;

  return (
    <TooltipProvider>
      <Tooltip open={open} onOpenChange={setOpen}>
        <TooltipTrigger asChild>
          <span
            className={cn('cursor-not-allowed', className)}
            onClick={() => {
              setOpen((prev) => !prev);
            }}
          >
            {children}
          </span>
        </TooltipTrigger>
        <TooltipContent align="start">{t(tooltipText)}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
