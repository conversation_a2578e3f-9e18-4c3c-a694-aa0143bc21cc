import { LoaderPage } from '@/shared';
import { useValidateInvitation } from '../hooks';
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from 'react-router';
import { useCookies } from 'react-cookie';
import { useEffect } from 'react';

export const AcceptInvite = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { token } = useParams<{ token: string }>();
  const [cookies] = useCookies(['token', 'refreshToken']);
  const [searchParams] = useSearchParams();
  const toClass = searchParams.get('classroom') as string | undefined;
  const { mutate: validateInvitation } = useValidateInvitation(token!, toClass);

  useEffect(() => {
    if (token) {
      if (cookies.token && cookies.refreshToken) {
        localStorage.removeItem('inviteRedirect');
        validateInvitation();
      } else {
        localStorage.setItem(
          'inviteRedirect',
          location.pathname + location.search,
        );
        navigate('/?login=true', { state: { from: location.pathname } });
      }
    } else {
      navigate('/classrooms', { replace: true });
    }
  }, [token]);

  return <LoaderPage />;
};
