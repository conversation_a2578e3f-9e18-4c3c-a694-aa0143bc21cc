import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router';
import { useNotifications } from '@/shared';
import { useJoinClassroom } from '@/entities';
import { validateInvitation } from '../api';

export const useValidateInvitation = (token: string, classroomId?: string) => {
  const navigate = useNavigate();
  const { mutate: joinClassroom } = useJoinClassroom(classroomId);
  const { addNotification } = useNotifications();

  return useMutation({
    mutationFn: () => validateInvitation(token),
    onSuccess: (data) => {
      if (data.valid) {
        joinClassroom({ id: data.classroomId, invitationToken: token });
      } else {
        addNotification?.({
          title: 'notifications.errors.expiredInviteLink.title',
          description: 'notifications.errors.expiredInviteLink.description',
        });
        navigate('/classrooms', { replace: true });
      }
    },
    onError: () => {
      navigate('/classrooms', { replace: true });
      addNotification?.({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
