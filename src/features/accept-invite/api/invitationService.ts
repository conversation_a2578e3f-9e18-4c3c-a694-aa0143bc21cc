import { SERVER_URL } from '@/shared/constants';

export const validateInvitation = async (token: string) => {
  const response = await fetch(SERVER_URL + `/invitations/${token}`, {
    method: 'GET',
  });

  if (!response.ok) {
    let errorMessage = 'Failed to validate invitation!';
    try {
      const errorData = await response.json();
      errorMessage = errorData.error || errorMessage;
    } catch (parseError) {
      console.log(parseError);
    }
    throw new Error(errorMessage);
  }

  try {
    const result = await response.json();
    return result.data;
  } catch (error) {
    throw new Error('Failed to parse the response: ' + error);
  }
};
