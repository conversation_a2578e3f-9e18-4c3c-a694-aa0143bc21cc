import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SERVER_URL } from '@/shared/constants';
import { validateInvitation } from './';

describe('Invitation Service', () => {
  describe('validateInvitation', () => {
    const originalFetch = global.fetch;

    beforeEach(() => {
      vi.clearAllMocks();
    });

    afterEach(() => {
      global.fetch = originalFetch;
    });

    it('resolves with the parsed JSON data when response.ok is true', async () => {
      const data = { valid: true, roomId: 'xyz' };
      const payload = { data }; // Wrap in data property

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(payload),
      });

      const result = await validateInvitation('token123');

      expect(global.fetch).toHaveBeenCalledWith(
        `${SERVER_URL}/invitations/token123`,
        { method: 'GET' },
      );
      expect(result).toEqual(data); // Function returns result.data
    });

    it('throws the server error message when !response.ok and error field present', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        json: () => Promise.resolve({ error: 'Invalid token' }),
      });

      await expect(validateInvitation('badtoken')).rejects.toThrow(
        'Invalid token',
      );
    });

    it('falls back to generic message when !response.ok and parsing error', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        json: () => Promise.reject(new Error('boom')),
      });

      await expect(validateInvitation('badtoken')).rejects.toThrow(
        'Failed to validate invitation!',
      );
    });

    it('throws parse error when response.ok but json() throws', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.reject(new Error('bad json')),
      });

      await expect(validateInvitation('token123')).rejects.toThrow(
        'Failed to parse the response: Error: bad json',
      );
    });

    it('throws generic error when !response.ok and no error field in response', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        json: () => Promise.resolve({ message: 'Some other field' }),
      });

      await expect(validateInvitation('badtoken')).rejects.toThrow(
        'Failed to validate invitation!',
      );
    });
  });
});
