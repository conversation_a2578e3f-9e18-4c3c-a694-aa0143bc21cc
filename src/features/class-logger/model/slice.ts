import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type ParticipantLogs = {
  displayName: string | null;
  browser: string | null;
  device: string | null;
  os: string | null;
  isAECEnabled: boolean | null;
  isAnonymous: boolean | null;
  networkStats: {
    roundTrip: number;
    participants: Record<
      string,
      {
        jitterBuffer: number;
        minJitterBuffer: number;
        jitterBufferDelta: number;
        jitter: number;
        fractionLoss: number;
      }
    >;
  } | null;
  connectionQuality: string | null;
  audioBitrate: number | null;
  videoBitrate: number | null;
};

export type LocalParticipant = {
  audioLevel: number | null;
};

type ClassLoggerState = {
  localParticipantLogs: ParticipantLogs;
  audioLevels: Record<string, number | null>;
  remoteParticipantsLogs: Record<string, ParticipantLogs>;
};

const initialState: ClassLoggerState = {
  localParticipantLogs: {
    displayName: null,
    browser: null,
    device: null,
    os: null,
    isAECEnabled: null,
    isAnonymous: null,
    networkStats: null,
    audioBitrate: null,
    videoBitrate: null,
    connectionQuality: null,
  },
  audioLevels: {},
  remoteParticipantsLogs: {},
};

const classLoggerSlice = createSlice({
  name: 'classLogger',
  initialState,
  reducers: {
    setLocalParticipantLogs: (
      state,
      action: PayloadAction<Partial<ParticipantLogs>>,
    ) => {
      state.localParticipantLogs = {
        ...state.localParticipantLogs,
        ...action.payload,
      };
    },
    updateRemoteParticipantLogs: (
      state,
      action: PayloadAction<{ sid: string; logs: ParticipantLogs }>,
    ) => {
      const { sid, logs } = action.payload;
      state.remoteParticipantsLogs[sid] = logs;
    },
    updateAudioLevel: (
      state,
      action: PayloadAction<{ sid: string; audioLevel: number | null }>,
    ) => {
      const { sid, audioLevel } = action.payload;
      state.audioLevels[sid] = audioLevel;
    },
    setRemoteParticipantsLogs: (
      state,
      action: PayloadAction<Record<string, ParticipantLogs>>,
    ) => {
      state.remoteParticipantsLogs = action.payload;
    },
    setAudioLeveles: (
      state,
      action: PayloadAction<Record<string, number | null>>,
    ) => {
      state.audioLevels = action.payload;
    },
    clearRemoteParticipantLogs: (state, action: PayloadAction<string>) => {
      const sid = action.payload;
      delete state.remoteParticipantsLogs[sid];
      delete state.audioLevels[sid];
    },
    resetClassLogger: (state) => {
      state.localParticipantLogs = initialState.localParticipantLogs;
      state.remoteParticipantsLogs = {};
      state.audioLevels = {};
    },
  },
});

export const {
  setLocalParticipantLogs,
  updateRemoteParticipantLogs,
  updateAudioLevel,
  setRemoteParticipantsLogs,
  setAudioLeveles,
  clearRemoteParticipantLogs,
  resetClassLogger,
} = classLoggerSlice.actions;
export default classLoggerSlice.reducer;
