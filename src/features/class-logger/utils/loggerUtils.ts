import { Room } from 'livekit-client';
import { centralAudioProcessing } from '../../';

export const getNetworkStats = async (room: Room) => {
  const participants = room.remoteParticipants;
  const participantsNetworkStats: Record<
    string,
    {
      jitterBuffer: number;
      minJitterBuffer: number;
      jitterBufferDelta: number;
      jitter: number;
      fractionLoss: number;
    }
  > = {};
  let roundTrip: number = 0;

  for (const publication of room.localParticipant.trackPublications.values()) {
    const stats = await publication.track?.getRTCStatsReport();

    if (stats) {
      for (const stat of stats.values()) {
        if (stat.type === 'remote-inbound-rtp' && stat.kind === 'audio') {
          roundTrip = stat.roundTripTime * 1000 || 0;
        }
      }
    }
  }

  for (const participant of participants.values()) {
    const publications = participant.audioTrackPublications;

    for (const publication of publications.values()) {
      const stats = await publication.track?.getRTCStatsReport();

      if (stats) {
        for (const stat of stats.values()) {
          if (stat.type === 'inbound-rtp' && stat.kind === 'audio') {
            const oldDelay: number = stat.jitterBufferDelay;
            const oldMinDelay: number = stat.jitterBufferMinimumDelay;
            const oldEmittedCount: number = stat.jitterBufferEmittedCount;
            const oldPacketsLost: number = stat.packetsLost;
            const oldPacketsReceived: number = stat.packetsReceived;

            await new Promise((resolve) => {
              setTimeout(() => {
                resolve(null);
              }, 300);
            });

            const newStats = await publication.track?.getRTCStatsReport();
            if (newStats) {
              for (const newStat of newStats.values()) {
                if (
                  newStat.type === 'inbound-rtp' &&
                  newStat.kind === 'audio'
                ) {
                  const deltaDelay: number =
                    newStat.jitterBufferDelay - oldDelay;
                  const deltaMinDelay: number =
                    newStat.jitterBufferMinimumDelay - oldMinDelay;
                  const deltaEmittedCount: number =
                    newStat.jitterBufferEmittedCount - oldEmittedCount;

                  const jitterBuffer = (deltaDelay / deltaEmittedCount) * 1000;
                  const minJitterBuffer =
                    (deltaMinDelay / deltaEmittedCount) * 1000;

                  const packetsLost: number = newStat.packetsLost;
                  const packetsReceived: number = newStat.packetsReceived;

                  const fractionLoss =
                    ((packetsLost - oldPacketsLost) /
                      (packetsReceived -
                        oldPacketsReceived +
                        packetsLost -
                        oldPacketsLost)) *
                    100;

                  participantsNetworkStats[participant.identity] = {
                    jitterBuffer,
                    minJitterBuffer,
                    jitterBufferDelta: jitterBuffer - minJitterBuffer,
                    jitter: newStat.jitter * 1000,
                    fractionLoss,
                  };
                  break;
                }
              }
            }
            break;
          }
        }
      }
    }
  }

  return { participants: participantsNetworkStats, roundTrip };
};

export const getAudioLevel = (isLocal: boolean, sid?: string) => {
  let linearLevel = 0;

  if (isLocal) {
    linearLevel = centralAudioProcessing
      .getLocalAudioMixer()
      .getAudioLevelMonitor()
      .getCustomAnalyser()
      .getAudioLevel();
  } else if (sid) {
    linearLevel =
      centralAudioProcessing
        .getRemoteAudioMixer()
        .getParticipant(sid)
        ?.getParticipantNode()
        .getCustomAnalyser()
        .getAudioLevel() || 0;
  }

  if (linearLevel <= 0) {
    return -40;
  }

  return 20 * Math.log10(linearLevel);
};

export const getBitrate = async (
  room: Room,
): Promise<{ audio: number; video: number }> => {
  let audioResult = 0;
  let videoResult = 0;

  for (const publication of room.localParticipant.trackPublications.values()) {
    const stats = await publication.track?.getRTCStatsReport();

    if (stats) {
      for (const stat of stats.values()) {
        if (stat.type === 'outbound-rtp' && stat.kind === 'audio') {
          const oldBytesSent = stat.bytesSent || 0;
          const oldTimestamp = Date.now();

          await new Promise((resolve) => {
            setTimeout(() => {
              resolve(null);
            }, 300);
          });

          const newStats = await publication.track?.getRTCStatsReport();

          if (newStats) {
            for (const newStat of newStats.values()) {
              if (newStat.type === 'outbound-rtp' && newStat.kind === 'audio') {
                const bytesSent = newStat.bytesSent || 0;
                const timestamp = Date.now();

                const bytesDelta = bytesSent - oldBytesSent;
                const timeDeltaSec = (timestamp - oldTimestamp) / 1000;

                if (timeDeltaSec > 0) {
                  const bps = (bytesDelta * 8) / timeDeltaSec;
                  audioResult = bps / 1000;
                }

                break; // Found the audio stat, exit inner loop
              }
            }
          }
          break; // Found the audio stat, exit outer loop
        }

        if (stat.type === 'outbound-rtp' && stat.kind === 'video') {
          const oldBytesSent = stat.bytesSent || 0;
          const oldTimestamp = Date.now();

          await new Promise((resolve) => {
            setTimeout(() => {
              resolve(null);
            }, 300);
          });

          const newStats = await publication.track?.getRTCStatsReport();

          if (newStats) {
            for (const newStat of newStats.values()) {
              if (newStat.type === 'outbound-rtp' && newStat.kind === 'video') {
                const bytesSent = newStat.bytesSent || 0;
                const timestamp = Date.now();

                const bytesDelta = bytesSent - oldBytesSent;
                const timeDeltaSec = (timestamp - oldTimestamp) / 1000;

                if (timeDeltaSec > 0) {
                  const bps = (bytesDelta * 8) / timeDeltaSec;
                  videoResult = bps / 1000;
                }

                break; // Found the video stat, exit inner loop
              }
            }
          }
          break; // Found the video stat, exit outer loop
        }
      }
    }

    if (audioResult > 0 && videoResult > 0) {
      break; // Found both bitrates, exit publication loop
    }
  }

  return { audio: audioResult, video: videoResult };
};
