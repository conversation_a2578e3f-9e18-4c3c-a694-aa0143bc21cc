import { useClassLogger } from './useClassLogger';
import { useEffect, useRef, useState } from 'react';
import {
  getClientData,
  isLowEndDevice,
  isMobile,
  logger,
  useAppSelector,
  useUpdateReduxState,
} from '@/shared';
import { sendData, useLivekitContext } from '../../livekit';
import { useLocalMediaContext } from '../../local-media';
import {
  resetClassLogger,
  setLocalParticipantLogs,
  updateAudioLevel,
  updateRemoteParticipantLogs,
} from '../model';
import { getAudioLevel, getBitrate, getNetworkStats } from '../utils';
import { Class, useUser } from '@/entities';
import { ConferenceStatus } from '../../conference';

interface IParticipantLogsProps {
  classDetails: Class | undefined;
  isPending: boolean;
}

export const useParticipantLogs = ({
  classDetails,
  isPending,
}: IParticipantLogsProps) => {
  const { status, localUser } = useAppSelector((state) => state.livekit);
  const { status: participantStatus } = useAppSelector(
    (state) => state.conference,
  );
  const { user, roleType } = useUser();
  const { localParticipantLogs } = useClassLogger();
  const { room } = useLivekitContext();
  const { hasProcessing } = useLocalMediaContext();
  const [sessionStarted, setSessionStarted] = useState(false);
  const updateReduxState = useUpdateReduxState();

  const audioIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const networkIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Ensure that the session starts only once
  useEffect(() => {
    if (classDetails && !isPending && room && localUser && !sessionStarted) {
      logger.metric({
        type: 'session',
        name: 'session_start',
        session_id: classDetails.roomId,
        participant_id: '',
        labels: {
          client_id: localUser.identity,
          session_name: classDetails.title,
          max_participants: 100000,
        },
        timestamp: Date.now() * 1000,
      });

      setSessionStarted(true);
    }
  }, [room, classDetails, isPending, localUser, sessionStarted]);

  useEffect(() => {
    if (audioIntervalRef.current) {
      clearInterval(audioIntervalRef.current);
      audioIntervalRef.current = null;
    }
    if (networkIntervalRef.current) {
      clearInterval(networkIntervalRef.current);
      networkIntervalRef.current = null;
    }

    // Initialize user and metrics intervals
    if (
      localUser &&
      localUser.sid &&
      status === 'connected' &&
      localUser.profile &&
      roleType !== 'watcher' &&
      sessionStarted &&
      participantStatus === ConferenceStatus.Ready
    ) {
      const { browser, device, os } = getClientData(navigator.userAgent);
      const audioInterval = isMobile() ? (isLowEndDevice() ? 2000 : 1500) : 500;
      const networkInterval = isMobile()
        ? isLowEndDevice()
          ? 15000
          : 10000
        : 5000;

      updateReduxState(
        setLocalParticipantLogs({
          displayName: user?.displayName || '',
          browser,
          device,
          os,
          isAECEnabled: hasProcessing,
          isAnonymous: user?.isAnonymous ?? false,
        }),
      );

      logger.metric({
        type: 'participant',
        name: 'participant_info',
        session_id: classDetails?.roomId || '',
        participant_id: user!.id,
        timestamp: Date.now() * 1000,
        labels: {
          username: user?.displayName || '',
          browser_type: browser,
          device,
          os,
          user_status: user!.isAnonymous ? 'guest' : 'registered',
          aec_enabled: hasProcessing ?? false,
          is_client: true,
        },
      });

      audioIntervalRef.current = setInterval(() => {
        const localAudioLevel = getAudioLevel(true);

        updateReduxState(
          updateAudioLevel({
            sid: room.localParticipant.sid,
            audioLevel: localAudioLevel,
          }),
        );

        room.remoteParticipants.forEach(async (participant) => {
          const { sid } = participant;

          if (participant.metadata) {
            try {
              const metadata = JSON.parse(participant.metadata);
              if (
                participant.sid !== room.localParticipant.sid &&
                metadata.role !== 'agent' &&
                metadata.role !== 'watcher'
              ) {
                const audioLevel = getAudioLevel(false, sid);
                updateReduxState(
                  updateAudioLevel({
                    sid,
                    audioLevel,
                  }),
                );
              }
            } catch (error) {
              console.log('Failed to parse participant metadata', error);
              return;
            }
          }
        });
      }, audioInterval);

      networkIntervalRef.current = setInterval(async () => {
        const networkStats = await getNetworkStats(room);
        const quality = room.localParticipant.connectionQuality;
        const localAudioLevel = getAudioLevel(true);
        const timestamp = Date.now() * 1000;
        const bitrate = await getBitrate(room);

        updateReduxState(
          setLocalParticipantLogs({
            videoBitrate: bitrate.video,
            audioBitrate: bitrate.audio,
            networkStats,
            connectionQuality: quality,
          }),
        );

        logger.metric({
          type: 'participant',
          name: 'network_stats',
          session_id: classDetails?.roomId ?? '',
          participant_id: localUser.identity,
          timestamp,
          labels: {
            network_connection: quality,
            audio_bitrate: bitrate.audio,
            video_bitrate: bitrate.video,
            is_client: true,
            round_trip: networkStats.roundTrip,
            stream_stats: networkStats.participants
              ? Object.keys(networkStats.participants).map(
                  (participantKey) => ({
                    remote_participant_id: participantKey,
                    jitter_buffer:
                      networkStats.participants[participantKey].jitterBuffer,
                    jitter_buffer_min:
                      networkStats.participants[participantKey].minJitterBuffer,
                    jitter: networkStats.participants[participantKey].jitter,
                    fraction_loss:
                      networkStats.participants[participantKey].fractionLoss,
                  }),
                )
              : {},
          },
        });

        logger.metric({
          type: 'participant',
          name: 'audio_metrics',
          session_id: classDetails?.roomId || '',
          participant_id: localUser.identity,
          labels: {
            audio_rms_db: localAudioLevel,
            is_client: true,
          },
          timestamp,
        });
      }, networkInterval);
    }

    return () => {
      if (audioIntervalRef.current) {
        clearInterval(audioIntervalRef.current);
        audioIntervalRef.current = null;
      }
      if (networkIntervalRef.current) {
        clearInterval(networkIntervalRef.current);
        networkIntervalRef.current = null;
      }
    };
  }, [localUser, status, participantStatus]);

  useEffect(() => {
    if (!hasProcessing) return;

    updateReduxState(
      setLocalParticipantLogs({
        isAECEnabled: hasProcessing,
      }),
    );

    if (localParticipantLogs && user && sessionStarted) {
      const { displayName, browser, device, os, isAnonymous } =
        localParticipantLogs;

      logger.metric({
        type: 'participant',
        name: 'participant_info',
        session_id: classDetails?.roomId || '',
        participant_id: localUser?.identity || '',
        timestamp: Date.now() * 1000,
        labels: {
          username: displayName || '',
          browser_type: browser ?? '',
          device: device ?? '',
          os: os ?? '',
          user_status: isAnonymous ? 'guest' : 'registered',
          aec_enabled: hasProcessing,
          is_client: true,
        },
      });
    }
  }, [hasProcessing]);

  useEffect(() => {
    if (
      Object.keys(localParticipantLogs).every(
        (key) =>
          localParticipantLogs[key as keyof typeof localParticipantLogs] !==
          null,
      ) &&
      localUser
    ) {
      const sid = localUser.sid;

      updateReduxState(
        updateRemoteParticipantLogs({ sid, logs: localParticipantLogs }),
      );

      sendData(room, {
        type: 'participant-logs',
        message: {
          sid: room.localParticipant.sid,
          message: JSON.stringify(localParticipantLogs),
        },
      });
    }
  }, [localParticipantLogs]);

  useEffect(() => {
    updateReduxState(resetClassLogger());

    return () => {
      updateReduxState(resetClassLogger());
    };
  }, []);
};
