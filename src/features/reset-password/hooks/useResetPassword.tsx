import { useMutation } from '@tanstack/react-query';
import { resetPassword } from '../api';
import { useNavigate } from 'react-router';
import { useNotifications } from '@/shared';

export const useResetPassword = () => {
  const navigate = useNavigate();
  const { addToast, addNotification } = useNotifications();

  return useMutation({
    mutationFn: async ({
      token,
      newPassword,
    }: {
      token: string;
      newPassword: string;
    }) => {
      return await resetPassword(token, newPassword);
    },
    onSuccess: () => {
      addToast({
        text: 'notifications.success.passwordChanged',
      });
      navigate('/', { replace: true });
    },
    onError: (error: Error) => {
      if (error.message == 'invalid or expired reset token') {
        addNotification({
          title: 'notifications.errors.expiredPasswordToken.title',
          description: 'notifications.errors.expiredPasswordToken.description',
        });
        navigate('/', { replace: true });
      } else {
        addNotification({
          title: 'notifications.errors.serverError.title',
          description: 'notifications.errors.serverError.description',
        });
        navigate('/', { replace: true });
      }
    },
  });
};
