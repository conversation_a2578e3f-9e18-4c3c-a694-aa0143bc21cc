import { useMutation } from '@tanstack/react-query';
import { requestPasswordReset } from '../api';
import { useNavigate } from 'react-router';
import { useNotifications } from '@/shared';

export const useRequestPasswordReset = () => {
  const navigate = useNavigate();
  const { addToast } = useNotifications();

  return useMutation({
    mutationFn: async (email: string) => {
      return await requestPasswordReset(email);
    },
    onSuccess: () => {
      addToast({
        text: 'notifications.success.requestPasswordReset',
      });
      navigate('/', { replace: true });
    },
  });
};
