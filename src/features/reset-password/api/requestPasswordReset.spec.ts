import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { AxiosError } from 'axios';
import { api } from '@/shared';
import { requestPasswordReset } from './requestPasswordReset';

describe('requestPasswordReset', () => {
  let originalPost: typeof api.post;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    originalPost = api.post;
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' &&
          error !== null &&
          'response' in (error as object),
      );
    vi.clearAllMocks();
  });

  afterEach(() => {
    api.post = originalPost;
    isAxiosErrorSpy.mockRestore();
  });

  it('should successfully request password reset', async () => {
    const email = '<EMAIL>';

    api.post = vi.fn().mockResolvedValue({ data: {} });

    await expect(requestPasswordReset(email)).resolves.toBeUndefined();

    expect(api.post).toHaveBeenCalledWith(
      '/auth/request-password-reset',
      { email },
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );
    expect(api.post).toHaveBeenCalledTimes(1);
  });

  it('should throw provided error message from response when error is a string', async () => {
    const email = '<EMAIL>';
    const errorMessage = 'User with this email does not exist';

    const errorResponse = {
      response: {
        data: {
          error: errorMessage,
        },
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(requestPasswordReset(email)).rejects.toThrow(errorMessage);
  });

  it('should throw default error message when error is empty string', async () => {
    const email = '<EMAIL>';

    const errorResponse = {
      response: {
        data: {
          error: '   ', // whitespace only
        },
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(requestPasswordReset(email)).rejects.toThrow(
      'Failed to request password reset!',
    );
  });

  it('should throw default error message when error is not a string', async () => {
    const email = '<EMAIL>';

    const errorResponse = {
      response: {
        data: {
          error: { message: 'Some error object' }, // object instead of string
        },
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(requestPasswordReset(email)).rejects.toThrow(
      'Failed to request password reset!',
    );
  });

  it('should throw default error message when error is a number', async () => {
    const email = '<EMAIL>';

    const errorResponse = {
      response: {
        data: {
          error: 404, // number instead of string
        },
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(requestPasswordReset(email)).rejects.toThrow(
      'Failed to request password reset!',
    );
  });

  it('should throw default error message when error is null', async () => {
    const email = '<EMAIL>';

    const errorResponse = {
      response: {
        data: {
          error: null,
        },
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(requestPasswordReset(email)).rejects.toThrow(
      'Failed to request password reset!',
    );
  });

  it('should throw default error message when error is undefined', async () => {
    const email = '<EMAIL>';

    const errorResponse = {
      response: {
        data: {}, // no error property
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(requestPasswordReset(email)).rejects.toThrow(
      'Failed to request password reset!',
    );
  });

  it('should throw default error message when response data is undefined', async () => {
    const email = '<EMAIL>';

    const errorResponse = {
      response: {}, // no data property
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(requestPasswordReset(email)).rejects.toThrow(
      'Failed to request password reset!',
    );
  });

  it('should throw default error message when response is undefined', async () => {
    const email = '<EMAIL>';

    const errorResponse = {} as AxiosError; // no response property

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(requestPasswordReset(email)).rejects.toThrow(
      'Failed to request password reset!',
    );
  });

  it('should throw default error message for non-Axios error', async () => {
    const email = '<EMAIL>';

    isAxiosErrorSpy.mockReturnValue(false);
    api.post = vi.fn().mockRejectedValue(new Error('Network failure'));

    await expect(requestPasswordReset(email)).rejects.toThrow(
      'Failed to request password reset!',
    );
  });

  it('should handle empty email', async () => {
    const email = '';

    api.post = vi.fn().mockResolvedValue({ data: {} });

    await expect(requestPasswordReset(email)).resolves.toBeUndefined();

    expect(api.post).toHaveBeenCalledWith(
      '/auth/request-password-reset',
      { email },
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );
  });

  it('should handle email with special characters', async () => {
    const email = '<EMAIL>';

    api.post = vi.fn().mockResolvedValue({ data: {} });

    await expect(requestPasswordReset(email)).resolves.toBeUndefined();

    expect(api.post).toHaveBeenCalledWith(
      '/auth/request-password-reset',
      { email },
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );
  });
});
