import axios from 'axios';
import { api } from '@/shared';

export const resetPassword = async (
  token: string,
  newPassword: string,
): Promise<void> => {
  try {
    await api.post(
      '/auth/reset-password',
      { token, newPassword },
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );
  } catch (error) {
    let errorMessage = 'Failed to reset password!';

    if (axios.isAxiosError(error) && error.response) {
      const responseError = error.response.data?.error?.message;
      if (
        responseError &&
        typeof responseError === 'string' &&
        responseError.trim()
      ) {
        errorMessage = responseError;
      }
    }

    throw new Error(errorMessage);
  }
};
