import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { AxiosError } from 'axios';
import { api } from '@/shared';
import { resetPassword } from './resetPassword';

describe('resetPassword', () => {
  let originalPost: typeof api.post;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    originalPost = api.post;
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' &&
          error !== null &&
          'response' in (error as object),
      );
    vi.clearAllMocks();
  });

  afterEach(() => {
    api.post = originalPost;
    isAxiosErrorSpy.mockRestore();
  });

  it('should successfully reset password', async () => {
    const token = 'reset-token-123';
    const newPassword = 'newSecurePassword123';

    api.post = vi.fn().mockResolvedValue({ data: {} });

    await expect(resetPassword(token, newPassword)).resolves.toBeUndefined();

    expect(api.post).toHaveBeenCalledWith(
      '/auth/reset-password',
      { token, newPassword },
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );
    expect(api.post).toHaveBeenCalledTimes(1);
  });

  it('should throw provided error message from response when error.message is a string', async () => {
    const token = 'invalid-token';
    const newPassword = 'newPassword123';
    const errorMessage = 'Invalid or expired reset token';

    const errorResponse = {
      response: {
        data: {
          error: {
            message: errorMessage,
          },
        },
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(resetPassword(token, newPassword)).rejects.toThrow(
      errorMessage,
    );
  });

  it('should throw default error message when error.message is empty string', async () => {
    const token = 'token-123';
    const newPassword = 'newPassword123';

    const errorResponse = {
      response: {
        data: {
          error: {
            message: '   ', // whitespace only
          },
        },
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(resetPassword(token, newPassword)).rejects.toThrow(
      'Failed to reset password!',
    );
  });

  it('should throw default error message when error.message is not a string', async () => {
    const token = 'token-123';
    const newPassword = 'newPassword123';

    const errorResponse = {
      response: {
        data: {
          error: {
            message: 123, // not a string
          },
        },
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(resetPassword(token, newPassword)).rejects.toThrow(
      'Failed to reset password!',
    );
  });

  it('should throw default error message when error.message is undefined', async () => {
    const token = 'token-123';
    const newPassword = 'newPassword123';

    const errorResponse = {
      response: {
        data: {
          error: {}, // no message property
        },
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(resetPassword(token, newPassword)).rejects.toThrow(
      'Failed to reset password!',
    );
  });

  it('should throw default error message when error object is undefined', async () => {
    const token = 'token-123';
    const newPassword = 'newPassword123';

    const errorResponse = {
      response: {
        data: {}, // no error property
      },
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(resetPassword(token, newPassword)).rejects.toThrow(
      'Failed to reset password!',
    );
  });

  it('should throw default error message when response data is undefined', async () => {
    const token = 'token-123';
    const newPassword = 'newPassword123';

    const errorResponse = {
      response: {}, // no data property
    } as AxiosError;

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(resetPassword(token, newPassword)).rejects.toThrow(
      'Failed to reset password!',
    );
  });

  it('should throw default error message when response is undefined', async () => {
    const token = 'token-123';
    const newPassword = 'newPassword123';

    const errorResponse = {} as AxiosError; // no response property

    api.post = vi.fn().mockRejectedValue(errorResponse);

    await expect(resetPassword(token, newPassword)).rejects.toThrow(
      'Failed to reset password!',
    );
  });

  it('should throw default error message for non-Axios error', async () => {
    const token = 'token-123';
    const newPassword = 'newPassword123';

    isAxiosErrorSpy.mockReturnValue(false);
    api.post = vi.fn().mockRejectedValue(new Error('Network failure'));

    await expect(resetPassword(token, newPassword)).rejects.toThrow(
      'Failed to reset password!',
    );
  });

  it('should handle empty token and password', async () => {
    const token = '';
    const newPassword = '';

    api.post = vi.fn().mockResolvedValue({ data: {} });

    await expect(resetPassword(token, newPassword)).resolves.toBeUndefined();

    expect(api.post).toHaveBeenCalledWith(
      '/auth/reset-password',
      { token, newPassword },
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );
  });
});
