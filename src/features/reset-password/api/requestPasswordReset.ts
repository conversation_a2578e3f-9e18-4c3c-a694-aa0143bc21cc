import axios from 'axios';
import { api } from '@/shared';

export const requestPasswordReset = async (email: string): Promise<void> => {
  try {
    await api.post(
      '/auth/request-password-reset',
      { email },
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );
  } catch (error) {
    let errorMessage = 'Failed to request password reset!';

    if (axios.isAxiosError(error) && error.response) {
      const responseError = error.response.data?.error;
      if (
        responseError &&
        typeof responseError === 'string' &&
        responseError.trim()
      ) {
        errorMessage = responseError;
      }
    }

    throw new Error(errorMessage);
  }
};
