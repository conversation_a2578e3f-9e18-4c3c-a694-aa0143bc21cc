import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shadcn/components/ui/dialog';
import { Input } from '@/shadcn/components/ui/input';
import { FC, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router';
import { useRequestPasswordReset } from '../hooks';

export const RequestResetPasswordDialog: FC = () => {
  const [searchParams] = useSearchParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const resetPassword = searchParams.get('reset-password');
  const token = searchParams.get('token');
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
    reset,
  } = useForm<{ email: string }>();
  const { mutate: requestPasswordReset } = useRequestPasswordReset();

  const onSubmit = (data: { email: string }) => {
    requestPasswordReset(data.email);
  };

  useEffect(() => {
    if (resetPassword === 'true' && token === null) {
      reset();
    }
  }, [resetPassword]);

  return (
    <Dialog
      open={resetPassword === 'true' && token === null}
      onOpenChange={(param) => {
        if (!param) {
          navigate('/', { replace: true });
        }
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('home.dialogs.resetPassword.title')}</DialogTitle>
          <DialogDescription className="text-sm text-primary/70">
            {t('home.dialogs.resetPassword.description')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <Input
              data-testid="sign-in-forgot-password-email-input"
              placeholder={t('globalFields.email.label')}
              className={
                errors?.email ? 'border-destructive' : 'border-[#00000030]'
              }
              {...register('email', {
                required: t('globalFields.email.validation.required'),
                onChange: (e) => {
                  setValue('email', e.target.value.replace(/\s+/g, ''));
                },
                pattern: {
                  value: /^[^@\s]+@(?:[A-Za-z0-9-]+\.)+[A-Za-z0-9-]+$/,
                  message: t('globalFields.email.validation.invalid'),
                },
                minLength: {
                  value: 3,
                  message: t('globalFields.email.validation.minLength'),
                },
                maxLength: {
                  value: 50,
                  message: t('globalFields.email.validation.maxLength'),
                },
              })}
              inputMode="email"
            />
            {errors.email && (
              <p
                data-testid="sign-up-email-error"
                className="text-destructive text-sm"
              >
                {errors.email.message}
              </p>
            )}
          </div>
          <div className="grid grid-cols-[auto_1fr] max-[550px]:grid-cols-1 gap-2">
            <Button
              size="lg"
              variant="outline"
              className="w-full whitespace-normal"
              type="button"
              onClick={() => {
                navigate('/?login=true', { replace: true });
              }}
            >
              {t('home.dialogs.resetPassword.buttons.back')}
            </Button>
            <Button
              data-testid="sign-in-forgot-password-submit-button"
              size="lg"
              className="w-full whitespace-normal"
              type="submit"
            >
              {t('home.dialogs.resetPassword.buttons.send')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
