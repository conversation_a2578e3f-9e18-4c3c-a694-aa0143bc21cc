import { Button } from '@/shadcn/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/shadcn/components/ui/dialog';
import { Input } from '@/shadcn/components/ui/input';
import { FC, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router';
import {
  calculatePasswordStrength,
  PASSWORD_MIN_LENGTH,
  PASSWORD_PATTERN,
} from '@/shared';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { Info } from 'lucide-react';
import { Progress } from '@/shadcn/components/ui/progress';
import { useResetPassword } from '../hooks';

type FormValues = {
  password: string;
  confirmPassword: string;
};

export const ResetPasswordDialog: FC = () => {
  const [searchParams] = useSearchParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const resetPassword = searchParams.get('reset-password');
  const token = searchParams.get('token');
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    reset,
  } = useForm<FormValues>({ mode: 'onChange' });
  const password = watch('password');
  const { percentage, strength, colorClass } = useMemo(
    () => calculatePasswordStrength(password ?? ''),
    [password],
  );
  const [showPasswordStrengthTooltip, setShowPasswordStrengthTooltip] =
    useState(false);
  const { mutate } = useResetPassword();

  const onSubmit = (data: FormValues) => {
    mutate({ token: token ?? '', newPassword: data.password });
  };

  useEffect(() => {
    if (resetPassword === 'true' && token !== null) {
      reset();
    }
  }, [resetPassword]);

  return (
    <Dialog
      open={resetPassword === 'true' && token !== null}
      onOpenChange={(param) => {
        if (!param) {
          navigate('/', { replace: true });
        }
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('home.dialogs.resetPassword.title')}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
          <div>
            <Input
              data-testid="sign-up-password-input"
              type="password"
              showPasswordToggle
              className={
                errors?.password ? 'border-destructive' : 'border-[#00000030]'
              }
              placeholder={t('globalFields.password.label')}
              {...register('password', {
                required: t('globalFields.password.validation.required'),
                minLength: {
                  value: PASSWORD_MIN_LENGTH,
                  message: t('globalFields.password.validation.minLength'),
                },
                maxLength: {
                  value: 50,
                  message: t('globalFields.password.validation.maxLength'),
                },
                pattern: {
                  value: PASSWORD_PATTERN,
                  message: t('globalFields.password.validation.pattern'),
                },
                validate: (v: string) =>
                  calculatePasswordStrength(v).percentage >=
                    Math.round((3 / 6) * 100) ||
                  t('globalFields.password.validation.weakStrength'),
              })}
            />
            {errors.password && (
              <p
                data-testid="sign-up-password-error"
                className="text-destructive text-sm"
              >
                {errors.password.message}
              </p>
            )}
          </div>
          <div>
            <Input
              data-testid="sign-up-confirm-password-input"
              type="password"
              showPasswordToggle
              className={
                errors?.confirmPassword
                  ? 'border-destructive'
                  : 'border-[#00000030]'
              }
              placeholder={t('globalFields.confirmPassword.label')}
              {...register('confirmPassword', {
                required: t('globalFields.confirmPassword.validation.required'),
                validate: (v) =>
                  v === watch('password') ||
                  t('globalFields.confirmPassword.validation.match'),
              })}
            />
            {errors.confirmPassword && (
              <p
                data-testid="sign-up-confirm-password-error"
                className="text-destructive text-sm"
              >
                {errors.confirmPassword.message}
              </p>
            )}
          </div>

          {password && (
            <div
              data-testid="sign-up-password-strength"
              className="flex flex-col gap-2"
            >
              <div className="flex items-center justify-between max-[350px]:flex-col max-[350px]:items-start gap-2">
                <TooltipProvider>
                  <Tooltip
                    open={showPasswordStrengthTooltip}
                    onOpenChange={setShowPasswordStrengthTooltip}
                  >
                    <TooltipTrigger asChild>
                      <div
                        data-testid="sign-up-password-strength-info"
                        className="flex items-center justify-center gap-2"
                        onClick={() => setShowPasswordStrengthTooltip(true)}
                      >
                        <Info className="size-4 text-primary/60 " />
                        <h3 className="text-primary/60 text-xs">
                          {t('globals.passwordStrength.title')}
                        </h3>
                      </div>
                    </TooltipTrigger>

                    <TooltipContent align="start" className="max-w-[300px]">
                      <p>{t('globals.passwordStrength.tooltip')}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <h4
                  className="text-xs font-medium"
                  style={{ color: `var(--${colorClass})` }}
                >
                  {t(strength)}
                </h4>
              </div>
              <Progress
                data-testid="sign-up-password-strength-progress"
                value={percentage}
                className="w-full h-[6px]"
                progressColorVar={`--${colorClass}`}
              />
            </div>
          )}

          <Button 
            data-testid="sign-up-reset-password-submit-button"
            size="lg" 
            className="w-full" 
            type="submit"
          >
            {t('home.dialogs.resetPassword.buttons.save')}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
};
