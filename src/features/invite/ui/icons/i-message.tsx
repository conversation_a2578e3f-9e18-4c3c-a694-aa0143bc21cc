import { SVGProps } from 'react';

export const IMessageIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={25}
    height={22}
    fill="none"
    {...props}
  >
    <path
      fill="#fff"
      d="M12.155 20.36c6.713 0 12.154-4.557 12.154-10.18C24.309 4.558 18.868 0 12.155 0S0 4.558 0 10.18c0 3.516 2.128 6.616 5.364 8.445l.004.008c.37.268.79 1.11-.494 2.345-1.604 1.542 1.234.617 2.098.092.224-.136.43-.272.622-.399.546-.362.95-.65 1.383-.65l.07.005.027.005c.984.215 2.016.33 3.08.33l.001-.001Z"
    />
    <mask
      id="a"
      width={25}
      height={22}
      x={0}
      y={0}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: 'alpha',
      }}
    >
      <path
        fill="#fff"
        d="M12.155 20.36c6.713 0 12.154-4.557 12.154-10.18C24.309 4.558 18.868 0 12.155 0S0 4.558 0 10.18c0 3.516 2.128 6.616 5.364 8.445l.004.008c.37.268.79 1.11-.494 2.345-1.604 1.542 1.234.617 2.098.092.224-.136.43-.272.622-.399.546-.362.95-.65 1.383-.65l.07.005.027.005c.984.215 2.016.33 3.08.33l.001-.001Z"
      />
    </mask>
    <g mask="url(#a)">
      <path
        fill="#fff"
        d="M4.782 21.255c1.406-1.357 1.1-2.21.771-2.468.494-.444.967-.185 1.141 0-.072.566-.462 1.858-1.45 2.5-.987.64-.72.246-.462-.032Z"
      />
    </g>
  </svg>
);
