import { SVGProps } from 'react';

export const WhatsAppIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={21}
    fill="none"
    {...props}
  >
    <path
      fill="#fff"
      d="m0 20.018 1.435-5.336a9.867 9.867 0 0 1 2.664-12.67 9.91 9.91 0 0 1 12.94.837 9.874 9.874 0 0 1 1.006 12.907A9.912 9.912 0 0 1 5.39 18.578L0 20.018Zm5.65-3.435.333.197a8.01 8.01 0 0 0 9.689-1.177 7.981 7.981 0 0 0-2.524-13.062 8.012 8.012 0 0 0-9.432 2.506 7.978 7.978 0 0 0-1.654 4.875 7.887 7.887 0 0 0 1.17 4.14l.209.344-.803 2.978 3.012-.8Z"
    />
    <path
      fill="#fff"
      fillRule="evenodd"
      d="M13.74 11.292a1.65 1.65 0 0 0-1.413-.322c-.367.153-.605.728-.842 1.015a.35.35 0 0 1-.452.102 6.398 6.398 0 0 1-3.197-2.735.383.383 0 0 1 .05-.53 2.21 2.21 0 0 0 .566-.942 2.05 2.05 0 0 0-.26-1.128 2.639 2.639 0 0 0-.82-1.236 1.131 1.131 0 0 0-1.214.187 2.493 2.493 0 0 0-.865 1.974c.001.21.028.417.08.62.13.487.333.953.598 1.382.192.328.401.647.628.953a9.624 9.624 0 0 0 2.717 2.515 8.16 8.16 0 0 0 1.695.807 3.75 3.75 0 0 0 1.971.31 2.368 2.368 0 0 0 1.785-1.325 1.12 1.12 0 0 0 .085-.677c-.101-.468-.728-.745-1.112-.97Z"
      clipRule="evenodd"
    />
  </svg>
);
