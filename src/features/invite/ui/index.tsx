import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { Plus } from 'lucide-react';
import { FC, ReactNode } from 'react';
import { DesktopInviteDialogContent } from './desktop';
import { useTranslation } from 'react-i18next';
import { useACL, useClassroomSettings } from '@/shared';

export const InviteDialog: FC<{
  name: string;
  id: string;
  toClass: boolean;
  defaultRole: 'student' | 'guest' | 'teacher';
  trigger?: ReactNode;
}> = ({ name, defaultRole, id, toClass, trigger }) => {
  const { t } = useTranslation();
  const acl = useACL();
  const classroomSettings = useClassroomSettings();

  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger ? (
          trigger
        ) : (
          <Button
            data-testid={`invite-trigger-${defaultRole}`}
            variant="monochrome_outline"
            className="w-full gap-1 px-4 py-2 font-semibold !bg-[#fff2e51a] !border-[#8F7E72] hover:!bg-[#fff2e580] active:!bg-[#FFF2E5]"
            disabled={
              !acl ||
              !classroomSettings ||
              (defaultRole === 'guest'
                ? !(classroomSettings.allowInviteGuests && acl.canGuestInvite)
                : !acl.canParticipantInvite)
            }
          >
            <Plus />
            {t('invite.trigger')}
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="max-[540px]:w-full max-[540px]:max-h-none max-[540px]:!p-3 max-[540px]:rounded-none max-[540px]:h-[100dvh] w-[calc(100%-2rem)] !max-w-[84rem] max-h-[clamp(0px,100dvh-2rem,825px)] h-full gap-8 bg-[#FFFAF1]">
        <DesktopInviteDialogContent
          name={name}
          defaultRole={defaultRole}
          id={id}
          toClass={toClass}
        />
      </DialogContent>
    </Dialog>
  );
};
