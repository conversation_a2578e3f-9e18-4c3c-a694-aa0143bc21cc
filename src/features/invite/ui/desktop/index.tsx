import { ChangeEvent, FC, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  downloadFlyerWithQR,
  getCurrentTime,
  isTimeInPast,
  isToday,
  validateTime,
} from '../../utils';
import { useCreateInvitation } from '../../hooks';
import {
  InviteDescriptionSchema,
  InviteTitleSchema,
  InviteUsesSchema,
} from '../../model';
import moment from 'moment';
import {
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shadcn/components/ui/dialog';
import { FlyerRenderer } from '../flyer';
import { Flyer } from './flyer';
import { Form } from './form';
import { Share } from './share';

export const DesktopInviteDialogContent: FC<{
  name: string;
  id: string;
  toClass: boolean;
  defaultRole: 'student' | 'guest' | 'teacher';
}> = ({ name, defaultRole, id, toClass }) => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const renderer = useRef<FlyerRenderer | null>(null);
  const [selectedRole, setSelectedRole] = useState<
    'teacher' | 'student' | 'guest'
  >(defaultRole);
  const [title, setTitle] = useState(
    t('invite.form.title.defaults.classroom', {
      name,
    }),
  );
  const [description, setDescription] = useState(
    t('invite.form.description.defaults.classroom'),
  );
  const [requireSignUp, setRequireSignUp] = useState(true);
  const [isEvent, setIsEvent] = useState(false);
  const [eventDate, setEventDate] = useState<Date | undefined>(new Date());
  const [eventTime, setEventTime] = useState<string>(getCurrentTime());
  const [expirationPeriod, setExpirationPeriod] = useState<string>('never');
  const [usesLimitSelect, setUsesLimitSelect] = useState<string>('unlimited');
  const [usesLimit, setUsesLimit] = useState<number>(10);
  const [showCopyToast, setShowCopyToast] = useState(false);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string | undefined>
  >({});
  const [isCopyPending, setIsCopyPending] = useState(false);
  const [isWhatsappSharePending, setIsWhatsappSharePending] = useState(false);
  const [isFacebookSharePending, setIsFacebookSharePending] = useState(false);
  const [isIMessageSharePending, setIsIMessageSharePending] = useState(false);
  const [isTelegramSharePending, setIsTelegramSharePending] = useState(false);
  const [isPdfExportPending, setIsPdfExportPending] = useState(false);

  const { mutate: createInvitationMutation } = useCreateInvitation();

  const handleDateChange = (newDate: Date | undefined) => {
    setEventDate(newDate);

    if (
      newDate &&
      eventTime &&
      isToday(newDate) &&
      isTimeInPast(eventTime, newDate)
    ) {
      const currentTime = getCurrentTime();
      setEventTime(currentTime);
    }
  };

  const handleTimeChange = (newTime: string) => {
    setEventTime(newTime);
  };

  const handleTimeBlur = () => {
    const validatedTime = validateTime(eventTime, eventDate);
    if (validatedTime === eventTime) return;
    setEventTime(validatedTime);
  };

  const handleCopy = async () => {
    setIsCopyPending(true);
    createInvitation(
      async (token: string) => {
        await navigator.clipboard.writeText(getInviteLink(token));

        setShowCopyToast(true);
        setIsCopyPending(false);
        setTimeout(() => setShowCopyToast(false), 2000);
      },
      () => {
        setIsCopyPending(false);
      },
    );
  };

  const handleShare = async (
    provider: 'whatsapp' | 'iMessage' | 'telegram' | 'facebook',
  ) => {
    switch (provider) {
      case 'whatsapp':
        setIsWhatsappSharePending(true);
        break;
      case 'iMessage':
        setIsIMessageSharePending(true);
        break;
      case 'telegram':
        setIsTelegramSharePending(true);
        break;
      case 'facebook':
        setIsFacebookSharePending(true);
        break;
    }

    createInvitation(
      async (token: string) => {
        const fullInviteLink = getInviteLink(token);

        let shareLink = '';

        if (provider === 'whatsapp') {
          shareLink = `https://api.whatsapp.com/send?text=${encodeURIComponent(
            t('invite.share.social.linkText2', {
              link: fullInviteLink,
            }),
          )}`;
          setIsWhatsappSharePending(false);
        } else if (provider === 'facebook') {
          shareLink = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
            fullInviteLink,
          )}&quote=${encodeURIComponent(t('invite.share.social.linkText2'))}`;
          setIsFacebookSharePending(false);
        } else if (provider === 'iMessage') {
          shareLink = `sms:?&body=${encodeURIComponent(
            t('invite.share.social.linkText2', {
              link: fullInviteLink,
            }),
          )}`;
          setIsIMessageSharePending(false);
        } else if (provider === 'telegram') {
          shareLink = `https://t.me/share/url?url=${encodeURIComponent(
            fullInviteLink,
          )}&text=${encodeURIComponent(t('invite.share.social.linkText1'))}`;
          setIsTelegramSharePending(false);
        }

        window.open(shareLink, '_blank');
      },
      () => {
        setIsWhatsappSharePending(false);
        setIsIMessageSharePending(false);
        setIsTelegramSharePending(false);
        setIsFacebookSharePending(false);
      },
    );
  };

  const handleExportPDF = async () => {
    setIsPdfExportPending(true);
    try {
      if (!canvasRef.current) return;
      const canvas = canvasRef.current;

      createInvitation(
        async (token: string) => {
          await downloadFlyerWithQR(
            canvas,
            getInviteLink(token),
            'Invitation.pdf',
            77,
          );
          setIsPdfExportPending(false);
        },
        () => {
          setIsPdfExportPending(false);
        },
      );
    } catch (err) {
      setIsPdfExportPending(false);
      console.error(err);
    }
  };

  const createInvitation = (
    callback: (token: string) => void,
    onError?: (error: Error) => void,
  ) => {
    const validatedEventDate = eventDate
      ? eventDate < new Date()
        ? new Date(Date.now() + 5 * 60 * 1000)
        : eventDate
      : new Date(Date.now() + 5 * 60 * 1000);

    const parsedExpirationPeriod = parseExpirationPeriod(expirationPeriod);

    createInvitationMutation(
      {
        id,
        inviteRole: selectedRole,
        requireSignUp,
        eventDate: validatedEventDate.toISOString(),
        isUnlimited: usesLimitSelect === 'unlimited',
        usesLimit: usesLimitSelect === 'unlimited' ? undefined : usesLimit,
        expirationPeriod:
          parsedExpirationPeriod === 'never'
            ? undefined
            : parsedExpirationPeriod * 24 * 60 * 60,
      },
      {
        onSuccess: (data) => {
          callback(data);
        },
        onError: (error) => {
          if (onError) onError(error);
        },
      },
    );
  };

  const onTitleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const errors = InviteTitleSchema.safeParse(value).error?.message;

    if (errors) return event.preventDefault();

    setTitle(value);
    setValidationErrors((e) => ({
      ...e,
      title: errors,
    }));
  };

  const onDescriptionChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const errors = InviteDescriptionSchema.safeParse(value).error?.message;

    if (errors) return event.preventDefault();

    setDescription(value);
    setValidationErrors((e) => ({
      ...e,
      description: errors,
    }));
  };

  const onUsesLimitChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const errors = InviteUsesSchema.safeParse(value).error?.message;

    if (errors) return event.preventDefault();

    setUsesLimit(Number(value));
    setValidationErrors((e) => ({
      ...e,
      usesLimit: errors,
    }));
  };

  useEffect(() => {
    if (isEvent) {
      setTitle(t('invite.form.title.defaults.event'));
      setDescription(t('invite.form.description.defaults.event'));
    } else {
      setTitle(
        t('invite.form.title.defaults.classroom', {
          name,
        }),
      );
      setDescription(t('invite.form.description.defaults.classroom'));
    }
  }, [isEvent]);

  useEffect(() => {
    const canvas = canvasRef.current!;
    if (!canvas) return;

    canvas.width = 487;
    canvas.height = 716;

    (async () => {
      if (!renderer.current) {
        const env =
          window.location.hostname === 'test.virtuosohub.ai'
            ? 'test'
            : window.location.hostname === 'localhost' ||
                window.location.hostname === 'dev.virtuosohub.ai'
              ? 'dev'
              : 'prod';
        renderer.current = await FlyerRenderer.getInstance(canvas, env);

        if (!renderer.current.isReady) return;

        renderer.current.createFlyer({
          title,
          description,
          date: Date.now(),
          uses: usesLimitSelect === 'unlimited' ? 'unlimited' : usesLimit,
          access: (requireSignUp ? 'close' : 'open') as 'open' | 'close',
          validUntil: 'never',
          joiningAs: selectedRole,
          isEvent: false,
        });
      }
    })();
  }, []);

  useEffect(() => {
    return () => {
      if (renderer.current) {
        FlyerRenderer.reset();
        renderer.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (!renderer.current || !renderer.current.isReady) return;

    const date =
      eventDate?.setHours(
        Number(eventTime.split(':')[0]),
        Number(eventTime.split(':')[1]),
      ) || Date.now();

    const parsedExpirationPeriod = parseExpirationPeriod(expirationPeriod);

    const validUntil =
      parsedExpirationPeriod === 'never'
        ? 'never'
        : moment(
            new Date(Date.now() + parsedExpirationPeriod * 24 * 60 * 60 * 1000),
          ).format('DD/MM/YYYY');

    const config = {
      title,
      description,
      date,
      uses:
        usesLimitSelect === 'unlimited'
          ? 'unlimited'
          : (usesLimit as 'unlimited' | number),
      access: requireSignUp ? 'close' : ('open' as 'open' | 'close'),
      validUntil,
      joiningAs: selectedRole,
      isEvent,
    };

    renderer.current.createFlyer(config);
  }, [
    title,
    description,
    eventDate,
    eventTime,
    selectedRole,
    expirationPeriod,
    usesLimitSelect,
    requireSignUp,
    usesLimit,
    isEvent,
  ]);

  const parseExpirationPeriod = (expirationPeriod: string) => {
    return expirationPeriod === '1d'
      ? 1
      : expirationPeriod === '3d'
        ? 3
        : expirationPeriod === '7d'
          ? 7
          : expirationPeriod === '1m'
            ? 30
            : expirationPeriod === '1y'
              ? 365
              : 'never';
  };

  const getInviteLink = (token: string) => {
    return (
      window.location.origin +
      '/join/' +
      token +
      (toClass ? `?classroom=${id}` : '')
    );
  };

  return (
    <>
      <DialogHeader className="w-full flex items-start justify-center gap-0">
        <DialogTitle className="font-bold text-[2rem] text-primary text-left">
          {t('invite.title')}
        </DialogTitle>
        <DialogDescription />
      </DialogHeader>

      <div className="grid grid-cols-[1fr_auto] gap-4 flex-1 min-h-0 overflow-hidden">
        <div className="h-full flex flex-col min-h-0 overflow-y-auto gap-2.5">
          <Form
            title={title}
            description={description}
            selectedRole={selectedRole}
            setSelectedRole={setSelectedRole}
            errors={validationErrors}
            onTitleChange={onTitleChange}
            onDescriptionChange={onDescriptionChange}
            requireSignUp={requireSignUp}
            setRequireSignUp={setRequireSignUp}
            isEvent={isEvent}
            setIsEvent={setIsEvent}
            expirationPeriod={expirationPeriod}
            setExpirationPeriod={setExpirationPeriod}
            usesLimitSelect={usesLimitSelect}
            setUsesLimitSelect={setUsesLimitSelect}
            usesLimit={usesLimit}
            onUsesLimitChange={onUsesLimitChange}
            eventTime={eventTime}
            eventDate={eventDate}
            handleDateChange={handleDateChange}
            handleTimeChange={handleTimeChange}
            handleTimeBlur={handleTimeBlur}
          />

          <Share
            handleCopy={handleCopy}
            handleShare={handleShare}
            handleExportPDF={handleExportPDF}
            isCopyPending={isCopyPending}
            showCopyToast={showCopyToast}
            isWhatsappSharePending={isWhatsappSharePending}
            isFacebookSharePending={isFacebookSharePending}
            isIMessageSharePending={isIMessageSharePending}
            isTelegramSharePending={isTelegramSharePending}
            isPdfExportPending={isPdfExportPending}
          />
        </div>

        <Flyer canvasRef={canvasRef} />
      </div>
    </>
  );
};
