import { Calendar } from '@/shadcn/components/ui/calendar';
import { Input } from '@/shadcn/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/shadcn/components/ui/popover';
import { Switch } from '@/shadcn/components/ui/switch';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { cn } from '@/shadcn/lib/utils';
import { useACL, useClassroomSettings } from '@/shared';
import { Calendar as CalendarIcon, Clock3, Info } from 'lucide-react';
import { ChangeEvent, Dispatch, FC, SetStateAction, useState } from 'react';
import { getMinTime } from '../../utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shadcn/components/ui/select';
import { useTranslation } from 'react-i18next';

interface IFormProps {
  title: string;
  description: string;
  selectedRole: 'teacher' | 'student' | 'guest';
  setSelectedRole: Dispatch<SetStateAction<'teacher' | 'student' | 'guest'>>;
  errors: Record<string, string | undefined>;
  onTitleChange: (evant: ChangeEvent<HTMLInputElement>) => void;
  onDescriptionChange: (evant: ChangeEvent<HTMLInputElement>) => void;
  requireSignUp: boolean;
  setRequireSignUp: Dispatch<SetStateAction<boolean>>;
  isEvent: boolean;
  setIsEvent: Dispatch<SetStateAction<boolean>>;
  expirationPeriod: string;
  setExpirationPeriod: Dispatch<SetStateAction<string>>;
  usesLimitSelect: string;
  setUsesLimitSelect: Dispatch<SetStateAction<string>>;
  usesLimit: number;
  onUsesLimitChange: (event: ChangeEvent<HTMLInputElement>) => void;
  eventTime: string;
  eventDate: Date | undefined;
  handleDateChange: (date: Date | undefined) => void;
  handleTimeChange: (time: string) => void;
  handleTimeBlur: () => void;
}

export const Form: FC<IFormProps> = ({
  selectedRole,
  setSelectedRole,
  errors,
  title,
  description,
  onTitleChange,
  onDescriptionChange,
  requireSignUp,
  setRequireSignUp,
  isEvent,
  setIsEvent,
  expirationPeriod,
  setExpirationPeriod,
  usesLimitSelect,
  setUsesLimitSelect,
  usesLimit,
  onUsesLimitChange,
  eventTime,
  eventDate,
  handleDateChange,
  handleTimeChange,
  handleTimeBlur,
}) => {
  const { t } = useTranslation();
  const [openTooltip, setOpenTooltip] = useState(false);
  const [openDatePicker, setOpenDatePicker] = useState(false);
  const acl = useACL();
  const classroomSettings = useClassroomSettings();

  return (
    <div className="max-[540px]:px-3 max-[540px]:pb-4 max-[540px]:pt-3 px-6 pt-6 pb-8 bg-[#FFFDFA] shadow-[0_0_12px_0_rgba(88,63,52,0.04)] rounded-lg flex flex-col gap-4">
      <div className="flex flex-col gap-3">
        <div className="flex items-center justify-between gap-4 flex-wrap">
          <h1 className="text-primary font-bold text-lg">
            {t('invite.form.role.label')}
          </h1>

          <div className="flex items-center justify-center gap-1 py-1 pl-2 pr-3 border-1 border-accent/50 rounded-sm">
            <Info className="text-accent size-[18px]" />

            <div className="relative">
              <TooltipProvider>
                <Tooltip open={openTooltip} onOpenChange={setOpenTooltip}>
                  <TooltipTrigger asChild>
                    <div
                      aria-hidden
                      className="absolute inset-0 pointer-events-none"
                    />
                  </TooltipTrigger>

                  <p className="text-xs">
                    {selectedRole === 'teacher'
                      ? t('invite.form.role.tooltip.teacher.label')
                      : selectedRole === 'student'
                        ? t('invite.form.role.tooltip.student.label')
                        : t('invite.form.role.tooltip.guest.label')}
                    <button
                      type="button"
                      className="text-accent underline cursor-pointer inline"
                      onClick={() => setOpenTooltip((v) => !v)}
                      data-testid="invite-form-role-more-button"
                    >
                      {t('invite.form.role.more')}
                    </button>
                  </p>

                  <TooltipContent
                    side="bottom"
                    align="center"
                    sideOffset={8}
                    className="max-w-[312px] bg-[#FFEDE3] text-primary
           [&>span>svg>polygon]:fill-[#FFEDE3]
           [&>span>svg]:bg-[#FFEDE3]"
                  >
                    {selectedRole === 'teacher'
                      ? t('invite.form.role.tooltip.teacher.content')
                      : selectedRole === 'student'
                        ? t('invite.form.role.tooltip.student.content')
                        : t('invite.form.role.tooltip.guest.content')}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>

        <div className="flex max-[460px]:p-1 p-2 gap-4 bg-[#FFEDE3] rounded-[16px]">
          {acl?.canParticipantInvite && (
            <>
              <div
                className={cn(
                  'py-5 px-3 flex items-center justify-center cursor-pointer rounded-lg transition-all hover:bg-[#FCD0B7] duration-300 group max-[460px]:py-3 w-full',
                  selectedRole === 'teacher' && 'bg-accent hover:bg-accent',
                )}
                onClick={() => {
                  if (acl && acl.canParticipantInvite)
                    setSelectedRole('teacher');
                }}
                data-testid="invite-form-role-teacher"
              >
                <h2
                  className={cn(
                    'max-[460px]:text-sm max-[460px]:font-semibold font-medium text-xl text-accent leading-3',
                    selectedRole === 'teacher' && 'font-bold text-white',
                  )}
                >
                  {t('invite.form.role.teacher')}
                </h2>
              </div>

              <div
                className={cn(
                  'py-5 px-3 flex items-center justify-center cursor-pointer rounded-lg transition-all hover:bg-[#FCD0B7] duration-300 group max-[460px]:py-3 w-full',
                  selectedRole === 'student' && 'bg-accent hover:bg-accent',
                )}
                onClick={() => {
                  if (acl && acl.canParticipantInvite)
                    setSelectedRole('student');
                }}
                data-testid="invite-form-role-student"
              >
                <h2
                  className={cn(
                    'max-[460px]:text-sm max-[460px]:font-semibold font-medium text-xl text-accent leading-3',
                    selectedRole === 'student' && 'font-bold text-white',
                  )}
                >
                  {t('invite.form.role.student')}
                </h2>
              </div>
            </>
          )}

          {classroomSettings?.allowInviteGuests && acl?.canGuestInvite && (
            <div
              className={cn(
                'py-5 px-3 flex items-center justify-center cursor-pointer rounded-lg transition-all hover:bg-[#FCD0B7] duration-300 group max-[460px]:py-3 w-full',
                selectedRole === 'guest' && 'bg-accent hover:bg-accent',
              )}
              onClick={() => {
                if (
                  classroomSettings &&
                  classroomSettings.allowInviteGuests &&
                  acl &&
                  acl.canGuestInvite
                )
                  setSelectedRole('guest');
              }}
              data-testid="invite-form-role-guest"
            >
              <h2
                className={cn(
                  'max-[460px]:text-sm max-[460px]:font-semibold font-medium text-xl text-accent leading-3',
                  selectedRole === 'guest' && 'font-bold text-white',
                )}
              >
                {t('invite.form.role.guest')}
              </h2>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-[2fr_3fr] max-[650px]:grid-cols-1 items-center gap-3">
        <div className="flex flex-col gap-1 w-full flex-1 self-stretch">
          <h5 className="text-primary font-medium text-xs">
            {t('invite.form.title.label')}
          </h5>
          <Input
            className="w-full p-3 border-1 border-primary/30"
            aria-invalid={!!errors.title}
            placeholder={t('invite.form.title.placeholder')}
            value={title}
            onChange={onTitleChange}
            data-testid="invite-form-title-input"
          />
        </div>

        <div className="flex flex-col gap-1 flex-1 self-stretch">
          <h5 className="text-primary font-medium text-xs">
            {t('invite.form.description.label')}
          </h5>
          <Input
            className="w-full p-3 border-1 border-primary/30"
            placeholder={t('invite.form.description.placeholder')}
            value={description}
            onChange={onDescriptionChange}
            aria-invalid={!!errors.description}
            data-testid="invite-form-description-input"
          />
        </div>
      </div>

      <div className="flex items-center gap-9 max-[700px]:gap-4 flex-wrap">
        <div className="flex items-center justify-center gap-2">
          <Switch
            id="access"
            checked={requireSignUp}
            onCheckedChange={setRequireSignUp}
            data-testid="invite-form-access-switch"
          />
          <label
            htmlFor="access"
            className="text-sm font-medium text-primary/70"
          >
            {t('invite.form.access')}
          </label>
        </div>

        <div className="flex items-center justify-center gap-2">
          <Switch
            id="event"
            checked={isEvent}
            onCheckedChange={setIsEvent}
            data-testid="invite-form-event-switch"
          />
          <label
            htmlFor="event"
            className="text-sm font-medium text-primary/70"
          >
            {t('invite.form.event')}
          </label>
        </div>
      </div>

      <div className="flex items-start flex-wrap gap-3">
        <div className="flex flex-col gap-1 self-stretch">
          <h5 className="text-primary font-medium text-xs">
            {t('invite.form.date')}
          </h5>
          <label className="relative w-fit" htmlFor="date">
            <CalendarIcon
              className={cn(
                'size-5 text-[#727272] absolute top-0 bottom-0 my-auto left-3',
                !isEvent && 'opacity-50',
              )}
            />

            <Popover
              open={!isEvent ? false : openDatePicker}
              onOpenChange={!isEvent ? undefined : setOpenDatePicker}
            >
              <PopoverTrigger asChild>
                <Input
                  className="!w-fit max-w-[132px] pl-10 pr-3 py-3 border border-primary/30 cursor-pointer"
                  id="date"
                  value={eventDate?.toLocaleDateString()}
                  disabled={!isEvent}
                  data-testid="invite-form-date-input"
                />
              </PopoverTrigger>
              <PopoverContent
                className="w-auto overflow-hidden p-0 z-5000 pointer-events-auto"
                align="start"
                data-testid="invite-form-date-picker"
              >
                <Calendar
                  mode="single"
                  disabled={(day) => {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    return day < today;
                  }}
                  selected={eventDate}
                  captionLayout="label"
                  onSelect={(selectedDate) => {
                    handleDateChange(selectedDate);
                    setOpenDatePicker(false);
                  }}
                  data-testid="invite-form-calendar"
                />
              </PopoverContent>
            </Popover>
          </label>
        </div>

        <div className="flex flex-col gap-1 self-stretch">
          <h5 className="text-primary font-medium text-xs">
            {t('invite.form.time')}
          </h5>
          <label className="relative w-fit" htmlFor="time">
            <Clock3
              className={cn(
                'size-5 text-[#727272] absolute top-0 bottom-0 my-auto left-3',
                !isEvent && 'opacity-50',
              )}
            />

            <Input
              type="time"
              id="time"
              value={eventTime}
              min={getMinTime(eventDate)}
              onChange={(e) => handleTimeChange(e.target.value)}
              onBlur={handleTimeBlur}
              disabled={!isEvent}
              className="w-full max-w-[150px] pl-10 pr-3 py-3 border border-primary/30 appearance-none [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-calendar-picker-indicator]:appearance-none"
              data-testid="invite-form-time-input"
            />
          </label>
        </div>

        <div className="flex flex-col gap-1 self-stretch">
          <h5 className="text-primary font-medium text-xs">
            {t('invite.form.expires.label')}
          </h5>
          <Select
            value={expirationPeriod}
            onValueChange={setExpirationPeriod}
            disabled={!isEvent}
          >
            <SelectTrigger
              className="w-[113px]"
              data-testid="invite-form-expiration-select"
            >
              <SelectValue />
            </SelectTrigger>
            <SelectContent data-testid="invite-form-expiration-options">
              <SelectItem
                value="never"
                data-testid="invite-form-expiration-never"
              >
                {t('invite.form.expires.options.never')}
              </SelectItem>
              <SelectItem value="1d" data-testid="invite-form-expiration-1d">
                {t('invite.form.expires.options.1d')}
              </SelectItem>
              <SelectItem value="3d" data-testid="invite-form-expiration-3d">
                {t('invite.form.expires.options.3d')}
              </SelectItem>
              <SelectItem value="7d" data-testid="invite-form-expiration-7d">
                {t('invite.form.expires.options.7d')}
              </SelectItem>
              <SelectItem value="1m" data-testid="invite-form-expiration-1m">
                {t('invite.form.expires.options.1m')}
              </SelectItem>
              <SelectItem value="1y" data-testid="invite-form-expiration-1y">
                {t('invite.form.expires.options.1y')}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-col gap-1 self-stretch">
          <h5 className="text-primary font-medium text-xs">
            {t('invite.form.userLimit.label')}
          </h5>

          <div className="flex">
            <Select
              value={usesLimitSelect}
              onValueChange={setUsesLimitSelect}
              disabled={!isEvent}
            >
              <SelectTrigger
                className="border-r-[0] rounded-r-[0]"
                data-testid="invite-form-uses-limit-select"
              >
                <SelectValue />
              </SelectTrigger>
              <SelectContent data-testid="invite-form-uses-limit-options">
                <SelectItem
                  value="unlimited"
                  data-testid="invite-form-uses-limit-unlimited"
                >
                  {t('invite.form.userLimit.options.unlimited')}
                </SelectItem>
                <SelectItem
                  value="limited"
                  data-testid="invite-form-uses-limit-limited"
                >
                  {t('invite.form.userLimit.options.custom')}
                </SelectItem>
              </SelectContent>
            </Select>

            <Input
              type="number"
              disabled={usesLimitSelect === 'unlimited' || !isEvent}
              className="border-primary/30 rounded-l-[0] w-fit max-w-[100px]"
              value={usesLimit}
              onChange={onUsesLimitChange}
              aria-invalid={!!errors.usesLimit}
              data-testid="invite-form-uses-limit-input"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
