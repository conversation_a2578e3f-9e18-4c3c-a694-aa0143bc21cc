import { FC, RefObject } from 'react';

interface IFlyerProps {
  canvasRef: RefObject<HTMLCanvasElement | null>;
}

export const Flyer: FC<IFlyerProps> = ({ canvasRef }) => {
  return (
    <div className="h-full min-h-0 overflow-hidden flex items-stretch max-[980px]:hidden">
      <div
        id="preview"
        className="h-full max-h-full w-auto aspect-[30.4/44.7] flex items-center justify-center"
      >
        <canvas
          ref={canvasRef}
          className="block max-h-full max-w-full h-auto w-auto rounded-xl"
        />
      </div>
    </div>
  );
};
