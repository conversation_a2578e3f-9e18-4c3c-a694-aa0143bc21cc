import { Input } from '@/shadcn/components/ui/input';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { AnimatePresence, motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, Copy, Loader2, SendHorizonal } from 'lucide-react';
import { FC } from 'react';
import {
  FacebookIcon,
  IMessageIcon,
  TelegramIcon,
  WhatsAppIcon,
} from '../icons';
import { Button } from '@/shadcn/components/ui/button';
import { useTranslation } from 'react-i18next';

interface IShareProps {
  handleCopy: () => Promise<void>;
  handleShare: (
    provider: 'whatsapp' | 'facebook' | 'iMessage' | 'telegram',
  ) => Promise<void>;
  handleExportPDF: () => Promise<void>;
  isCopyPending: boolean;
  showCopyToast: boolean;
  isWhatsappSharePending: boolean;
  isFacebookSharePending: boolean;
  isIMessageSharePending: boolean;
  isTelegramSharePending: boolean;
  isPdfExportPending: boolean;
}

export const Share: FC<IShareProps> = ({
  handleCopy,
  handleShare,
  handleExportPDF,
  isCopyPending,
  showCopyToast,
  isWhatsappSharePending,
  isFacebookSharePending,
  isIMessageSharePending,
  isTelegramSharePending,
  isPdfExportPending,
}) => {
  const { t } = useTranslation();

  return (
    <div className="max-[540px]:px-3 max-[540px]:pb-4 max-[540px]:pt-3 px-6 pt-6 pb-8 bg-[#FFFDFA] shadow-[0_0_12px_0_rgba(88,63,52,0.04)] rounded-lg flex flex-col gap-6">
      <div className="flex flex-col gap-2.5">
        <h1 className="text-primary font-bold text-lg">
          {t('invite.share.title')}
        </h1>

        <div className="grid grid-cols-[1fr_auto_1fr] max-[980px]:grid-cols-[1fr_auto_1fr] max-[800px]:grid-cols-1 max-[1250px]:grid-cols-1 items-center justify-center gap-3">
          <div className="flex flex-col gap-1">
            <h3 className="text-primary/80 text-xs leading-4 font-bold">
              {t('invite.share.copy.label')}
            </h3>
            <div className="w-full pl-3 py-1 pr-1 rounded-md border-1 border-primary/30 flex items-center justify-between relative gap-2 !min-h-[0] !h-[46px]">
              <h1 className="text-primary text-sm leading-5">
                https://{window.location.hostname}/...
              </h1>

              <button
                className="flex items-center justify-center gap-2 px-3 py-2 transition-opacity duration-300 cursor-pointer hover:opacity-80 rounded-sm bg-accent absolute top-1 bottom-1 right-1 my-auto"
                onClick={handleCopy}
                disabled={isCopyPending}
                data-testid="invite-share-copy-button"
              >
                {isCopyPending ? (
                  <Loader2 className="text-white size-[18px] animate-spin" />
                ) : (
                  <Copy className="text-white size-[18px]" />
                )}
                <span className="text-white text-sm leading-5 font-semibold">
                  {t('invite.share.copy.button')}
                </span>
              </button>

              <AnimatePresence>
                {showCopyToast && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="absolute w-full h-full rounded-md inset-0 bg-[#337039] flex items-center justify-center gap-[8px] pl-3 py-1 pr-1 border-1 border-[#337039] z-50"
                    data-testid="invite-share-copy-success-toast"
                  >
                    <CircleCheck className="text-white size-6" />
                    <span className="text-sm font-semibold text-white select-none">
                      {t('invite.share.copy.success')}
                    </span>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          <div className="grid grid-cols-[14px_auto_14px] max-[800px]:grid-cols-[14px_20px_14px] max-[800px]:my-2 max-[1250px]:grid-cols-[14px_20px_14px] max-[1250px]:my-2 items-center gap-1 max-[980px]:mt-5 mt-5 mx-auto">
            <div className="w-full bg-primary h-px" />
            <h4 className="leading-3 text-primary w-full text-center">
              {t('invite.share.or')}
            </h4>
            <div className="w-full bg-primary h-px" />
          </div>

          <div className="flex flex-col gap-1">
            <h3 className="text-primary/80 text-xs leading-4 font-bold">
              {t('invite.share.email.label')}
            </h3>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="relative">
                    <Input
                      className="border-primary/30 !pl-3 !py-1 !pr-1 !min-h-[0] !h-[46px]"
                      disabled
                      placeholder={t('invite.share.email.placeholder')}
                      data-testid="invite-share-email-input"
                    />

                    <button
                      className="flex items-center justify-center gap-2 px-3 py-2 transition-opacity duration-300 cursor-pointer hover:opacity-80 rounded-sm bg-accent absolute right-1 top-0 bottom-0 my-auto max-h-[38px] disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled
                      onClick={handleCopy}
                      data-testid="invite-share-email-send-button"
                    >
                      <SendHorizonal className="text-white size-[18px]" />
                      <span className="text-white text-sm leading-5 font-semibold">
                        {t('invite.share.email.send')}
                      </span>
                    </button>
                  </div>
                </TooltipTrigger>

                <TooltipContent>
                  <p>{t('invite.share.email.tooltip')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <div className="mt-0.5 flex flex-col gap-[9px] items-center justify-center">
          <h4 className="text-center text-primary/80 text-sm font-medium">
            {t('invite.share.social.label')}
          </h4>
          <div className="w-fit grid grid-cols-4 gap-3">
            <button
              className="bg-accent size-10 rounded-full flex items-center justify-center cursor-pointer transition-opacity duration-300 hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isWhatsappSharePending}
              onClick={() => handleShare('whatsapp')}
              data-testid="invite-share-whatsapp-button"
            >
              {isWhatsappSharePending ? (
                <Loader2 className="size-5 text-white animate-spin" />
              ) : (
                <WhatsAppIcon />
              )}
            </button>

            <button
              className="bg-accent size-10 rounded-full flex items-center justify-center cursor-pointer transition-opacity duration-300 hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isFacebookSharePending}
              onClick={() => handleShare('facebook')}
              data-testid="invite-share-facebook-button"
            >
              {isFacebookSharePending ? (
                <Loader2 className="size-5 text-white animate-spin" />
              ) : (
                <FacebookIcon />
              )}
            </button>

            <button
              className="bg-accent size-10 rounded-full flex items-center justify-center cursor-pointer transition-opacity duration-300 hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isIMessageSharePending}
              onClick={() => handleShare('iMessage')}
              data-testid="invite-share-imessage-button"
            >
              {isIMessageSharePending ? (
                <Loader2 className="size-5 text-white animate-spin" />
              ) : (
                <IMessageIcon />
              )}
            </button>

            <button
              className="bg-accent size-10 rounded-full flex items-center justify-center cursor-pointer transition-opacity duration-300 hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isTelegramSharePending}
              onClick={() => handleShare('telegram')}
              data-testid="invite-share-telegram-button"
            >
              {isTelegramSharePending ? (
                <Loader2 className="size-5 text-white animate-spin" />
              ) : (
                <TelegramIcon />
              )}
            </button>
          </div>
        </div>
      </div>

      <Button
        size={'lg'}
        type="button"
        variant="monochrome_outline"
        className="w-full mx-auto !bg-[#fff2e51a] !border-[#8F7E72] hover:!bg-[#fff2e580] active:!bg-[#FFF2E5] !h-[unset] py-2 px-4 !leading-6 !font-semibold text-base max-w-[270px]"
        disabled={isPdfExportPending}
        onClick={handleExportPDF}
        data-testid="invite-share-pdf-export-button"
      >
        {isPdfExportPending && (
          <Loader2 className="size-4 text-primary animate-spin" />
        )}
        {isPdfExportPending
          ? t('invite.share.pdf.pending')
          : t('invite.share.pdf.button')}
      </Button>
    </div>
  );
};
