import moment from 'moment';
import i18next from '@/app/i18n';

type Config = {
  date: number;
  title: string;
  description: string;
  joiningAs: 'student' | 'guest' | 'teacher';
  access: 'open' | 'close';
  validUntil: string | 'never';
  uses: 'unlimited' | number;
  isEvent: boolean;
};

type ImageAsset = {
  path: string;
  x: number;
  y: number;
  image?: HTMLImageElement;
};

export class FlyerRenderer {
  private static instance: FlyerRenderer | null = null;
  private static currentCanvas: HTMLCanvasElement | null = null;

  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private imageAssets: ImageAsset[];
  private imagesLoaded = false;
  private fontsLoaded = false;
  private imageLoadingPromise: Promise<void> | null = null;
  private fontLoadingPromise: Promise<void> | null = null;

  private readonly requiredFonts = ['Noto Serif', 'Noto Sans'];

  private constructor(canvas: HTMLCanvasElement, env: 'dev' | 'test' | 'prod') {
    this.canvas = canvas;
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Cannot get canvas context');
    this.ctx = ctx;

    this.imageAssets = [
      { path: '/assets/images/flyer/logo.svg', x: 30, y: 20 },
      { path: `/assets/images/flyer/${env}-qr.svg`, x: 381, y: 20 },
      { path: '/assets/images/flyer/calendar.svg', x: 30, y: 157 },
      { path: '/assets/images/flyer/keys.svg', x: 0, y: 519 },
      { path: '/assets/images/flyer/users.svg', x: 30, y: 431 },
      { path: '/assets/images/flyer/key.svg', x: 142, y: 431 },
      { path: '/assets/images/flyer/time.svg', x: 254, y: 431 },
      { path: '/assets/images/flyer/ticket.svg', x: 366, y: 431 },
    ];
  }

  static async getInstance(
    canvas: HTMLCanvasElement,
    env: 'dev' | 'test' | 'prod',
  ): Promise<FlyerRenderer> {
    if (!this.instance || this.currentCanvas !== canvas) {
      this.instance = new FlyerRenderer(canvas, env);
      this.currentCanvas = canvas;
    }

    await Promise.all([this.instance.loadImages(), this.instance.loadFonts()]);

    return this.instance;
  }

  static reset(): void {
    this.instance = null;
    this.currentCanvas = null;
  }

  private async loadFonts(): Promise<void> {
    if (this.fontsLoaded) return;

    if (this.fontLoadingPromise) {
      return this.fontLoadingPromise;
    }

    this.fontLoadingPromise = this.doLoadFonts();
    await this.fontLoadingPromise;
  }

  private async doLoadFonts(): Promise<void> {
    try {
      await document.fonts.ready;

      if ('fonts' in document) {
        const fontPromises = this.requiredFonts.map(async (fontFamily) => {
          try {
            const weights = ['400', '500', '654', '700'];
            const checkPromises = weights.map((weight) => {
              const latinPromise = document.fonts.load(
                `${weight} 16px ${fontFamily}`,
                'Test ABC abc',
              );
              const cyrillicPromise = document.fonts.load(
                `${weight} 16px ${fontFamily}`,
                'Тест АБВ абв',
              );
              return Promise.all([latinPromise, cyrillicPromise]);
            });
            await Promise.all(checkPromises);
          } catch (error) {
            console.warn(`Font loading check failed for ${fontFamily}:`, error);
          }
        });

        await Promise.all(fontPromises);
      }

      await this.testFontRendering();
      this.fontsLoaded = true;
    } catch (error) {
      console.warn('Font loading failed, proceeding with system fonts:', error);
      this.fontsLoaded = true;
    }
  }

  private async testFontRendering(): Promise<void> {
    return new Promise((resolve) => {
      const testCanvas = document.createElement('canvas');
      const testCtx = testCanvas.getContext('2d');
      if (!testCtx) {
        resolve();
        return;
      }

      const testFont = () => {
        testCtx.font = "16px 'Noto Serif'";
        const latinMetrics = testCtx.measureText('Test ABC');
        const cyrillicMetrics = testCtx.measureText('Тест АБВ');
        return latinMetrics.width > 0 && cyrillicMetrics.width > 0;
      };

      if (testFont()) {
        resolve();
        return;
      }

      let attempts = 0;
      const maxAttempts = 15;
      const checkInterval = setInterval(() => {
        attempts++;
        if (testFont() || attempts >= maxAttempts) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
    });
  }

  private async loadImages(): Promise<void> {
    if (this.imagesLoaded) return;

    if (this.imageLoadingPromise) {
      return this.imageLoadingPromise;
    }

    this.imageLoadingPromise = this.doLoadImages();
    await this.imageLoadingPromise;
  }

  private async doLoadImages(): Promise<void> {
    const loadPromises = this.imageAssets.map(async (asset) => {
      try {
        asset.image = await this.fetchImage(asset.path);
      } catch (err) {
        console.error(`Failed to load image: ${asset.path}`, err);
      }
    });

    await Promise.all(loadPromises);
    this.imagesLoaded = true;
  }

  async createFlyer(config: Config): Promise<void> {
    await Promise.all([this.loadImages(), this.loadFonts()]);
    this.renderComplete(config);
  }

  updateText(config: Config): void {
    if (!this.imagesLoaded || !this.fontsLoaded) {
      throw new Error('Images and fonts not loaded. Call createFlyer() first.');
    }
    this.renderComplete(config);
  }

  private renderComplete(config: Config): void {
    this.clearCanvas();
    this.drawImages(config);
    this.drawText(config);
  }

  private clearCanvas(): void {
    this.ctx.fillStyle = '#f2834f';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
  }

  private drawImages(config: Config): void {
    this.imageAssets.forEach((asset) => {
      if (asset.image) {
        if (
          !config.isEvent &&
          asset.y === 431 &&
          (asset.x === 254 || asset.x === 366)
        )
          return;
        this.ctx.drawImage(asset.image, asset.x, asset.y);
      }
    });
  }

  private drawText(config: Config): void {
    const formatted = moment(config.date)
      .locale(i18next.language)
      .format('MMMM DD | HH:mm');

    this.drawSimpleText({
      text: formatted.charAt(0).toUpperCase() + formatted.slice(1),
      x: 55,
      y: 160,
      font: "700 16px 'Noto Serif'",
    });

    const titleHeight = drawWrappedText(
      this.ctx,
      config.title,
      30,
      202,
      391,
      42,
      "654 38px 'Noto Serif'",
    );

    drawWrappedText(
      this.ctx,
      config.description,
      30,
      217 + titleHeight,
      391,
      20,
      "500 14px 'Noto Sans'",
    );

    if (config.isEvent) {
      const labelBlocks = [
        { text: i18next.t('invite.flyer.joiningAs.title'), x: 30 },
        { text: i18next.t('invite.flyer.access.title'), x: 142 },
        { text: i18next.t('invite.flyer.validUntil.title'), x: 254 },
        { text: i18next.t('invite.flyer.users.title'), x: 366 },
      ];
      labelBlocks.forEach(({ text, x }) =>
        this.drawSimpleText({ text, x, y: 453, font: "400 12px 'Noto Sans'" }),
      );

      const valueBlocks = [
        {
          text:
            config.joiningAs === 'teacher'
              ? i18next.t('invite.flyer.joiningAs.teacher')
              : config.joiningAs === 'student'
                ? i18next.t('invite.flyer.joiningAs.student')
                : i18next.t('invite.flyer.joiningAs.guest'),
          x: 30,
        },
        {
          text:
            config.access === 'open'
              ? i18next.t('invite.flyer.access.open')
              : i18next.t('invite.flyer.access.signup'),
          x: 142,
        },
        {
          text:
            config.validUntil === 'never'
              ? i18next.t('invite.flyer.validUntil.never')
              : config.validUntil,
          x: 254,
        },
        {
          text:
            config.uses === 'unlimited'
              ? i18next.t('invite.flyer.users.unlimited')
              : config.uses + '',
          x: 366,
        },
      ];
      valueBlocks.forEach(({ text, x }) =>
        this.drawSimpleText({ text, x, y: 469, font: "700 12px 'Noto Sans'" }),
      );
    } else {
      const labelBlocks = [
        { text: i18next.t('invite.flyer.joiningAs.title'), x: 30 },
        { text: i18next.t('invite.flyer.access.title'), x: 142 },
      ];
      labelBlocks.forEach(({ text, x }) =>
        this.drawSimpleText({ text, x, y: 453, font: "400 12px 'Noto Sans'" }),
      );

      const valueBlocks = [
        {
          text:
            config.joiningAs === 'teacher'
              ? i18next.t('invite.flyer.joiningAs.teacher')
              : config.joiningAs === 'student'
                ? i18next.t('invite.flyer.joiningAs.student')
                : i18next.t('invite.flyer.joiningAs.guest'),
          x: 30,
        },
        {
          text:
            config.access === 'open'
              ? i18next.t('invite.flyer.access.open')
              : i18next.t('invite.flyer.access.signup'),
          x: 142,
        },
      ];
      valueBlocks.forEach(({ text, x }) =>
        this.drawSimpleText({ text, x, y: 469, font: "700 12px 'Noto Sans'" }),
      );
    }
  }

  private drawSimpleText({
    text,
    x,
    y,
    font,
    color = '#FFF',
    textAlign = 'left' as CanvasTextAlign,
    textBaseline = 'top' as CanvasTextBaseline,
  }: {
    text: string;
    x: number;
    y: number;
    font: string;
    color?: string;
    textAlign?: CanvasTextAlign;
    textBaseline?: CanvasTextBaseline;
  }): void {
    this.ctx.fillStyle = color;
    this.ctx.font = font;
    this.ctx.textAlign = textAlign;
    this.ctx.textBaseline = textBaseline;
    this.ctx.fillText(text, x, y);
  }

  private async fetchImage(path: string): Promise<HTMLImageElement> {
    const res = await fetch(path);
    const contentType = res.headers.get('content-type') || '';

    let url: string;
    if (contentType.includes('svg') || path.endsWith('.svg')) {
      const svgText = await res.text();
      const blob = new Blob([svgText], { type: 'image/svg+xml' });
      url = URL.createObjectURL(blob);
    } else {
      const blob = await res.blob();
      url = URL.createObjectURL(blob);
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve(img);
      };
      img.onerror = (e) => {
        URL.revokeObjectURL(url);
        reject(e);
      };
      img.src = url;
    });
  }

  get isReady(): boolean {
    return this.imagesLoaded && this.fontsLoaded;
  }
}

function drawWrappedText(
  ctx: CanvasRenderingContext2D,
  text: string,
  x: number,
  y: number,
  maxWidth: number,
  lineHeight: number,
  font: string,
): number {
  ctx.fillStyle = '#FFF';
  ctx.font = font;
  ctx.textAlign = 'left';
  ctx.textBaseline = 'top';

  let consumed = 0;
  const paragraphs = (text ?? '').split('\n');

  for (const para of paragraphs) {
    const words = para.split(' ');
    let line = '';

    for (let i = 0; i < words.length; i++) {
      let currentWord = words[i];

      const separator = line ? ' ' : '';
      const testLine = line + separator + currentWord;
      const testWidth = ctx.measureText(testLine).width;

      if (testWidth <= maxWidth) {
        line = testLine;
      } else {
        if (line) {
          ctx.fillText(line, x, y);
          y += lineHeight;
          consumed += lineHeight;
          line = '';
        }

        while (currentWord.length > 0) {
          const maxFitLength = findMaxFitLength(ctx, currentWord, maxWidth);

          const takeLength = Math.max(1, maxFitLength);
          const fitPart = currentWord.substring(0, takeLength);

          ctx.fillText(fitPart, x, y);
          y += lineHeight;
          consumed += lineHeight;

          currentWord = currentWord.substring(takeLength);
        }
      }
    }

    if (line) {
      ctx.fillText(line, x, y);
      y += lineHeight;
      consumed += lineHeight;
    }
  }

  return consumed;
}

function findMaxFitLength(
  ctx: CanvasRenderingContext2D,
  text: string,
  maxWidth: number,
): number {
  if (text.length === 0) return 0;

  if (ctx.measureText(text).width <= maxWidth) {
    return text.length;
  }

  let left = 0;
  let right = text.length;
  let maxFit = 0;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    const testText = text.substring(0, mid);
    const width = ctx.measureText(testText).width;

    if (width <= maxWidth) {
      maxFit = mid;
      left = mid + 1;
    } else {
      right = mid - 1;
    }
  }

  return maxFit;
}
