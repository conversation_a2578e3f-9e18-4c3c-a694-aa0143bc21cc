import { z } from 'zod';

export const InviteTitleSchema = z.string().min(3).max(30);
export const InviteDescriptionSchema = z.string().min(3).max(200);

export const InviteUsesSchema = z
  .string()
  .trim()
  .regex(/^[1-9]\d{0,4}$/, {
    message: 'Enter a number between 1 and 99999 without leading zeros',
  })
  .transform(Number)
  .refine((n) => n >= 1 && n <= 99999, {
    message: 'Value must be between 1 and 99999',
  });
