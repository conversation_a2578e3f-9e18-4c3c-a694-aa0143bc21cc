import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { createInvitation } from './createInvitation';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { post: vi.fn() },
}));

describe('Create Invitation API', () => {
  let mockPost: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockPost = api.post as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' && error !== null && 'response' in error,
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  describe('createInvitation', () => {
    const mockToken = 'invite-token-abc123xyz789';

    it('should create invitation successfully and return token', async () => {
      const mockResponse = {
        data: {
          data: {
            token: mockToken,
          },
        },
      };

      mockPost.mockResolvedValueOnce(mockResponse);

      const result = await createInvitation(
        'classroom123',
        'student',
        true,
        '2024-01-15T14:30:00Z',
        5,
        7,
        false,
      );

      expect(mockPost).toHaveBeenCalledWith(
        '/classrooms/classroom123/invitations',
        {
          inviteRole: 'student',
          requireSignup: true,
          eventDate: '2024-01-15T14:30:00Z',
          usesLimit: 5,
          isUnlimited: false,
          expirationPeriod: 7,
        },
      );
      expect(result).toBe(mockToken);
    });

    it('should work with different classroom IDs', async () => {
      const testCases = [
        'abc-123',
        'classroom-xyz-789',
        '12345',
        'special-chars_classroom',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      ];

      for (const classroomId of testCases) {
        const mockResponse = {
          data: { data: { token: `token-${classroomId}` } },
        };
        mockPost.mockResolvedValueOnce(mockResponse);

        const result = await createInvitation(
          classroomId,
          'teacher',
          false,
          '2024-01-15T10:00:00Z',
          undefined,
          undefined,
          true,
        );

        expect(mockPost).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/invitations`,
          expect.any(Object),
        );
        expect(result).toBe(`token-${classroomId}`);
      }
    });

    describe('invite roles', () => {
      it('should handle student role', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'student',
          true,
          '2024-01-15T14:30:00Z',
          10,
          7,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          {
            inviteRole: 'student',
            requireSignup: true,
            eventDate: '2024-01-15T14:30:00Z',
            usesLimit: 10,
            isUnlimited: false,
            expirationPeriod: 7,
          },
        );
      });

      it('should handle teacher role', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'teacher',
          false,
          '2024-01-20T09:00:00Z',
          3,
          14,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          {
            inviteRole: 'teacher',
            requireSignup: false,
            eventDate: '2024-01-20T09:00:00Z',
            usesLimit: 3,
            isUnlimited: false,
            expirationPeriod: 14,
          },
        );
      });

      it('should handle guest role', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'guest',
          true,
          '2024-01-25T16:00:00Z',
          1,
          1,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          {
            inviteRole: 'guest',
            requireSignup: true,
            eventDate: '2024-01-25T16:00:00Z',
            usesLimit: 1,
            isUnlimited: false,
            expirationPeriod: 1,
          },
        );
      });
    });

    describe('requireSignup parameter', () => {
      it('should handle requireSignup: true', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'student',
          true,
          '2024-01-15T14:30:00Z',
          5,
          7,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          expect.objectContaining({ requireSignup: true }),
        );
      });

      it('should handle requireSignup: false', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'student',
          false,
          '2024-01-15T14:30:00Z',
          5,
          7,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          expect.objectContaining({ requireSignup: false }),
        );
      });
    });

    describe('eventDate formats', () => {
      it('should handle ISO datetime strings', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        const eventDate = '2024-12-25T18:30:00Z';
        await createInvitation(
          'classroom123',
          'student',
          true,
          eventDate,
          5,
          7,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          expect.objectContaining({ eventDate }),
        );
      });

      it('should handle different timezone formats', async () => {
        const eventDates = [
          '2024-01-15T14:30:00Z',
          '2024-01-15T14:30:00+02:00',
          '2024-01-15T14:30:00-05:00',
          '2024-01-15T14:30:00.000Z',
        ];

        for (const eventDate of eventDates) {
          const mockResponse = {
            data: { data: { token: `token-${Date.now()}` } },
          };
          mockPost.mockResolvedValueOnce(mockResponse);

          await createInvitation(
            'classroom123',
            'student',
            true,
            eventDate,
            5,
            7,
            false,
          );

          expect(mockPost).toHaveBeenCalledWith(
            '/classrooms/classroom123/invitations',
            expect.objectContaining({ eventDate }),
          );
        }
      });
    });

    describe('usesLimit parameter', () => {
      it('should handle numeric usesLimit', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'student',
          true,
          '2024-01-15T14:30:00Z',
          10,
          7,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          expect.objectContaining({ usesLimit: 10 }),
        );
      });

      it('should handle undefined usesLimit', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'student',
          true,
          '2024-01-15T14:30:00Z',
          undefined,
          7,
          true,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          expect.objectContaining({ usesLimit: undefined }),
        );
      });

      it('should handle different usesLimit values', async () => {
        const usesLimits = [1, 5, 10, 25, 50, 100, 1000];

        for (const usesLimit of usesLimits) {
          const mockResponse = {
            data: { data: { token: `token-${usesLimit}` } },
          };
          mockPost.mockResolvedValueOnce(mockResponse);

          await createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            usesLimit,
            7,
            false,
          );

          expect(mockPost).toHaveBeenCalledWith(
            '/classrooms/classroom123/invitations',
            expect.objectContaining({ usesLimit }),
          );
        }
      });
    });

    describe('expirationPeriod parameter', () => {
      it('should handle numeric expirationPeriod', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'student',
          true,
          '2024-01-15T14:30:00Z',
          5,
          14,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          expect.objectContaining({ expirationPeriod: 14 }),
        );
      });

      it('should handle undefined expirationPeriod', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'student',
          true,
          '2024-01-15T14:30:00Z',
          5,
          undefined,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          expect.objectContaining({ expirationPeriod: undefined }),
        );
      });

      it('should handle different expirationPeriod values', async () => {
        const expirationPeriods = [1, 3, 7, 14, 30, 90, 365];

        for (const expirationPeriod of expirationPeriods) {
          const mockResponse = {
            data: { data: { token: `token-${expirationPeriod}` } },
          };
          mockPost.mockResolvedValueOnce(mockResponse);

          await createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            5,
            expirationPeriod,
            false,
          );

          expect(mockPost).toHaveBeenCalledWith(
            '/classrooms/classroom123/invitations',
            expect.objectContaining({ expirationPeriod }),
          );
        }
      });
    });

    describe('isUnlimited parameter', () => {
      it('should handle isUnlimited: true', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'student',
          true,
          '2024-01-15T14:30:00Z',
          undefined,
          7,
          true,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          expect.objectContaining({ isUnlimited: true }),
        );
      });

      it('should handle isUnlimited: false', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'student',
          true,
          '2024-01-15T14:30:00Z',
          5,
          7,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          expect.objectContaining({ isUnlimited: false }),
        );
      });
    });

    describe('request body structure', () => {
      it('should send all parameters in correct order', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'teacher',
          false,
          '2024-01-15T14:30:00Z',
          10,
          14,
          true,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          {
            inviteRole: 'teacher',
            requireSignup: false,
            eventDate: '2024-01-15T14:30:00Z',
            usesLimit: 10,
            isUnlimited: true,
            expirationPeriod: 14,
          },
        );
      });

      it('should include undefined values in request body', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'guest',
          true,
          '2024-01-15T14:30:00Z',
          undefined,
          undefined,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          {
            inviteRole: 'guest',
            requireSignup: true,
            eventDate: '2024-01-15T14:30:00Z',
            usesLimit: undefined,
            isUnlimited: false,
            expirationPeriod: undefined,
          },
        );
      });
    });

    describe('response handling', () => {
      it('should extract token from nested response structure', async () => {
        const expectedToken = 'very-long-secure-token-abc123def456ghi789';
        const mockResponse = {
          data: {
            data: {
              token: expectedToken,
              id: 'invitation-123',
              createdAt: '2024-01-15T14:30:00Z',
            },
          },
        };

        mockPost.mockResolvedValueOnce(mockResponse);

        const result = await createInvitation(
          'classroom123',
          'student',
          true,
          '2024-01-15T14:30:00Z',
          5,
          7,
          false,
        );

        expect(result).toBe(expectedToken);
      });

      it('should handle different token formats', async () => {
        const tokenFormats = [
          'simple-token',
          'TOKEN_WITH_UNDERSCORES',
          'token-with-dashes-123',
          'veryLongTokenWithMixedCaseAndNumbers123ABC',
          'jwt.like.token.format',
          'base64EncodedTokenExample==',
        ];

        for (const token of tokenFormats) {
          const mockResponse = { data: { data: { token } } };
          mockPost.mockResolvedValueOnce(mockResponse);

          const result = await createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            5,
            7,
            false,
          );

          expect(result).toBe(token);
        }
      });
    });

    describe('error handling', () => {
      it('should throw provided error message for classroom not found', async () => {
        const errorResponse = {
          response: { data: { error: 'Classroom not found' } },
        };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          createInvitation(
            'nonexistent-classroom',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            5,
            7,
            false,
          ),
        ).rejects.toThrow('Classroom not found');
      });

      it('should throw provided error message for insufficient permissions', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Insufficient permissions to create invitations' },
          },
        };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          createInvitation(
            'classroom123',
            'teacher',
            true,
            '2024-01-15T14:30:00Z',
            5,
            7,
            false,
          ),
        ).rejects.toThrow('Insufficient permissions to create invitations');
      });

      it('should throw provided error message for invalid event date', async () => {
        const errorResponse = {
          response: { data: { error: 'Event date must be in the future' } },
        };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          createInvitation(
            'classroom123',
            'student',
            true,
            '2020-01-15T14:30:00Z',
            5,
            7,
            false,
          ),
        ).rejects.toThrow('Event date must be in the future');
      });

      it('should throw provided error message for invalid uses limit', async () => {
        const errorResponse = {
          response: { data: { error: 'Uses limit must be greater than 0' } },
        };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            0,
            7,
            false,
          ),
        ).rejects.toThrow('Uses limit must be greater than 0');
      });

      it('should throw provided error message for invalid expiration period', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Expiration period must be between 1 and 365 days' },
          },
        };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            5,
            400,
            false,
          ),
        ).rejects.toThrow('Expiration period must be between 1 and 365 days');
      });

      it('should throw provided error message for conflicting unlimited settings', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Cannot set uses limit when unlimited is enabled' },
          },
        };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            10,
            7,
            true,
          ),
        ).rejects.toThrow('Cannot set uses limit when unlimited is enabled');
      });

      it('should throw default message when API error has no error field', async () => {
        const errorResponse = { response: { data: {} } };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            5,
            7,
            false,
          ),
        ).rejects.toThrow('Failed to create an invitation!');
      });

      it('should throw default message when API error has null error', async () => {
        const errorResponse = { response: { data: { error: null } } };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            5,
            7,
            false,
          ),
        ).rejects.toThrow('Failed to create an invitation!');
      });

      it('should throw default message when API error has undefined error', async () => {
        const errorResponse = { response: { data: { error: undefined } } };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            5,
            7,
            false,
          ),
        ).rejects.toThrow('Failed to create an invitation!');
      });

      it('should throw default message when API error has no response', async () => {
        const errorResponse = { response: undefined };
        mockPost.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            5,
            7,
            false,
          ),
        ).rejects.toThrow('Failed to create an invitation!');
      });

      it('should throw default message when error is not axios error', async () => {
        isAxiosErrorSpy.mockReturnValueOnce(false);
        mockPost.mockRejectedValueOnce(new Error('Network failure'));

        await expect(
          createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            5,
            7,
            false,
          ),
        ).rejects.toThrow('Failed to create an invitation!');
      });

      it('should handle generic error correctly', async () => {
        mockPost.mockRejectedValueOnce(new Error('Some generic error'));

        await expect(
          createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            5,
            7,
            false,
          ),
        ).rejects.toThrow('Failed to create an invitation!');
      });

      it('should handle timeout error', async () => {
        const timeoutError = {
          response: {
            data: { error: 'Request timeout while creating invitation' },
          },
        };
        mockPost.mockRejectedValueOnce(timeoutError as AxiosError);

        await expect(
          createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            5,
            7,
            false,
          ),
        ).rejects.toThrow('Request timeout while creating invitation');
      });

      it('should handle server error', async () => {
        const serverError = {
          response: {
            data: { error: 'Internal server error during invitation creation' },
          },
        };
        mockPost.mockRejectedValueOnce(serverError as AxiosError);

        await expect(
          createInvitation(
            'classroom123',
            'student',
            true,
            '2024-01-15T14:30:00Z',
            5,
            7,
            false,
          ),
        ).rejects.toThrow('Internal server error during invitation creation');
      });
    });

    describe('edge cases', () => {
      it('should handle very long event dates', async () => {
        const longEventDate = '2024-12-31T23:59:59.999999999+14:00';
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'student',
          true,
          longEventDate,
          1,
          1,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          expect.objectContaining({ eventDate: longEventDate }),
        );
      });

      it('should handle zero-length expiration and uses limit for unlimited', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'student',
          false,
          '2024-01-15T14:30:00Z',
          undefined,
          undefined,
          true,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          {
            inviteRole: 'student',
            requireSignup: false,
            eventDate: '2024-01-15T14:30:00Z',
            usesLimit: undefined,
            isUnlimited: true,
            expirationPeriod: undefined,
          },
        );
      });

      it('should handle maximum realistic values', async () => {
        const mockResponse = { data: { data: { token: mockToken } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        await createInvitation(
          'classroom123',
          'teacher',
          true,
          '2024-12-31T23:59:59Z',
          9999,
          365,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          {
            inviteRole: 'teacher',
            requireSignup: true,
            eventDate: '2024-12-31T23:59:59Z',
            usesLimit: 9999,
            isUnlimited: false,
            expirationPeriod: 365,
          },
        );
      });

      it('should handle multiple invitations in sequence', async () => {
        const invitations = [
          { role: 'student' as const, token: 'token-1' },
          { role: 'teacher' as const, token: 'token-2' },
          { role: 'guest' as const, token: 'token-3' },
        ];

        for (const invitation of invitations) {
          const mockResponse = { data: { data: { token: invitation.token } } };
          mockPost.mockResolvedValueOnce(mockResponse);
        }

        const results = [];
        for (const invitation of invitations) {
          const result = await createInvitation(
            'classroom123',
            invitation.role,
            true,
            '2024-01-15T14:30:00Z',
            5,
            7,
            false,
          );
          results.push(result);
        }

        expect(results).toEqual(['token-1', 'token-2', 'token-3']);
        expect(mockPost).toHaveBeenCalledTimes(3);
      });
    });

    describe('business logic scenarios', () => {
      it('should handle unlimited invitation correctly', async () => {
        const mockResponse = { data: { data: { token: 'unlimited-token' } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        const result = await createInvitation(
          'classroom123',
          'student',
          false,
          '2024-01-15T14:30:00Z',
          undefined,
          30,
          true,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          {
            inviteRole: 'student',
            requireSignup: false,
            eventDate: '2024-01-15T14:30:00Z',
            usesLimit: undefined,
            isUnlimited: true,
            expirationPeriod: 30,
          },
        );
        expect(result).toBe('unlimited-token');
      });

      it('should handle limited invitation with specific constraints', async () => {
        const mockResponse = { data: { data: { token: 'limited-token' } } };
        mockPost.mockResolvedValueOnce(mockResponse);

        const result = await createInvitation(
          'classroom123',
          'guest',
          true,
          '2024-01-15T14:30:00Z',
          1,
          1,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          {
            inviteRole: 'guest',
            requireSignup: true,
            eventDate: '2024-01-15T14:30:00Z',
            usesLimit: 1,
            isUnlimited: false,
            expirationPeriod: 1,
          },
        );
        expect(result).toBe('limited-token');
      });

      it('should handle teacher invitation with no signup required', async () => {
        const mockResponse = {
          data: { data: { token: 'teacher-no-signup-token' } },
        };
        mockPost.mockResolvedValueOnce(mockResponse);

        const result = await createInvitation(
          'classroom123',
          'teacher',
          false,
          '2024-01-15T14:30:00Z',
          3,
          14,
          false,
        );

        expect(mockPost).toHaveBeenCalledWith(
          '/classrooms/classroom123/invitations',
          {
            inviteRole: 'teacher',
            requireSignup: false,
            eventDate: '2024-01-15T14:30:00Z',
            usesLimit: 3,
            isUnlimited: false,
            expirationPeriod: 14,
          },
        );
        expect(result).toBe('teacher-no-signup-token');
      });
    });
  });
});
