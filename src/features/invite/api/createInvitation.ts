import { api } from '@/shared';
import axios from 'axios';

export const createInvitation = async (
  id: string,
  inviteRole: 'student' | 'teacher' | 'guest',
  requireSignup: boolean,
  eventDate: string,
  usesLimit: number | undefined,
  expirationPeriod: number | undefined,
  isUnlimited: boolean,
): Promise<string> => {
  try {
    const response = await api.post(`/classrooms/${id}/invitations`, {
      inviteRole,
      requireSignup,
      eventDate,
      usesLimit,
      isUnlimited,
      expirationPeriod,
    });
    return response.data.data.token;
  } catch (error) {
    let errorMessage = 'Failed to create an invitation!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
