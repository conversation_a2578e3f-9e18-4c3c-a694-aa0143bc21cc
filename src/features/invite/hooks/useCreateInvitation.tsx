import { useMutation } from '@tanstack/react-query';
import { createInvitation } from '../api';
import { useNavigate } from 'react-router';
import { useNotifications } from '@/shared';

export const useCreateInvitation = () => {
  const navigate = useNavigate();
  const { addNotification } = useNotifications();

  return useMutation({
    mutationFn: (invite: {
      id: string;
      inviteRole: 'teacher' | 'student' | 'guest';
      requireSignUp: boolean;
      eventDate: string;
      isUnlimited: boolean;
      usesLimit?: number;
      expirationPeriod?: number;
    }) =>
      createInvitation(
        invite.id,
        invite.inviteRole,
        invite.requireSignUp,
        invite.eventDate,
        invite.usesLimit,
        invite.expirationPeriod,
        invite.isUnlimited,
      ),
    onError: (error) => {
      if (error.message === 'classroom not found') {
        navigate('/classrooms');
        addNotification?.({
          title: 'notifications.errors.classroomNotFound.title',
          description: 'notifications.errors.classroomNotFound.description',
        });
      }
      addNotification?.({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
