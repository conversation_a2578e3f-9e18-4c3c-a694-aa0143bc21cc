import QRCode from 'qrcode';
import jsPDF from 'jspdf';

const addQRToCanvas = async (
  canvas: HTMLCanvasElement,
  url: string,
  x: number = 381,
  y: number = 20,
  size: number = 100,
): Promise<void> => {
  const ctx = canvas.getContext('2d');
  if (!ctx) throw new Error('Cannot get canvas context');

  const qrDataUrl = await QRCode.toDataURL(url, {
    width: size,
    margin: 1,
    color: { dark: '#000000', light: '#FFFFFF' },
  });

  const img = new Image();

  return new Promise((resolve, reject) => {
    img.onload = () => {
      ctx.drawImage(img, x, y, size, size);
      resolve();
    };
    img.onerror = reject;
    img.src = qrDataUrl;
  });
};

export const downloadFlyerWithQR = async (
  originalCanvas: HTMLCanvasElement,
  url: string,
  filename: string = 'Invitation.pdf',
  qrSize: number = 100,
): Promise<void> => {
  const tempCanvas = document.createElement('canvas');
  tempCanvas.width = originalCanvas.width;
  tempCanvas.height = originalCanvas.height;
  tempCanvas.style.position = 'absolute';
  tempCanvas.style.left = '-9999px';
  tempCanvas.style.top = '-9999px';
  document.body.appendChild(tempCanvas);

  const tempCtx = tempCanvas.getContext('2d');
  if (!tempCtx) throw new Error('Cannot get temp canvas context');

  tempCtx.drawImage(originalCanvas, 0, 0);

  await addQRToCanvas(tempCanvas, url, 381, 20, qrSize);

  const imgData = tempCanvas.toDataURL('image/png');
  const pdf = new jsPDF({
    orientation:
      tempCanvas.width > tempCanvas.height ? 'landscape' : 'portrait',
  });

  const imgProps = pdf.getImageProperties(imgData);
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  const margin = 10;
  const availableWidth = pageWidth - margin * 2;
  const availableHeight = pageHeight - margin * 2;
  const ratio = Math.min(
    availableWidth / imgProps.width,
    availableHeight / imgProps.height,
  );
  const finalWidth = imgProps.width * ratio;
  const finalHeight = imgProps.height * ratio;
  const xOffset = (pageWidth - finalWidth) / 2;
  const yOffset = (pageHeight - finalHeight) / 2;

  pdf.setFillColor('#ffffff');

  pdf.rect(0, 0, pageWidth, pageHeight, 'F');

  pdf.addImage(imgData, 'PNG', xOffset, yOffset, finalWidth, finalHeight);
  pdf.save(filename);

  document.body.removeChild(tempCanvas);
};
