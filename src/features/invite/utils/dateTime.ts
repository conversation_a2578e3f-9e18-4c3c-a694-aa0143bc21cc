/**
 * Utility functions for date and time handling in forms
 */

/**
 * Gets the current time formatted as HH:MM (without seconds)
 * @returns Current time string in HH:MM format
 */
export const getCurrentTime = (): string => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
};

/**
 * Checks if a given time is in the past for the selected date
 * @param timeString - Time in HH:MM or HH:MM:SS format
 * @param selectedDate - The selected date to check against
 * @returns True if the time is in the past for the given date
 */
export const isTimeInPast = (
  timeString: string,
  selectedDate: Date | undefined,
): boolean => {
  if (!selectedDate || !timeString) return false;

  const today = new Date();
  const isToday = selectedDate.toDateString() === today.toDateString();

  if (!isToday) return false;

  // Handle incomplete time inputs (like "11:2" while typing "11:20")
  const timeParts = timeString.split(':');
  if (timeParts.length !== 2) return false;

  const [hoursStr, minutesStr] = timeParts;
  const hours = parseInt(hoursStr, 10);
  const minutes = parseInt(minutesStr, 10);

  // If minutes part is incomplete (single digit), don't validate yet
  if (minutesStr.length === 1 && minutes < 6) {
    // Could be typing "20", "30", etc. - don't block yet
    return false;
  }

  const currentHours = today.getHours();
  const currentMinutes = today.getMinutes();

  // Compare time only (hours and minutes)
  if (hours < currentHours) return true;
  if (hours > currentHours) return false;
  // If same hour, compare minutes
  return minutes < currentMinutes;
};

/**
 * Gets the minimum allowed time for a given date
 * Returns current time for today, "00:00" for future dates
 * @param date - The date to get minimum time for
 * @returns Minimum time string in HH:MM format
 */
export const getMinTime = (date: Date | undefined): string => {
  if (!date) return '00:00';

  const today = new Date();
  const isToday = date.toDateString() === today.toDateString();

  if (isToday) {
    return getCurrentTime();
  }

  return '00:00';
};

/**
 * Validates and corrects a time selection to ensure it's not in the past
 * @param timeString - The time to validate in HH:MM format
 * @param selectedDate - The selected date
 * @returns Valid time string (corrected to current time if original was in the past)
 */
export const validateTime = (
  timeString: string,
  selectedDate: Date | undefined,
): string => {
  if (isTimeInPast(timeString, selectedDate)) {
    return getCurrentTime();
  }
  return timeString;
};

/**
 * Checks if a date is today
 * @param date - Date to check
 * @returns True if the date is today
 */
export const isToday = (date: Date | undefined): boolean => {
  if (!date) return false;
  const today = new Date();
  return date.toDateString() === today.toDateString();
};

/**
 * Formats a time string to ensure it's in HH:MM format (removes seconds if present)
 * @param timeString - Time string that might include seconds
 * @returns Time string in HH:MM format
 */
export const formatTimeToHHMM = (timeString: string): string => {
  if (!timeString) return '';
  const [hours, minutes] = timeString.split(':');
  return `${hours}:${minutes}`;
};
