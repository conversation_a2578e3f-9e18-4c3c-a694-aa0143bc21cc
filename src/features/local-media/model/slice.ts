import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { InstrumentType, RoomAcoustics } from '../lib';

interface LocalMediaState {
  deviceUpdating: boolean;
  // Array of "deviceId_groupId"
  audioDevices: string[];
  audioOutputDevice: string | null;
  videoDevice: string | null;
  instrumentType: InstrumentType;
  roomAcoustics: RoomAcoustics;
}

const initialState: LocalMediaState = {
  deviceUpdating: false,
  audioDevices: [],
  audioOutputDevice: null,
  videoDevice: null,
  instrumentType: InstrumentType.General,
  roomAcoustics: RoomAcoustics.Normal,
};

const localMediaSlice = createSlice({
  name: 'local-media',
  initialState,
  reducers: {
    setLocalMediaAudioDevices: (state, action: PayloadAction<string[]>) => {
      state.audioDevices = action.payload;
    },
    addLocalMediaAudioDevices: (state, action: PayloadAction<string>) => {
      if (!state.audioDevices.includes(action.payload)) {
        state.audioDevices.push(action.payload);
      }
    },
    setLocalMediaDeviceUpdating: (state, action: PayloadAction<boolean>) => {
      state.deviceUpdating = action.payload;
    },
    removeLocalMediaAudioDevices: (state, action: PayloadAction<string>) => {
      state.audioDevices = state.audioDevices.filter(
        (device) => device !== action.payload,
      );
    },
    setLocalMediaVideoDevice: (state, action: PayloadAction<string>) => {
      state.videoDevice = action.payload;
    },
    setLocalMediaAudioOutputDevice: (
      state,
      action: PayloadAction<string | null>,
    ) => {
      state.audioOutputDevice = action.payload;
    },
    setLocalMediaInstrumentType: (
      state,
      action: PayloadAction<InstrumentType>,
    ) => {
      state.instrumentType = action.payload;
    },
    setLocalMediaRoomAcoustics: (
      state,
      action: PayloadAction<RoomAcoustics>,
    ) => {
      state.roomAcoustics = action.payload;
    },
    resetLocalMedia: (state) => {
      state.audioDevices = [];
      state.audioOutputDevice = null;
      state.videoDevice = null;
      state.instrumentType = InstrumentType.General;
      state.roomAcoustics = RoomAcoustics.Normal;
    },
  },
});

export const {
  setLocalMediaAudioDevices,
  setLocalMediaDeviceUpdating,
  addLocalMediaAudioDevices,
  removeLocalMediaAudioDevices,
  setLocalMediaVideoDevice,
  setLocalMediaAudioOutputDevice,
  setLocalMediaInstrumentType,
  setLocalMediaRoomAcoustics,
  resetLocalMedia,
} = localMediaSlice.actions;
export default localMediaSlice.reducer;
