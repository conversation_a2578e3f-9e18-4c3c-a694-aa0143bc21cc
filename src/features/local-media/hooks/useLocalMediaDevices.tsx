import { RefObject, useEffect, useRef } from 'react';
import { logger, useAppSelector, useUpdateReduxState } from '@/shared';
import { useLocalMediaContext } from '@/features';
import { detectNewDefaultDevice, getCurrentDefaultFromStream } from '../utils';
import { setLocalMediaAudioDevices } from '../model';

export const useLocalMediaDevices = (
  localStreamRef: RefObject<MediaStream | null>,
) => {
  const updateReduxState = useUpdateReduxState();
  const { audioDevices } = useAppSelector((state) => state.localMedia);
  const { availableDevices, setAvailableDevices } = useLocalMediaContext();

  const audioInputDevicesRef = useRef<MediaDeviceInfo[]>([]);
  const lastKnownDefaultDevice = useRef<{
    deviceId: string | null;
    groupId: string | null;
  }>({ deviceId: null, groupId: null });

  useEffect(() => {
    audioInputDevicesRef.current = availableDevices.audioInputDevices;
  }, [availableDevices.audioInputDevices]);

  // Concurrency control
  const isProcessingRef = useRef(false);
  const eventQueueRef = useRef<Array<() => Promise<void>>>([]);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const updateCurrentDevices = async (): Promise<{
    video: MediaDeviceInfo[];
    audioInput: MediaDeviceInfo[];
    audioOutput: MediaDeviceInfo[];
  }> => {
    const video: MediaDeviceInfo[] = [];
    const audioInput: MediaDeviceInfo[] = [];
    const audioOutput: MediaDeviceInfo[] = [];

    const devices = await navigator.mediaDevices.enumerateDevices();
    const defaultAudioInput = devices.find(
      (device) => device.deviceId === 'default' && device.kind === 'audioinput',
    );
    const defaultAudioOutput = devices.find(
      (device) =>
        device.deviceId === 'default' && device.kind === 'audiooutput',
    );

    devices.map((device) => {
      if (device.kind === 'videoinput') video.push(device);
      if (device.kind === 'audioinput') {
        if (
          defaultAudioInput &&
          device.label.includes(defaultAudioInput.label.split(' - ')[1]) &&
          device.deviceId !== 'default'
        ) {
          return;
        }

        if (
          audioInput.find(
            (d) =>
              d.label.includes(device.label) || device.label.includes(d.label),
          ) &&
          device.deviceId !== 'default'
        ) {
          return;
        }

        audioInput.push(device);
      }
      if (device.kind === 'audiooutput') {
        if (
          defaultAudioOutput &&
          device.label.includes(defaultAudioOutput.label.split(' - ')[1]) &&
          device.deviceId !== 'default'
        ) {
          return;
        }

        if (
          audioOutput.find(
            (d) =>
              d.label.includes(device.label) || device.label.includes(d.label),
          ) &&
          device.deviceId !== 'default'
        ) {
          return;
        }

        audioOutput.push(device);
      }
    });

    return { audioInput, video, audioOutput };
  };

  // Process events from queue sequentially
  const processEventQueue = async () => {
    if (isProcessingRef.current || eventQueueRef.current.length === 0) {
      return;
    }

    isProcessingRef.current = true;
    try {
      // Process all queued events sequentially
      while (eventQueueRef.current.length > 0) {
        const nextEvent = eventQueueRef.current.shift();
        if (nextEvent) {
          await nextEvent();
        }
      }
    } catch (err) {
      logger.error('Error processing device change event queue:', {
        error: err instanceof Error ? err.message : String(err),
        error_name: err instanceof DOMException ? err.name : 'Unknown',
        event_type: 'audio_device_change_error',
      });
    } finally {
      isProcessingRef.current = false;

      // Check if more events were added while processing
      if (eventQueueRef.current.length > 0) {
        setTimeout(processEventQueue, 10); // Small delay before processing new events
      }
    }
  };

  // Add event to queue with debouncing
  const queueDeviceChangeEvent = () => {
    // Clear existing debounce timer
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Handle device change event
    const handleDeviceChange = async () => {
      const latestAudioInputDevices = [...audioInputDevicesRef.current];
      let newAudioDevices = [...audioDevices];
      const { audioInput, video, audioOutput } = await updateCurrentDevices();

      // Cleaning disconnected devices
      for (const selectedDeviceInfo of audioDevices) {
        const info = selectedDeviceInfo.split('_');
        const currentTrack = localStreamRef
          .current!.getAudioTracks()
          .find(
            (track) =>
              track.getSettings().deviceId === info[0] &&
              track.getSettings().groupId === info[1],
          );

        if (
          currentTrack &&
          !audioInput.find(
            (deviceInfo) =>
              deviceInfo.deviceId === info[0] && deviceInfo.groupId === info[1],
          )
        ) {
          console.log('Removing the old track');
          newAudioDevices = newAudioDevices.filter(
            (device) => device !== selectedDeviceInfo,
          );
        }
      }

      // Method 1: Detect new devices by comparing device lists
      const potentialNewDefault = detectNewDefaultDevice(
        audioInput,
        latestAudioInputDevices,
      );

      if (potentialNewDefault) {
        const potentialFingerprint = `${potentialNewDefault.deviceId}_${potentialNewDefault.groupId}`;
        const isAlreadySelected = audioDevices.includes(potentialFingerprint);

        if (!isAlreadySelected) {
          logger.info('Adding new default device to state:', {
            device_id: potentialNewDefault.deviceId,
            group_id: potentialNewDefault.groupId,
            event_type: 'audio_input_device_added',
          });
          newAudioDevices.push(potentialFingerprint);
        }
      } else {
        // Method 2: Check if device order changed (indicating new default)
        const currentDefault = getCurrentDefaultFromStream(
          localStreamRef.current,
        );
        const previousDefault = lastKnownDefaultDevice.current;

        if (
          currentDefault.deviceId &&
          (currentDefault.deviceId !== previousDefault.deviceId ||
            currentDefault.groupId !== previousDefault.groupId)
        ) {
          const newDefaultDevice = audioInput.find(
            (d) => d.deviceId === currentDefault.deviceId,
          );

          if (
            newDefaultDevice &&
            !audioDevices.includes(
              `${newDefaultDevice.deviceId}_${newDefaultDevice.groupId}`,
            )
          ) {
            logger.info('Adding new default device to state:', {
              device_id: newDefaultDevice.deviceId,
              group_id: newDefaultDevice.groupId,
              event_type: 'audio_input_device_added',
            });

            newAudioDevices.push(
              `${newDefaultDevice.deviceId}_${newDefaultDevice.groupId}`,
            );
          }
        }

        lastKnownDefaultDevice.current = currentDefault;
      }

      // Helper function to compare arrays
      const arraysEqual = (a: string[], b: string[]) => {
        if (a.length !== b.length) return false;

        // Sort both arrays before comparison to handle order differences
        const sortedA = [...a].sort();
        const sortedB = [...b].sort();

        return sortedA.every((val, index) => val === sortedB[index]);
      };

      // Only update Redux state if devices actually changed
      if (!arraysEqual(newAudioDevices, audioDevices)) {
        console.log('Audio devices changed, updating state:', {
          previous: audioDevices,
          new: newAudioDevices,
          added: newAudioDevices.filter((d) => !audioDevices.includes(d)),
          removed: audioDevices.filter((d) => !newAudioDevices.includes(d)),
        });

        updateReduxState(setLocalMediaAudioDevices(newAudioDevices));
      }

      setAvailableDevices(audioInput, audioOutput, video);
    };

    // Debounce: wait for events to settle before adding to queue
    debounceTimeoutRef.current = setTimeout(() => {
      eventQueueRef.current.push(handleDeviceChange);

      // Start processing if not already processing
      if (!isProcessingRef.current) {
        processEventQueue();
      }
    }, 250);
  };

  useEffect(() => {
    if (!localStreamRef.current) return;

    queueDeviceChangeEvent();
    navigator.mediaDevices.addEventListener(
      'devicechange',
      queueDeviceChangeEvent,
    );
    return () => {
      navigator.mediaDevices.removeEventListener(
        'devicechange',
        queueDeviceChangeEvent,
      );

      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      eventQueueRef.current = [];
      isProcessingRef.current = false;
    };
  }, [audioDevices]);
};
