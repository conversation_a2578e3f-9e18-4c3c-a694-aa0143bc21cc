import { useEffect } from 'react';
import { isIOSDevice, useAppSelector } from '@/shared';
import {
  centralAudioProcessing,
  ConferenceStatus,
  updateLocalAudioTracks,
  useLivekitContext,
  useLocalMediaContext,
} from '@/features';
import { useUser } from '@/entities';

export const useLocalMediaProcessing = () => {
  const { roleType } = useUser();
  const { audioContextReady } = useAppSelector((state) => state.conference);

  const { room } = useLivekitContext();
  const { localStream } = useLocalMediaContext();

  const { status } = useAppSelector((state) => state.conference);
  const { roomAcoustics } = useAppSelector((state) => state.localMedia);

  // Loads local raw audio into processing
  useEffect(() => {
    if (!localStream || !audioContextReady || roleType !== 'party') return;
    const audioTracks = localStream.getAudioTracks();
    centralAudioProcessing
      .getLocalAudioMixer()
      .getAudioLevelMonitor()
      .getTrackMixer()
      .updateTracks(audioTracks);

    // Apply gain based on room acoustics
    if (isIOSDevice() || roomAcoustics === 'dead') {
      centralAudioProcessing
        .getLocalAudioMixer()
        .getAudioLevelMonitor()
        .setGain(1.5);
    }

    if (status === ConferenceStatus.Ready) {
      const newStream = centralAudioProcessing
        .getLocalAudioMixer()
        .getCurrentStream();

      (async () => await updateLocalAudioTracks(room, newStream))();
    }
  }, [localStream, audioContextReady]);

  // Applies room correction to the local stream processing
  useEffect(() => {
    if (!centralAudioProcessing.isInitialized()) return;
    if (roomAcoustics === 'dead') {
      centralAudioProcessing
        .getLocalAudioMixer()
        .getAudioLevelMonitor()
        .setGain(1.5);
    }
  }, [roomAcoustics]);
};
