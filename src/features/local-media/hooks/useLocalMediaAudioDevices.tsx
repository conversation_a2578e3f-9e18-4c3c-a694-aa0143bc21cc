import { RefObject, useEffect } from 'react';
import { logger, useAppSelector, useUpdateReduxState } from '@/shared';
import {
  getMediaStreamConstraints,
  setIOSAudioSession,
  toggleMute,
} from '../utils';
import { sendData, useLivekitContext } from '../../livekit';
import { setLocalMediaDeviceUpdating } from '../model';
import { useLocalMediaContext } from '../context';
import { LocalMediaStatus } from '../lib';

export const useLocalMediaAudioDevices = (
  localStreamRef: RefObject<MediaStream | null>,
) => {
  const updateReduxState = useUpdateReduxState();

  const { room } = useLivekitContext();
  const { hasProcessing, setLocalStream, setStatus } = useLocalMediaContext();

  const { localUser } = useAppSelector((state) => state.livekit);
  const { audioDevices } = useAppSelector((state) => state.localMedia);

  // Updates audio devices locally
  useEffect(() => {
    if (!localStreamRef.current) return;

    setStatus(LocalMediaStatus.Updating);
    const stream = localStreamRef.current;
    const currentTrackInfos = stream.getAudioTracks().map((track) => {
      return {
        deviceId: track.getSettings().deviceId,
        groupId: track.getSettings().groupId,
      };
    });

    // Log the start of audio input device change
    logger.info('Audio input device change started', {
      current_track_device_ids: JSON.stringify(currentTrackInfos),
      new_audio_devices: JSON.stringify(audioDevices),
      local_tracks_raw: JSON.stringify(
        stream.getAudioTracks().map((t) => ({
          id: t.id,
          label: t.label,
          deviceId: t.getSettings().deviceId,
          enabled: t.enabled,
          muted: t.muted,
          readyState: t.readyState,
          settings: t.getSettings(),
        })),
      ),
      event_type: 'audio_input_device_change_start',
    });

    const revalidateAudioDevices = async () => {
      let checkedDevices = [...audioDevices];

      setIOSAudioSession('play-and-record');
      for (const info of currentTrackInfos) {
        if (info.deviceId && info.groupId) {
          const fingerprint = `${info.deviceId}_${info.groupId}`;
          if (audioDevices.includes(fingerprint)) {
            checkedDevices = checkedDevices.filter(
              (info) => info !== fingerprint,
            );
          } else {
            const audioTrack = stream
              .getAudioTracks()
              .find(
                (track) =>
                  track.getSettings().deviceId === info.deviceId &&
                  track.getSettings().groupId === info.groupId,
              );

            // Log device removal
            logger.info('Removing audio input device', {
              device_id: info.deviceId,
              group_id: info.groupId,
              track_id: audioTrack?.id ?? '',
              track_label: audioTrack?.label ?? '',
              track_settings: audioTrack
                ? JSON.stringify(audioTrack.getSettings())
                : '',
              event_type: 'audio_input_device_removed',
            });

            if (audioTrack) {
              audioTrack.stop();
              stream.removeTrack(audioTrack);
            }
          }
        }
      }

      for (const fingerprint of checkedDevices) {
        try {
          const info = fingerprint.split('_');
          const newStream = await navigator.mediaDevices.getUserMedia(
            getMediaStreamConstraints(info[0], null, hasProcessing ?? false),
          );

          const newTrack = newStream.getAudioTracks()[0];

          // Log device addition
          logger.info('Adding audio input device', {
            device_id: info[0],
            group_id: info[1],
            track_id: newTrack?.id,
            track_label: newTrack?.label,
            track_settings: newTrack
              ? JSON.stringify(newTrack.getSettings())
              : '',
            constraints: JSON.stringify(
              getMediaStreamConstraints(info[0], null),
            ),
            event_type: 'audio_input_device_added',
          });

          if (newTrack) stream.addTrack(newTrack);
        } catch (err) {
          // Log device addition failure
          logger.error('Failed to add audio input device', {
            device_id: fingerprint.split('_')[0],
            group_id: fingerprint.split('_')[1],
            error: err instanceof Error ? err.message : String(err),
            error_name: err instanceof DOMException ? err.name : 'Unknown',
            constraints: JSON.stringify(
              getMediaStreamConstraints(fingerprint.split('_')[0], null),
            ),
            event_type: 'audio_input_device_add_error',
          });
        }
      }
    };

    revalidateAudioDevices().then(() => {
      if (localUser && !localUser.isMicrophoneOn) {
        toggleMute(room, localUser.sid, false, stream);
        sendData(room, {
          type: 'medias',
          message: { sid: localUser.sid, type: 'mic', state: false },
        });
      }

      // Log the completion of audio input device change
      logger.info('Audio input device change completed', {
        final_device_list: JSON.stringify(audioDevices),
        final_tracks_info: JSON.stringify(
          stream.getAudioTracks().map((t) => ({
            id: t.id,
            label: t.label,
            deviceId: t.getSettings().deviceId,
            enabled: t.enabled,
            settings: t.getSettings(),
          })),
        ),
        microphone_muted: localUser ? !localUser.isMicrophoneOn : false,
        event_type: 'audio_input_device_change_complete',
      });

      setIOSAudioSession('playback');
      updateReduxState(setLocalMediaDeviceUpdating(false));

      //localStreamRef.current = new MediaStream(stream.getTracks());
      setLocalStream(new MediaStream(stream.getTracks()));
      setStatus(LocalMediaStatus.Ready);
    });
  }, [audioDevices]);
};
