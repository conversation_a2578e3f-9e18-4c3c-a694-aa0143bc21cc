import { useEffect, useState } from 'react';
import { useLocalMediaContext } from '../context';

export const useLocalMediaVideoStream = () => {
  const { localStream } = useLocalMediaContext();
  const [localVideoStream, setLocalVideoStream] =
    useState<MediaStream | null>();

  // Helper to get track IDs (or use references if preferred)
  const getVideoTrackIds = (stream: MediaStream | null) =>
    stream
      ?.getVideoTracks()
      .map((track) => track.id)
      .join(',') ?? '';

  const videoTrackIds = getVideoTrackIds(localStream);

  useEffect(() => {
    if (!localStream) return;

    const videoTracks = localStream.getVideoTracks();
    if (videoTracks.length === 0) {
      setLocalVideoStream(null);
      return;
    }

    setLocalVideoStream(new MediaStream(videoTracks));
  }, [videoTrackIds]);

  return { localVideoStream };
};
