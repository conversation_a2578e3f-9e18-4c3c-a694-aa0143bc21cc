import { RefObject, useEffect } from 'react';
import { logger, useAppSelector, useUpdateReduxState } from '@/shared';
import { useLocalMediaContext } from '../context';
import { getMediaStreamConstraints, setIOSAudioSession } from '../utils';
import { LocalMediaStatus } from '../lib';
import { addLocalMediaAudioDevices, setLocalMediaVideoDevice } from '../model';
import { ConferenceStatus, setConferenceStatus } from '../../conference';
import { useUser } from '@/entities';

export const useLocalMediaInit = (
  localStreamRef: RefObject<MediaStream | null>,
) => {
  const updateReduxState = useUpdateReduxState();

  const { role, roleType } = useUser();
  const { setLocalStream, setStatus } = useLocalMediaContext();
  const { status } = useAppSelector((state) => state.conference);
  const { instrumentType, roomAcoustics } = useAppSelector(
    (state) => state.localMedia,
  );

  // Manages local stream changes
  useEffect(() => {
    if (roleType !== 'party' || status !== ConferenceStatus.LocalMedia) return;
    (async () => {
      try {
        // Log local media acquisition start
        logger.info('Local media acquisition started', {
          role: role as string,
          roleType: roleType,
          status: status,
          aec_enabled: false,
          instrument_type: instrumentType,
          room_acoustics: roomAcoustics,
          constraints: JSON.stringify(getMediaStreamConstraints(null, null)),
          event_type: 'local_media_acquisition_start',
        });

        setIOSAudioSession('play-and-record');
        const stream = await navigator.mediaDevices.getUserMedia(
          getMediaStreamConstraints(null, null),
        );

        const audioTrack = stream.getAudioTracks()[0];
        if (audioTrack) {
          const deviceId = audioTrack.getSettings().deviceId;
          const groupdId = audioTrack.getSettings().groupId;
          if (deviceId && groupdId) {
            updateReduxState(
              addLocalMediaAudioDevices(`${deviceId}_${groupdId}`),
            );
          }
        }

        const videoTrack = stream.getVideoTracks()[0];
        if (videoTrack) {
          const deviceId = videoTrack.getSettings().deviceId;
          if (deviceId) {
            updateReduxState(setLocalMediaVideoDevice(deviceId));
          }
        }

        // Log successful local media acquisition
        logger.info('Local media acquired', {
          audio_tracks_count: stream.getAudioTracks().length,
          video_tracks_count: stream.getVideoTracks().length,
          aec_enabled: false,
          instrument_type: instrumentType,
          room_acoustics: roomAcoustics,
          audio_device_id: audioTrack?.getSettings().deviceId ?? '',
          video_device_id: videoTrack?.getSettings().deviceId ?? '',
          constraints: JSON.stringify(getMediaStreamConstraints(null, null)),
          local_tracks_raw: JSON.stringify(
            stream.getTracks().map((t) => ({
              kind: t.kind,
              id: t.id,
              label: t.label,
              enabled: t.enabled,
              settings: t.getSettings(),
            })),
          ),
          event_type: 'local_media_acquired',
        });

        setLocalStream(stream);
        localStreamRef.current = stream;

        setIOSAudioSession('playback');
        setStatus(LocalMediaStatus.Ready);
        updateReduxState(setConferenceStatus(ConferenceStatus.Livekit));
      } catch (err) {
        // Log local media error
        logger.error('Error accessing media devices', {
          error: err instanceof Error ? err.message : String(err),
          error_name: err instanceof DOMException ? err.name : 'Unknown',
          role: role as string,
          roleType: roleType,
          status: status,
          constraints: JSON.stringify(getMediaStreamConstraints(null, null)),
          event_type: 'local_media_error',
        });

        setIOSAudioSession('playback');

        let name: string | undefined;
        if (err instanceof DOMException) {
          name = err.name;
        }

        if (name === 'NotAllowedError' || name === 'PermissionDeniedError') {
          setStatus(LocalMediaStatus.PermissionError);
        } else if (
          name === 'NotFoundError' ||
          name === 'DevicesNotFoundError'
        ) {
          setStatus(LocalMediaStatus.DeviceError);
        } else if (
          name === 'NotReadableError' ||
          name === 'TrackStartError' ||
          name === 'OverconstrainedError'
        ) {
          setStatus(LocalMediaStatus.TakenDeviceError);
        }
      }
    })();
  }, [role, roleType, status]);

  useEffect(() => {
    return () => {
      if (localStreamRef.current) {
        // Log local media disposal
        logger.info('Disposing local media', {
          tracks_count: localStreamRef.current.getTracks().length,
          tracks_info: JSON.stringify(
            localStreamRef.current.getTracks().map((t) => ({
              kind: t.kind,
              id: t.id,
              label: t.label,
              readyState: t.readyState,
            })),
          ),
          event_type: 'local_media_disposed',
        });

        localStreamRef.current.getTracks().forEach((track) => track.stop());
        localStreamRef.current = null;
        setLocalStream(null);
      }
    };
  }, []);
};
