import { RefObject, useEffect } from 'react';
import { useLocalMediaContext } from '../context';
import {
  getMediaStreamConstraints,
  setIOSAudioSession,
  toggleMute,
} from '../utils';
import { logger, useAppSelector } from '@/shared';
import { sendData, useLivekitContext } from '../../livekit';
import { LocalMediaStatus } from '../lib';

export const useLocalMediaConstraints = (
  localStreamRef: RefObject<MediaStream | null>,
) => {
  const { room } = useLivekitContext();
  const { localUser } = useAppSelector((state) => state.livekit);
  const { hasProcessing, setLocalStream, setStatus } = useLocalMediaContext();

  useEffect(() => {
    if (!localStreamRef.current || hasProcessing === null) return;

    setIOSAudioSession('play-and-record');
    setStatus(LocalMediaStatus.Updating);

    const stream = localStreamRef.current;
    const deviceIds = stream
      .getAudioTracks()
      .map((track) => track.getSettings().deviceId);

    (async () => {
      for (const deviceId of deviceIds) {
        if (deviceId) {
          const currentTrack = stream
            .getAudioTracks()
            .find((track) => track.getSettings().deviceId === deviceId);

          try {
            const newStream = await navigator.mediaDevices.getUserMedia(
              getMediaStreamConstraints(deviceId, null, hasProcessing),
            );

            if (currentTrack) {
              stream.removeTrack(currentTrack);
              currentTrack.stop();
            }

            const newTrack = newStream.getAudioTracks()[0];
            stream.addTrack(newTrack);

            // Log device addition
            logger.info('Updated constraints for the audio track', {
              device_id: deviceId,
              track_id: newTrack?.id,
              track_label: newTrack?.label,
              track_settings: newTrack
                ? JSON.stringify(newTrack.getSettings())
                : '',
              constraints: JSON.stringify(
                getMediaStreamConstraints(deviceId, null, hasProcessing),
              ),
              event_type: 'audio_constraints_updated',
            });
          } catch (err) {
            logger.error('Failed to add audio input device', {
              device_id: deviceId,
              error: err instanceof Error ? err.message : String(err),
              error_name: err instanceof DOMException ? err.name : 'Unknown',
              event_type: 'audio_constraints_error',
            });
          }
        }
      }

      if (localUser && !localUser.isMicrophoneOn) {
        toggleMute(room, localUser.sid, false, stream);
        sendData(room, {
          type: 'medias',
          message: { sid: localUser.sid, type: 'mic', state: false },
        });
      }

      localStreamRef.current = new MediaStream(stream.getTracks());
      setLocalStream(new MediaStream(stream.getTracks()));

      setIOSAudioSession('playback');
      setStatus(LocalMediaStatus.Ready);
    })();
  }, [hasProcessing]);
};
