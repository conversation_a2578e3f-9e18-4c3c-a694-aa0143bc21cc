import { RefObject, useEffect } from 'react';
import { logger, useAppSelector, useUpdateReduxState } from '@/shared';
import { getMediaStreamConstraints, toggleVideo } from '../utils';
import { sendData, useLivekitContext, VIDEO_OPTIONS } from '../../livekit';
import { setLocalMediaDeviceUpdating } from '../model';
import { useLocalMediaContext } from '../context';
import { LocalMediaStatus } from '../lib';

export const useLocalMediaVideoDevices = (
  localStreamRef: RefObject<MediaStream | null>,
) => {
  const updateReduxState = useUpdateReduxState();

  const { room } = useLivekitContext();
  const { setLocalStream, setStatus } = useLocalMediaContext();
  const { localUser } = useAppSelector((state) => state.livekit);
  const { videoDevice } = useAppSelector((state) => state.localMedia);

  // Updates video device locally and in livekit
  useEffect(() => {
    if (!localStreamRef.current || !videoDevice) return;

    setStatus(LocalMediaStatus.Updating);
    const stream = localStreamRef.current;
    const currentTrack = stream.getVideoTracks()[0];
    const currentDeviceId = currentTrack.getSettings().deviceId;

    if (currentDeviceId && currentDeviceId !== videoDevice) {
      // Log video device change start
      logger.info('Video device change started', {
        old_device_id: currentDeviceId,
        new_device_id: videoDevice,
        current_track_info: JSON.stringify({
          id: currentTrack.id,
          label: currentTrack.label,
          enabled: currentTrack.enabled,
          settings: currentTrack.getSettings(),
        }),
        current_local_tracks: JSON.stringify(
          stream.getVideoTracks().map((t) => ({
            id: t.id,
            label: t.label,
            deviceId: t.getSettings().deviceId,
            enabled: t.enabled,
            settings: t.getSettings(),
          })),
        ),
        event_type: 'video_device_change_start',
      });

      (async () => {
        try {
          const newStream = await navigator.mediaDevices.getUserMedia(
            getMediaStreamConstraints(null, videoDevice),
          );

          const newTrack = newStream.getVideoTracks()[0];

          // Log successful video track acquisition
          logger.info('New video track acquired', {
            new_device_id: videoDevice,
            new_track_info: JSON.stringify({
              id: newTrack.id,
              label: newTrack.label,
              enabled: newTrack.enabled,
              settings: newTrack.getSettings(),
            }),
            constraints: JSON.stringify(
              getMediaStreamConstraints(null, videoDevice),
            ),
            event_type: 'video_track_acquired',
          });

          currentTrack.stop();
          stream.removeTrack(currentTrack);
          await room.localParticipant.unpublishTrack(currentTrack);

          // Log track replacement
          logger.info('Video track replaced in stream', {
            old_track_id: currentTrack.id,
            new_track_id: newTrack.id,
            device_id: videoDevice,
            event_type: 'video_track_replaced',
          });

          stream.addTrack(newTrack);
          await room.localParticipant.publishTrack(newTrack, VIDEO_OPTIONS);

          // Log track publication
          logger.info('Video track published to room', {
            track_id: newTrack.id,
            device_id: videoDevice,
            video_options: JSON.stringify(VIDEO_OPTIONS),
            event_type: 'video_track_published',
          });

          if (localUser && !localUser.isVideoOn) {
            toggleVideo(room, localUser.sid, false, stream);
            sendData(room, {
              type: 'medias',
              message: { sid: localUser.sid, type: 'video', state: false },
            });
          }

          // Log video device change completion
          logger.info('Video device change completed', {
            new_device_id: videoDevice,
            new_track_id: newTrack.id,
            final_track_info: JSON.stringify({
              id: newTrack.id,
              label: newTrack.label,
              enabled: newTrack.enabled,
              settings: newTrack.getSettings(),
            }),
            video_muted: localUser ? !localUser.isVideoOn : true,
            event_type: 'video_device_change_complete',
          });

          updateReduxState(setLocalMediaDeviceUpdating(false));
          localStreamRef.current = new MediaStream(stream.getTracks());
          setLocalStream(new MediaStream(stream.getTracks()));
          setStatus(LocalMediaStatus.Ready);
        } catch (err) {
          console.error(`Failed to get track for device ${videoDevice}:`, err);

          // Log video device change error
          logger.error('Video device change failed', {
            attempted_device_id: videoDevice,
            current_device_id: currentDeviceId,
            error: err instanceof Error ? err.message : String(err),
            error_name: err instanceof DOMException ? err.name : 'Unknown',
            event_type: 'video_device_change_error',
          });
        }
      })();
    }
  }, [videoDevice]);
};
