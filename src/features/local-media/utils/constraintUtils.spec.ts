/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, afterEach, beforeEach } from 'vitest';
import {
  detectBrowser,
  getMediaStreamConstraints,
  setIOSAudioSession,
} from './constraintUtils';
import { logger, isMobile, isLowEndDevice } from '@/shared';

// Mock shared module (logger + device helpers)
vi.mock('@/shared', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
  },
  isMobile: vi.fn(() => false),
  isLowEndDevice: vi.fn(() => false),
}));

describe('constraintUtils', () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('detectBrowser', () => {
    const setUA = (ua: string) => {
      vi.spyOn(window.navigator, 'userAgent', 'get').mockReturnValue(ua);
    };

    it('detects chrome', () => {
      setUA('Chrome/99');
      expect(detectBrowser()).toBe('chrome');
    });

    it('detects edge', () => {
      setUA('Edg/99');
      expect(detectBrowser()).toBe('edge');
    });

    it('detects yandex', () => {
      setUA('YaBrowser/22');
      expect(detectBrowser()).toBe('yandex');
    });

    it('detects firefox', () => {
      setUA('Firefox/88');
      expect(detectBrowser()).toBe('firefox');
    });

    it('detects safari', () => {
      setUA('Safari/605');
      expect(detectBrowser()).toBe('safari');
    });

    it('detects other', () => {
      setUA('OtherBrowser/1.0');
      expect(detectBrowser()).toBe('other');
    });
  });

  describe('getMediaStreamConstraints', () => {
    beforeEach(() => {
      // Default UA to Chrome unless overridden
      vi.spyOn(window.navigator, 'userAgent', 'get').mockReturnValue(
        'Chrome/99',
      );
      vi.mocked(isMobile).mockReturnValue(false);
      vi.mocked(isLowEndDevice).mockReturnValue(false);
    });

    it('returns audio+video when no device IDs are provided', () => {
      const result = getMediaStreamConstraints(null, null);
      expect(result?.audio).toBeDefined();
      expect(result?.video).toBeDefined();
      expect((result!.audio as any).deviceId).toEqual({ exact: 'default' });
    });

    it('respects provided audio and video device IDs', () => {
      const result = getMediaStreamConstraints('mic-id', 'cam-id');
      expect((result?.audio as any).deviceId).toEqual({ exact: 'mic-id' });
      expect((result?.video as any).deviceId).toEqual({ exact: 'cam-id' });
    });

    it('returns only video when audioDevice is null and videoDevice provided (non-safari/firefox)', () => {
      const result = getMediaStreamConstraints(null, 'cam-id');
      expect(result?.audio).toBeUndefined();
      expect(result?.video).toBeDefined();
      expect((result?.video as any).deviceId).toEqual({ exact: 'cam-id' });
    });

    it('omits audio deviceId for Safari and Firefox when audioDevice is null', () => {
      vi.spyOn(window.navigator, 'userAgent', 'get').mockReturnValue(
        'Safari/605',
      );
      let res = getMediaStreamConstraints(null, null);
      expect((res?.audio as any).deviceId).toBeUndefined();

      vi.spyOn(window.navigator, 'userAgent', 'get').mockReturnValue(
        'Firefox/99',
      );
      res = getMediaStreamConstraints(null, null);
      expect((res?.audio as any).deviceId).toBeUndefined();
    });

    it('uses mobile-optimized video constraints when isMobile() is true', () => {
      vi.mocked(isMobile).mockReturnValue(true);
      vi.mocked(isLowEndDevice).mockReturnValue(false);

      const res = getMediaStreamConstraints(null, null);
      const video = res!.video as any;
      expect(video.width.max).toBe(1280);
      expect(video.height.max).toBe(720);
      expect(video.frameRate.max).toBe(24);
    });

    it('uses low-end mobile constraints when both mobile and low-end', () => {
      vi.mocked(isMobile).mockReturnValue(true);
      vi.mocked(isLowEndDevice).mockReturnValue(true);

      const res = getMediaStreamConstraints(null, null);
      const video = res!.video as any;
      expect(video.width.ideal).toBe(640);
      expect(video.height.ideal).toBe(480);
      expect(video.frameRate.ideal).toBe(15);
    });
  });

  describe('setIOSAudioSession', () => {
    afterEach(() => {
      delete (navigator as any).audioSession;
      vi.clearAllMocks();
    });

    it('returns false when audioSession is not available', () => {
      delete (navigator as any).audioSession;
      expect(setIOSAudioSession()).toBe(false);
      expect(logger.info).not.toHaveBeenCalled();
      expect(logger.error).not.toHaveBeenCalled();
    });

    it('sets mode and logs info when audioSession is available', () => {
      (navigator as any).audioSession = { type: '' };

      const ok = setIOSAudioSession('playback');
      expect(ok).toBe(true);
      expect((navigator as any).audioSession.type).toBe('playback');

      expect(logger.info).toHaveBeenCalledWith(
        'iOS: Audio session set to new mode',
        expect.objectContaining({
          mode: 'playback',
          event_type: 'ios_audio_session_changed_mode',
        }),
      );
    });

    it('logs error and returns false when accessing audioSession throws', () => {
      Object.defineProperty(navigator, 'audioSession', {
        configurable: true,
        get() {
          throw new Error('boom');
        },
      });

      const ok = setIOSAudioSession('play-and-record');
      expect(ok).toBe(false);
      expect(logger.error).toHaveBeenCalledWith(
        'iOS: Could not set audio session',
        expect.objectContaining({
          error: 'boom',
          event_type: 'ios_audio_session_error',
        }),
      );

      delete (navigator as any).audioSession;
    });
  });
});
