import { isLowEndDevice, isMobile, logger } from '../../../shared';

export const setIOSAudioSession = (
  mode: 'playback' | 'play-and-record' | 'transient' = 'playback',
): boolean => {
  try {
    if ('audioSession' in navigator) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const audioSession = (navigator as any).audioSession;
      if (audioSession && typeof audioSession.type !== 'undefined') {
        audioSession.type = mode;
        logger.info('iOS: Audio session set to new mode', {
          mode: mode,
          event_type: 'ios_audio_session_changed_mode',
        });
        return true;
      }
    }
  } catch (error) {
    logger.error('iOS: Could not set audio session', {
      error: error instanceof Error ? error.message : String(error),
      event_type: 'ios_audio_session_error',
    });
  }
  return false;
};

export const getMediaStreamConstraints = (
  audioDevice: string | null,
  videoDevice: string | null,
  aec: boolean = false,
): MediaStreamConstraints | undefined => {
  const browser = detectBrowser();
  const constraints: MediaStreamConstraints = {};

  let audioConstraints: MediaTrackConstraints = {
    // Disable all voice-optimized processing
    echoCancellation: aec,
    noiseSuppression: false,
    autoGainControl: false,

    // High quality audio settings
    sampleRate: { ideal: 48000, min: 44100 },
    sampleSize: { ideal: 24, min: 16 },
    channelCount: { ideal: 2, min: 1 },

    // Browser-specific optimizations
    ...(browser === 'chrome' && {
      googEchoCancellation: false,
      googAutoGainControl: false,
      googNoiseSuppression: false,
      googHighpassFilter: false,
      googTypingNoiseDetection: false,
    }),
  };

  const videoConstraints = isMobile()
    ? {
        // Mobile: Optimized for mobile screens and hardware
        width: { ideal: isLowEndDevice() ? 640 : 1280, max: 1280 },
        height: { ideal: isLowEndDevice() ? 480 : 720, max: 720 },
        frameRate: { ideal: isLowEndDevice() ? 15 : 24, max: 24 },
        aspectRatio: 16 / 9,
      }
    : {
        // Desktop: Keep existing high quality
        width: { ideal: 1920, min: 1280 },
        height: { ideal: 1080, min: 720 },
        frameRate: { ideal: 30, min: 24 },
        aspectRatio: 16 / 9,
      };

  if (browser !== 'safari' && browser !== 'firefox' && !audioDevice) {
    audioConstraints = {
      ...audioConstraints,
      deviceId: { exact: 'default' },
    };
  }

  if (audioDevice) {
    constraints.audio = {
      ...audioConstraints,
      deviceId: { exact: audioDevice },
    };
  }

  if (videoDevice) {
    constraints.video = {
      deviceId: { exact: videoDevice },
      ...videoConstraints,
    };
  }

  return Object.keys(constraints).length > 0
    ? constraints
    : {
        audio: audioConstraints,
        video: videoConstraints,
      };
};

export const detectBrowser = ():
  | 'chrome'
  | 'safari'
  | 'firefox'
  | 'edge'
  | 'yandex'
  | 'other' => {
  const ua = navigator.userAgent;

  // Edge must come before Chrome
  if (/Edg\//.test(ua)) {
    return 'edge';
  }
  // Yandex uses “YaBrowser” in its UA
  if (/YaBrowser\//.test(ua)) {
    return 'yandex';
  }
  // Chrome (but not Edge or Yandex)
  if (/Chrome\//.test(ua) && !/Edg\//.test(ua) && !/YaBrowser\//.test(ua)) {
    return 'chrome';
  }
  // Firefox
  if (/Firefox\//.test(ua)) {
    return 'firefox';
  }
  // Safari (but not Chrome)
  if (/Safari\//.test(ua) && !/Chrome\//.test(ua)) {
    return 'safari';
  }
  return 'other';
};
