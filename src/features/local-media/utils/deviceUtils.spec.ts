/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect } from 'vitest';
import {
  detectNewDefaultDevice,
  getCurrentDefaultFromStream,
} from './deviceUtils';

const mic = (over: Partial<MediaDeviceInfo> = {}): MediaDeviceInfo =>
  ({
    deviceId: 'id-x',
    groupId: 'grp-x',
    kind: 'audioinput',
    label: 'Internal Microphone',
    toJSON() {
      return this as any;
    },
    ...over,
  }) as MediaDeviceInfo;

describe('detectNewDefaultDevice', () => {
  it('returns a new device that looks like a default (AirPods)', () => {
    const previous = [mic({ deviceId: 'builtin-1', label: 'MacBook Mic' })];
    const current = [
      ...previous,
      mic({ deviceId: 'airpods-1', label: 'John’s AirPods' }),
    ];

    const found = detectNewDefaultDevice(current, previous);
    expect(found?.deviceId).toBe('airpods-1');
  });

  it('returns a new device that looks like a default (Bluetooth/wireless)', () => {
    const previous = [mic({ deviceId: 'builtin-1', label: 'Internal Mic' })];
    const current = [
      ...previous,
      mic({ deviceId: 'bt-1', label: 'My Bluetooth Headset' }),
      mic({ deviceId: 'wls-1', label: 'Wireless Mic' }),
    ];

    const found = detectNewDefaultDevice(current, previous);
    // First match in newDevices that satisfies the predicate is returned
    expect(['bt-1', 'wls-1']).toContain(found?.deviceId);
  });

  it('returns a device with deviceId="default" if it appears', () => {
    const previous = [mic({ deviceId: 'builtin-1' })];
    const current = [
      ...previous,
      mic({ deviceId: 'default', label: 'Default Audio Device' }),
    ];

    const found = detectNewDefaultDevice(current, previous);
    expect(found?.deviceId).toBe('default');
  });

  it('falls back to first-device order change when no obvious new default', () => {
    const previous = [
      mic({ deviceId: 'a', label: 'Mic A' }),
      mic({ deviceId: 'b', label: 'Mic B' }),
    ];
    // Reordered: first device changed from 'a' to 'b'
    const current = [
      mic({ deviceId: 'b', label: 'Mic B' }),
      mic({ deviceId: 'a', label: 'Mic A' }),
    ];

    const found = detectNewDefaultDevice(current, previous);
    expect(found?.deviceId).toBe('b');
  });

  it('returns null when nothing changed', () => {
    const previous = [
      mic({ deviceId: 'a', groupId: 'g1', label: 'Mic A' }),
      mic({ deviceId: 'b', groupId: 'g2', label: 'Mic B' }),
    ];
    const current = [
      mic({ deviceId: 'a', groupId: 'g1', label: 'Mic A' }),
      mic({ deviceId: 'b', groupId: 'g2', label: 'Mic B' }),
    ];

    const found = detectNewDefaultDevice(current, previous);
    expect(found).toBeNull();
  });
});

describe('getCurrentDefaultFromStream', () => {
  const track = (deviceId?: string, groupId?: string) =>
    ({
      getSettings: () => ({ deviceId, groupId }),
    }) as unknown as MediaStreamTrack;

  it('returns nulls when stream is null', () => {
    const res = getCurrentDefaultFromStream(null);
    expect(res).toEqual({ deviceId: null, groupId: null });
  });

  it('returns nulls when there are no audio tracks', () => {
    const stream = {
      getAudioTracks: () => [],
    } as unknown as MediaStream;

    const res = getCurrentDefaultFromStream(stream);
    expect(res).toEqual({ deviceId: null, groupId: null });
  });

  it('returns deviceId/groupId from the single audio track settings', () => {
    const stream = {
      getAudioTracks: () => [track('dev-1', 'grp-1')],
    } as unknown as MediaStream;

    const res = getCurrentDefaultFromStream(stream);
    expect(res).toEqual({ deviceId: 'dev-1', groupId: 'grp-1' });
  });

  it('returns settings from the most recent (last) audio track', () => {
    const stream = {
      getAudioTracks: () => [track('dev-1', 'grp-1'), track('dev-2', 'grp-2')],
    } as unknown as MediaStream;

    const res = getCurrentDefaultFromStream(stream);
    expect(res).toEqual({ deviceId: 'dev-2', groupId: 'grp-2' });
  });

  it('normalizes undefined settings to null', () => {
    const stream = {
      getAudioTracks: () => [track(undefined, undefined)],
    } as unknown as MediaStream;

    const res = getCurrentDefaultFromStream(stream);
    expect(res).toEqual({ deviceId: null, groupId: null });
  });
});
