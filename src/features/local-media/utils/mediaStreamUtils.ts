import { Room } from 'livekit-client';
import { sendData } from '@/features';

export const isMicrophoneEnabled = (localStream: MediaStream): boolean => {
  const videoTracks = localStream.getAudioTracks();
  if (videoTracks.length === 0) return false;
  return videoTracks[0].enabled;
};

export const toggleMute = (
  room: Room,
  sid: string,
  newState: boolean,
  localStream: MediaStream,
): boolean => {
  const audioTracks = localStream.getAudioTracks();
  audioTracks.forEach((track) => {
    track.enabled = newState;
  });

  const enabled = audioTracks[0]?.enabled;
  sendData(room, {
    type: 'medias',
    message: { sid, type: 'mic', state: enabled },
  });
  return enabled;
};

export const isVideoEnabled = (localStream: MediaStream): boolean => {
  const videoTracks = localStream.getVideoTracks();
  if (videoTracks.length === 0) return false;
  return videoTracks[0].enabled;
};

export const toggleVideo = (
  room: Room,
  sid: string,
  newState: boolean,
  localStream: MediaStream,
): boolean => {
  const videoTracks = localStream.getVideoTracks();
  videoTracks.forEach((track) => {
    track.enabled = newState;
  });

  const enabled = videoTracks[0]?.enabled;
  sendData(room, {
    type: 'medias',
    message: { sid, type: 'video', state: enabled },
  });
  return enabled;
};
