export const detectNewDefaultDevice = (
  currentDevices: MediaDeviceInfo[],
  previousDevices: MediaDeviceInfo[],
): MediaDeviceInfo | null => {
  // Look for new devices that weren't in the previous list
  const newDevices = currentDevices.filter(
    (current) =>
      !previousDevices.some(
        (prev) =>
          prev.deviceId === current.deviceId &&
          prev.groupId === current.groupId,
      ),
  );

  // Check if any new device looks like it could be the new default
  // (AirPods, Bluetooth devices, etc. often appear first in the list)
  const potentialNewDefault = newDevices.find((device) => {
    const label = device.label.toLowerCase();
    return (
      label.includes('airpods') ||
      label.includes('bluetooth') ||
      label.includes('wireless') ||
      device.deviceId === 'default'
    );
  });

  if (potentialNewDefault) {
    return potentialNewDefault;
  }

  // If no obvious new default, check if device order changed (first device is often default)
  const firstDevice = currentDevices[0];
  const wasFirstDevice = previousDevices[0];

  if (
    firstDevice &&
    wasFirstDevice &&
    firstDevice.deviceId !== wasFirstDevice.deviceId
  ) {
    return firstDevice;
  }

  return null;
};

// Alternative method: use existing stream tracks to infer current default
export const getCurrentDefaultFromStream = (
  localStream: MediaStream | null,
): {
  deviceId: string | null;
  groupId: string | null;
} => {
  if (!localStream) return { deviceId: null, groupId: null };

  const audioTracks = localStream.getAudioTracks();
  if (audioTracks.length === 0) return { deviceId: null, groupId: null };

  // If we have multiple tracks, the most recent one might be the current default
  const mostRecentTrack = audioTracks[audioTracks.length - 1];
  const settings = mostRecentTrack.getSettings();

  return {
    deviceId: settings.deviceId || null,
    groupId: settings.groupId || null,
  };
};
