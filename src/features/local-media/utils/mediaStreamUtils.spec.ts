/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, afterEach } from 'vitest';
import { Room } from 'livekit-client';
import {
  isMicrophoneEnabled,
  isVideoEnabled,
  toggleMute,
  toggleVideo,
} from './mediaStreamUtils';

vi.mock('@/features/livekit/utils/dataChannelUtils', async (importActual) => {
  const actual = await importActual<any>();
  return {
    ...actual,
    sendData: vi.fn(),
  };
});

import * as LivekitUtils from '@/features/livekit/utils/dataChannelUtils';

const makeRoom = (): Room =>
  ({
    localParticipant: { sid: 'local' },
  }) as unknown as Room;

const makeTrack = (kind: 'audio' | 'video') =>
  ({ kind, enabled: true }) as MediaStreamTrack;

const makeStream = (tracks: MediaStreamTrack[]): MediaStream =>
  ({
    getAudioTracks: () => tracks.filter((t) => t.kind === 'audio'),
    getVideoTracks: () => tracks.filter((t) => t.kind === 'video'),
  }) as unknown as MediaStream;

describe('mediaStreamUtils', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('isMicrophoneEnabled reflects track state', () => {
    const track = makeTrack('audio');
    const stream = makeStream([track]);
    expect(isMicrophoneEnabled(stream)).toBe(true);
    track.enabled = false;
    expect(isMicrophoneEnabled(stream)).toBe(false);
  });

  it('toggleMute updates track state and calls sendData', () => {
    const track = makeTrack('audio');
    const stream = makeStream([track]);
    const room = makeRoom();

    const result = toggleMute(room, 'sid123', false, stream);

    expect(result).toBe(false);
    expect(track.enabled).toBe(false);
    expect(LivekitUtils.sendData).toHaveBeenCalledWith(room, {
      type: 'medias',
      message: { sid: 'sid123', type: 'mic', state: false },
    });
  });

  it('isVideoEnabled reflects track state', () => {
    const track = makeTrack('video');
    const stream = makeStream([track]);
    expect(isVideoEnabled(stream)).toBe(true);
    track.enabled = false;
    expect(isVideoEnabled(stream)).toBe(false);
  });

  it('toggleVideo updates track state and calls sendData', () => {
    const track = makeTrack('video');
    const stream = makeStream([track]);
    const room = makeRoom();

    const result = toggleVideo(room, 'sid456', true, stream);

    expect(result).toBe(true);
    expect(track.enabled).toBe(true);
    expect(LivekitUtils.sendData).toHaveBeenCalledWith(room, {
      type: 'medias',
      message: { sid: 'sid456', type: 'video', state: true },
    });
  });
});
