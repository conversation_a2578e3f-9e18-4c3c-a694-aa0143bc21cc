import { createContext, useContext, useState, ReactNode } from 'react';
import { LocalMediaStatus } from '../lib';

type LocalMediaContextType = {
  localStream: MediaStream | null;
  status: LocalMediaStatus;
  hasProcessing: boolean | null;
  availableDevices: {
    audioInputDevices: MediaDeviceInfo[];
    audioOutputDevices: MediaDeviceInfo[];
    videoDevices: MediaDeviceInfo[];
  };
  setLocalStream: (stream: MediaStream | null) => void;
  setStatus: (status: LocalMediaStatus) => void;
  setHasProcessing: (value: boolean) => void;
  setAvailableDevices: (
    audioInputDevices: MediaDeviceInfo[],
    audioOutputDevices: MediaDeviceInfo[],
    videoDevices: MediaDeviceInfo[],
  ) => void;
};

const LocalMediaContext = createContext<LocalMediaContextType | undefined>(
  undefined,
);

export const LocalMediaProvider = ({ children }: { children: ReactNode }) => {
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [status, setStatus] = useState<LocalMediaStatus>(
    LocalMediaStatus.Requesting,
  );
  const [hasProcessing, setHasProcessing] = useState<boolean | null>(null);

  const [videoDevices, setVideoDevices] = useState<MediaDeviceInfo[]>([]);
  const [audioInputDevices, setAudioInputDevices] = useState<MediaDeviceInfo[]>(
    [],
  );
  const [audioOutputDevices, setAudioOutputDevices] = useState<
    MediaDeviceInfo[]
  >([]);

  const setAvailableDevices = (
    audioInputDevices: MediaDeviceInfo[],
    audioOutputDevices: MediaDeviceInfo[],
    videoDevices: MediaDeviceInfo[],
  ) => {
    setAudioInputDevices(audioInputDevices);
    setAudioOutputDevices(audioOutputDevices);
    setVideoDevices(videoDevices);
  };

  return (
    <LocalMediaContext.Provider
      value={{
        localStream,
        status,
        hasProcessing,
        availableDevices: {
          audioInputDevices,
          audioOutputDevices,
          videoDevices,
        },
        setLocalStream,
        setStatus,
        setHasProcessing,
        setAvailableDevices,
      }}
    >
      {children}
    </LocalMediaContext.Provider>
  );
};

export const useLocalMediaContext = (): LocalMediaContextType => {
  const context = useContext(LocalMediaContext);
  if (!context) {
    throw new Error('useLocalMedia must be used within a LocalMediaProvider');
  }
  return context;
};
