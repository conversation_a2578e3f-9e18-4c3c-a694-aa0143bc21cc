import { useRef } from 'react';
import {
  useLocalMediaAudioDevices,
  useLocalMediaConstraints,
  useLocalMediaDevices,
  useLocalMediaInit,
  useLocalMediaProcessing,
  useLocalMediaVideoDevices,
} from './hooks';

export const useLocalMedia = () => {
  const localStreamRef = useRef<MediaStream | null>(null);

  useLocalMediaInit(localStreamRef);

  useLocalMediaAudioDevices(localStreamRef);
  useLocalMediaVideoDevices(localStreamRef);
  useLocalMediaDevices(localStreamRef);

  useLocalMediaConstraints(localStreamRef);
  useLocalMediaProcessing();
};
