import { logger } from '@/shared';
import {
  Room,
  RoomEvent,
  RemoteAudioTrack,
  ConnectionQuality,
} from 'livekit-client';

export class NetworkAdaptiveJitterBuffer {
  private static instance: NetworkAdaptiveJitterBuffer | null = null;

  static getInstance(): NetworkAdaptiveJitterBuffer {
    if (!this.instance) {
      this.instance = new NetworkAdaptiveJitterBuffer();
    }
    return this.instance;
  }

  private room: Room | null = null;
  private tracks: Map<string, RemoteAudioTrack> = new Map();
  private currentBufferMs: number = 300; // Default 300ms for music

  private constructor() {}

  /**
   * Initialize with LiveKit room
   */
  initialize(room: Room): void {
    this.room = room;
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    if (!this.room) return;

    // Track when audio tracks are subscribed
    this.room.on(RoomEvent.TrackSubscribed, (track, _, participant) => {
      if (track.kind === 'audio') {
        const audioTrack = track as RemoteAudioTrack;
        const trackId = `${participant.sid}_${track.sid}`;

        logger.info(
          'Remote audio processing: Audio track subscribed for jitter buffer',
          {
            participant_sid: participant.sid,
            track_sid: track.sid ?? '',
            track_id: trackId,
            initial_buffer_ms: this.currentBufferMs,
            event_type: 'remote_audio_processing_jitter_buffer_add',
          },
        );

        this.tracks.set(trackId, audioTrack);
        this.setJitterBuffer(audioTrack, this.currentBufferMs);
      }
    });

    // Clean up when tracks are unsubscribed
    this.room.on(RoomEvent.TrackUnsubscribed, (track, _, participant) => {
      if (track.kind === 'audio') {
        const trackId = `${participant.sid}_${track.sid}`;

        logger.info(
          'Remote audio processing: Audio track unsubscribed from jitter buffer',
          {
            participant_sid: participant.sid,
            track_sid: track.sid ?? '',
            track_id: trackId,
            event_type: 'remote_audio_processing_jitter_buffer_remove',
          },
        );

        this.tracks.delete(trackId);
      }
    });

    // Adjust buffer based on connection quality changes
    this.room.on(RoomEvent.ConnectionQualityChanged, (quality, participant) => {
      if (participant && participant.sid !== this.room?.localParticipant.sid) {
        this.adjustBufferForQuality(quality, participant.sid);
      }
    });
  }

  /**
   * Set jitter buffer using modern API or fallback
   */
  private setJitterBuffer(track: RemoteAudioTrack, targetMs: number): boolean {
    logger.info('Remote audio processing: Setting jitter buffer', {
      track_sid: track.sid || 'unknown',
      target_ms: targetMs,
      has_receiver: !!track.receiver,
      event_type: 'remote_audio_processing_jitter_buffer_set',
    });

    if (!track.receiver) {
      console.warn('No receiver available for jitter buffer control');
      return false;
    }

    let success = false;

    // Try modern jitterBufferTarget API first (more precise control)
    if ('jitterBufferTarget' in track.receiver) {
      try {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (track.receiver as any).jitterBufferTarget = targetMs;
        console.log(`✓ Set jitterBufferTarget to ${targetMs}ms`);
        success = true;
      } catch (error) {
        console.warn('Failed to set jitterBufferTarget:', error);
      }
    }

    // Always also set playoutDelayHint as fallback/supplement
    try {
      track.setPlayoutDelay(targetMs / 1000);
      console.log(`✓ Set playoutDelayHint to ${targetMs}ms`);
      success = true;
    } catch (error) {
      console.warn('Failed to set playoutDelayHint:', error);
    }

    if (success) {
      logger.info('Remote audio processing: Jitter buffer set successfully', {
        track_sid: track.sid || 'unknown',
        target_ms: targetMs,
        event_type: 'remote_audio_processing_jitter_buffer_set_success',
      });
    } else {
      logger.warn('Remote audio processing: Failed to set jitter buffer', {
        track_sid: track.sid || 'unknown',
        target_ms: targetMs,
        event_type: 'remote_audio_processing_jitter_buffer_set_failed',
      });
    }

    return success;
  }

  /**
   * Adjust buffer based on connection quality
   */
  private adjustBufferForQuality(
    quality: ConnectionQuality,
    participantSid: string,
  ): void {
    let targetBuffer: number;
    switch (quality) {
      case ConnectionQuality.Poor:
        targetBuffer = 600; // High buffer for poor connection
        break;
      case ConnectionQuality.Good:
        targetBuffer = 400; // Medium buffer for good connection
        break;
      case ConnectionQuality.Excellent:
        targetBuffer = 300; // Lower buffer for excellent connection
        break;
      default:
        targetBuffer = 450; // Safe default
        break;
    }

    // Find tracks for this participant and update their buffers
    this.tracks.forEach((track, trackId) => {
      if (trackId.startsWith(participantSid)) {
        this.setJitterBuffer(track, targetBuffer);
      }
    });

    this.currentBufferMs = targetBuffer;

    logger.info(
      'Remote audio processing: Adjusting jitter buffer for quality',
      {
        participant_sid: participantSid,
        connection_quality: quality,
        new_buffer_ms: this.currentBufferMs,
        event_type: 'remote_audio_processing_jitter_buffer_adjust',
      },
    );

    console.log(
      `Connection quality ${quality} for ${participantSid} -> buffer adjusted to ${targetBuffer}ms`,
    );
  }

  /**
   * Manually set buffer size for all tracks (emergency function)
   */
  setFixedBuffer(bufferMs: number): void {
    this.currentBufferMs = bufferMs;

    this.tracks.forEach((track, _) => {
      this.setJitterBuffer(track, bufferMs);
    });
  }

  getCurrentBuffer(): number {
    return this.currentBufferMs;
  }

  destroy(): void {
    if (this.room) {
      this.room.removeAllListeners();
      this.room = null;
    }
    this.tracks.clear();
    NetworkAdaptiveJitterBuffer.instance = null;
  }
}

export const networkAdaptiveJitterBuffer =
  NetworkAdaptiveJitterBuffer.getInstance();
