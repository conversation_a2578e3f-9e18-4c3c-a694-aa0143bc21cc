import { logger } from '@/shared';
import { LocalAudioMixer } from './local-audio-mixer';
import { RemoteAudioMixer } from './remote-audio-mixer';
import { AECController } from './aec';
import { AudioContextProvider } from '../audio-context';

interface TestWindow extends Window {
  testMode?: boolean;
  centralAudioProcessing?: CentralAudioProcessing;
}

const isTestEnvironment = (): boolean => {
  if (typeof window === 'undefined') return false;

  const testWindow = window as TestWindow;

  return (
    window.location.href.includes('localhost:5173') ||
    window.location.href.includes('test') ||
    new URLSearchParams(window.location.search).has('test') ||
    testWindow.testMode === true ||
    window.location.href.includes('playwright') ||
    (process.env.NODE_ENV === 'development' &&
      window.location.href.includes('localhost'))
  );
};

const exposeAudioComponentsForTesting = (
  centralAudioProcessing: CentralAudioProcessing,
): void => {
  if (!isTestEnvironment()) return;

  try {
    const testWindow = window as TestWindow;
    testWindow.centralAudioProcessing = centralAudioProcessing;
  } catch (error) {
    logger.warn('Failed to expose audio components for testing', {
      error: error instanceof Error ? error.message : String(error),
      event_type: 'audio_components_exposure_failed',
    });
  }
};

class CentralAudioProcessing {
  private static instance: CentralAudioProcessing | null = null;

  public static getInstance() {
    if (this.instance === null) {
      this.instance = new CentralAudioProcessing();
    }
    return this.instance;
  }

  private audioContextProvider!: AudioContextProvider;

  isInitialized(): boolean {
    return !!this.audioContextProvider && this.audioContextProvider.isValid();
  }

  getAudioContextProvider() {
    return this.audioContextProvider;
  }

  async createAudioContext(): Promise<boolean> {
    try {
      this.audioContextProvider = await AudioContextProvider.create();
      const audioContext = this.audioContextProvider.getAudioContext();

      logger.info('CentralAudioProcessing context created', {
        state: audioContext.state,
        event_type: 'central_audio_processing_context_created',
      });

      return audioContext.state === 'running';
    } catch (error) {
      logger.error('CentralAudioProcessing context creation failed', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'central_audio_processing_context_error',
      });
      throw error;
    }
  }

  async resumeAudioContext() {
    logger.info('Audio context resumed', {
      event_type: 'central_audio_processing_context_resumed',
    });
    await this.audioContextProvider.resume();
  }

  private localAudioMixer!: LocalAudioMixer;
  private remoteAudioMixer!: RemoteAudioMixer;

  private aecController!: AECController;

  async initialize() {
    if (!this.audioContextProvider.isValid()) {
      logger.warn('Cannot setup audio chain: AudioContext is not valid');
      return;
    }

    this.localAudioMixer = new LocalAudioMixer(this.audioContextProvider);
    this.remoteAudioMixer = new RemoteAudioMixer(this.audioContextProvider);

    this.aecController = new AECController(
      this.audioContextProvider,
      this.localAudioMixer,
      this.remoteAudioMixer,
    );

    exposeAudioComponentsForTesting(this);
  }

  getLocalAudioMixer() {
    return this.localAudioMixer;
  }

  getRemoteAudioMixer() {
    if (isTestEnvironment()) {
      exposeAudioComponentsForTesting(this);
    }

    return this.remoteAudioMixer;
  }

  getAECController() {
    return this.aecController;
  }

  async destroy() {
    if (!this.isInitialized()) return;

    if (this.audioContextProvider.isValid()) {
      await this.audioContextProvider.close();
    }

    await this.remoteAudioMixer.destroy();
    await this.aecController.disableAEC();
    CentralAudioProcessing.instance = null;
  }
}

export const centralAudioProcessing = CentralAudioProcessing.getInstance();
