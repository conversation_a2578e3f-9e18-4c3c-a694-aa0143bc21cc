/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, beforeEach, afterEach, expect, vi } from 'vitest';
import { ParticipantNode } from './ParticipantNode';
import { RemoteAudioSourceFactory } from './remote-audio-source';
import type { RemoteAudioSourceStrategy } from '../interfaces';
import { AudioContextProvider } from '../../audio-context';

describe('ParticipantNode', () => {
  let fakeProvider: Awaited<ReturnType<typeof AudioContextProvider.create>>;
  let masterNode: GainNode;
  let mockStream: MediaStream;
  let mockStrategy: RemoteAudioSourceStrategy;

  beforeEach(async () => {
    // Use the provider’s test AudioContext so CustomAudioAnalyser can be constructed
    fakeProvider = await AudioContextProvider.create();
    masterNode = fakeProvider.getAudioContext().createGain();
    mockStream = new MediaStream();

    // Strategy mock used by factory
    mockStrategy = {
      create: vi
        .fn()
        .mockResolvedValue(fakeProvider.getAudioContext().createGain()),
      destroy: vi.fn(),
    };

    vi.spyOn(RemoteAudioSourceFactory, 'select').mockReturnValue(mockStrategy);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('creates participant chain with gain=1', async () => {
    const node = new ParticipantNode(fakeProvider, masterNode);
    await node.create(mockStream);

    const gain = node.getGainNode();
    expect(gain).toBeDefined();
    expect(typeof gain.gain.value).toBe('number');
    expect(gain.gain.value).toBeCloseTo(1);
    // analyser is created and connected
    expect(node.getCustomAnalyser()).toBeDefined();
  });

  it('clamps gain in setGain()', async () => {
    const node = new ParticipantNode(fakeProvider, masterNode);
    await node.create(mockStream);

    const param = node.getGainNode().gain;
    const setSpy = vi.spyOn(param, 'setValueAtTime');

    node.setGain(1.5);
    expect(setSpy).toHaveBeenCalledWith(1.5, expect.any(Number));

    node.setGain(3); // clamp to 2
    expect(setSpy).toHaveBeenCalledWith(2, expect.any(Number));

    node.setGain(-1); // clamp to 0
    expect(setSpy).toHaveBeenCalledWith(0, expect.any(Number));
  });

  it('updates stream: fades out old source, swaps, and fades in', async () => {
    const node = new ParticipantNode(fakeProvider, masterNode);
    await node.create(mockStream);

    const oldSource = (node as any)['source'] as AudioNode;
    const disconnectSpy = vi.spyOn(oldSource, 'disconnect');

    const audioCtx = fakeProvider.getAudioContext();
    const gainParam = node.getGainNode().gain;
    const setSpy = vi.spyOn(gainParam, 'setValueAtTime');
    const rampSpy = vi.spyOn(gainParam, 'linearRampToValueAtTime');

    // Schedule a gain; note: AudioParam.value itself won't change
    node.setGain(1.2);

    const newStream = new MediaStream();
    await node.update(newStream);

    expect(disconnectSpy).toHaveBeenCalled();

    // fade out
    expect(rampSpy).toHaveBeenCalledWith(0, audioCtx.currentTime + 0.1);
    expect(setSpy).toHaveBeenCalledWith(0, audioCtx.currentTime);

    // fade in to whatever .value currently is (usually 1)
    const expectedTarget = gainParam.value; // this is what the code reads
    expect(rampSpy).toHaveBeenCalledWith(
      expectedTarget,
      audioCtx.currentTime + 0.1,
    );
  });

  it('destroy disconnects source and destroys strategy', async () => {
    const node = new ParticipantNode(fakeProvider, masterNode);
    await node.create(mockStream);

    const source = (node as any)['source'] as AudioNode;
    const disconnectSpy = vi.spyOn(source, 'disconnect');

    await node.destroy();

    expect(disconnectSpy).toHaveBeenCalled();
    expect(mockStrategy.destroy).toHaveBeenCalled();
  });

  it('reads audio level via CustomAudioAnalyser', async () => {
    const node = new ParticipantNode(fakeProvider, masterNode);
    await node.create(mockStream);

    const analyser = node.getCustomAnalyser();

    // Stub analyser data
    const freqSpy = vi
      .spyOn(analyser, 'getByteFrequencyData')
      .mockImplementation((buf) => {
        buf.fill(128);
      });

    vi.spyOn(analyser, 'getByteTimeDomainData').mockImplementation((buf) => {
      for (let i = 0; i < buf.length; i++) {
        buf[i] = i % 2 === 0 ? 200 : 56; // peak 200
      }
    });

    // Ensure we’re past the cache interval so a real compute happens
    let now = 100;
    const nowSpy = vi.spyOn(performance, 'now').mockImplementation(() => now);

    const level = analyser.getAudioLevel(); // compute
    expect(freqSpy).toHaveBeenCalled(); // ensure our stub ran
    expect(level).toBeGreaterThanOrEqual(0); // normalized range
    expect(level).toBeLessThanOrEqual(1);

    now = 200; // advance so peak recomputes
    const peak = analyser.getPeakLevel();
    expect(peak).toBeCloseTo((200 - 128) / 128, 3);

    nowSpy.mockRestore();
  });
});
