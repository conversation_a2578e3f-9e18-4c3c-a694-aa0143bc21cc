import { AudioMetrics, KalmanState } from '../../interfaces';

export class AudioMetricsCalculations {
  private static readonly EMA_ALPHA = 0.15; // EMA smoothing factor

  private static calculateMedian(samples: number[]): number {
    if (samples.length === 0) return 0;

    const sorted = [...samples].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);

    if (sorted.length % 2 === 0) {
      return (sorted[mid - 1] + sorted[mid]) / 2;
    } else {
      return sorted[mid];
    }
  }

  private static calculateMAD(samples: number[]): number {
    const median = this.calculateMedian(samples);
    const deviations = samples.map((x) => Math.abs(x - median));
    return this.calculateMedian(deviations);
  }

  private static filterOutliers(samples: number[]): number[] {
    const median = this.calculateMedian(samples);
    const mad = this.calculateMAD(samples);

    // Handle case where MAD is 0 (all values identical)
    if (mad === 0) return samples;

    // Modified Z-score using MAD
    return samples.map((sample) => {
      const modifiedZScore = (sample - median) / (1.4826 * mad);
      return Math.abs(modifiedZScore) > 3.0 ? median : sample;
    });
  }

  static calculateAudioMetrics(
    emaState: number,
    kalmanState: KalmanState,
    noiseFloor: number,
    samples: number[],
  ): AudioMetrics {
    const filteredSamples = this.filterOutliers(samples);

    // RMS calculation with sliding window
    const rms = this.calculateRMS(filteredSamples);

    // EMA calculation
    const median = this.calculateMedian(filteredSamples);
    const ema = this.calculateEMA(emaState, median);

    // Kalman filter
    const kalman = this.calculateKalman(kalmanState, rms);

    // Peak detection
    const peak = Math.max(...samples);

    // Variance calculation
    const mad = this.calculateMAD(filteredSamples);
    const variance = Math.pow(mad * 1.4826, 2);

    // Signal-to-noise ratio
    const snr = this.calculateSNR(rms, noiseFloor);

    return {
      rms,
      ema,
      kalman,
      peak,
      variance,
      snr,
      median,
    };
  }

  private static calculateRMS(samples: number[]): number {
    const sorted = [...samples].sort((a, b) => a - b);

    const trimStart = Math.floor(sorted.length * 0.1);
    const trimEnd = Math.floor(sorted.length * 0.9);
    const trimmed = sorted.slice(trimStart, trimEnd);

    const sumSquares = trimmed.reduce((acc, val) => acc + val * val, 0);
    return Math.sqrt(sumSquares / trimmed.length);
  }

  private static calculateEMA(ema: number, currentValue: number): number {
    return this.EMA_ALPHA * currentValue + (1 - this.EMA_ALPHA) * ema;
  }

  private static calculateKalman(
    kalman: KalmanState,
    measurement: number,
  ): number {
    // Prediction step
    // x_pred = x (assuming constant audio level)
    const P_pred = kalman.P + kalman.Q;

    // Update step
    kalman.K = P_pred / (P_pred + kalman.R);
    kalman.x = kalman.x + kalman.K * (measurement - kalman.x);
    kalman.P = (1 - kalman.K) * P_pred;

    return kalman.x;
  }

  private static calculateSNR(signal: number, noise: number): number {
    if (noise <= 0) return 100; // Very high SNR
    return 20 * Math.log10(signal / noise);
  }
}
