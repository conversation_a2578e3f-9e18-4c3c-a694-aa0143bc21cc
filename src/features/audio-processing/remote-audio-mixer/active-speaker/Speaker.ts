import { logger } from '@/shared';
import { AudioMetrics, CircularBuffer, KalmanState } from '../../interfaces';
import { ParticipantNode } from '../ParticipantNode';
import { AudioMetricsCalculations } from './AudioMetricsCalculations';

export class Speaker {
  private sid: string;
  private node: ParticipantNode;

  private isCalibrated: boolean = false;
  private calibrationSamples: number = 0;

  // High-frequency sampling buffers (10ms samples)
  private sampleBuffer: CircularBuffer;

  // Different aggregation methods
  private emaState: number = 0;
  private kalmanState: KalmanState;

  // Adaptive thresholding
  private noiseFloor: number = 0.001;
  private backgroundNoise: number = 0.001;
  private adaptiveThreshold: number = 0.05;

  // Historical metrics
  private recentMetrics: AudioMetrics[] = [];
  private speakingHistory: boolean[] = [];

  // Timing
  private sampleCount: number = 0;
  private samplingTimer: NodeJS.Timeout | null = null;
  private computeTimer: NodeJS.Timeout | null = null;

  // Timing configuration
  private readonly SAMPLE_INTERVAL = 10; // 10ms sampling
  private readonly COMPUTE_INTERVAL = 175; // 175ms computation interval
  private readonly CALIBRATION_SAMPLES = 50; // 500ms calibration period

  private readonly BUFFER_SIZE = 32; // Store last 320ms of samples (32 * 10ms)
  private readonly KALMAN_Q = 0.001; // Process noise
  private readonly KALMAN_R = 0.1; // Measurement noise

  constructor(sid: string, node: ParticipantNode) {
    this.sid = sid;
    this.node = node;
    this.sampleBuffer = this.createCircularBuffer(this.BUFFER_SIZE);
    this.kalmanState = {
      x: 0,
      P: 1,
      Q: this.KALMAN_Q,
      R: this.KALMAN_R,
      K: 0,
    };
  }

  isInitialized(): boolean {
    return this.isCalibrated || this.speakingHistory.length !== 0;
  }

  getSpeakingHistory(): boolean[] {
    return this.speakingHistory;
  }

  getRecentMetrics(): AudioMetrics[] {
    return this.recentMetrics;
  }

  getAdaptiveTreshold(): number {
    return this.adaptiveThreshold;
  }

  startMonitoringParticipant(): void {
    // High-frequency sampling (10ms)
    this.samplingTimer = setInterval(() => {
      this.collectAudioSample();
    }, this.SAMPLE_INTERVAL);

    // Lower-frequency computation (175ms)
    this.computeTimer = setInterval(() => {
      this.computeAudioMetrics();
    }, this.COMPUTE_INTERVAL);
  }

  stopMonitoringParticipant(): void {
    if (this.samplingTimer) {
      clearInterval(this.samplingTimer);
      this.samplingTimer = null;
    }

    if (this.computeTimer) {
      clearInterval(this.computeTimer);
      this.computeTimer = null;
    }
  }

  private collectAudioSample(): void {
    try {
      const rawLevel = this.node.getCustomAnalyser().getPeakLevel();
      if (!isFinite(rawLevel) || rawLevel < 0) return;

      // Basic normalization
      const normalizedLevel = Math.min(rawLevel, 1.0);

      // Add to circular buffer
      this.addToCircularBuffer(this.sampleBuffer, normalizedLevel);
      this.sampleCount++;

      // Update noise floor adaptation
      this.updateNoiseFloor(normalizedLevel);
    } catch (error) {
      console.warn(`Failed to collect audio sample for ${this.sid}:`, error);
    }
  }

  private computeAudioMetrics(): void {
    const samples = this.getBufferValues(this.sampleBuffer);
    if (samples.length === 0) return;

    // Compute comprehensive audio metrics
    const metrics = AudioMetricsCalculations.calculateAudioMetrics(
      this.emaState,
      this.kalmanState,
      this.noiseFloor,
      samples,
    );

    // Store metrics history
    this.recentMetrics.push(metrics);
    if (this.recentMetrics.length > 20) {
      // Keep last 20 computations (3.5 seconds)
      this.recentMetrics.shift();
    }

    // Determine if participant is speaking using ensemble approach
    const isSpeaking = this.determineIfSpeaking(metrics);

    // Update speaking history
    this.speakingHistory.push(isSpeaking);
    if (this.speakingHistory.length > 10) {
      // Keep last 10 decisions (1.75 seconds)
      this.speakingHistory.shift();
    }

    // Update adaptive threshold
    this.updateAdaptiveThreshold(metrics, isSpeaking);

    // Calibration phase
    if (!this.isCalibrated) {
      this.calibrationSamples++;
      if (this.calibrationSamples >= this.CALIBRATION_SAMPLES) {
        this.calibrateParticipant();
      }
    }

    logger.info('ActiveSpeakerManager: Computed audio metrics', {
      participant_sid: this.sid,
      metrics: JSON.stringify({
        rms: metrics.rms.toFixed(4),
        ema: metrics.ema.toFixed(4),
        kalman: metrics.kalman.toFixed(4),
        snr: metrics.snr.toFixed(2),
      }),
      is_speaking: isSpeaking,
      adaptive_threshold: this.adaptiveThreshold.toFixed(4),
      event_type: 'enhanced_speaker_metrics',
    });
  }

  private determineIfSpeaking(metrics: AudioMetrics): boolean {
    if (!this.isCalibrated) {
      // Use simple threshold during calibration
      return metrics.rms > this.adaptiveThreshold;
    }

    const indicators = {
      rms: metrics.rms > this.adaptiveThreshold * 0.7,
      ema: metrics.ema > this.adaptiveThreshold * 0.5,
      kalman: metrics.kalman > this.adaptiveThreshold * 0.6,
      variance: metrics.variance > this.adaptiveThreshold * 0.3,
    };

    // Weighted voting
    const weights = {
      rms: 0.3,
      variance: 0.25,
      kalman: 0.25,
      ema: 0.2,
    };

    const score = Object.entries(indicators).reduce((acc, [key, value]) => {
      return acc + (value ? weights[key as keyof typeof weights] : 0);
    }, 0);

    const recentSpeaking = this.speakingHistory.slice(-3).some((s) => s);
    const hangoverThreshold = recentSpeaking ? 0.25 : 0.35;
    return score > hangoverThreshold;
  }

  private updateAdaptiveThreshold(
    metrics: AudioMetrics,
    isSpeaking: boolean,
  ): void {
    if (!this.isCalibrated) return;

    // Update background noise estimate during silence
    if (!isSpeaking) {
      const adaptationRate = 0.05;
      this.backgroundNoise =
        this.backgroundNoise * (1 - adaptationRate) +
        metrics.rms * adaptationRate;
    }

    // Adaptive threshold: μ_noise + k*σ_noise
    const k = 1.5; // 1.5 standard deviations above noise
    const noiseVariance = Math.max(0.001, metrics.variance * 0.1);
    this.adaptiveThreshold =
      this.backgroundNoise + k * Math.sqrt(noiseVariance);

    // Clamp threshold to reasonable bounds
    this.adaptiveThreshold = Math.max(
      0.01,
      Math.min(0.3, this.adaptiveThreshold),
    );
  }

  private calibrateParticipant(): void {
    if (this.recentMetrics.length < 10) return;

    // Analyze historical metrics for calibration
    const rmsValues = this.recentMetrics.map((m) => m.rms);
    rmsValues.sort((a, b) => a - b);

    // Estimate noise floor as 15th percentile
    const noiseFloorIdx = Math.floor(rmsValues.length * 0.15);
    this.backgroundNoise = rmsValues[noiseFloorIdx];

    // Initial adaptive threshold
    this.adaptiveThreshold = this.backgroundNoise * 3;

    this.isCalibrated = true;

    logger.info('ActiveSpeakerManager: Participant calibrated', {
      participant_sid: this.sid,
      background_noise: this.backgroundNoise.toFixed(4),
      initial_threshold: this.adaptiveThreshold.toFixed(4),
      event_type: 'speaker_calibrated',
    });
  }

  // Circular buffer helpers
  private createCircularBuffer(size: number): CircularBuffer {
    return {
      data: new Array(size).fill(0),
      size,
      head: 0,
      count: 0,
    };
  }

  private addToCircularBuffer(buffer: CircularBuffer, value: number): void {
    buffer.data[buffer.head] = value;
    buffer.head = (buffer.head + 1) % buffer.size;
    buffer.count = Math.min(buffer.count + 1, buffer.size);
  }

  private getBufferValues(buffer: CircularBuffer): number[] {
    if (buffer.count === 0) return [];

    const values: number[] = [];
    let index = buffer.count < buffer.size ? 0 : buffer.head;

    for (let i = 0; i < buffer.count; i++) {
      values.push(buffer.data[index]);
      index = (index + 1) % buffer.size;
    }

    return values;
  }

  // Noise floor helpers
  private updateNoiseFloor(level: number): void {
    // Slowly adapt noise floor (1ms time constant)
    const adaptationRate = 0.001;
    this.noiseFloor = Math.min(
      level,
      this.noiseFloor * (1 + adaptationRate) + level * adaptationRate,
    );
  }
}
