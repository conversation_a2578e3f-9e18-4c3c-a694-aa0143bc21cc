import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { Speaker } from './Speaker';
import { ParticipantNode } from '../ParticipantNode';
import { AudioMetricsCalculations } from './AudioMetricsCalculations';
import { AudioMetrics, KalmanState } from '../../interfaces';

vi.useFakeTimers();

vi.mock('./AudioMetricsCalculations', async () => {
  return {
    AudioMetricsCalculations: {
      calculateAudioMetrics: vi.fn(
        (
          _ema: number,
          _kalman: KalmanState,
          _noiseFloor: number,
          _samples: number[],
        ): AudioMetrics => ({
          rms: 0.06,
          ema: 0.04,
          kalman: 0.04,
          snr: 4,
          variance: 0.02,
          median: 0.035,
          peak: 0.06,
        }),
      ),
    },
  };
});

describe('Speaker', () => {
  let mockNode: ParticipantNode;
  let speaker: Speaker;

  beforeEach(() => {
    const mockAnalyser = {
      getPeakLevel: vi.fn().mockReturnValue(0.05),
    };

    mockNode = {
      getCustomAnalyser: vi.fn(() => mockAnalyser),
    } as unknown as ParticipantNode;

    speaker = new Speaker('participant-1', mockNode);
  });

  afterEach(() => {
    speaker.stopMonitoringParticipant();
    vi.clearAllTimers();
    vi.resetAllMocks();
  });

  it('initializes correctly', () => {
    expect(speaker.getSpeakingHistory()).toEqual([]);
    expect(speaker.getRecentMetrics()).toEqual([]);
  });

  it('starts and stops monitoring', () => {
    const setSpy = vi.spyOn(global, 'setInterval');
    const clearSpy = vi.spyOn(global, 'clearInterval');

    speaker.startMonitoringParticipant();
    expect(setSpy).toHaveBeenCalledTimes(2);

    speaker.stopMonitoringParticipant();
    expect(clearSpy).toHaveBeenCalledTimes(2);
  });

  it('collects audio sample and stores it in buffer', () => {
    speaker['collectAudioSample']();
    const values = speaker['getBufferValues'](speaker['sampleBuffer']);
    expect(values.length).toBe(1);
    expect(values[0]).toBeCloseTo(0.05);
  });

  it('computes audio metrics and updates history', () => {
    speaker['addToCircularBuffer'](speaker['sampleBuffer'], 0.05);
    speaker['computeAudioMetrics']();
    expect(speaker.getRecentMetrics().length).toBe(1);
    expect(speaker.getSpeakingHistory().length).toBe(1);
  });

  it('calibrates correctly when enough metrics are collected', () => {
    for (let i = 0; i < 10; i++) {
      speaker['recentMetrics'].push({
        rms: 0.01 * (i + 1),
        ema: 0,
        kalman: 0,
        snr: 0,
        variance: 0,
        median: 0,
        peak: 0,
      });
    }
    speaker['calibrationSamples'] = 50;
    speaker['calibrateParticipant']();

    expect(speaker['isCalibrated']).toBe(true);
    expect(speaker.getAdaptiveTreshold()).toBeGreaterThan(0);
  });

  it('uses ensemble logic correctly to detect speaking', () => {
    speaker['isCalibrated'] = true;
    speaker['adaptiveThreshold'] = 0.05;

    const metrics = AudioMetricsCalculations.calculateAudioMetrics(
      0,
      { x: 0, P: 1, Q: 0.001, R: 0.1, K: 0 },
      0.001,
      [0.05, 0.05],
    );

    const isSpeaking = speaker['determineIfSpeaking'](metrics);
    expect(isSpeaking).toBe(true);
  });

  it('runs end-to-end with timers', () => {
    speaker.startMonitoringParticipant();
    vi.advanceTimersByTime(600); // enough to trigger several intervals

    expect(speaker.getRecentMetrics().length).toBeGreaterThan(0);
    expect(speaker.getSpeakingHistory().length).toBeGreaterThan(0);
  });

  it('handles errors in getPeakLevel gracefully', () => {
    const analyser = mockNode.getCustomAnalyser();
    (analyser.getPeakLevel as ReturnType<typeof vi.fn>).mockImplementationOnce(
      () => {
        throw new Error('Simulated error');
      },
    );

    expect(() => speaker['collectAudioSample']()).not.toThrow();
  });

  it('detects no speech during calibration if RMS below threshold', () => {
    speaker['isCalibrated'] = false;
    speaker['adaptiveThreshold'] = 0.1;

    const metrics: AudioMetrics = {
      rms: 0.05,
      ema: 0,
      kalman: 0,
      snr: 0,
      variance: 0,
      median: 0,
      peak: 0,
    };
    const isSpeaking = speaker['determineIfSpeaking'](metrics);
    expect(isSpeaking).toBe(false);
  });

  it('detects speech during calibration if RMS above threshold', () => {
    speaker['isCalibrated'] = false;
    speaker['adaptiveThreshold'] = 0.05;

    const metrics: AudioMetrics = {
      rms: 0.1,
      ema: 0,
      kalman: 0,
      snr: 0,
      variance: 0,
      median: 0,
      peak: 0,
    };
    const isSpeaking = speaker['determineIfSpeaking'](metrics);
    expect(isSpeaking).toBe(true);
  });
});
