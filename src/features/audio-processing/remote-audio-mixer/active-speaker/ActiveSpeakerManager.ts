import { logger } from '@/shared';
import { Speaker } from './Speaker';
import { ParticipantNode } from '../ParticipantNode';

export class ActiveSpeakerManager {
  private speakers: Map<string, Speaker> = new Map();
  private activeSpeakers: string[] = [];
  private isEnabled: boolean = true;

  // Timing configuration
  private timeInterval: NodeJS.Timeout | null = null;
  private readonly COMPUTE_INTERVAL = 175; // 175ms computation interval

  constructor(speakers: Map<string, ParticipantNode>) {
    speakers.forEach((participant, sid) => {
      const speaker = new Speaker(sid, participant);
      this.speakers.set(sid, speaker);
      speaker.startMonitoringParticipant();
    });
  }

  addParticipant(sid: string, participant: ParticipantNode): void {
    const speaker = new Speaker(sid, participant);
    this.speakers.set(sid, speaker);
    speaker.startMonitoringParticipant();

    logger.info('ActiveSpeakerManager: Added participant', {
      participant_sid: sid,
      event_type: 'speaker_manager_add_participant',
    });
  }

  removeParticipant(sid: string): void {
    const speaker = this.speakers.get(sid);
    speaker?.stopMonitoringParticipant();

    this.speakers.delete(sid);
    this.activeSpeakers = this.activeSpeakers.filter(
      (speaker) => speaker !== sid,
    );

    logger.info('ActiveSpeakerManager: Removed participant', {
      participant_sid: sid,
      event_type: 'speaker_manager_remove_participant',
    });
  }

  startMonitoring(callback: (speakers: string[]) => void): string {
    const id = crypto.randomUUID();

    // Aggregate speaker decisions every 175ms
    this.timeInterval = setInterval(() => {
      this.aggregateSpeakerDecisions(callback);
    }, this.COMPUTE_INTERVAL);

    return id;
  }

  private aggregateSpeakerDecisions(
    callback: (speakers: string[]) => void,
  ): void {
    if (!this.isEnabled) return;

    const currentSpeakers: Array<{ sid: string; confidence: number }> = [];

    this.speakers.forEach((speaker, sid) => {
      if (!speaker.isInitialized()) return;

      const recentDecisions = speaker.getSpeakingHistory().slice(-8); // Last 1.4 seconds instead of 875ms
      const speakingRatio =
        recentDecisions.filter((s) => s).length / recentDecisions.length;

      if (speakingRatio > 0.25) {
        const latestMetrics =
          speaker.getRecentMetrics()[speaker.getRecentMetrics().length - 1];
        const confidence = latestMetrics
          ? Math.min(1.0, latestMetrics.rms / speaker.getAdaptiveTreshold())
          : 0;

        currentSpeakers.push({ sid, confidence });
      }
    });

    // Sort by confidence and apply stickiness
    currentSpeakers.sort((a, b) => b.confidence - a.confidence);

    // Apply speaker stickiness logic
    const newActiveSpeakers = this.applySpeakerStickiness(
      currentSpeakers.map((s) => s.sid),
      currentSpeakers,
    );

    // Update if changed
    if (!this.arraysEqual(newActiveSpeakers, this.activeSpeakers)) {
      this.activeSpeakers = newActiveSpeakers;
      callback(this.activeSpeakers);

      logger.info('ActiveSpeakerManager: Active speakers updated', {
        active_speakers: JSON.stringify(this.activeSpeakers),
        speaker_confidences: JSON.stringify(
          currentSpeakers.map((s) => ({
            sid: s.sid,
            confidence: s.confidence.toFixed(3),
          })),
        ),
        event_type: 'active_speakers_updated',
      });
    }
  }

  private applySpeakerStickiness(
    newSpeakers: string[],
    speakerData: Array<{ sid: string; confidence: number }>,
  ): string[] {
    if (this.activeSpeakers.length === 0) return newSpeakers;

    const currentPrimary = this.activeSpeakers[0];
    const currentData = speakerData.find((s) => s.sid === currentPrimary);

    if (
      currentData &&
      newSpeakers.length > 0 &&
      newSpeakers[0] !== currentPrimary
    ) {
      const newPrimaryData = speakerData.find((s) => s.sid === newSpeakers[0]);

      // Apply stickiness if current speaker is still reasonably active
      if (
        currentData.confidence > 0.3 &&
        newPrimaryData &&
        newPrimaryData.confidence < currentData.confidence * 1.5
      ) {
        // Keep current speaker as primary
        const result = [currentPrimary];
        newSpeakers.forEach((sid) => {
          if (sid !== currentPrimary) result.push(sid);
        });
        return result;
      }
    }

    return newSpeakers;
  }

  private arraysEqual(a: string[], b: string[]): boolean {
    if (a.length !== b.length) return false;
    return a.every((val, index) => val === b[index]);
  }

  setIsEnabled(value: boolean): void {
    this.isEnabled = value;
  }

  destroy(): void {
    this.speakers.forEach((speaker, _) => {
      speaker.stopMonitoringParticipant();
    });

    if (this.timeInterval) {
      clearInterval(this.timeInterval);
      this.timeInterval = null;
    }

    this.speakers.clear();
    this.activeSpeakers = [];
  }
}
