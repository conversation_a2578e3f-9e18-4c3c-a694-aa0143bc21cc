/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, vi, expect, beforeEach, afterEach } from 'vitest';
import { ActiveSpeakerManager } from './ActiveSpeakerManager';
import { Speaker } from './Speaker';
import { ParticipantNode } from '../ParticipantNode';

vi.mock('./Speaker', () => {
  return {
    Speaker: vi.fn().mockImplementation((sid: string, _participant: any) => ({
      sid,
      startMonitoringParticipant: vi.fn(),
      stopMonitoringParticipant: vi.fn(),
      isInitialized: vi.fn().mockReturnValue(true),
      getSpeakingHistory: vi
        .fn()
        .mockReturnValue([true, false, true, true, false]),
      getRecentMetrics: vi.fn().mockReturnValue([{ rms: 0.3 }]),
      getAdaptiveTreshold: vi.fn().mockReturnValue(0.2),
    })),
  };
});

describe('ActiveSpeakerManager', () => {
  let manager: ActiveSpeakerManager;
  let mockCallback: ReturnType<typeof vi.fn>;

  const mockParticipant = {} as ParticipantNode;

  beforeEach(() => {
    const speakers = new Map([['user1', mockParticipant]]);
    manager = new ActiveSpeakerManager(speakers);
    mockCallback = vi.fn();
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
    manager.destroy();
  });

  it('adds and removes participant correctly', () => {
    const newParticipant = {} as ParticipantNode;
    manager.addParticipant('user2', newParticipant);
    expect(Speaker).toHaveBeenCalledWith('user2', newParticipant);

    manager.removeParticipant('user2');
    expect(Speaker).toHaveBeenCalledTimes(2);
  });

  it('starts monitoring and triggers callback with active speakers', () => {
    vi.useFakeTimers();
    manager.startMonitoring(mockCallback);

    vi.advanceTimersByTime(200);
    expect(mockCallback).toHaveBeenCalledWith(['user1']);
  });

  it('respects isEnabled flag', () => {
    vi.useFakeTimers();
    manager.setIsEnabled(false);
    manager.startMonitoring(mockCallback);

    vi.advanceTimersByTime(200);
    expect(mockCallback).not.toHaveBeenCalled();
  });

  it('cleans up properly on destroy', () => {
    vi.useFakeTimers(); // ensure timers are mocked
    const clearSpy = vi.spyOn(global, 'clearInterval');

    manager.startMonitoring(mockCallback);

    manager.destroy();
    expect(clearSpy).toHaveBeenCalled();
  });
});
