import { logger } from '@/shared';
import { RemoteAudioSourceStrategy } from '../interfaces';
import { RemoteAudioSourceFactory } from './remote-audio-source';
import { CustomAudioAnalyser } from '../custom-nodes';
import { AudioContextProvider } from '../../audio-context';

export class ParticipantNode {
  private audioContextProvider: AudioContextProvider;
  private masterNode: AudioNode;

  private stream!: MediaStream;
  private source!: AudioNode;
  private strategy!: RemoteAudioSourceStrategy;
  private gainNode!: GainNode;
  private analyser!: CustomAudioAnalyser;

  constructor(
    audioContextProvider: AudioContextProvider,
    masterNode: AudioNode,
  ) {
    this.audioContextProvider = audioContextProvider;
    this.masterNode = masterNode;
  }

  getParticipantNode(): ParticipantNode {
    return this;
  }

  getStream(): MediaStream {
    return this.stream;
  }

  getGainNode(): GainNode {
    return this.gainNode;
  }

  getCustomAnalyser(): CustomAudioAnalyser {
    return this.analyser;
  }

  async create(stream: MediaStream) {
    logger.info('Remote audio processing: Creating participant node', {
      stream_id: stream.id,
      event_type: 'remote_audio_processing_create',
    });

    const audioContext = this.audioContextProvider.getAudioContext();
    const source = await this.createWebAudioFromRemoteTrack(
      stream,
      audioContext,
    );

    this.stream = stream;
    this.source = source;

    // Individual gain control
    this.gainNode = audioContext.createGain();
    this.gainNode.gain.value = 1;

    this.analyser = new CustomAudioAnalyser(this.audioContextProvider);

    // Connect individual chain
    this.source
      .connect(this.gainNode)
      .connect(this.analyser)
      .connect(this.masterNode);

    logger.info(
      'Remote audio processing: Participant node created successfully',
      {
        stream_id: stream.id,
        event_type: 'remote_audio_processing_created',
      },
    );
  }

  async update(newStream: MediaStream): Promise<void> {
    logger.info('Remote audio processing: Updating participant node', {
      old_stream_id: this.stream?.id,
      new_stream_id: newStream.id,
      event_type: 'remote_audio_processing_update',
    });

    const audioContext = this.audioContextProvider.getAudioContext();

    // Store current settings before updating
    const currentGain = this.gainNode.gain.value;

    // Fade out current audio to prevent pops
    this.gainNode.gain.setValueAtTime(
      this.gainNode.gain.value,
      audioContext.currentTime,
    );
    this.gainNode.gain.linearRampToValueAtTime(
      0,
      audioContext.currentTime + 0.1,
    );

    await new Promise((resolve) => setTimeout(resolve, 100));
    this.source.disconnect();

    // Create new source from new stream
    const newSource = await this.createWebAudioFromRemoteTrack(
      newStream,
      audioContext,
    );

    this.stream = newStream;
    this.source = newSource;

    // Reconnect the audio chain with new source
    this.source.connect(this.gainNode);

    // Restore previous settings and fade in
    this.gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    this.gainNode.gain.linearRampToValueAtTime(
      currentGain,
      audioContext.currentTime + 0.1,
    );

    logger.info(
      'Remote audio processing: Participant node updated successfully',
      {
        new_stream_id: newStream.id,
        event_type: 'remote_audio_processing_updated',
      },
    );
  }

  private async createWebAudioFromRemoteTrack(
    stream: MediaStream,
    context: AudioContext,
  ): Promise<AudioNode> {
    const strategy = RemoteAudioSourceFactory.select();
    this.strategy = strategy;
    console.log('Using audio source strategy:', strategy.constructor.name);
    return await strategy.create(stream, context);
  }

  setGain(gain: number) {
    const audioContext = this.audioContextProvider.getAudioContext();
    const clampedGain = Math.max(0, Math.min(2, gain));
    this.gainNode.gain.setValueAtTime(clampedGain, audioContext.currentTime);
  }

  async destroy(): Promise<void> {
    logger.info('Remote audio processing: Destroying participant node', {
      stream_id: this.stream?.id,
      event_type: 'remote_audio_processing_destroy',
    });

    const audioContext = this.audioContextProvider.getAudioContext();

    // Fade out to prevent pops
    this.gainNode.gain.setValueAtTime(
      this.gainNode.gain.value,
      audioContext.currentTime,
    );
    this.gainNode.gain.linearRampToValueAtTime(
      0,
      audioContext.currentTime + 0.1,
    );

    await new Promise((resolve) => setTimeout(resolve, 100));
    this.source.disconnect();
    this.strategy.destroy();

    logger.info('Remote audio processing: Participant node destroyed', {
      event_type: 'remote_audio_processing_destroyed',
    });
  }
}
