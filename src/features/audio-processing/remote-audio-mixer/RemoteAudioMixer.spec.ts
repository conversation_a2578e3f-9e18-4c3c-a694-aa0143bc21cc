/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, beforeEach, afterEach, expect, vi } from 'vitest';
import { RemoteAudioMixer } from './RemoteAudioMixer';
import { ParticipantNode } from './ParticipantNode';
import { AudioContextProvider } from '../../audio-context';

// Create shared mock instance
const activeSpeakerManagerMock = {
  addParticipant: vi.fn(),
  removeParticipant: vi.fn(),
  destroy: vi.fn(),
  startMonitoring: vi.fn(),
  setIsEnabled: vi.fn(),
};

// Mock module and return shared mock
vi.mock('./active-speaker', () => {
  return {
    ActiveSpeakerManager: vi.fn(() => activeSpeakerManagerMock),
  };
});

export function createMockAudioContextProvider(): AudioContextProvider {
  const context = new AudioContext();

  return {
    audioContext: context,
    isValid: () => context.state !== 'closed',
    getAudioContext: () => context,
    getCurrentSinkId: () => context.sinkId ?? 'default',
    allowedSetSinkId: () => typeof context.setSinkId === 'function',
    initialize: vi.fn(),
    resume: vi.fn().mockResolvedValue(undefined),
    setSinkId: vi.fn().mockResolvedValue(undefined),
    close: vi.fn().mockResolvedValue(undefined),
  } as unknown as AudioContextProvider;
}

function patchParticipantSource(node: ParticipantNode): void {
  const anyNode = node as any;
  anyNode['source'] = {
    disconnect: vi.fn(),
    connect: vi.fn().mockReturnThis(),
  };
  anyNode['strategy'] = {
    destroy: vi.fn(),
    create: vi.fn(),
  };
}

describe('RemoteAudioMixer', () => {
  let mixer: RemoteAudioMixer;

  beforeEach(() => {
    const provider = createMockAudioContextProvider();
    mixer = new RemoteAudioMixer(provider);
    vi.clearAllMocks();
  });

  afterEach(async () => {
    await mixer.destroy();
    vi.restoreAllMocks();
  });

  it('should initialize audio chain correctly', async () => {
    await new Promise((resolve) => setTimeout(resolve, 0));
    const output = mixer.getOutputNode();
    expect(output.constructor.name).toMatch(/GainNode/i);
  });

  it('should add and get a participant', async () => {
    const stream = new MediaStream();
    await mixer.addParticipant('alice', stream);
    const p = mixer.getParticipant('alice');
    expect(p).toBeInstanceOf(ParticipantNode);
    expect(activeSpeakerManagerMock.addParticipant).toHaveBeenCalledWith(
      'alice',
      p,
    );
  });

  it('should update a participant if already exists', async () => {
    const stream1 = new MediaStream();
    const stream2 = new MediaStream();
    await mixer.addParticipant('bob', stream1);
    const original = mixer.getParticipant('bob')!;
    patchParticipantSource(original);

    await mixer.updateParticipant('bob', stream2);
    expect(mixer.getParticipant('bob')).toBe(original);
    expect(activeSpeakerManagerMock.addParticipant).toHaveBeenCalledWith(
      'bob',
      original,
    );
  });

  it('should add a participant if update called on missing sid', async () => {
    const stream = new MediaStream();
    await mixer.updateParticipant('newbie', stream);
    expect(mixer.getParticipant('newbie')).toBeInstanceOf(ParticipantNode);
  });

  it('should remove participant', async () => {
    const stream = new MediaStream();
    await mixer.addParticipant('eve', stream);
    const p = mixer.getParticipant('eve')!;
    patchParticipantSource(p);

    await mixer.removeParticipant('eve');
    expect(mixer.getParticipant('eve')).toBeNull();
    expect(activeSpeakerManagerMock.removeParticipant).toHaveBeenCalledWith(
      'eve',
    );
  });

  it('should not throw when removing nonexistent participant', async () => {
    await expect(mixer.removeParticipant('ghost')).resolves.toBeUndefined();
  });

  it('should clamp and set gain values correctly', async () => {
    await new Promise((resolve) => setTimeout(resolve, 0));
    const spy = vi.spyOn((mixer as any).masterGain.gain, 'setValueAtTime');

    mixer.setMasterGain(0.75);
    expect(spy).toHaveBeenCalledWith(0.75, expect.any(Number));

    mixer.setMasterGain(-1);
    expect(spy).toHaveBeenCalledWith(0, expect.any(Number));

    mixer.setMasterGain(2);
    expect(spy).toHaveBeenCalledWith(1, expect.any(Number));
  });

  it('should destroy all participants and stop active speaker manager', async () => {
    const stream = new MediaStream();
    await mixer.addParticipant('temp', stream);
    const p = mixer.getParticipant('temp')!;
    patchParticipantSource(p);

    await mixer.destroy();

    expect(mixer.getParticipant('temp')).toBeNull();
    expect(activeSpeakerManagerMock.destroy).toHaveBeenCalled();
  });

  it('should expose active speaker manager and allow monitoring control', () => {
    const manager = mixer.getActiveSpeakerManager();
    const callback = vi.fn();

    manager.startMonitoring(callback);
    manager.setIsEnabled(false);
    manager.setIsEnabled(true);

    expect(manager.startMonitoring).toHaveBeenCalledWith(callback);
    expect(manager.setIsEnabled).toHaveBeenCalledWith(false);
    expect(manager.setIsEnabled).toHaveBeenCalledWith(true);
  });
});
