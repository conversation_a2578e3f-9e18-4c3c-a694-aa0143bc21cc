import { ParticipantNode } from './ParticipantNode';
import { logger } from '@/shared';
import { ActiveSpeakerManager } from './active-speaker';
import { AudioContextProvider } from '../../audio-context';

export class RemoteAudioMixer {
  private audioContextProvider: AudioContextProvider;
  private masterGain!: GainNode;
  private aecReferenceNode!: GainNode;

  private participants: Map<string, ParticipantNode> = new Map();
  private activeSpeakerManager!: ActiveSpeakerManager;

  constructor(audioContextProvider: AudioContextProvider) {
    this.audioContextProvider = audioContextProvider;
    this.activeSpeakerManager = new ActiveSpeakerManager(this.participants);
    (async () => await this.initialize())();
  }

  private async initialize(): Promise<void> {
    if (!this.audioContextProvider.isValid()) {
      logger.warn('Cannot setup audio chain: AudioContext is not valid');
      return;
    }

    const audioContext = this.audioContextProvider.getAudioContext();

    // Create master chain
    this.masterGain = audioContext.createGain();
    this.masterGain.gain.value = 1;

    // Create aec referenceNode
    this.aecReferenceNode = audioContext.createGain();
    this.aecReferenceNode.gain.value = 1;

    this.masterGain.connect(audioContext.destination);
    this.masterGain.connect(this.aecReferenceNode);
  }

  getOutputNode(): AudioNode {
    return this.aecReferenceNode;
  }

  getActiveSpeakerManager() {
    return this.activeSpeakerManager;
  }

  getParticipant(sid: string): ParticipantNode | null {
    const participant = this.participants.get(sid);
    if (!participant) {
      logger.warn(`Participant ${sid} not found`);
      return null;
    }
    return participant;
  }

  async addParticipant(sid: string, stream: MediaStream): Promise<void> {
    logger.info('RemoteAudioMixer: Adding participant', {
      participant_sid: sid,
      total_participants_before: this.participants.size,
      stream_id: stream.id,
      event_type: 'remote_audio_mixer_add_participant',
    });

    if (!this.audioContextProvider.isValid()) {
      logger.warn('Cannot setup audio chain: AudioContext is not valid');
      return;
    }

    // Remove existing participant if any
    if (this.participants.has(sid)) {
      await this.removeParticipant(sid);
    }

    const participantNode = new ParticipantNode(
      this.audioContextProvider,
      this.masterGain,
    );
    await participantNode.create(stream);

    this.participants.set(sid, participantNode);
    this.activeSpeakerManager.addParticipant(sid, participantNode);

    logger.info('RemoteAudioMixer: Participant added successfully', {
      participant_sid: sid,
      total_participants_after: this.participants.size,
      event_type: 'remote_audio_mixer_participant_added',
    });
  }

  async updateParticipant(sid: string, newStream: MediaStream): Promise<void> {
    logger.info('RemoteAudioMixer: Updating participant', {
      participant_sid: sid,
      new_stream_id: newStream.id,
      participant_exists: this.participants.has(sid),
      event_type: 'remote_audio_mixer_update_participant',
    });

    if (!this.audioContextProvider.isValid()) {
      logger.warn('Cannot setup audio chain: AudioContext is not valid');
      return;
    }

    const participant = this.participants.get(sid);
    if (!participant) {
      logger.warn(`Participant ${sid} not found, adding as new participant`);
      await this.addParticipant(sid, newStream);
      return;
    }

    await participant.update(newStream);
    this.participants.set(sid, participant);
    this.activeSpeakerManager.addParticipant(sid, participant);
  }

  async removeParticipant(sid: string): Promise<void> {
    const participant = this.participants.get(sid);

    logger.info('RemoteAudioMixer: Removing participant', {
      participant_sid: sid,
      participant_exists: !!participant,
      total_participants_before: this.participants.size,
      event_type: 'remote_audio_mixer_remove_participant',
    });

    if (!participant) return;

    await participant.destroy();
    this.participants.delete(sid);
    this.activeSpeakerManager.removeParticipant(sid);

    logger.info('RemoteAudioMixer: Participant removed successfully', {
      participant_sid: sid,
      total_participants_after: this.participants.size,
      event_type: 'remote_audio_mixer_participant_removed',
    });
  }

  setMasterGain(gain: number): void {
    if (!this.audioContextProvider.isValid()) {
      logger.warn('Cannot setup audio chain: AudioContext is not valid');
      return;
    }

    const audioContext = this.audioContextProvider.getAudioContext();
    const clampedGain = Math.max(0, Math.min(1, gain));
    this.masterGain.gain.setValueAtTime(clampedGain, audioContext.currentTime);
  }

  async destroy(): Promise<void> {
    for (const [sid] of this.participants) {
      await this.removeParticipant(sid);
    }

    this.activeSpeakerManager.destroy();
  }
}
