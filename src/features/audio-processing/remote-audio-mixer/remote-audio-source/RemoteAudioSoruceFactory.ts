import { ChromeRemoteAudioSourceStrategy } from './ChromeRemoteAudioSourceStrategy';
import { FallbackRemoteAudioSourceStrategy } from './FallbacRemoteAudioSourceStrategy';
import { RemoteAudioSourceStrategy } from '../../interfaces';

export class RemoteAudioSourceFactory {
  static select(): RemoteAudioSourceStrategy {
    const isChrome =
      /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);

    if (isChrome) {
      return new ChromeRemoteAudioSourceStrategy();
    }

    return new FallbackRemoteAudioSourceStrategy();
  }
}
