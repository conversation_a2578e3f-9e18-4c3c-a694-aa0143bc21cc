import { RemoteAudioSourceStrategy } from '../../interfaces';

export class ChromeRemoteAudioSourceStrategy
  implements RemoteAudioSourceStrategy
{
  private audioElement: HTMLAudioElement | null = null;

  async create(stream: MediaStream, context: AudioContext): Promise<AudioNode> {
    this.destroy();

    const audioElement = new Audio();
    audioElement.srcObject = stream;
    audioElement.muted = true;
    audioElement.volume = 0;
    audioElement.autoplay = true;
    this.audioElement = audioElement;

    try {
      await audioElement.play();
    } catch (e) {
      console.warn('Chrome strategy: audio element play() failed', e);
    }

    const newStream = audioElement.srcObject;
    const source = context.createMediaStreamSource(newStream);
    return source;
  }

  destroy() {
    if (!this.audioElement) return;
    this.audioElement.pause();
    this.audioElement.srcObject = null;
    this.audioElement.load();
    if (this.audioElement.parentElement) {
      this.audioElement.remove();
    }
    this.audioElement = null;
  }
}
