import { ChromeRemoteAudioSourceStrategy } from './ChromeRemoteAudioSourceStrategy';
import { FallbackRemoteAudioSourceStrategy } from './FallbacRemoteAudioSourceStrategy';
import { RemoteAudioSourceFactory } from './RemoteAudioSoruceFactory';

describe('RemoteAudioSourceFactory', () => {
  const originalUserAgent = navigator.userAgent;
  const originalVendor = navigator.vendor;

  afterEach(() => {
    // Restore original navigator properties
    Object.defineProperty(navigator, 'userAgent', {
      value: originalUserAgent,
      configurable: true,
    });
    Object.defineProperty(navigator, 'vendor', {
      value: originalVendor,
      configurable: true,
    });
  });

  it('returns ChromeRemoteAudioSourceStrategy for Chrome user agent', () => {
    Object.defineProperty(navigator, 'userAgent', {
      value:
        'Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      configurable: true,
    });
    Object.defineProperty(navigator, 'vendor', {
      value: 'Google Inc.',
      configurable: true,
    });

    const strategy = RemoteAudioSourceFactory.select();
    expect(strategy).toBeInstanceOf(ChromeRemoteAudioSourceStrategy);
  });

  it('returns FallbackRemoteAudioSourceStrategy for non-Chrome user agent', () => {
    Object.defineProperty(navigator, 'userAgent', {
      value:
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/114.0',
      configurable: true,
    });
    Object.defineProperty(navigator, 'vendor', {
      value: 'Mozilla',
      configurable: true,
    });

    const strategy = RemoteAudioSourceFactory.select();
    expect(strategy).toBeInstanceOf(FallbackRemoteAudioSourceStrategy);
  });
});
