import { logger } from '@/shared';
import { AudioContextProvider } from '../../audio-context';

export class StereoToMonoConverter {
  private audioContextProvider: AudioContextProvider;
  private sourceNode: AudioNode | null;
  private outputNode!: AudioNode;
  private isConnected = false;

  private splitter?: ChannelSplitterNode;
  private merger?: ChannelMergerNode;
  private mixGain?: GainNode;

  constructor(
    audioContextProvider: AudioContextProvider,
    sourceNode: AudioNode,
  ) {
    this.audioContextProvider = audioContextProvider;
    this.sourceNode = sourceNode;
    this.createAudioGraph();
  }

  private createAudioGraph() {
    if (!this.sourceNode) {
      throw new Error('Source node is required');
    }

    const audioContext = this.audioContextProvider.getAudioContext();

    // Get the actual channel count from the source node
    const sourceChannelCount = this.sourceNode.channelCount;

    if (sourceChannelCount === 1) {
      // Source is already mono - just add a gain node for consistency
      this.mixGain = audioContext.createGain();
      this.mixGain.gain.value = 1.0;
      this.outputNode = this.mixGain;

      // Connect source directly to gain
      this.sourceNode.connect(this.mixGain);
    } else {
      // Source has multiple channels - need to mix to mono
      this.splitter = audioContext.createChannelSplitter(sourceChannelCount);
      this.merger = audioContext.createChannelMerger(1);
      this.mixGain = audioContext.createGain();

      // Set gain to prevent clipping when mixing multiple channels
      // Use 1/sqrt(channelCount) for RMS normalization
      this.mixGain.gain.value = 1.0 / Math.sqrt(sourceChannelCount);

      // Connect the audio graph
      this.sourceNode.connect(this.splitter);

      // Mix all channels to mono output
      for (let i = 0; i < sourceChannelCount; i++) {
        this.splitter.connect(this.merger, i, 0);
      }

      this.merger.connect(this.mixGain);
      this.outputNode = this.mixGain;
    }

    this.isConnected = true;
  }

  getOutputNode(): AudioNode {
    if (!this.isConnected) {
      throw new Error('Converter not properly initialized');
    }
    return this.outputNode;
  }

  disconnect() {
    if (!this.isConnected || !this.sourceNode) return;

    try {
      if (this.splitter && this.merger) {
        // Multi-channel setup
        this.sourceNode.disconnect(this.splitter);
        this.splitter.disconnect();
        this.merger.disconnect();
      } else if (this.mixGain) {
        // Mono passthrough setup
        this.sourceNode.disconnect(this.mixGain);
      }

      if (this.mixGain) {
        this.mixGain.disconnect();
      }

      this.isConnected = false;
    } catch (error) {
      logger.error('[StereoToMonoConverter] Error during disconnect', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'aec_controller_enable_error',
      });
    }
  }
}
