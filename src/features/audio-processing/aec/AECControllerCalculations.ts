import { logger } from '@/shared';

export class AECControllerCalculations {
  private static worker: Worker | null = null;
  private static workerTaskId = 0;
  private static pendingTasks = new Map<
    number,
    {
      resolve: (value: {
        bestOffset: number;
        maxCorrelation: number;
        correlationValues: number[];
      }) => void;
      reject: (error: Error) => void;
    }
  >();

  private static async initializeWorker(): Promise<void> {
    if (!this.worker) {
      this.worker = new Worker('/audio/cross-correlate-worker.js');

      this.worker.onmessage = (e) => {
        const { taskId, success, result, error } = e.data;
        const task = this.pendingTasks.get(taskId);

        if (task) {
          this.pendingTasks.delete(taskId);
          if (success) {
            task.resolve(result);
          } else {
            task.reject(new Error(error));
          }
        }
      };

      this.worker.onerror = (error) => {
        logger.error('Cross-correlation worker error', {
          error: error.message,
          event_type: 'cross_correlation_worker_error',
        });
      };
    }
  }

  private static async crossCorrelate(
    signal1: Float32Array,
    signal2: Float32Array,
    maxDelay: number = 4800,
  ): Promise<{
    bestOffset: number;
    maxCorrelation: number;
    correlationValues: number[];
  }> {
    await this.initializeWorker();

    const taskId = ++this.workerTaskId;

    return new Promise((resolve, reject) => {
      this.pendingTasks.set(taskId, { resolve, reject });

      // Send task to worker
      this.worker!.postMessage({
        taskId,
        signal1: Array.from(signal1), // Convert to regular array for transfer
        signal2: Array.from(signal2),
        maxDelay,
      });

      // Set timeout for worker task
      setTimeout(() => {
        if (this.pendingTasks.has(taskId)) {
          this.pendingTasks.delete(taskId);
          reject(new Error('Cross-correlation worker timeout'));
          this.cleanup();
        }
      }, 30000); // 30 second timeout
    });
  }

  static async alignSignalsUsingCorrelation(
    farEndAudio: Float32Array,
    nearEndAudio: Float32Array,
  ): Promise<{
    farEndAligned: Float32Array;
    nearEndAligned: Float32Array;
    alignmentInfo: {
      originalFarLength: number;
      originalNearLength: number;
      finalLength: number;
      bestOffset: number;
      maxCorrelation: number;
      delayMs: number;
      method: string;
    };
  }> {
    const originalFarLength = farEndAudio.length;
    const originalNearLength = nearEndAudio.length;

    logger.info('Starting cross-correlation alignment', {
      farEndLength: originalFarLength,
      nearEndLength: originalNearLength,
      maxSearchDelayMs: 100, // 100ms search window
      event_type: 'aec_cross_correlation_start',
    });

    // Perform cross-correlation to find optimal alignment using worker
    // Search up to ±100ms (4800 samples at 48kHz) for acoustic delays
    const maxDelay = Math.floor(0.1 * 48000); // 100ms in samples

    try {
      const correlation = await this.crossCorrelate(
        farEndAudio,
        nearEndAudio,
        maxDelay,
      );

      const delayMs = (correlation.bestOffset / 48000) * 1000;

      logger.info('Cross-correlation completed', {
        bestOffset: correlation.bestOffset,
        maxCorrelation: correlation.maxCorrelation.toFixed(6),
        delayMs: delayMs.toFixed(2),
        correlationQuality:
          correlation.maxCorrelation > 0.5
            ? 'excellent'
            : correlation.maxCorrelation > 0.3
              ? 'good'
              : correlation.maxCorrelation > 0.1
                ? 'fair'
                : 'poor',
        event_type: 'aec_cross_correlation_result',
      });

      // Apply the alignment offset
      let farStart = 0;
      let nearStart = 0;

      if (correlation.bestOffset > 0) {
        // Near-end is delayed relative to far-end
        nearStart = correlation.bestOffset;
      } else if (correlation.bestOffset < 0) {
        // Far-end is delayed relative to near-end
        farStart = Math.abs(correlation.bestOffset);
      }

      // Calculate final aligned length
      const farRemaining = originalFarLength - farStart;
      const nearRemaining = originalNearLength - nearStart;
      const finalLength = Math.min(farRemaining, nearRemaining);

      // Create aligned signals
      const farEndAligned = new Float32Array(finalLength);
      const nearEndAligned = new Float32Array(finalLength);

      // Copy aligned portions
      farEndAligned.set(farEndAudio.subarray(farStart, farStart + finalLength));
      nearEndAligned.set(
        nearEndAudio.subarray(nearStart, nearStart + finalLength),
      );

      const alignmentInfo = {
        originalFarLength,
        originalNearLength,
        finalLength,
        bestOffset: correlation.bestOffset,
        maxCorrelation: correlation.maxCorrelation,
        delayMs,
        method: 'cross_correlation',
      };

      // Validate alignment quality
      if (correlation.maxCorrelation < 0.1) {
        logger.warn('Low correlation detected - alignment may be poor', {
          correlation: correlation.maxCorrelation,
          event_type: 'aec_alignment_quality_warning',
        });
      }

      logger.info('Signal alignment completed', {
        ...alignmentInfo,
        finalDurationSeconds: (finalLength / 48000).toFixed(2),
        alignmentQuality:
          correlation.maxCorrelation > 0.3 ? 'good' : 'needs_improvement',
        event_type: 'aec_signal_alignment_complete',
      });

      return {
        farEndAligned,
        nearEndAligned,
        alignmentInfo,
      };
    } catch (error) {
      logger.error('Cross-correlation failed', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'aec_cross_correlation_error',
      });
      throw error;
    }
  }

  // Enhanced debugging with spectrum analysis
  static async debugCrossCorrelation(
    farEndAudio: Float32Array,
    nearEndAudio: Float32Array,
  ): Promise<void> {
    // Quick correlation test on a smaller window for debugging
    const testLength = Math.min(48000, farEndAudio.length, nearEndAudio.length); // 1 second
    const farTest = farEndAudio.slice(0, testLength);
    const nearTest = nearEndAudio.slice(0, testLength);

    try {
      const correlation = await this.crossCorrelate(farTest, nearTest, 2400); // ±50ms

      console.log('[AEC Debug] Cross-correlation analysis:', {
        testLength,
        bestOffset: correlation.bestOffset,
        bestDelayMs: ((correlation.bestOffset / 48000) * 1000).toFixed(2),
        maxCorrelation: correlation.maxCorrelation.toFixed(6),
        correlationValues: correlation.correlationValues.slice(0, 10), // First 10 values
      });

      // Calculate RMS levels for both signals
      const farRMS = Math.sqrt(
        farTest.reduce((sum, val) => sum + val * val, 0) / farTest.length,
      );
      const nearRMS = Math.sqrt(
        nearTest.reduce((sum, val) => sum + val * val, 0) / nearTest.length,
      );

      console.log('[AEC Debug] Signal levels:', {
        farRMS: farRMS.toFixed(6),
        nearRMS: nearRMS.toFixed(6),
        levelRatio: (nearRMS / farRMS).toFixed(3),
        signalPresent: farRMS > 0.001 && nearRMS > 0.001,
      });

      // Add spectral analysis to see if signals are related
      this.compareSignalSpectrums(farTest, nearTest);

      // Add signal similarity test
      this.testSignalSimilarity(farTest, nearTest);
    } catch (error) {
      console.error('[AEC Debug] Cross-correlation analysis failed:', error);
    }
  }

  private static compareSignalSpectrums(
    farSignal: Float32Array,
    nearSignal: Float32Array,
  ): void {
    // Simple frequency domain comparison
    const analyzeSpectrum = (signal: Float32Array) => {
      let lowFreq = 0,
        midFreq = 0,
        highFreq = 0;
      const windowSize = 1024;

      for (
        let i = 0;
        i < Math.min(signal.length - windowSize, windowSize);
        i += windowSize
      ) {
        for (let j = 0; j < windowSize; j++) {
          const sample = Math.abs(signal[i + j]);
          if (j < windowSize / 6)
            lowFreq += sample; // Low freq approximation
          else if (j < windowSize / 2)
            midFreq += sample; // Mid freq
          else highFreq += sample; // High freq
        }
      }

      return { lowFreq, midFreq, highFreq };
    };

    const farSpectrum = analyzeSpectrum(farSignal);
    const nearSpectrum = analyzeSpectrum(nearSignal);

    console.log('[AEC Debug] Spectrum comparison:', {
      farSpectrum: {
        low: farSpectrum.lowFreq.toFixed(0),
        mid: farSpectrum.midFreq.toFixed(0),
        high: farSpectrum.highFreq.toFixed(0),
      },
      nearSpectrum: {
        low: nearSpectrum.lowFreq.toFixed(0),
        mid: nearSpectrum.midFreq.toFixed(0),
        high: nearSpectrum.highFreq.toFixed(0),
      },
      spectralSimilarity: {
        lowRatio:
          nearSpectrum.lowFreq > 0
            ? (farSpectrum.lowFreq / nearSpectrum.lowFreq).toFixed(2)
            : 'N/A',
        midRatio:
          nearSpectrum.midFreq > 0
            ? (farSpectrum.midFreq / nearSpectrum.midFreq).toFixed(2)
            : 'N/A',
        highRatio:
          nearSpectrum.highFreq > 0
            ? (farSpectrum.highFreq / nearSpectrum.highFreq).toFixed(2)
            : 'N/A',
      },
    });
  }

  private static testSignalSimilarity(
    farSignal: Float32Array,
    nearSignal: Float32Array,
  ): void {
    // Test if near-end contains ANY portion of far-end signal
    const testWindows = 10; // Test multiple time windows
    const windowSize = Math.floor(farSignal.length / testWindows);

    let bestWindowCorrelation = -Infinity;
    let bestWindow = -1;

    for (let window = 0; window < testWindows; window++) {
      const startIdx = window * windowSize;
      const endIdx = Math.min(
        startIdx + windowSize,
        Math.min(farSignal.length, nearSignal.length),
      );

      const farWindow = farSignal.slice(startIdx, endIdx);
      const nearWindow = nearSignal.slice(startIdx, endIdx);

      // Simple correlation for this window
      let correlation = 0;
      let farEnergy = 0;
      let nearEnergy = 0;

      for (let i = 0; i < farWindow.length; i++) {
        correlation += farWindow[i] * nearWindow[i];
        farEnergy += farWindow[i] * farWindow[i];
        nearEnergy += nearWindow[i] * nearWindow[i];
      }

      // Normalize correlation
      const normalizedCorr =
        farEnergy > 0 && nearEnergy > 0
          ? correlation / Math.sqrt(farEnergy * nearEnergy)
          : 0;

      if (normalizedCorr > bestWindowCorrelation) {
        bestWindowCorrelation = normalizedCorr;
        bestWindow = window;
      }
    }

    console.log('[AEC Debug] Window correlation test:', {
      bestWindow,
      bestWindowCorrelation: bestWindowCorrelation.toFixed(6),
      windowDuration: (windowSize / 48000).toFixed(2) + 's',
      signalRelationship:
        bestWindowCorrelation > 0.1
          ? 'RELATED'
          : bestWindowCorrelation > 0.01
            ? 'WEAKLY_RELATED'
            : 'UNRELATED',
    });
  }

  // Cleanup method to terminate worker when no longer needed
  private static cleanup(): void {
    if (this.worker) {
      // Reject all pending tasks
      this.pendingTasks.forEach(({ reject }) => {
        reject(new Error('Worker terminated'));
      });
      this.pendingTasks.clear();

      // Terminate worker
      this.worker.terminate();
      this.worker = null;

      logger.info('Cross-correlation worker terminated', {
        event_type: 'cross_correlation_worker_cleanup',
      });
    }
  }
}
