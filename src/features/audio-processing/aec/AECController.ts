import { logger } from '@/shared';
import { RemoteAudioMixer } from '../remote-audio-mixer/RemoteAudioMixer';
import { LocalAudioMixer } from '../local-audio-mixer';
import { StereoToMonoConverter } from './StereoToMonoConverter';
import { AECControllerCalculations } from './AECControllerCalculations';
import { AudioContextProvider } from '../../audio-context';

export class AECController {
  private audioContextProvider: AudioContextProvider;
  private localAudioMixer: LocalAudioMixer;
  private remoteAudioMixer: RemoteAudioMixer;

  private isInitialized = false;
  private isProcessing = false;
  private aecEnabled = false;

  private calibrationWorker: Worker | null = null;

  private nearEndConverter!: StereoToMonoConverter;
  private farEndConverter!: StereoToMonoConverter;
  private aecWorkletNode!: AudioWorkletNode;

  private onCalibrationComplete?: () => void;

  constructor(
    audioContextProvider: AudioContextProvider,
    localAudioMixer: LocalAudioMixer,
    remoteAudioMixer: RemoteAudioMixer,
  ) {
    this.audioContextProvider = audioContextProvider;
    this.localAudioMixer = localAudioMixer;
    this.remoteAudioMixer = remoteAudioMixer;
  }

  async enableAEC() {
    if (!this.audioContextProvider || this.aecEnabled) return;

    try {
      const audioContext = this.audioContextProvider.getAudioContext();

      const [wasmBinary, jsWrapper] = await Promise.all([
        fetch('/audio/wasm/aec_agc_bg.wasm').then((r) => r.arrayBuffer()),
        fetch('/audio/wasm/aec_agc.js').then((r) => r.text()),
      ]);

      this.calibrationWorker = new Worker('/audio/calibration-worker.js');
      await audioContext.audioWorklet.addModule('/audio/aec-processor.js');

      // Create AEC worklet node
      this.aecWorkletNode = new AudioWorkletNode(
        audioContext,
        'aec-processor',
        {
          numberOfInputs: 2, // Far-end and near-end
          numberOfOutputs: 1, // Processed output
          channelCount: 1,
        },
      );

      // Set up message listeners for worklet communication
      this.setupWorkletMessageListeners();

      // Initialize worker with same WASM
      const initPromise = new Promise((resolve, reject) => {
        const timeoutId = setTimeout(
          () => reject(new Error('Worker init timeout')),
          10000,
        );

        this.calibrationWorker!.onmessage = (event) => {
          if (event.data.type === 'initialized') {
            clearTimeout(timeoutId);
            if (event.data.success) {
              resolve(void 0);
            } else {
              reject(new Error(event.data.error));
            }
          }
        };
      });

      this.calibrationWorker.postMessage({
        type: 'initialize',
        data: { wasmBinary: wasmBinary.slice(), jsWrapper },
        id: 'init',
      });

      await initPromise;

      // Pass both to worklet
      this.aecWorkletNode.port.postMessage(
        {
          type: 'init',
          data: { wasmBinary, jsWrapper },
        },
        [wasmBinary],
      );

      this.localAudioMixer.connectAECNode(this.aecWorkletNode);

      this.aecEnabled = true;
      this.isInitialized = true;
      logger.info('AEC enabled successfully');
    } catch (error) {
      logger.error('Failed to enable aec', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'aec_controller_enable_error',
      });
      throw error;
    }
  }

  private setupWorkletMessageListeners(): void {
    if (!this.aecWorkletNode) return;

    this.aecWorkletNode.port.onmessage = (event) => {
      const { type, data, error } = event.data;

      // Handle different message types
      switch (type) {
        case 'initialized':
          logger.info('AEC worklet initialized successfully', {
            event_type: 'aec_worklet_initialized',
            sample_rate: data?.sampleRate,
            block_size: data?.blockSize,
          });
          break;

        case 'calibrationComplete':
          console.log(`[AEC Controller] Calibration completed`);
          logger.info('AEC calibration completed in worklet', {
            event_type: 'aec_worklet_calibration_complete',
          });
          this.onCalibrationComplete?.();
          break;

        case 'processingStarted':
          console.log(`[AEC Controller] Processing started`);
          logger.info('AEC processing started in worklet', {
            event_type: 'aec_worklet_processing_started',
          });
          break;

        case 'progress':
          logger.info('AEC processing progress update', {
            event_type: 'aec_worklet_progress',
            frame_count: data?.frameCount,
            current_erle: data?.currentErle,
          });
          break;

        case 'metrics':
          logger.info('AEC final metrics received', {
            event_type: 'aec_worklet_metrics',
            frame_count: data?.frameCount,
            erle: data?.erle,
            processing_efficiency: data?.processingEfficiency,
          });
          break;

        case 'stopped':
          console.log(`[AEC Controller] Processing stopped`);
          logger.info('AEC processing stopped in worklet', {
            event_type: 'aec_worklet_stopped',
          });
          break;

        case 'error':
          console.error(`[AEC Controller] Worklet Error:`, error);
          logger.error('AEC worklet error', {
            error: error || 'Unknown worklet error',
            event_type: 'aec_worklet_error',
          });
          break;

        default:
          console.warn(`[AEC Controller] ❓ Unknown message: ${type}`);
          logger.warn('Unknown AEC worklet message type', {
            message_type: type,
            event_type: 'aec_worklet_unknown_message',
          });
          break;
      }
    };

    this.aecWorkletNode.port.onmessageerror = (event) => {
      console.error(`[AEC Controller] Message Error:`, event);
      logger.error('AEC worklet message error', {
        error: 'Message error occurred',
        event_type: 'aec_worklet_message_error',
      });
    };

    logger.info('AEC worklet message listeners set up', {
      event_type: 'aec_worklet_listeners_setup',
    });
  }

  async startCalibration(onStarted?: () => void, onComplete?: () => void) {
    if (!this.aecEnabled || !this.aecWorkletNode || !this.isInitialized) {
      throw new Error('AEC not enabled');
    }

    try {
      const farEndAudio = await this.loadWAVFile(
        '/assets/audio/calibration.wav',
      );

      const nearEndAudio =
        await this.captureFromAnalyserWhilePlaying(farEndAudio);

      onStarted?.();

      // Debug the raw signals first
      console.log('[AEC Controller] Raw signal lengths:', {
        farEnd: farEndAudio.length,
        nearEnd: nearEndAudio.length,
        difference: nearEndAudio.length - farEndAudio.length,
      });

      AECControllerCalculations.debugCrossCorrelation(
        farEndAudio,
        nearEndAudio,
      );

      // Perform cross-correlation alignment
      const { farEndAligned, nearEndAligned, alignmentInfo } =
        await AECControllerCalculations.alignSignalsUsingCorrelation(
          farEndAudio,
          nearEndAudio,
        );

      console.log(
        `[AEC Controller] Signals aligned using cross-correlation:`,
        alignmentInfo,
      );

      // Run calibration in worker (non-blocking!)
      const calibrationPromise = new Promise((resolve, reject) => {
        const timeoutId = setTimeout(
          () => reject(new Error('Calibration timeout')),
          30000,
        );

        this.calibrationWorker!.onmessage = (event) => {
          if (event.data.type === 'calibrationResult') {
            clearTimeout(timeoutId);
            if (event.data.success) {
              resolve(event.data.calibration);
            } else {
              reject(new Error(event.data.error));
            }
          }
        };
      });

      this.calibrationWorker?.postMessage({
        type: 'calibrate',
        data: {
          calibrationSignal: farEndAligned,
          calibrationEcho: nearEndAligned,
          turns: 3,
        },
        id: 'calibrate',
      });

      const calibration = await calibrationPromise;

      // Send results to AudioWorklet
      this.aecWorkletNode.port.postMessage({
        type: 'setCalibration',
        data: { calibration },
      });

      this.onCalibrationComplete = onComplete;
    } catch (error) {
      logger.error('Failed to start calibration', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'aec_calibration_error',
      });
      throw error;
    }
  }

  async startProcessing(): Promise<void> {
    if (!this.aecEnabled || !this.aecWorkletNode || !this.isInitialized) {
      logger.warn('AEC not enabled');
      return;
    }

    if (this.isProcessing) {
      logger.warn('AEC processing already started');
      return;
    }

    try {
      const remoteOutputNode = this.remoteAudioMixer.getOutputNode();
      this.farEndConverter = new StereoToMonoConverter(
        this.audioContextProvider,
        remoteOutputNode,
      );

      const localAudioNode = this.localAudioMixer
        .getAudioLevelMonitor()
        .getProcessedOutputNode();
      this.nearEndConverter = new StereoToMonoConverter(
        this.audioContextProvider,
        localAudioNode,
      );

      this.farEndConverter.getOutputNode().connect(this.aecWorkletNode, 0, 0); // Far-end input
      this.nearEndConverter.getOutputNode().connect(this.aecWorkletNode, 0, 1); // Near-end input

      this.aecWorkletNode.port.postMessage({
        type: 'startProcessing',
        data: {},
      });

      this.isProcessing = true;
      logger.info('AEC processing started');
    } catch (error) {
      logger.error('Failed to start processing', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'aec_processing_error',
      });
      throw error;
    }
  }

  private async stopProcessing(): Promise<void> {
    if (!this.aecEnabled || !this.aecWorkletNode || !this.isInitialized) return;

    try {
      this.aecWorkletNode.port.postMessage({
        type: 'stop',
        data: {},
      });

      if (this.audioContextProvider.isValid()) {
        this.localAudioMixer.disconnectAECNode(this.aecWorkletNode);
      }

      // Clean up converters
      if (this.farEndConverter) {
        this.farEndConverter.disconnect();
      }

      if (this.nearEndConverter) {
        this.nearEndConverter.disconnect();
      }

      this.isProcessing = false;
      logger.info('AEC processing stopped');
    } catch (error) {
      logger.error('Failed to stop processing', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'aec_stop_error',
      });
    }
  }

  async disableAEC(): Promise<void> {
    if (!this.aecEnabled || !this.aecWorkletNode || !this.isInitialized) return;

    try {
      await this.stopProcessing();

      this.aecWorkletNode.port.onmessage = null;
      this.aecWorkletNode.port.onmessageerror = null;

      if (this.calibrationWorker) {
        this.calibrationWorker.terminate();
        this.calibrationWorker = null;
      }

      this.aecWorkletNode.disconnect();
      this.aecEnabled = false;
      this.isInitialized = false;
      logger.info('AEC disabled');
    } catch (error) {
      logger.error('Failed to disable AEC', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'aec_disable_error',
      });
    }
  }

  private async loadWAVFile(filePath: string): Promise<Float32Array> {
    const response = await fetch(filePath);
    if (!response.ok) {
      throw new Error(`Failed to fetch WAV file: ${response.status}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    const audioContext = this.audioContextProvider.getAudioContext();
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

    const channelData = audioBuffer.getChannelData(0);
    const audioData = new Float32Array(channelData.length);
    audioData.set(channelData);

    return audioData;
  }

  private async captureFromAnalyserWhilePlaying(
    audioData: Float32Array,
  ): Promise<Float32Array> {
    return new Promise((resolve, reject) => {
      try {
        const audioContext = this.audioContextProvider.getAudioContext();
        const analyser = this.localAudioMixer
          .getAudioLevelMonitor()
          .getCustomAnalyser();

        // Create audio buffer and source with higher volume
        const audioBuffer = audioContext.createBuffer(
          1,
          audioData.length,
          48000,
        );
        audioBuffer.getChannelData(0).set(audioData);

        const source = audioContext.createBufferSource();
        source.buffer = audioBuffer;

        // Add gain for louder playback
        const playbackGain = audioContext.createGain();
        playbackGain.gain.value = 1.5;
        source.connect(playbackGain);
        playbackGain.connect(audioContext.destination);

        // Calculate expected duration and samples
        const expectedDurationMs =
          (audioData.length / audioContext.sampleRate) * 1000;
        const bufferLength = analyser.fftSize;
        const captureIntervalMs =
          (bufferLength / audioContext.sampleRate) * 1000;

        // Pre-calculate expected number of captures
        const expectedCaptures =
          Math.ceil(expectedDurationMs / captureIntervalMs) + 2;
        const expectedTotalSamples = expectedCaptures * bufferLength;

        logger.info('Starting precise audio capture', {
          expectedDurationMs: expectedDurationMs.toFixed(1),
          captureIntervalMs: captureIntervalMs.toFixed(1),
          expectedCaptures,
          expectedTotalSamples,
          event_type: 'aec_precise_capture_start',
        });

        const capturedSamples: number[] = [];
        const dataArray = new Float32Array(bufferLength);
        let captureCount = 0;

        const captureInterval = setInterval(() => {
          analyser.getFloatTimeDomainData(dataArray);
          capturedSamples.push(...Array.from(dataArray));
          captureCount++;
        }, captureIntervalMs);

        // Start playback after a small delay
        setTimeout(() => {
          source.start();
        }, 100);

        // Stop capturing based on expected duration, not onended event
        setTimeout(() => {
          clearInterval(captureInterval);
          source.stop();
          source.disconnect();
          playbackGain.disconnect();

          const nearEndAudio = new Float32Array(capturedSamples);

          logger.info('Precise capture completed', {
            capturedSamples: nearEndAudio.length,
            actualCaptures: captureCount,
            expectedSamples: expectedTotalSamples,
            event_type: 'aec_precise_capture_complete',
          });

          resolve(nearEndAudio);
        }, expectedDurationMs + 200);
      } catch (error) {
        console.error(
          '[AEC Controller] Failed to setup precise capture:',
          error,
        );
        reject(error);
      }
    });
  }
}
