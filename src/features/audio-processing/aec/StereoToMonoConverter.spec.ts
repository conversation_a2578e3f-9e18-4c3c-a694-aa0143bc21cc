/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { StereoToMonoConverter } from './StereoToMonoConverter';
import { logger } from '@/shared';
import { AudioContextProvider } from '../../audio-context';

describe('StereoToMonoConverter', () => {
  let audioContext: AudioContext;
  let fakeProvider: AudioContextProvider;
  let sourceNode: AudioNode;

  beforeEach(async () => {
    const mockCtx = new AudioContext();
    audioContext = mockCtx;

    vi.spyOn(AudioContextProvider.prototype, 'getAudioContext').mockReturnValue(
      mockCtx,
    );
    fakeProvider = new AudioContextProvider();

    sourceNode = audioContext.createGain(); // default 1-channel
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('initializes mono path with gain node', () => {
    sourceNode.channelCount = 1;

    const converter = new StereoToMonoConverter(fakeProvider, sourceNode);
    const output = converter.getOutputNode();

    expect(output).toBeDefined();
    expect((output as GainNode).gain.value).toBe(1.0);
  });

  it('initializes stereo path with splitter and merger', () => {
    sourceNode.channelCount = 2;

    const connectSpy = vi.spyOn(sourceNode, 'connect');

    const converter = new StereoToMonoConverter(fakeProvider, sourceNode);
    const output = converter.getOutputNode();

    expect(output).toBeDefined();
    expect((output as GainNode).gain.value).toBeCloseTo(1 / Math.sqrt(2));
    expect(connectSpy).toHaveBeenCalled();
  });

  it('throws when getOutputNode is called before initialization', () => {
    const converter = new StereoToMonoConverter(fakeProvider, sourceNode);
    // Manually tamper
    (converter as any).isConnected = false;
    expect(() => converter.getOutputNode()).toThrow(
      'Converter not properly initialized',
    );
  });

  it('handles disconnect for mono passthrough', () => {
    sourceNode.channelCount = 1;
    const disconnectSpy = vi.spyOn(sourceNode, 'disconnect');
    const converter = new StereoToMonoConverter(fakeProvider, sourceNode);

    converter.disconnect();
    expect(disconnectSpy).toHaveBeenCalled();
  });

  it('handles disconnect for multi-channel path', () => {
    sourceNode.channelCount = 3;
    const converter = new StereoToMonoConverter(fakeProvider, sourceNode);

    const splitter = (converter as any).splitter!;
    const merger = (converter as any).merger!;
    const mixGain = (converter as any).mixGain!;

    const splitterSpy = vi.spyOn(splitter, 'disconnect');
    const mergerSpy = vi.spyOn(merger, 'disconnect');
    const mixGainSpy = vi.spyOn(mixGain, 'disconnect');
    const sourceSpy = vi.spyOn(sourceNode, 'disconnect');

    converter.disconnect();

    expect(splitterSpy).toHaveBeenCalled();
    expect(mergerSpy).toHaveBeenCalled();
    expect(mixGainSpy).toHaveBeenCalled();
    expect(sourceSpy).toHaveBeenCalled();
  });

  it('does nothing if disconnect is called when not connected', () => {
    sourceNode.channelCount = 1;
    const converter = new StereoToMonoConverter(fakeProvider, sourceNode);
    (converter as any).isConnected = false;
    const sourceSpy = vi.spyOn(sourceNode, 'disconnect');

    converter.disconnect();
    expect(sourceSpy).not.toHaveBeenCalled();
  });

  it('logs error on disconnect failure', () => {
    sourceNode.channelCount = 2;

    const converter = new StereoToMonoConverter(fakeProvider, sourceNode);
    const loggerSpy = vi.spyOn(logger, 'error').mockImplementation(() => {});
    const mixGain = (converter as any).mixGain!;
    vi.spyOn(mixGain, 'disconnect').mockImplementation(() => {
      throw new Error('disconnect failed');
    });

    converter.disconnect();

    expect(loggerSpy).toHaveBeenCalledWith(
      '[StereoToMonoConverter] Error during disconnect',
      expect.objectContaining({
        error: 'disconnect failed',
        event_type: 'aec_controller_enable_error',
      }),
    );
  });
});
