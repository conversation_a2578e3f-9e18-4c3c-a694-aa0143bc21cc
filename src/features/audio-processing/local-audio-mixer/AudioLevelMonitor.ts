import { logger } from '@/shared';
import { TrackMixer } from './TrackMixer';
import { CustomAudioAnalyser } from '../custom-nodes';
import { AudioContextProvider } from '../../audio-context';

export class AudioLevelMonitor {
  private audioContextProvider: AudioContextProvider;
  private trackMixer!: TrackMixer;

  private analyser!: CustomAudioAnalyser;
  private gainNode!: GainNode;
  private compressor!: DynamicsCompressorNode;

  private monitoringSessions: Map<string, number> = new Map();

  constructor(audioContextProvider: AudioContextProvider) {
    this.audioContextProvider = audioContextProvider;
    (async () => await this.initialize())();
  }

  private async initialize() {
    try {
      const audioContext = this.audioContextProvider.getAudioContext();

      // Create track mixer for multiple audio tracks
      this.trackMixer = new TrackMixer(this.audioContextProvider);
      const mixerNode = this.trackMixer.getMixer();

      // Create gain node for volume control
      this.gainNode = audioContext.createGain();
      this.gainNode.gain.value = 1.0;

      this.analyser = new CustomAudioAnalyser(this.audioContextProvider);

      // Create compressor for dynamic range control
      this.compressor = audioContext.createDynamicsCompressor();
      this.compressor.threshold.value = -24;
      this.compressor.knee.value = 30;
      this.compressor.ratio.value = 4;
      this.compressor.attack.value = 0.003;
      this.compressor.release.value = 0.25;

      mixerNode
        .connect(this.gainNode)
        .connect(this.analyser)
        .connect(this.compressor);

      logger.info('Local audio processing initialized', {
        gain_value: this.gainNode.gain.value,
        compressor_threshold: this.compressor.threshold.value,
        compressor_knee: this.compressor.knee.value,
        compressor_ratio: this.compressor.ratio.value,
        compressor_attack: this.compressor.attack.value,
        compressor_release: this.compressor.release.value,
        analyser_fft_size: this.analyser.fftSize,
        audio_context_state: audioContext.state,
        event_type: 'local_audio_processing_init',
      });
    } catch (error) {
      logger.error('Failed to initialize local audio processing', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'local_audio_processing_init_error',
      });
      throw error;
    }
  }

  getTrackMixer(): TrackMixer {
    return this.trackMixer;
  }

  getCompressor(): DynamicsCompressorNode {
    return this.compressor;
  }

  getCustomAnalyser(): CustomAudioAnalyser {
    return this.analyser;
  }

  getProcessedOutputNode(): AudioNode {
    return this.compressor;
  }

  getRawOutputNode(): AudioNode {
    return this.analyser;
  }

  getGain(): number {
    return this.gainNode.gain.value;
  }

  setGain(value: number) {
    if (!this.audioContextProvider.isValid()) {
      logger.warn('Cannot connect stream: AudioContext is not valid');
      return;
    }

    const oldGain = this.gainNode.gain.value;
    const audioContext = this.audioContextProvider.getAudioContext();
    const clampedValue = Math.max(0, Math.min(3, value));

    logger.info('Local audio processing gain changed', {
      old_gain: oldGain,
      new_gain: clampedValue,
      requested_gain: value,
      was_clamped: value !== clampedValue,
      event_type: 'local_audio_processing_gain_change',
    });

    this.gainNode.gain.setValueAtTime(clampedValue, audioContext.currentTime);
  }

  startMonitoring(callback: (level: number, peak: number) => void): string {
    const id = crypto.randomUUID();

    const monitor = () => {
      const level = this.analyser.getAudioLevel();
      const peak = this.analyser.getPeakLevel();
      callback(level, peak);
      const animationId = requestAnimationFrame(monitor);
      this.monitoringSessions.set(id, animationId);
    };

    monitor();
    return id;
  }

  stopMonitoring(id: string) {
    const animationId = this.monitoringSessions.get(id);
    if (animationId !== undefined) {
      cancelAnimationFrame(animationId);
      this.monitoringSessions.delete(id);
    }
  }

  private stopAllMonitoring() {
    for (const animationId of this.monitoringSessions.values()) {
      cancelAnimationFrame(animationId);
    }
    this.monitoringSessions.clear();
  }

  async destroy() {
    this.stopAllMonitoring();
    await this.trackMixer.destroy();
  }
}
