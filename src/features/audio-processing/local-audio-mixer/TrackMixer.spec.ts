/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TrackMixer } from './TrackMixer';
import { logger } from '@/shared';
import { AudioContextProvider } from '../../audio-context';

describe('TrackMixer', () => {
  let mixer: TrackMixer;
  let fakeProvider: Awaited<ReturnType<typeof AudioContextProvider.create>>;
  let warnSpy: ReturnType<typeof vi.spyOn>;
  let errorSpy: ReturnType<typeof vi.spyOn>;

  const createdGainNodes: GainNode[] = [];

  // helper to create a mock MediaStreamTrack the mixer is happy with
  const mTrack = (id: string, label = id) =>
    ({
      id,
      label,
      getSettings: vi.fn(() => ({})),
    }) as unknown as MediaStreamTrack;

  beforeEach(async () => {
    fakeProvider = await AudioContextProvider.create();

    vi.spyOn(AudioContextProvider.prototype, 'getAudioContext').mockReturnValue(
      fakeProvider.getAudioContext(),
    );
    vi.spyOn(AudioContextProvider.prototype, 'isValid').mockReturnValue(true);

    warnSpy = vi.spyOn(logger, 'warn').mockImplementation(() => {});
    errorSpy = vi.spyOn(logger, 'error').mockImplementation(() => {});

    // Track gain nodes *except the first one* (which is for mixer)
    const originalCreateGain = fakeProvider.getAudioContext().createGain;
    let callCount = 0;

    vi.spyOn(fakeProvider.getAudioContext(), 'createGain').mockImplementation(
      () => {
        const node = originalCreateGain.call(fakeProvider.getAudioContext());
        vi.spyOn(node.gain, 'setValueAtTime');
        callCount++;

        if (callCount > 1) {
          // skip the root mixer node on first call
          createdGainNodes.push(node);
        }

        return node;
      },
    );

    mixer = new TrackMixer(new AudioContextProvider());
  });

  afterEach(() => {
    vi.restoreAllMocks();
    createdGainNodes.length = 0;
  });

  it('initializes with mixerNode', () => {
    const node = mixer.getMixer();
    expect(node).toBeDefined();
    expect(node.gain.value).toBe(1);
  });

  it('adds and removes tracks with updateTracks', () => {
    const track1 = mTrack('t1', 'Test Track 1');
    const track2 = mTrack('t2', 'Test Track 2');

    mixer.updateTracks([track1, track2]);

    for (const node of createdGainNodes) {
      expect(node.gain.setValueAtTime).toHaveBeenCalled();
    }

    mixer.updateTracks([track2]); // should remove track1

    for (const node of createdGainNodes) {
      expect(node.gain.setValueAtTime).toHaveBeenCalled();
    }
  });

  it('removes and cleans up tracks correctly', async () => {
    const track = mTrack('t1', 'Label');
    mixer.updateTracks([track]);
    mixer.updateTracks([]);

    await mixer.destroy();
    expect(errorSpy).not.toHaveBeenCalled();
  });

  it('handles duplicate tracks gracefully', () => {
    const track = mTrack('dup1', 'Label');
    mixer.updateTracks([track]);
    mixer.updateTracks([track]); // should not re-add or warn (handled by updateTracks logic)
    expect(warnSpy).not.toHaveBeenCalledWith(
      expect.stringContaining('already exists'),
    );
  });

  it('calculates correct gain values for multiple tracks', () => {
    const t1 = mTrack('a', 'a');
    const t2 = mTrack('b', 'b');
    const t3 = mTrack('c', 'c');

    mixer.updateTracks([t1]);
    expect(
      createdGainNodes.at(-1)?.gain.setValueAtTime,
    ).toHaveBeenLastCalledWith(0.8, expect.any(Number));

    mixer.updateTracks([t1, t2]);
    expect(
      createdGainNodes.at(-1)?.gain.setValueAtTime,
    ).toHaveBeenLastCalledWith(0.5, expect.any(Number));

    mixer.updateTracks([t1, t2, t3]);
    expect(
      createdGainNodes.at(-1)?.gain.setValueAtTime,
    ).toHaveBeenLastCalledWith(
      expect.closeTo(0.7 / Math.sqrt(3), 3),
      expect.any(Number),
    );
  });

  it('handles invalid AudioContext gracefully on addTrack', () => {
    vi.spyOn(AudioContextProvider.prototype, 'isValid').mockReturnValue(false);
    const badTrack = mTrack('bad', 'Bad');
    // call the public API that reaches addTrack
    mixer.updateTracks([badTrack]);
    expect(warnSpy).toHaveBeenCalledWith(
      'Cannot add track to TrackMixer: AudioContext is not valid',
    );
  });

  it('destroy disconnects all tracks and clears state', async () => {
    const t1 = mTrack('x1', 'track');
    mixer.updateTracks([t1]);
    await mixer.destroy();
    expect(errorSpy).not.toHaveBeenCalled();
  });
});
