import { logger } from '@/shared';
import { AudioContextProvider } from '../../audio-context';

interface TrackNode {
  source: MediaStreamAudioSourceNode;
  gainNode: GainNode;
  track: MediaStreamTrack;
}

export class TrackMixer {
  private audioContextProvider!: AudioContextProvider;
  private mixerNode: GainNode;

  private audioTracks: MediaStreamTrack[] = [];
  private trackNodes: Map<string, TrackNode> = new Map();

  constructor(audioContextProvider: AudioContextProvider) {
    this.audioContextProvider = audioContextProvider;
    const audioContext = this.audioContextProvider.getAudioContext();

    this.mixerNode = audioContext.createGain();
    this.mixerNode.gain.value = 1;
  }

  getMixer(): GainNode {
    return this.mixerNode;
  }

  updateTracks(tracks: MediaStreamTrack[]) {
    let oldTracks = [...this.audioTracks];
    for (const track of tracks) {
      const existingTrack = this.audioTracks.find(
        (oldTrack) => oldTrack.id === track.id,
      );

      if (existingTrack) {
        oldTracks = oldTracks.filter((oldTrack) => oldTrack.id !== track.id);
      } else {
        this.addTrack(track);
      }
    }

    for (const track of oldTracks) {
      this.removeTrack(track.id);
    }
  }

  private addTrack(track: MediaStreamTrack) {
    if (!this.audioContextProvider.isValid()) {
      logger.warn('Cannot add track to TrackMixer: AudioContext is not valid');
      return;
    }

    if (this.trackNodes.has(track.id)) {
      logger.warn(`Track ${track.id} already exists in mixer`);
      return;
    }

    try {
      const audioContext = this.audioContextProvider.getAudioContext();
      const stream = new MediaStream([track]);

      const source = audioContext.createMediaStreamSource(stream);
      const gainNode = audioContext.createGain();
      source.connect(gainNode).connect(this.mixerNode);

      const trackNode: TrackNode = { source, gainNode, track };
      this.trackNodes.set(track.id, trackNode);
      this.audioTracks.push(track);

      this.updateTrackGains();
      logger.info('TrackMixer Added track', {
        track_id: track.id,
        track_label: track.label,
        track_settings: JSON.stringify(track.getSettings()),
        audio_track_count: this.audioTracks.length,
        event_type: 'track_mixer_add_track',
      });
    } catch (error) {
      logger.error(`Failed to add track ${track.id}`, {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'track_mixer_add_track_error',
      });
      return;
    }
  }

  private removeTrack(trackId: string) {
    const trackNode = this.trackNodes.get(trackId);
    if (!trackNode) {
      return;
    }

    this.cleanupTrackNode(trackId, trackNode);
  }

  private updateTrackGains(): void {
    const trackCount = this.trackNodes.size;
    if (trackCount === 0) return;

    // Calculate optimal gain per track to prevent clipping
    // Using square root scaling for better perceived loudness
    const gainPerTrack = this.calculateTrackGain();
    const audioContext = this.audioContextProvider.getAudioContext();

    this.trackNodes.forEach((trackNode, trackId) => {
      try {
        trackNode.gainNode.gain.setValueAtTime(
          gainPerTrack,
          audioContext.currentTime,
        );
      } catch (error) {
        logger.error(`Error updating gain for track ${trackId}`, {
          error: error instanceof Error ? error.message : String(error),
          event_type: 'track_mixer_gain_update_error',
        });
      }
    });
  }

  private calculateTrackGain(): number {
    const trackCount = this.trackNodes.size;
    if (trackCount === 0) return 1.0;

    // More conservative gain calculation
    if (trackCount === 1) return 0.8;
    if (trackCount === 2) return 0.5; // More conservative for 2 tracks

    // For more tracks, use square root scaling
    return 0.7 / Math.sqrt(trackCount);
  }

  private cleanupTrackNode(trackId: string, trackNode: TrackNode): void {
    try {
      trackNode.source.disconnect();
      trackNode.gainNode.disconnect();
    } catch (error) {
      logger.error(`Error disconnecting track ${trackId}`, {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'track_mixer_disconnect_track_error',
      });
    }

    // Remove from maps and arrays
    this.trackNodes.delete(trackId);
    this.audioTracks = this.audioTracks.filter((track) => track.id !== trackId);

    // Recalculate gain levels for remaining tracks
    this.updateTrackGains();

    logger.info('TrackMixer Cleaned up track', {
      track_id: trackId,
      audio_track_count: this.audioTracks.length,
      event_type: 'track_mixer_disconnect_track',
    });
  }

  async destroy(): Promise<void> {
    const trackIds = Array.from(this.trackNodes.keys());
    for (const trackId of trackIds) {
      this.removeTrack(trackId);
    }

    try {
      this.mixerNode.disconnect();
    } catch (error) {
      logger.error(`Error disconnecting mixer node:`, {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'track_mixer_disconnect_error',
      });
    }

    this.trackNodes.clear();
    this.audioTracks = [];
  }
}
