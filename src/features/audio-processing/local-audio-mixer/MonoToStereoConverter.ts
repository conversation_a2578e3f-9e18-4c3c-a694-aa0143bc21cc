import { logger } from '@/shared';
import { AudioContextProvider } from '../../audio-context';

export class MonoToStereoConverter {
  private audioContextProvider: AudioContextProvider;
  private sourceNode: AudioNode | null;
  private outputNode!: AudioNode;
  private isConnected = false;

  private splitter?: ChannelSplitterNode;
  private merger?: ChannelMergerNode;
  private gainNode!: GainNode;
  private leftGain?: GainNode;
  private rightGain?: GainNode;

  constructor(
    audioContextProvider: AudioContextProvider,
    sourceNode: AudioNode,
  ) {
    this.audioContextProvider = audioContextProvider;
    this.sourceNode = sourceNode;
    this.createAudioGraph();
  }

  private createAudioGraph() {
    if (!this.sourceNode) {
      throw new Error('Source node is required');
    }

    const audioContext = this.audioContextProvider.getAudioContext();

    // ALWAYS force stereo conversion, regardless of source channel count
    // This ensures <PERSON><PERSON> gets explicit stereo data in both channels

    // Create the stereo conversion chain
    this.splitter = audioContext.createChannelSplitter(2); // Accept up to 2 channels
    this.merger = audioContext.createChannelMerger(2);

    // Create separate gain nodes for left and right to ensure both channels exist
    this.leftGain = audioContext.createGain();
    this.rightGain = audioContext.createGain();
    this.leftGain.gain.value = 1.0;
    this.rightGain.gain.value = 1.0;

    // Final gain node with explicit stereo configuration
    this.gainNode = audioContext.createGain();
    this.gainNode.gain.value = 1.0;
    this.gainNode.channelCount = 2;
    this.gainNode.channelCountMode = 'explicit';
    this.gainNode.channelInterpretation = 'speakers';

    // Force source to output stereo first
    this.sourceNode.channelCount = 2;
    this.sourceNode.channelCountMode = 'max';
    this.sourceNode.channelInterpretation = 'speakers';

    // Connect: source → splitter → separate gains → merger → final gain
    this.sourceNode.connect(this.splitter);

    // Connect left channel
    this.splitter.connect(this.leftGain, 0, 0);
    this.leftGain.connect(this.merger, 0, 0);

    // Connect right channel (duplicate left if mono source)
    // This ensures both channels always have audio data
    this.splitter.connect(this.rightGain, 0, 0); // Use left channel for both if mono
    try {
      // Try to connect actual right channel if it exists
      this.splitter.connect(this.rightGain, 1, 0);
    } catch {
      // If right channel doesn't exist, leftGain→rightGain ensures duplication
      this.leftGain.connect(this.rightGain);
    }
    this.rightGain.connect(this.merger, 0, 1);

    this.merger.connect(this.gainNode);
    this.outputNode = this.gainNode;

    this.isConnected = true;

    logger.info('MonoToStereoConverter created with forced stereo routing', {
      sourceChannelCount: this.sourceNode.channelCount,
      event_type: 'mono_to_stereo_converter_created',
    });
  }

  getOutputNode(): AudioNode {
    if (!this.isConnected) {
      throw new Error('Converter not properly initialized');
    }
    return this.outputNode;
  }

  disconnect() {
    if (!this.isConnected || !this.sourceNode) return;

    try {
      // Disconnect all nodes in the chain
      if (this.sourceNode) {
        this.sourceNode.disconnect();
      }
      if (this.splitter) {
        this.splitter.disconnect();
      }
      if (this.leftGain) {
        this.leftGain.disconnect();
      }
      if (this.rightGain) {
        this.rightGain.disconnect();
      }
      if (this.merger) {
        this.merger.disconnect();
      }
      if (this.gainNode) {
        this.gainNode.disconnect();
      }

      this.isConnected = false;

      logger.info('MonoToStereoConverter disconnected', {
        event_type: 'mono_to_stereo_converter_disconnected',
      });
    } catch (error) {
      logger.error('[MonoToStereoConverter] Error during disconnect', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'mono_to_stereo_converter_disconnect_error',
      });
    }
  }
}
