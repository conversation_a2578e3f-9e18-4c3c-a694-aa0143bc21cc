import { logger } from '@/shared';
import { AudioLevelMonitor } from './AudioLevelMonitor';
import { Metronome } from '../../metronome';
import { MonoToStereoConverter } from './MonoToStereoConverter';
import { AudioContextProvider } from '../../audio-context';

export class LocalAudioMixer {
  private audioContextProvider: AudioContextProvider;

  private audioLevelMonitor!: AudioLevelMonitor;
  private monoToStereoConverter!: MonoToStereoConverter;
  private metronome!: Metronome;

  private trackMixer!: GainNode;
  private destination!: MediaStreamAudioDestinationNode;

  constructor(audioContextProvider: AudioContextProvider) {
    this.audioContextProvider = audioContextProvider;
    (async () => {
      try {
        await this.initialize();
      } catch (err) {
        logger.error('Unhandled initialization failure', {
          error: err instanceof Error ? err.message : String(err),
          event_type: 'local_track_mixing_constructor_error',
        });
      }
    })();
  }

  private async initialize() {
    try {
      const audioContext = this.audioContextProvider.getAudioContext();

      // Create track mixer for multiple audio tracks
      this.trackMixer = audioContext.createGain();
      this.trackMixer.gain.value = 1;

      // Initializing local audio processing
      this.audioLevelMonitor = new AudioLevelMonitor(this.audioContextProvider);
      this.monoToStereoConverter = new MonoToStereoConverter(
        this.audioContextProvider,
        this.audioLevelMonitor.getRawOutputNode(),
      );

      this.monoToStereoConverter.getOutputNode().connect(this.trackMixer);

      // Initializing metronome
      this.metronome = new Metronome(
        this.audioContextProvider,
        this.trackMixer,
      );

      this.destination = audioContext.createMediaStreamDestination();
      this.trackMixer.connect(this.destination);

      logger.info('LocalTrackMixer was initialized', {
        event_type: 'local_track_mixing_initialized',
      });
    } catch (error) {
      logger.error('Failed to initialize local track mixing', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'local_track_mixing_init_error',
      });
      throw error;
    }
  }

  getAudioLevelMonitor(): AudioLevelMonitor {
    return this.audioLevelMonitor;
  }

  getMetronome(): Metronome {
    return this.metronome;
  }

  getCurrentStream(): MediaStream {
    return this.destination.stream;
  }

  connectAECNode(aecNode: AudioWorkletNode) {
    this.monoToStereoConverter.getOutputNode().disconnect(this.trackMixer);
    aecNode.connect(this.trackMixer);
  }

  disconnectAECNode(aecNode: AudioWorkletNode) {
    aecNode.disconnect(this.trackMixer);
    this.monoToStereoConverter.getOutputNode().connect(this.trackMixer);
  }
}
