/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  afterEach,
  beforeAll,
} from 'vitest';

let LocalAudioMixer: any;
let logger: any;
let FakeAudioLevelMonitor: any;
let FakeMetronome: any;
let FakeMonoToStereoConverter: any;

beforeAll(async () => {
  // ✅ Fakes
  const mockOutputNode = {
    connect: vi.fn(),
    disconnect: vi.fn(),
  };

  FakeAudioLevelMonitor = class {
    constructor(_: any) {}
    getRawOutputNode() {
      return mockOutputNode;
    }
  };

  FakeMonoToStereoConverter = class {
    constructor(_: any, __: any) {}
    getOutputNode() {
      return mockOutputNode;
    }
  };

  FakeMetronome = class {
    constructor(_: any, __: any) {}
  };

  // ✅ Mocks
  vi.mock('@/shared', () => ({
    logger: {
      info: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    },
  }));

  vi.mock('./AudioLevelMonitor', () => ({
    AudioLevelMonitor: FakeAudioLevelMonitor,
  }));

  vi.mock('../../metronome', () => ({
    Metronome: FakeMetronome,
  }));

  vi.mock('./MonoToStereoConverter', () => ({
    MonoToStereoConverter: FakeMonoToStereoConverter,
  }));

  // ✅ Import after mocks
  const mod = await import('./LocalAudioMixer');
  LocalAudioMixer = mod.LocalAudioMixer;

  const shared = await import('@/shared');
  logger = shared.logger;
});

describe('LocalAudioMixer', () => {
  let mixer: any;
  let mockAudioContext: any;
  let mockDestination: any;
  let mockStream: MediaStream;
  let audioContextProvider: any;

  beforeEach(async () => {
    vi.clearAllMocks();

    mockStream = {} as MediaStream;
    mockDestination = {
      stream: mockStream,
      connect: vi.fn(),
    };

    mockAudioContext = {
      state: 'running',
      currentTime: 0,
      createGain: vi.fn().mockReturnValue({
        gain: { value: 1 },
        connect: vi.fn(),
      }),
      createMediaStreamDestination: vi.fn().mockReturnValue(mockDestination),
    };

    audioContextProvider = {
      getAudioContext: () => mockAudioContext,
      isValid: () => true,
    };

    mixer = new LocalAudioMixer(audioContextProvider);
    await new Promise((r) => setTimeout(r, 10));
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('initializes audio processing and logs success', () => {
    expect(logger.info).toHaveBeenCalledWith(
      'LocalTrackMixer was initialized',
      expect.objectContaining({
        event_type: 'local_track_mixing_initialized',
      }),
    );
  });

  it('getCurrentStream returns the mixed MediaStream', () => {
    expect(mixer.getCurrentStream()).toBe(mockStream);
  });

  it('getAudioLevelMonitor returns the AudioLevelMonitor instance', () => {
    const monitor = mixer.getAudioLevelMonitor();
    expect(monitor).toBeInstanceOf(FakeAudioLevelMonitor);
  });

  it('getMetronome returns the Metronome instance', () => {
    const metronome = mixer.getMetronome();
    expect(metronome).toBeInstanceOf(FakeMetronome);
  });

  it('logs error if initialization fails', async () => {
    const badProvider = {
      getAudioContext: () => {
        throw new Error('init failed');
      },
      isValid: () => true,
    };

    const errorSpy = vi.spyOn(logger, 'error');
    const unhandled = vi
      .spyOn(globalThis, 'queueMicrotask')
      .mockImplementation((cb) => cb());

    new LocalAudioMixer(badProvider as any);
    await new Promise((resolve) => setTimeout(resolve, 10));

    expect(errorSpy).toHaveBeenCalledWith(
      'Failed to initialize local track mixing',
      expect.objectContaining({
        error: 'init failed',
        event_type: 'local_track_mixing_init_error',
      }),
    );

    unhandled.mockRestore();
  });
});
