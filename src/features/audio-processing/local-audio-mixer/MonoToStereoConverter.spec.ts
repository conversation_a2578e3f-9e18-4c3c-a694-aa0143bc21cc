/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { logger } from '@/shared';
import { MonoToStereoConverter } from './MonoToStereoConverter';

// ✅ Mock logger fully
vi.mock('@/shared', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
  },
}));

describe('MonoToStereoConverter', () => {
  let audioContextMock: any;
  let audioContextProviderMock: any;
  let sourceNodeMock: any;

  let splitterNodeMock: any;
  let mergerNodeMock: any;
  let gainNodes: any[];

  beforeEach(() => {
    vi.clearAllMocks();

    gainNodes = Array.from({ length: 3 }).map(() => ({
      gain: { value: 1 },
      channelCount: 0,
      channelCountMode: '',
      channelInterpretation: '',
      connect: vi.fn(),
      disconnect: vi.fn(),
    }));

    // Return gain nodes in the order: leftGain, rightGain, final gainNode
    let gainCallIndex = 0;
    const createGainMock = vi.fn(() => gainNodes[gainCallIndex++]);

    splitterNodeMock = {
      connect: vi.fn(),
      disconnect: vi.fn(),
    };

    mergerNodeMock = {
      channelCount: 0,
      channelCountMode: '',
      channelInterpretation: '',
      connect: vi.fn(),
      disconnect: vi.fn(),
    };

    audioContextMock = {
      createGain: createGainMock,
      createChannelSplitter: vi.fn().mockReturnValue(splitterNodeMock),
      createChannelMerger: vi.fn().mockReturnValue(mergerNodeMock),
    };

    audioContextProviderMock = {
      getAudioContext: () => audioContextMock,
    };

    sourceNodeMock = {
      channelCount: 1, // Will be overridden by converter
      channelCountMode: '',
      channelInterpretation: '',
      connect: vi.fn(),
      disconnect: vi.fn(),
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('forces stereo conversion regardless of input and creates full audio graph', () => {
    const converter = new MonoToStereoConverter(
      audioContextProviderMock,
      sourceNodeMock,
    );

    const outputNode = converter.getOutputNode();
    expect(outputNode).toBe(gainNodes[2]); // final gain node

    expect(audioContextMock.createChannelSplitter).toHaveBeenCalledWith(2);
    expect(audioContextMock.createChannelMerger).toHaveBeenCalledWith(2);
    expect(audioContextMock.createGain).toHaveBeenCalledTimes(3);

    // Ensure source connects to splitter
    expect(sourceNodeMock.connect).toHaveBeenCalledWith(splitterNodeMock);

    // Check splitter → leftGain → merger
    expect(splitterNodeMock.connect).toHaveBeenCalledWith(gainNodes[0], 0, 0);
    expect(gainNodes[0].connect).toHaveBeenCalledWith(mergerNodeMock, 0, 0);

    // splitter → rightGain (left-duplicate), and merger
    expect(splitterNodeMock.connect).toHaveBeenCalledWith(gainNodes[1], 0, 0);
    expect(gainNodes[1].connect).toHaveBeenCalledWith(mergerNodeMock, 0, 1);

    // Final connection: merger → gain
    expect(mergerNodeMock.connect).toHaveBeenCalledWith(gainNodes[2]);

    // Source node should be forced stereo
    expect(sourceNodeMock.channelCount).toBe(2);
    expect(sourceNodeMock.channelCountMode).toBe('max');
    expect(sourceNodeMock.channelInterpretation).toBe('speakers');
  });

  it('throws if source node is null', () => {
    expect(() => {
      new MonoToStereoConverter(audioContextProviderMock, null as any);
    }).toThrow('Source node is required');
  });

  it('throws if getOutputNode is called before initialization', () => {
    const converter = new MonoToStereoConverter(
      audioContextProviderMock,
      sourceNodeMock,
    );
    (converter as any).isConnected = false;

    expect(() => converter.getOutputNode()).toThrow(
      'Converter not properly initialized',
    );
  });

  it('disconnects audio graph safely', () => {
    const converter = new MonoToStereoConverter(
      audioContextProviderMock,
      sourceNodeMock,
    );

    converter.disconnect();

    expect(sourceNodeMock.disconnect).toHaveBeenCalled();
    expect(splitterNodeMock.disconnect).toHaveBeenCalled();
    expect(gainNodes[0].disconnect).toHaveBeenCalled(); // leftGain
    expect(gainNodes[1].disconnect).toHaveBeenCalled(); // rightGain
    expect(mergerNodeMock.disconnect).toHaveBeenCalled();
    expect(gainNodes[2].disconnect).toHaveBeenCalled(); // final gain

    expect(logger.info).toHaveBeenCalledWith(
      'MonoToStereoConverter disconnected',
      expect.objectContaining({
        event_type: 'mono_to_stereo_converter_disconnected',
      }),
    );
  });

  it('logs error if disconnect throws', () => {
    sourceNodeMock.disconnect = vi.fn(() => {
      throw new Error('disconnect failed');
    });

    const converter = new MonoToStereoConverter(
      audioContextProviderMock,
      sourceNodeMock,
    );

    converter.disconnect();

    expect(logger.error).toHaveBeenCalledWith(
      '[MonoToStereoConverter] Error during disconnect',
      expect.objectContaining({
        error: 'disconnect failed',
        event_type: 'mono_to_stereo_converter_disconnect_error',
      }),
    );
  });
});
