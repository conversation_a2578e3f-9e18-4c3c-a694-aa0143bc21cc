/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TrackMixer } from './TrackMixer';
import { AudioLevelMonitor } from './AudioLevelMonitor';
import { AudioContextProvider } from '../../audio-context';
import { CustomAudioAnalyser } from '../custom-nodes';
import { logger } from '@/shared';

describe('AudioLevelMonitor', () => {
  let monitor: AudioLevelMonitor;
  let fakeProvider: Awaited<ReturnType<typeof AudioContextProvider.create>>;
  let mixerDestroySpy: ReturnType<typeof vi.fn>;
  let infoSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(async () => {
    fakeProvider = await AudioContextProvider.create();

    vi.spyOn(AudioContextProvider.prototype, 'getAudioContext').mockReturnValue(
      fakeProvider.getAudioContext(),
    );
    vi.spyOn(AudioContextProvider.prototype, 'isValid').mockReturnValue(true);

    infoSpy = vi.spyOn(logger, 'info').mockImplementation(() => {});

    mixerDestroySpy = vi.fn().mockResolvedValue(undefined);

    vi.spyOn(TrackMixer.prototype, 'getMixer').mockReturnValue(
      fakeProvider.getAudioContext().createGain(),
    );
    vi.spyOn(TrackMixer.prototype, 'destroy').mockImplementation(
      mixerDestroySpy,
    );

    vi.stubGlobal('crypto', {
      randomUUID: () => 'mocked-uuid',
    });

    monitor = new AudioLevelMonitor(new AudioContextProvider());

    // wait for async initialize to finish
    await new Promise((resolve) => setTimeout(resolve, 10));
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('initializes correctly and logs init', () => {
    expect(infoSpy).toHaveBeenCalledWith(
      'Local audio processing initialized',
      expect.objectContaining({
        gain_value: 1.0,
        compressor_threshold: -24,
        analyser_fft_size: expect.any(Number),
        event_type: 'local_audio_processing_init',
      }),
    );
  });

  it('exposes nodes/getters', () => {
    expect(monitor.getTrackMixer()).toBeInstanceOf(TrackMixer);
    expect(monitor.getCompressor()).toBeInstanceOf(
      fakeProvider.getAudioContext().createDynamicsCompressor().constructor,
    );
    expect(monitor.getCustomAnalyser()).toBeInstanceOf(CustomAudioAnalyser);
    // getRawOutputNode should be the analyser, processed is the compressor
    expect(monitor.getRawOutputNode()).toBe(monitor.getCustomAnalyser());
    expect(monitor.getProcessedOutputNode()).toBe(monitor.getCompressor());
  });

  it('getGain returns current gain value', () => {
    expect(monitor.getGain()).toBe(1.0);
  });

  it('setGain clamps value, logs change, and updates gain node', () => {
    const setSpy = vi.spyOn(monitor['gainNode'].gain, 'setValueAtTime');

    monitor.setGain(2.5);
    expect(setSpy).toHaveBeenCalledWith(2.5, expect.any(Number));

    monitor.setGain(5);
    expect(setSpy).toHaveBeenCalledWith(3, expect.any(Number));

    monitor.setGain(-1);
    expect(setSpy).toHaveBeenCalledWith(0, expect.any(Number));

    expect(infoSpy).toHaveBeenCalledWith(
      'Local audio processing gain changed',
      expect.objectContaining({
        event_type: 'local_audio_processing_gain_change',
      }),
    );
  });

  it('setGain warns if AudioContextProvider is invalid', () => {
    const warnSpy = vi.spyOn(logger, 'warn').mockImplementation(() => {});
    vi.spyOn(AudioContextProvider.prototype, 'isValid').mockReturnValue(false);
    monitor.setGain(1.0);
    expect(warnSpy).toHaveBeenCalledWith(
      'Cannot connect stream: AudioContext is not valid',
    );
  });

  it('CustomAnalyser getAudioLevel / getPeakLevel compute normalized values', () => {
    const analyser = monitor.getCustomAnalyser();

    // Stub data sources
    const freqSpy = vi
      .spyOn(analyser, 'getByteFrequencyData')
      .mockImplementation((buf) => buf.fill(128));
    vi.spyOn(analyser, 'getByteTimeDomainData').mockImplementation((buf) => {
      for (let i = 0; i < buf.length; i++) buf[i] = i % 2 === 0 ? 200 : 56;
    });

    // Control time: start beyond the interval so we compute, not cache
    let now = 100; // NOT 0
    const nowSpy = vi.spyOn(performance, 'now').mockImplementation(() => now);

    const level = analyser.getAudioLevel();
    expect(level).toBeGreaterThanOrEqual(0);
    expect(level).toBeLessThanOrEqual(1);
    expect(freqSpy).toHaveBeenCalled();

    // Advance time again so peak recomputes
    now = 200;
    expect(analyser.getPeakLevel()).toBeCloseTo((200 - 128) / 128, 3);

    nowSpy.mockRestore();
  });

  it('startMonitoring polls analyser and stopMonitoring cancels RAF', () => {
    const rafSpy = vi
      .spyOn(globalThis, 'requestAnimationFrame')
      .mockImplementation((): number => 456);
    const cafSpy = vi
      .spyOn(globalThis, 'cancelAnimationFrame')
      .mockImplementation(() => {});

    const analyser = monitor.getCustomAnalyser();
    const levelSpy = vi.spyOn(analyser, 'getAudioLevel').mockReturnValue(0.42);
    const peakSpy = vi.spyOn(analyser, 'getPeakLevel').mockReturnValue(0.84);

    const callback = vi.fn();
    const id = monitor.startMonitoring(callback);

    expect(callback).toHaveBeenCalledWith(0.42, 0.84);
    expect(levelSpy).toHaveBeenCalled();
    expect(peakSpy).toHaveBeenCalled();
    expect(id).toBe('mocked-uuid');

    monitor.stopMonitoring(id);
    expect(cafSpy).toHaveBeenCalledWith(456);

    rafSpy.mockRestore();
    cafSpy.mockRestore();
  });

  it('destroy stops monitoring and destroys TrackMixer', async () => {
    const cafSpy = vi
      .spyOn(globalThis, 'cancelAnimationFrame')
      .mockImplementation(() => {});
    const rafSpy = vi
      .spyOn(globalThis, 'requestAnimationFrame')
      .mockImplementation(() => 999);

    const sessionId = monitor.startMonitoring(() => {});
    expect(monitor['monitoringSessions'].has(sessionId)).toBe(true);

    await monitor.destroy();

    expect(cafSpy).toHaveBeenCalledWith(999);
    expect(mixerDestroySpy).toHaveBeenCalled();

    rafSpy.mockRestore();
    cafSpy.mockRestore();
  });
});
