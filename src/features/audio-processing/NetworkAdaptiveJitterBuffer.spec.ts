/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types */
import { describe, it, beforeEach, afterEach, expect, vi } from 'vitest';
import {
  NetworkAdaptiveJitterBuffer,
  networkAdaptiveJitterBuffer,
} from './NetworkAdaptiveJitterBuffer';
import {
  Room,
  RoomEvent,
  RemoteAudioTrack,
  ConnectionQuality,
  RemoteTrackPublication,
  RemoteParticipant,
} from 'livekit-client';

describe('NetworkAdaptiveJitterBuffer', () => {
  let buffer: NetworkAdaptiveJitterBuffer;
  let mockRoom: Room;

  beforeEach(() => {
    buffer = networkAdaptiveJitterBuffer;
    mockRoom = new Room();
    buffer.initialize(mockRoom);
  });

  afterEach(() => {
    buffer.destroy();
    vi.restoreAllMocks();
  });

  it('should initialize and store the room reference', () => {
    expect((buffer as any).room).toBe(mockRoom);
  });

  it('should add and remove audio tracks on TrackSubscribed and TrackUnsubscribed', () => {
    const mockTrack: RemoteAudioTrack = {
      kind: 'audio',
      setPlayoutDelay: vi.fn(),
      receiver: {},
      sid: 'track1', // for easier debugging
    } as unknown as RemoteAudioTrack;

    const participant: RemoteParticipant = {
      sid: 'participant1',
    } as RemoteParticipant;

    const publication: RemoteTrackPublication = {
      trackSid: 'track1',
      track: mockTrack,
      kind: 'audio',
    } as unknown as RemoteTrackPublication;

    mockRoom.emit(
      RoomEvent.TrackSubscribed,
      mockTrack,
      publication,
      participant,
    );

    const trackId = `${participant.sid}_${publication.trackSid}`;
    expect((buffer as any).tracks.has(trackId)).toBe(true);

    mockRoom.emit(
      RoomEvent.TrackUnsubscribed,
      mockTrack,
      publication,
      participant,
    );
    expect((buffer as any).tracks.has(trackId)).toBe(false);
  });

  it('should adjust buffer on ConnectionQualityChanged event', () => {
    const mockTrack: RemoteAudioTrack = {
      kind: 'audio',
      setPlayoutDelay: vi.fn(),
      receiver: {},
    } as unknown as RemoteAudioTrack;

    const participant: RemoteParticipant = {
      sid: 'participant2',
    } as RemoteParticipant;

    const publication: RemoteTrackPublication = {
      trackSid: 'track2',
      track: mockTrack,
      kind: 'audio',
    } as unknown as RemoteTrackPublication;

    const trackId = `${participant.sid}_${publication.trackSid}`;
    (buffer as any).tracks.set(trackId, mockTrack);

    const setJitterBufferSpy = vi.spyOn(buffer as any, 'setJitterBuffer');

    mockRoom.emit(
      RoomEvent.ConnectionQualityChanged,
      ConnectionQuality.Poor,
      participant,
    );
    expect(setJitterBufferSpy).toHaveBeenCalledWith(mockTrack, 600);

    mockRoom.emit(
      RoomEvent.ConnectionQualityChanged,
      ConnectionQuality.Good,
      participant,
    );
    expect(setJitterBufferSpy).toHaveBeenCalledWith(mockTrack, 400);

    mockRoom.emit(
      RoomEvent.ConnectionQualityChanged,
      ConnectionQuality.Excellent,
      participant,
    );
    expect(setJitterBufferSpy).toHaveBeenCalledWith(mockTrack, 300);
  });

  it('should setFixedBuffer on all tracks', () => {
    const mockTrack: RemoteAudioTrack = {
      setPlayoutDelay: vi.fn(),
      receiver: {},
    } as unknown as RemoteAudioTrack;

    (buffer as any).tracks.set('test_track', mockTrack);

    const spy = vi.spyOn(buffer as any, 'setJitterBuffer');
    buffer.setFixedBuffer(500);

    expect(spy).toHaveBeenCalledWith(mockTrack, 500);
    expect(buffer.getCurrentBuffer()).toBe(500);
  });

  it('should handle setJitterBuffer with jitterBufferTarget when available', () => {
    const receiver = { jitterBufferTarget: 0 };
    const mockTrack: RemoteAudioTrack = {
      setPlayoutDelay: vi.fn(),
      receiver,
    } as unknown as RemoteAudioTrack;

    const success = (buffer as any).setJitterBuffer(mockTrack, 450);
    expect(success).toBe(true);
    expect(receiver.jitterBufferTarget).toBe(450);
    expect(mockTrack.setPlayoutDelay).toHaveBeenCalledWith(0.45);
  });

  it('should handle setJitterBuffer gracefully when receiver is missing', () => {
    const mockTrack: RemoteAudioTrack = {
      setPlayoutDelay: vi.fn(),
      receiver: undefined,
    } as unknown as RemoteAudioTrack;

    const success = (buffer as any).setJitterBuffer(mockTrack, 450);
    expect(success).toBe(false);
    expect(mockTrack.setPlayoutDelay).not.toHaveBeenCalled();
  });

  it('should clean up properly on destroy', () => {
    const roomSpy = vi.spyOn(mockRoom, 'removeAllListeners');
    buffer.destroy();

    expect(roomSpy).toHaveBeenCalled();
    expect((buffer as any).room).toBeNull();
    expect((buffer as any).tracks.size).toBe(0);
    expect((NetworkAdaptiveJitterBuffer as any).instance).toBeNull();
  });
});
