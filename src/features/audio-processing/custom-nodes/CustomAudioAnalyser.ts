import { isLowEndDevice, isMobile } from '../../../shared/utils';
import { AudioContextProvider } from '../../audio-context';

export class CustomAudioAnalyser extends AnalyserNode {
  private dataArray!: Uint8Array<ArrayBuffer>;

  private lastUpdateTime: number = 0;
  private readonly updateInterval: number;

  private cachedLevel: number = 0;
  private cachedPeakLevel: number = 0;

  constructor(audioContextProvider: AudioContextProvider) {
    const audioContext = audioContextProvider.getAudioContext();
    super(audioContext);

    this.fftSize = this.getOptimalBufferSize();
    this.dataArray = new Uint8Array(this.frequencyBinCount);
    this.updateInterval = this.getOptimalUpdateInterval();
  }

  getAudioLevel(): number {
    const now = performance.now();

    // Use cached value if called too frequently
    if (now - this.lastUpdateTime < this.updateInterval) {
      return this.cachedLevel;
    }

    this.lastUpdateTime = now;

    if (!this || !this.dataArray) {
      return 0;
    }

    // Get frequency data
    this.getByteFrequencyData(this.dataArray);

    // Calculate RMS with optimized algorithm
    let sum = 0;
    const length = this.dataArray.length;

    // Use fewer samples on mobile for better performance
    const step = isMobile() ? 4 : 2;
    for (let i = 0; i < length; i += step) {
      sum += this.dataArray[i] * this.dataArray[i];
    }

    const rms = Math.sqrt(sum / (length / step));
    this.cachedLevel = rms / 255; // Normalize to 0-1

    return this.cachedLevel;
  }

  getPeakLevel(): number {
    const now = performance.now();

    // Use cached value if called too frequently
    if (now - this.lastUpdateTime < this.lastUpdateTime) {
      return this.cachedPeakLevel;
    }

    this.lastUpdateTime = now;

    if (!this || !this.dataArray) {
      return 0;
    }

    // Get time domain data for peak detection
    this.getByteTimeDomainData(this.dataArray);

    // Find peak with optimized algorithm
    let max = 0;
    const length = this.dataArray.length;

    // Use fewer samples on mobile for better performance
    const step = isMobile() ? (isLowEndDevice() ? 8 : 4) : 2;
    for (let i = 0; i < length; i += step) {
      const value = Math.abs(this.dataArray[i] - 128);
      if (value > max) {
        max = value;
      }
    }

    // Cache and return normalized peak level
    this.cachedPeakLevel = max / 128; // Normalize to 0-1
    return this.cachedPeakLevel;
  }

  private getOptimalBufferSize(): number {
    if (isMobile()) {
      // Larger buffers on mobile reduce processing frequency
      return isLowEndDevice() ? 8192 : 4096;
    }

    // Desktop: smaller buffers for lower latency
    return 2048;
  }

  private getOptimalUpdateInterval(): number {
    if (isMobile()) {
      // Longer intervals on mobile to save battery
      return isLowEndDevice() ? 100 : 50; // 10Hz or 20Hz on mobile
    }

    // Desktop: more frequent updates for responsiveness
    return 16; // ~60Hz on desktop
  }
}
