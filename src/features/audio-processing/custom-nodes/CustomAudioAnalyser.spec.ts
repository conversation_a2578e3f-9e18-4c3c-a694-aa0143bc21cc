/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock both possible paths BEFORE importing the SUT
vi.mock('@/shared', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@/shared')>();
  return {
    ...actual,
    isMobile: vi.fn(() => false),
    isLowEndDevice: vi.fn(() => false),
  };
});

vi.mock('../../../shared', async (importOriginal) => {
  const actual = await importOriginal<typeof import('../../../shared')>();
  return {
    ...actual,
    isMobile: vi.fn(() => false),
    isLowEndDevice: vi.fn(() => false),
  };
});

import { CustomAudioAnalyser } from './CustomAudioAnalyser';

// Import from BOTH paths so we can flip whichever the SUT used.
import * as SharedAlias from '@/shared';
import * as SharedRel from '../../../shared';

const setMobile = (val: boolean) => {
  vi.mocked(SharedAlias.isMobile).mockReturnValue(val);
  vi.mocked(SharedRel.isMobile).mockReturnValue(val);
};
const setLowEnd = (val: boolean) => {
  vi.mocked(SharedAlias.isLowEndDevice).mockReturnValue(val);
  vi.mocked(SharedRel.isLowEndDevice).mockReturnValue(val);
};

import { AudioContextProvider } from '../../audio-context';

describe('CustomAudioAnalyser', () => {
  let fakeProvider: Awaited<ReturnType<typeof AudioContextProvider.create>>;

  beforeEach(async () => {
    fakeProvider = await AudioContextProvider.create();
    vi.spyOn(AudioContextProvider.prototype, 'getAudioContext').mockReturnValue(
      fakeProvider.getAudioContext(),
    );
    // default desktop
    setMobile(false);
    setLowEnd(false);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('uses fftSize 2048 on desktop', () => {
    setMobile(false);
    const analyser = new CustomAudioAnalyser(new AudioContextProvider());
    expect(analyser.fftSize).toBe(2048);
  });

  it('uses larger fftSize on mobile (4096) and even larger on low-end (8192)', () => {
    // Stub the *private* method at runtime (ok in JS; TS "private" is compile-time)
    const spy = vi
      .spyOn(CustomAudioAnalyser.prototype as any, 'getOptimalBufferSize')
      // first constructor call => mobile non-low-end
      .mockReturnValueOnce(4096)
      // second constructor call => mobile low-end
      .mockReturnValueOnce(8192);

    const a1 = new CustomAudioAnalyser(new AudioContextProvider());
    expect(a1.fftSize).toBe(4096);

    const a2 = new CustomAudioAnalyser(new AudioContextProvider());
    expect(a2.fftSize).toBe(8192);

    spy.mockRestore();
  });

  it('getAudioLevel computes and caches within the update interval', () => {
    setMobile(false);
    const analyser = new CustomAudioAnalyser(new AudioContextProvider());

    const now = vi.spyOn(performance, 'now');
    now.mockReturnValue(100);
    const v1 = analyser.getAudioLevel();
    expect(typeof v1).toBe('number');
    expect(v1).toBeGreaterThanOrEqual(0);
    expect(v1).toBeLessThanOrEqual(1);

    now.mockReturnValue(110); // cached
    const v2 = analyser.getAudioLevel();
    expect(v2).toBe(v1);

    now.mockReturnValue(150); // recompute
    const v3 = analyser.getAudioLevel();
    expect(typeof v3).toBe('number');

    now.mockRestore();
  });

  it('getPeakLevel computes normalized peak from time-domain data', () => {
    const analyser = new CustomAudioAnalyser(new AudioContextProvider());
    const timeSpy = vi
      .spyOn(
        analyser as unknown as { getByteTimeDomainData(a: Uint8Array): void },
        'getByteTimeDomainData',
      )
      .mockImplementation((arr: Uint8Array) => arr.fill(200));

    const peak = analyser.getPeakLevel();
    expect(peak).toBeCloseTo(72 / 128, 5);
    expect(timeSpy).toHaveBeenCalledTimes(1);
  });
});
