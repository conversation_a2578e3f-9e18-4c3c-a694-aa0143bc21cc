import { Material, useDeleteMaterial, useUser } from '@/entities';
import { DisabledTooltip } from '@/features/disabled-tooltip';
import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { Role } from '@/shared';
import { Trash2 } from 'lucide-react';
import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/shadcn/lib/utils';

interface IDeleteMaterialDialogProps {
  classroomId: string;
  material: Material;
  type?: 'class' | 'classroom';
}

export const DeleteMaterialDialog: FC<IDeleteMaterialDialogProps> = ({
  classroomId,
  material,
  type = 'classroom',
}) => {
  const { t } = useTranslation();
  const { mutate: deleteMaterial } = useDeleteMaterial(
    classroomId,
    material.id,
  );
  const [open, setOpen] = useState(false);
  const { user, role } = useUser();

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <DisabledTooltip
          tooltipText="globals.permissions.canDeleteMaterial"
          disabled={
            role !== Role.Teacher &&
            role !== Role.LeadTeacher &&
            material.uploadedBy !== user!.id
          }
        >
          <Button
            data-testid={`material-delete-button-${material.id}`}
            size="sm"
            onClick={() => setOpen(true)}
            variant="destructive"
            className={cn(type === 'class' && 'bg-transparent')}
            disabled={
              role !== Role.Teacher &&
              role !== Role.LeadTeacher &&
              material.uploadedBy !== user!.id
            }
          >
            {type === 'class' ? (
              <Trash2 className="size-5 opacity-70" />
            ) : (
              <>
                <Trash2 className="size-4 text-destructive-foreground" />
                <span className="text-sm text-destructive-foreground font-bold">
                  {t('classroom.materials.buttons.delete')}
                </span>
              </>
            )}
          </Button>
        </DisabledTooltip>
      </DialogTrigger>

      <DialogContent data-testid={`material-delete-dialog-${material.id}`}>
        <DialogHeader>
          <DialogTitle
            data-testid={`material-delete-dialog-title-${material.id}`}
          >
            {t('classroom.materials.dialogs.delete.title')}
          </DialogTitle>
          <DialogDescription
            data-testid={`material-delete-dialog-description-${material.id}`}
          >
            {t('classroom.materials.dialogs.delete.description')}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex items-center !justify-center">
          <DialogClose asChild>
            <Button
              data-testid={`material-delete-cancel-button-${material.id}`}
              variant="outline"
              size="lg"
            >
              {t('classroom.materials.dialogs.delete.buttons.cancel')}
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              data-testid={`material-delete-confirm-button-${material.id}`}
              onClick={() => deleteMaterial()}
              size="lg"
              variant="destructive"
            >
              {t('classroom.materials.dialogs.delete.buttons.confirm')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
