import { useUser } from '@/entities';
import { useLivekitContext } from '../../livekit';
import { logger, safePlay, useAppSelector } from '@/shared';
import { useEffect, useRef } from 'react';
import { centralAudioProcessing } from '../../audio-processing';
import { useTranslationContext } from '../context';
import { ConferenceStatus } from '../../conference';

export const useAudioTranslation = () => {
  const { status } = useAppSelector((state) => state.conference);
  const { room } = useLivekitContext();
  const { user } = useUser();
  const { enabled, audio } = useTranslationContext();

  const audioQueueRef = useRef<Blob[]>([]);
  const isPlayingRef = useRef<boolean>(false);
  const audioRef = useRef<HTMLAudioElement>(document.createElement('audio'));

  // Function to play audio chunks
  const playAudioChunk = (blob: Blob) => {
    try {
      const audioUrl = URL.createObjectURL(blob);
      audioRef.current.src = audioUrl;

      audioRef.current.oncanplaythrough = async () => {
        await safePlay(audioRef.current);
        centralAudioProcessing.getRemoteAudioMixer().setMasterGain(0.5);
      };

      audioRef.current.onplay = () => {
        isPlayingRef.current = true;
        logger.info('Translation audio playback started', {
          blob_size: blob.size,
          blob_type: blob.type,
          queue_length: audioQueueRef.current.length,
          event_type: 'translation_audio_play_started',
        });
      };

      audioRef.current.onended = () => {
        URL.revokeObjectURL(audioUrl);

        if (audioQueueRef.current.length > 0) {
          const nextChunk = audioQueueRef.current.shift();
          playAudioChunk(nextChunk!);
        }

        centralAudioProcessing.getRemoteAudioMixer().setMasterGain(1);
        isPlayingRef.current = false;

        logger.info('Translation audio playback ended', {
          remaining_queue_length: audioQueueRef.current.length,
          event_type: 'translation_audio_play_ended',
        });
      };

      audioRef.current.onerror = (error) => {
        logger.error('Translation audio playback failed', {
          error: error instanceof Error ? error.message : String(error),
          error_name: error instanceof DOMException ? error.name : 'Unknown',
          blob_size: blob.size,
          event_type: 'translation_audio_play_error',
        });

        URL.revokeObjectURL(audioUrl);
        isPlayingRef.current = false;
      };
    } catch (error) {
      logger.error('Failed to create audio from translation chunk', {
        error: error instanceof Error ? error.message : String(error),
        error_name: error instanceof DOMException ? error.name : 'Unknown',
        blob_size: blob.size,
        event_type: 'translation_audio_chunk_error',
      });
    }
  };

  useEffect(() => {
    if (!user || status !== ConferenceStatus.Ready || !enabled || !audio)
      return;

    const translationLanguage =
      user.preferences.defaultClassroomSettings.translationLanguage;
    const audioTopic = `tts-audio-${translationLanguage}`;

    logger.info('Translation audio handler registered', {
      target_language: translationLanguage,
      audio_topic: audioTopic,
      event_type: 'translation_audio_enabled',
    });

    room.registerByteStreamHandler(audioTopic, async (reader) => {
      try {
        const audioData = await reader.readAll();
        const audioBlob = new Blob(audioData, {
          type: 'audio/ogg; codecs=opus',
        });

        logger.info('Translation audio chunk received', {
          audio_size: audioBlob.size,
          audio_type: audioBlob.type,
          currently_playing: isPlayingRef.current,
          queue_length_before: audioQueueRef.current.length,
          event_type: 'translation_audio_chunk_received',
        });

        if (isPlayingRef.current) {
          audioQueueRef.current.push(audioBlob);
          logger.info('Translation audio chunk queued', {
            new_queue_length: audioQueueRef.current.length,
            event_type: 'translation_audio_chunk_queued',
          });
        } else {
          playAudioChunk(audioBlob);
        }
      } catch (error) {
        logger.error('Failed to process translation audio stream', {
          error: error instanceof Error ? error.message : String(error),
          error_name: error instanceof DOMException ? error.name : 'Unknown',
          audio_topic: audioTopic,
          event_type: 'translation_audio_stream_error',
        });
      }
    });

    return () => {
      logger.info('Translation audio handler unregistered', {
        final_queue_length: audioQueueRef.current.length,
        was_playing: isPlayingRef.current,
        audio_topic: audioTopic,
        event_type: 'translation_audio_disabled',
      });

      room.unregisterByteStreamHandler(audioTopic);
    };
  }, [status, user, enabled, audio]);
};
