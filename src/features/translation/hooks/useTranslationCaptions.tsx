import { useEffect, useRef } from 'react';
import {
  RoomParticipant,
  selectAllRoomUsers,
  useLivekitContext,
} from '../../livekit';
import { useAppSelector } from '@/shared';
import { useUser } from '@/entities';
import { TranslationMessage } from '../types';
import { useTranslationContext } from '../context';
import { logger } from '@/shared';
import { ConferenceStatus } from '../../conference';

export const useTranslationCaptions = () => {
  const { status } = useAppSelector((state) => state.conference);
  const { room } = useLivekitContext();
  const { user } = useUser();
  const { enabled, captions } = useTranslationContext();

  const roomUsers = useAppSelector(selectAllRoomUsers);
  const roomUsersRef = useRef<RoomParticipant[]>([]);
  useEffect(() => {
    roomUsersRef.current = roomUsers;
  }, [roomUsers]);

  const messageBufferRef = useRef<string[]>([]);
  const captionsTexts = useRef<string[]>([]);

  useEffect(() => {
    if (!user || status !== ConferenceStatus.Ready || !enabled || !captions) {
      return;
    }

    const translationLanguage =
      user.preferences.defaultClassroomSettings.translationLanguage;

    logger.info('Translation captions handler registered', {
      target_language: translationLanguage,
      room_users_count: roomUsersRef.current.length,
      event_type: 'translation_captions_enabled',
    });

    room.registerTextStreamHandler('translation', async (reader) => {
      try {
        const data = await reader.readAll();
        const message = JSON.parse(data) as TranslationMessage;
        const text = message.translations[translationLanguage];

        if (!text) {
          logger.warn('Translation not available for target language', {
            target_language: translationLanguage,
            message_type: message.type,
            original_language: message.language,
            event_type: 'translation_language_missing',
          });
          return;
        }

        if (message.isFinal) {
          const identity = reader.info.attributes?.['participant_id'] ?? '';
          const participant = roomUsersRef.current.find(
            (value) => value.identity === identity,
          );

          if (!participant) {
            logger.warn('Participant not found for translation', {
              participant_identity: identity,
              event_type: 'translation_participant_not_found',
            });
          }

          const displayName = participant?.profile?.displayName || '';
          let newCaption = `${displayName}: `;

          for (const textSegments of messageBufferRef.current) {
            newCaption += textSegments + ' ';
          }
          newCaption += text;

          captionsTexts.current.push(newCaption);

          logger.info('Translation caption added', {
            participant_identity: identity,
            participant_name: displayName,
            caption_length: newCaption.length,
            buffer_segments_used: messageBufferRef.current.length,
            total_captions: captionsTexts.current.length,
            event_type: 'translation_caption_created',
          });

          messageBufferRef.current = [];

          setTimeout(() => {
            captionsTexts.current.shift();
          }, 5000);
        } else {
          messageBufferRef.current.push(text);
        }
      } catch (error) {
        logger.error('Failed to process translation message', {
          error: error instanceof Error ? error.message : String(error),
          error_name: error instanceof DOMException ? error.name : 'Unknown',
          event_type: 'translation_processing_error',
        });
      }
    });

    return () => {
      logger.info('Translation captions handler unregistered', {
        final_captions_count: captionsTexts.current.length,
        event_type: 'translation_captions_disabled',
      });
      room.unregisterTextStreamHandler('translation');
    };
  }, [user, status, enabled, captions]);

  return captionsTexts;
};
