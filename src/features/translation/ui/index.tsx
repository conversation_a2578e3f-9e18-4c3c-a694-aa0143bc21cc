import { AnimatePresence, motion } from 'framer-motion';
import { useTranslationCaptions } from '../hooks';
import { useTranslationContext } from '../context';

export const Captions = () => {
  const captionsTexts = useTranslationCaptions();
  const { enabled, captions } = useTranslationContext();

  return (
    <div className="absolute bottom-2 left-0 right-0 z-50 mx-auto flex flex-col justify-center items-center px-2">
      <AnimatePresence>
        {enabled &&
          captions &&
          captionsTexts.current.map((text, index) => {
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="bg-black/80 text-white font-medium text-xl rounded-sm p-2 max-w-[600px] text-center"
              >
                {text}
              </motion.div>
            );
          })}
      </AnimatePresence>
    </div>
  );
};
