import React, { createContext, useContext, useState, ReactNode } from 'react';

interface TranslationContextValue {
  enabled: boolean;
  setEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  captions: boolean;
  setCaptions: React.Dispatch<React.SetStateAction<boolean>>;
  audio: boolean;
  setAudio: React.Dispatch<React.SetStateAction<boolean>>;
}

const TranslationContext = createContext<TranslationContextValue | undefined>(
  undefined,
);

export const TranslationProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [enabled, setEnabled] = useState<boolean>(true);
  const [captions, setCaptions] = useState<boolean>(false);
  const [audio, setAudio] = useState<boolean>(true);

  return (
    <TranslationContext.Provider
      value={{
        enabled,
        setEnabled,
        captions,
        setCaptions,
        audio,
        setAudio,
      }}
    >
      {children}
    </TranslationContext.Provider>
  );
};

export function useTranslationContext(): TranslationContextValue {
  const context = useContext(TranslationContext);
  if (!context) {
    throw new Error(
      'useTranslationContext must be used within a TranslationProvider',
    );
  }
  return context;
}
