import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { LoaderCircle, Paperclip, Plus, Upload } from 'lucide-react';
import { FC, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Input } from '@/shadcn/components/ui/input';
import { useUploadMaterial } from '@/entities';
import { useACL, useClassroomSettings } from '@/shared';
import { DisabledTooltip } from '@/features/disabled-tooltip';

const MAX_SIZE_MB = 10;

interface FormData {
  title: string;
  file: FileList;
}

interface IUploadMaterialDialogProps {
  classroomId: string;
  type?: 'class' | 'classroom';
}

export const UploadMaterialDialog: FC<IUploadMaterialDialogProps> = ({
  classroomId,
  type = 'classroom',
}) => {
  const { t } = useTranslation();
  const [show, setShow] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const acl = useACL();
  const classroomSettings = useClassroomSettings();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setError,
    clearErrors,
  } = useForm<FormData>({
    mode: 'onChange',
  });

  const { mutate: uploadMaterial, isPending } = useUploadMaterial(
    classroomId,
    setShow,
  );

  const fileList = watch('file');

  const validateFile = (fileList: FileList): string | true => {
    if (!fileList || fileList.length === 0) {
      return 'classroom.dialogs.uploadMaterial.fields.chooseFile.validation.required';
    }

    const file = fileList[0];

    if (file.type !== 'application/pdf') {
      return 'classroom.dialogs.uploadMaterial.fields.chooseFile.validation.type';
    }

    if (file.size > MAX_SIZE_MB * 1024 * 1024) {
      return `classroom.dialogs.uploadMaterial.fields.chooseFile.validation.size`;
    }

    return true;
  };

  const onSubmit = async (data: FormData) => {
    const file = data.file[0];
    uploadMaterial({ title: data.title, file: file });
  };

  useEffect(() => {
    if (fileList && fileList.length > 0) {
      const file = fileList[0];
      const validationResult = validateFile(fileList);

      if (validationResult === true) {
        setSelectedFile(file);
        clearErrors('file');
      } else {
        setSelectedFile(null);
        setError('file', { message: validationResult });
      }
    } else {
      setSelectedFile(null);
    }
  }, [fileList, setError, clearErrors]);

  useEffect(() => {
    if (show) {
      reset();
      setSelectedFile(null);
    }
  }, [show, reset]);

  return (
    <Dialog onOpenChange={setShow} open={show}>
      <DialogTrigger asChild>
        <DisabledTooltip
          tooltipText="globals.permissions.canUploadMaterials"
          disabled={acl ? !acl.canUploadMaterials : true}
        >
          {type === 'class' ? (
            <Button
              size="lg"
              className="w-full bg-primary hover:bg-primary/90 disabled:bg-primary/50 text-white disabled:cursor-not-allowed"
              disabled={
                acl && classroomSettings
                  ? !(
                      acl.canUploadMaterials &&
                      classroomSettings.allowMaterialsUpload
                    )
                  : true
              }
              data-testid="materials-upload-button"
              onClick={() => setShow(true)}
            >
              <Upload />
              {t('class.tools.materials.main.actions.upload.text')}
            </Button>
          ) : (
            <Button
              data-testid="upload-material-button"
              size="lg"
              disabled={
                acl && classroomSettings
                  ? !(
                      acl.canUploadMaterials &&
                      classroomSettings.allowMaterialsUpload
                    )
                  : true
              }
              className="font-bold max-[550px]:!w-full"
              onClick={() => setShow(true)}
            >
              <Plus className="size-5 text-accent-foreground" />
              {t('classroom.dialogs.uploadMaterial.trigger')}
            </Button>
          )}
        </DisabledTooltip>
      </DialogTrigger>

      <DialogContent
        data-testid="upload-material-dialog"
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <DialogHeader data-testid="upload-material-dialog-header">
          <DialogTitle data-testid="upload-material-dialog-title">
            {t('classroom.dialogs.uploadMaterial.title')}
          </DialogTitle>
          <DialogDescription data-testid="upload-material-dialog-description">
            {t('classroom.dialogs.uploadMaterial.description')}
          </DialogDescription>
        </DialogHeader>

        <form
          data-testid="upload-material-form"
          onSubmit={handleSubmit(onSubmit)}
          className="grid gap-4"
        >
          <div
            data-testid="upload-material-title-field"
            className="flex flex-col gap-2"
          >
            <Input
              data-testid="upload-material-title-input"
              className="border-primary/20"
              placeholder={t(
                'classroom.dialogs.uploadMaterial.fields.title.label',
              )}
              {...register('title', {
                required:
                  'classroom.dialogs.uploadMaterial.fields.title.validation.required',
                maxLength: {
                  value: 50,
                  message:
                    'classroom.dialogs.uploadMaterial.fields.title.validation.maxLength',
                },
                minLength: {
                  value: 5,
                  message:
                    'classroom.dialogs.uploadMaterial.fields.title.validation.minLength',
                },
              })}
            />

            {errors.title && (
              <p
                data-testid="upload-material-title-error"
                className="text-sm text-destructive -mt-1"
              >
                {t(errors.title.message ?? '')}
              </p>
            )}
          </div>

          <div
            data-testid="upload-material-file-field"
            className="flex flex-col gap-2"
          >
            <label
              htmlFor="file-upload"
              data-testid="upload-material-file-label"
              className={`flex items-center justify-center gap-2 py-4 px-6 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                errors.file
                  ? 'border-destructive/50 bg-destructive/5'
                  : 'border-primary/20 hover:bg-primary/5'
              }`}
            >
              <Paperclip
                data-testid="upload-material-file-icon"
                className={`size-4 ${errors.file ? 'text-destructive' : 'text-primary'}`}
              />
              <span
                data-testid="upload-material-file-text"
                className={`font-medium ${errors.file ? 'text-destructive' : 'text-primary'}`}
              >
                {selectedFile
                  ? selectedFile.name
                  : t(
                      'classroom.dialogs.uploadMaterial.fields.chooseFile.label',
                    )}
              </span>
            </label>

            <input
              {...register('file', {
                validate: validateFile,
              })}
              data-testid="upload-material-file-input"
              id="file-upload"
              type="file"
              accept=".pdf,application/pdf"
              multiple={false}
              className="hidden"
            />

            {errors.file && (
              <p
                data-testid="upload-material-file-error"
                className="text-sm text-destructive -mt-1"
              >
                {t(errors.file.message ?? '')}
              </p>
            )}
          </div>

          <DialogFooter data-testid="upload-material-dialog-footer">
            <Button
              data-testid="upload-material-submit-button"
              type="submit"
              size="lg"
              className="w-fit mx-auto"
              disabled={
                !selectedFile ||
                isSubmitting ||
                isPending ||
                Object.keys(errors).length > 0
              }
            >
              {isPending ? (
                <>
                  <LoaderCircle data-testid="upload-material-loading-icon" />
                  {t('classroom.dialogs.uploadMaterial.buttons.uploading')}
                </>
              ) : (
                t('classroom.dialogs.uploadMaterial.buttons.upload')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
