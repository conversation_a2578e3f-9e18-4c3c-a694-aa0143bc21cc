import { Button } from '@/shadcn/components/ui/button';
import { Video } from 'lucide-react';
import { FC, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { cva } from 'class-variance-authority';
import { cn } from '@/shadcn/lib/utils';
import { useCreateClass, useJoinClass } from '@/entities';
import { useACL } from '@/shared';
import { DisabledTooltip } from '@/features/disabled-tooltip';

interface ICreateClassButtonProps {
  id: string;
  triggerPlace: 'header' | 'history';
  disabled?: boolean;
}

const triggerButtonVariants = cva('font-bold', {
  variants: {
    triggerPlace: {
      header: 'max-[650px]:!w-full',
      history: 'max-[430px]:!w-full',
    },
  },
  defaultVariants: {
    triggerPlace: 'header',
  },
});

export const CreateClassButton: FC<ICreateClassButtonProps> = ({
  id,
  disabled,
  triggerPlace,
}) => {
  const { t } = useTranslation();
  const { mutate: createClass, data, isPending } = useCreateClass();
  const { mutate: joinClass } = useJoinClass();
  const acl = useACL();

  useEffect(() => {
    if (data && !isPending) {
      joinClass({
        classroomId: data.classroomId,
        classId: data.class.id,
      });
    }
  }, [data, isPending]);

  return (
    <DisabledTooltip
      tooltipText="globals.permissions.canStartClass"
      disabled={acl ? !acl.canStartClass : true}
    >
      <Button
        data-testid={`create-class-trigger-${triggerPlace}`}
        variant="default"
        size="lg"
        className={cn(triggerButtonVariants({ triggerPlace }))}
        disabled={disabled || (acl ? !acl.canStartClass : true)}
        onClick={() => {
          createClass({ id, title: '' });
        }}
      >
        <Video className="size-5 text-accent-foreground" />
        {t('classroom.dialogs.create.trigger')}
      </Button>
    </DisabledTooltip>
  );
};
