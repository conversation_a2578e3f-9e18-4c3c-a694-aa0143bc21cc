/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// --- Mocks (must be hoisted) ---
vi.mock('@/shared', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
  // by default, pretend play() is allowed and resolves
  safePlay: vi.fn(async (_el: HTMLMediaElement) => {
    // no-op; tests will fire 'playing' events explicitly
    return;
  }),
}));

import { PlayerElement } from './PlayerElement';
import { logger, safePlay } from '@/shared';

const dispatch = (el: EventTarget, type: string) =>
  el.dispatchEvent(new Event(type));

describe('PlayerElement', () => {
  beforeEach(() => {
    vi.useFakeTimers();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  it('constructs audio/video elements with expected defaults', () => {
    const p = new PlayerElement();
    const audio = p.getAudioElement();
    const video = p.getVideoElement();

    expect(audio.tagName.toLowerCase()).toBe('audio');
    expect(video.tagName.toLowerCase()).toBe('video');

    expect(video.muted).toBe(true);
    expect(audio.muted).toBe(false);
    expect(audio.volume).toBe(1);
    expect(video.preload).toBe('metadata');
    expect(audio.preload).toBe('metadata');
  });

  it('play() resolves when both audio and video fire "playing"', async () => {
    const p = new PlayerElement();

    const audio = p.getAudioElement();
    const video = p.getVideoElement();

    const playPromise = p.play();

    // ✅ Let both awaited safePlay calls run
    await Promise.resolve();
    await Promise.resolve();

    expect(vi.mocked(safePlay)).toHaveBeenCalledTimes(2);
    expect(vi.mocked(safePlay).mock.calls[0][0]).toBe(audio);
    expect(vi.mocked(safePlay).mock.calls[1][0]).toBe(video);

    // Now simulate both elements reporting "playing"
    dispatch(audio, 'playing');

    // Still pending until video plays
    dispatch(video, 'playing');

    await expect(playPromise).resolves.toBeUndefined();
    expect(logger.error).not.toHaveBeenCalledWith(
      'PlayerElement: Failed to initiate play',
      expect.anything(),
    );
  });

  it('play() rejects if audio emits "error"', async () => {
    const p = new PlayerElement();
    const audio = p.getAudioElement();

    const playPromise = p.play();

    dispatch(audio, 'error');

    await expect(playPromise).rejects.toThrow(/Audio play error/i);
    expect(logger.error).toHaveBeenCalledWith(
      'PlayerElement: Audio play error',
      expect.objectContaining({
        event_type: 'player_element_audio_play_error',
      }),
    );
  });

  it('play() rejects if video emits "error"', async () => {
    const p = new PlayerElement();
    const video = p.getVideoElement();

    const playPromise = p.play();

    dispatch(video, 'error');

    await expect(playPromise).rejects.toThrow(/Video play error/i);
    expect(logger.error).toHaveBeenCalledWith(
      'PlayerElement: Video play error',
      expect.objectContaining({
        event_type: 'player_element_video_play_error',
      }),
    );
  });

  it('seekTo() sets audio currentTime immediately, video after ~10ms, and resolves on seeked + resume', async () => {
    const p = new PlayerElement();
    const audio = p.getAudioElement();
    const video = p.getVideoElement();

    // Make both elements "resumable" (readyState >= 3)
    Object.defineProperty(audio, 'readyState', {
      configurable: true,
      value: 3,
    });
    Object.defineProperty(video, 'readyState', {
      configurable: true,
      value: 3,
    });

    const seekPromise = p.seekTo(12.34);

    // Audio seek happens immediately
    expect(audio.currentTime).toBeCloseTo(12.34, 5);

    // Video seek scheduled after ~10ms
    vi.advanceTimersByTime(10);
    expect(video.currentTime).toBeCloseTo(12.34, 5);

    // The promise resolves when:
    // - both 'seeked' fired AND
    // - playback "resumed" ('playing' or 'canplay' on both)
    dispatch(audio, 'seeked');
    dispatch(video, 'seeked');

    // Satisfy resume
    dispatch(audio, 'canplay');
    dispatch(video, 'canplay');

    await expect(seekPromise).resolves.toBeUndefined();
  });

  it('seekTo() resolves on 5s timeout if events never arrive', async () => {
    const p = new PlayerElement();

    const seekPromise = p.seekTo(4.2);
    vi.advanceTimersByTime(10); // allow the 10ms video seek scheduling

    // ✅ Don’t assert "still pending"; some environments may auto-emit events.
    // Instead, fast-forward to the full timeout and assert it resolves with a warning.
    vi.advanceTimersByTime(5000);
    await expect(seekPromise).resolves.toBeUndefined();

    expect(logger.warn).toHaveBeenCalledWith(
      'PlayerElement: Seek operation timed out',
      expect.objectContaining({ event_type: 'player_element_seek_timeout' }),
    );
  });

  it('timeupdate on audio triggers sync when video is behind and ready', () => {
    const p = new PlayerElement();
    const audio = p.getAudioElement();
    const video = p.getVideoElement();

    // Force video to be "ready"
    Object.defineProperty(video, 'readyState', {
      configurable: true,
      value: 2,
    });

    // Make audio time ahead of video time
    audio.currentTime = 5;
    video.currentTime = 1;

    const nowSpy = vi.spyOn(Date, 'now');
    nowSpy.mockReturnValue(3000);

    // Dispatch audio time update to invoke sync path
    audio.dispatchEvent(new Event('timeupdate'));

    // performVideoSync sets video currentTime to audio's time
    expect(video.currentTime).toBeCloseTo(5, 5);

    // Fire 'seeked' to finish the sync cycle
    video.dispatchEvent(new Event('seeked'));
    nowSpy.mockRestore();
  });

  it('pause() pauses both elements', () => {
    const p = new PlayerElement();
    const audio = p.getAudioElement();
    const video = p.getVideoElement();

    const pauseAudio = vi.spyOn(audio, 'pause');
    const pauseVideo = vi.spyOn(video, 'pause');

    p.pause();

    expect(pauseAudio).toHaveBeenCalledTimes(1);
    expect(pauseVideo).toHaveBeenCalledTimes(1);
  });

  it('destroy() removes timeupdate listeners', () => {
    const p = new PlayerElement();

    const audio = p.getAudioElement();
    const video = p.getVideoElement();

    const remAudio = vi.spyOn(audio, 'removeEventListener');
    const remVideo = vi.spyOn(video, 'removeEventListener');

    p.destroy();

    expect(remAudio).toHaveBeenCalledWith('timeupdate', expect.any(Function));
    expect(remVideo).toHaveBeenCalledWith('timeupdate', expect.any(Function));
  });

  it('play() logs and rejects if safePlay throws on start', async () => {
    vi.mocked(safePlay).mockRejectedValueOnce(new Error('no autoplay'));
    const p = new PlayerElement();

    await expect(p.play()).rejects.toThrow('no autoplay');

    expect(logger.error).toHaveBeenCalledWith(
      'PlayerElement: Failed to initiate play',
      expect.objectContaining({
        error: 'no autoplay',
        event_type: 'player_element_play_initiate_error',
      }),
    );
  });
});
