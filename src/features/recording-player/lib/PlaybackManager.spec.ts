/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// ---- Mocks (hoisted) ----

// Mock API calls
vi.mock('../api', () => ({
  getRecordingDetails: vi.fn(async (_id: string) => ({
    id: _id,
    classId: 'class-1',
    classroomId: 'room-1',
    duration: 1234,
    fileSize: 111,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    activeSpeakers: [],
  })),
  getRecordingHls: vi.fn(async (_id: string) => ({
    id: _id,
    classId: 'class-1',
    classroomId: 'room-1',
    duration: 120,
    trackCount: 2,
    tracks: [
      {
        participantId: 'p1',
        participantName: 'P1',
        participantRole: 'role',
        participantFullName: 'P One',
        participantAvatar: '',
        playlistUrl: 'https://cdn.example.com/p1/master.m3u8',
        duration: 50,
        startTime: '2025-01-01T00:00:00.000Z',
        endTime: '2025-01-01T00:00:50.000Z',
        playlistItems: [
          { uri: 'https://cdn.example.com/p1/seg-1.ts', duration: 2 },
        ],
      },
      {
        participantId: 'p2',
        participantName: 'P2',
        participantRole: 'role',
        participantFullName: 'P Two',
        participantAvatar: '',
        playlistUrl: 'https://cdn.example.com/p2/master.m3u8',
        duration: 60,
        startTime: '2025-01-01T00:00:10.000Z', // starts 10s after p1
        endTime: '2025-01-01T00:01:10.000Z',
        playlistItems: [
          { uri: 'https://cdn.example.com/p2/seg-1.ts', duration: 2 },
        ],
      },
    ],
    createdAt: '2025-01-01T00:00:00.000Z',
    updatedAt: '2025-01-01T00:10:00.000Z',
  })),
}));

// Mock ParticipantPlayer — capture array lives inside factory; expose for tests
vi.mock('./ParticipantPlayer', () => {
  const players: any[] = [];

  class MockPlayerElement {
    element: HTMLVideoElement = document.createElement('video');
    playCalls = 0;
    pauseCalls = 0;
    lastSeekTo: number | null = null;

    getVideoElement() {
      return this.element;
    }
    async play() {
      this.playCalls += 1;
    }
    pause() {
      this.pauseCalls += 1;
    }
    async seekTo(t: number) {
      this.lastSeekTo = t;
    }
  }

  class MockParticipantPlayer {
    tracks: any[];
    player = new MockPlayerElement();
    destroyed = 0;

    // Signature: (tracks, provider, destination)
    constructor(tracks: any[], _provider: any, _dest: any) {
      this.tracks = tracks;
      players.push(this);
    }

    update(_t: number) {
      // SUT calls this during playAll / timeline tick — not essential to assert here
    }

    // For seekAll(), SUT asks active track directly. Return the first track.
    getActiveTrack() {
      return this.tracks[0] ?? null;
    }

    getPlayer() {
      return this.player;
    }

    destroy() {
      this.destroyed += 1;
    }
  }

  return { ParticipantPlayer: MockParticipantPlayer, __players: players };
});

// Mock audio context provider (new path)
vi.mock('@/features/audio-context', () => {
  const fakeGainNode = {
    connect: vi.fn(),
    disconnect: vi.fn(),
    gain: { value: 1, setValueAtTime: vi.fn() },
  };
  const fakeAudioContext = {
    state: 'running' as const,
    currentTime: 0,
    destination: {} as unknown as AudioDestinationNode,
    createGain: vi.fn(() => fakeGainNode), // 👈 vi.fn so .mock exists
  };
  class AudioContextProvider {
    static async create() {
      return new AudioContextProvider();
    }
    getAudioContext() {
      return fakeAudioContext as unknown as AudioContext;
    }
    async resume() {
      (fakeAudioContext as any).state = 'running';
    }
    async close() {}
  }
  return { AudioContextProvider };
});

vi.mock('@/shared', () => {
  const logger = {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };
  const isMobile = vi.fn(() => false);
  const isLowEndDevice = vi.fn(() => false);
  return { logger, isMobile, isLowEndDevice };
});

// ---- Imports AFTER mocks ----
import { getRecordingHls } from '../api';
import { PlaybackManager } from './PlaybackManager';
import { logger } from '@/shared';

describe('PlaybackManager (timeline + player wrapper)', () => {
  let root: HTMLDivElement;
  let playersCapture: any[];
  let playersModule: any;

  beforeEach(async () => {
    vi.useFakeTimers();

    // DOM container for video element swapping
    root = document.createElement('div');
    root.id = 'video-player';
    document.body.appendChild(root);

    // Access the mocked ParticipantPlayer module to get the capture array
    playersModule = await vi.importMock<any>('./ParticipantPlayer');
    playersCapture = playersModule.__players as any[];
    playersCapture.splice(0); // clear captured players

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.useRealTimers();
    if (root && document.body.contains(root)) {
      document.body.removeChild(root);
    }
    vi.restoreAllMocks();
  });

  const makeEvents = () => ({
    onParticipantSelected: vi.fn(), // now accepts string | null
    onShowPlayer: vi.fn(),
    onActiveParticipantsChange: vi.fn(),
  });

  it('initializes, builds timeline, creates participant players, sets primary, appends video and shows player (no auto-play)', async () => {
    const ev = makeEvents();
    const manager = await PlaybackManager.create('rec-1', ev);

    expect(ev.onShowPlayer).toHaveBeenCalledWith(true);

    // Two participant players created (p1, p2)
    expect(playersCapture.length).toBe(2);

    // Primary participant is earliest start => p1
    expect((manager as any).primaryParticipantId).toBe('p1');

    // Video element inserted into DOM
    expect(root.childElementCount).toBe(1);
    const primaryPlayer = playersCapture[0].player;

    // No auto-play on init anymore
    expect(primaryPlayer.playCalls).toBe(0);

    // init completion log
    expect(logger.info).toHaveBeenCalledWith(
      'PlaybackManager: Initialization completed successfully',
      expect.objectContaining({
        recording_id: 'rec-1',
        participant_count: 2,
        primary_participant: 'p1',
        event_type: 'playback_manager_init_complete',
      }),
    );

    // Global state remains "not playing" until playAll()
    expect(manager.getIsPlaying()).toBe(false);
  });

  it('switches primary video via setVideoElement and logs (does not play unless already playing)', async () => {
    const ev = makeEvents();
    const manager = await PlaybackManager.create('rec-2', ev);

    // Switch to p2
    manager.setVideoElement('p2');

    // The currently appended element should come from the second participant's player
    expect(root.childElementCount).toBe(1);
    const second = playersCapture[1];
    expect(root.children[0]).toBe(second.player.getVideoElement());

    // Not playing yet → no extra play() call
    expect(second.player.playCalls).toBe(0);

    expect(ev.onParticipantSelected).toHaveBeenCalledWith('p2');
    expect(logger.info).toHaveBeenCalledWith(
      'PlaybackManager: Primary video participant changed',
      expect.objectContaining({
        previous_participant: 'p1',
        new_participant: 'p2',
        event_type: 'playback_manager_primary_video_change',
      }),
    );
  });

  it('playAll resumes context if needed, logs, calls update() and play() for each participant', async () => {
    const ev = makeEvents();
    const manager = await PlaybackManager.create('rec-3', ev);

    await manager.playAll();

    // Each participant's player was told to play at least once
    expect(playersCapture.every((p: any) => p.player.playCalls >= 1)).toBe(
      true,
    );

    expect(logger.info).toHaveBeenCalledWith(
      'PlaybackManager: Starting global timeline playback',
      expect.objectContaining({ event_type: 'playback_manager_play_start' }),
    );

    expect(manager.getIsPlaying()).toBe(true);
  });

  it('pauseAll logs and pauses all players only if playing', async () => {
    const ev = makeEvents();
    const manager = await PlaybackManager.create('rec-4', ev);

    await manager.playAll();
    manager.pauseAll();

    playersCapture.forEach((p: any) => {
      expect(p.player.pauseCalls).toBeGreaterThanOrEqual(1);
    });

    expect(logger.info).toHaveBeenCalledWith(
      'PlaybackManager: Global timeline paused',
      expect.objectContaining({
        event_type: 'playback_manager_pause',
      }),
    );
  });

  it('seekAll clamps time and seeks relative to each participant startOffset; logs on large jumps', async () => {
    const ev = makeEvents();
    const manager = await PlaybackManager.create('rec-5', ev);

    // Our ParticipantPlayer mock returns getActiveTrack() as its first track.
    // calculateGlobalTimeline() built these as:
    // p1: startOffset = 0
    // p2: startOffset = 10
    await manager.seekAll(30);

    const p1 = playersCapture[0]; // first participant
    const p2 = playersCapture[1]; // second participant

    expect(p1.player.lastSeekTo).toBe(30); // 30 - 0
    expect(p2.player.lastSeekTo).toBe(20); // 30 - 10

    expect(logger.info).toHaveBeenCalledWith(
      'PlaybackManager: Timeline seek performed',
      expect.objectContaining({
        from_time: 0,
        to_time: 30,
        event_type: 'playback_manager_seek',
      }),
    );

    // Negative clamp -> 0
    await manager.seekAll(-10);
    expect(p1.player.lastSeekTo).toBe(0); // 0 - 0
    expect(p2.player.lastSeekTo).toBe(-10); // 0 - 10 => -10 (manager clamps global time only)
  });

  it('setMasterVolume sets AudioParam via provider context time', async () => {
    const ev = makeEvents();
    const manager = await PlaybackManager.create('rec-6', ev);

    manager.setMasterVolume(0.4);

    const audioCtxModule = await vi.importMock<any>('@/features/audio-context');
    const ctx =
      new audioCtxModule.AudioContextProvider().getAudioContext() as any;
    const gain = ctx.createGain.mock.results[0].value;

    expect(gain.gain.setValueAtTime).toHaveBeenCalledWith(0.4, ctx.currentTime);
  });

  it('handles missing video container gracefully in setVideoElement', async () => {
    const ev = makeEvents();
    const manager = await PlaybackManager.create('rec-7', ev);

    // remove container
    root.remove();

    manager.setVideoElement('p1');
    expect(logger.error).toHaveBeenCalledWith(
      'PlaybackManager: Video player container not found in DOM',
      expect.objectContaining({
        participant_id: 'p1',
        event_type: 'playback_manager_no_video_container',
      }),
    );
  });

  it('logs error and hides player if initialize sees no tracks', async () => {
    const ev = makeEvents();

    vi.mocked(getRecordingHls).mockResolvedValueOnce({
      id: 'rec-empty',
      classId: 'class-1',
      classroomId: 'room-1',
      duration: 0,
      trackCount: 0,
      tracks: [],
      createdAt: '2025-01-01T00:00:00.000Z',
      updatedAt: '2025-01-01T00:10:00.000Z',
    } as any);

    await PlaybackManager.create('rec-empty', ev);

    expect(logger.error).toHaveBeenCalledWith(
      'PlaybackManager: Initialization failed',
      expect.objectContaining({
        recording_id: 'rec-empty',
        event_type: 'playback_manager_init_error',
      }),
    );
    expect(ev.onShowPlayer).toHaveBeenLastCalledWith(false);
  });

  it('destroy clears timeline, destroys all participant players, closes context and logs', async () => {
    const ev = makeEvents();
    const manager = await PlaybackManager.create('rec-8', ev);

    await manager.destroy();

    playersCapture.forEach((p: any) => {
      expect(p.destroyed).toBe(1);
    });

    expect(logger.info).toHaveBeenCalledWith(
      'PlaybackManager: Destruction completed',
      expect.objectContaining({
        event_type: 'playback_manager_destroy_complete',
      }),
    );
  });
});
