import { logger, safePlay } from '@/shared';

export class PlayerElement {
  // Separate elements
  private videoElement!: HTMLVideoElement;
  private audioElement!: HTMLAudioElement;

  // Sync control
  private audioTimeUpdateHandler: (() => void) | null = null;
  private videoTimeUpdateHandler: (() => void) | null = null;

  private lastSyncTime: number = 0;
  private syncAttempts: number = 0;
  private maxSyncAttempts: number = 3;

  private isCurrentlySeeking: boolean = false;

  constructor() {
    this.createElements();
    this.setupSync();
  }

  private createElements() {
    // Video element - display only, always muted
    this.videoElement = document.createElement('video');
    this.videoElement.controls = false;
    this.videoElement.muted = true;
    this.videoElement.className =
      'h-full aspect-video max-[900px]:landscape:aspect-auto';
    this.videoElement.preload = 'metadata';

    // Audio element - hidden, for Web Audio processing
    this.audioElement = document.createElement('audio');
    this.audioElement.style.display = 'none';
    this.audioElement.muted = false;
    this.audioElement.volume = 1.0;
    this.audioElement.preload = 'metadata';
  }

  private setupSync() {
    // Listen to audio timeupdate event - much more efficient than setInterval
    this.audioTimeUpdateHandler = () => {
      if (!this.isCurrentlySeeking) {
        this.checkAndSyncVideo();
      }
    };

    // Listen to video events for better sync coordination
    this.videoTimeUpdateHandler = () => {
      if (!this.isCurrentlySeeking && this.syncAttempts > 0) {
        this.syncAttempts = Math.max(0, this.syncAttempts - 0.1);
      }
    };

    this.audioElement.addEventListener(
      'timeupdate',
      this.audioTimeUpdateHandler,
    );
    this.videoElement.addEventListener(
      'timeupdate',
      this.videoTimeUpdateHandler,
    );
  }

  private checkAndSyncVideo() {
    if (!this.audioElement || !this.videoElement) return;

    // Skip if video is not ready
    if (this.videoElement.readyState < 2) return;

    const audioTime = this.audioElement.currentTime;
    const videoTime = this.videoElement.currentTime;
    const timeDiff = Math.abs(audioTime - videoTime);
    const currentTime = Date.now();

    // Adaptive tolerance
    const baseTolerance = 0.2;
    const tolerance = this.syncAttempts > 1 ? 0.5 : baseTolerance;

    // Minimum time between sync attempts
    if (currentTime - this.lastSyncTime < 2000) return;

    if (timeDiff > tolerance) {
      // Prevent sync loops
      if (this.syncAttempts >= this.maxSyncAttempts) {
        return;
      }

      this.performVideoSync(audioTime);
    }
  }

  private performVideoSync(targetTime: number) {
    this.isCurrentlySeeking = true;
    this.syncAttempts++;
    this.lastSyncTime = Date.now();

    const onSeeked = () => {
      this.videoElement.removeEventListener('seeked', onSeeked);
      this.isCurrentlySeeking = false;

      // Reset attempts after successful sync
      setTimeout(() => {
        this.syncAttempts = Math.max(0, this.syncAttempts - 1);
      }, 5000);
    };

    this.videoElement.addEventListener('seeked', onSeeked);

    try {
      this.videoElement.currentTime = targetTime;
    } catch (error) {
      logger.warn('PlayerElement: Video sync failed', {
        target_time: targetTime,
        error: error instanceof Error ? error.message : String(error),
        event_type: 'player_element_sync_error',
      });
      this.isCurrentlySeeking = false;
      this.videoElement.removeEventListener('seeked', onSeeked);
    }
  }

  play(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        let audioPlaying = false;
        let videoPlaying = false;

        const checkCompletion = () => {
          if (audioPlaying && videoPlaying) {
            resolve();
          }
        };

        // Set up playing event listeners
        const onAudioPlaying = () => {
          this.audioElement.removeEventListener('playing', onAudioPlaying);
          audioPlaying = true;
          checkCompletion();
        };

        const onVideoPlaying = () => {
          this.videoElement.removeEventListener('playing', onVideoPlaying);
          videoPlaying = true;
          checkCompletion();
        };

        // Set up error listeners
        const onAudioError = () => {
          this.audioElement.removeEventListener('error', onAudioError);
          this.audioElement.removeEventListener('playing', onAudioPlaying);
          this.videoElement.removeEventListener('playing', onVideoPlaying);
          this.videoElement.removeEventListener('error', onVideoError);

          const error = new Error(
            `Audio play error: ${this.audioElement.error?.message || 'Unknown error'}`,
          );
          logger.error('PlayerElement: Audio play error', {
            error: error.message,
            event_type: 'player_element_audio_play_error',
          });
          reject(error);
        };

        const onVideoError = () => {
          this.videoElement.removeEventListener('error', onVideoError);
          this.audioElement.removeEventListener('playing', onAudioPlaying);
          this.videoElement.removeEventListener('playing', onVideoPlaying);
          this.audioElement.removeEventListener('error', onAudioError);

          const error = new Error(
            `Video play error: ${this.videoElement.error?.message || 'Unknown error'}`,
          );
          logger.error('PlayerElement: Video play error', {
            error: error.message,
            event_type: 'player_element_video_play_error',
          });
          reject(error);
        };

        // Add event listeners
        this.audioElement.addEventListener('playing', onAudioPlaying);
        this.videoElement.addEventListener('playing', onVideoPlaying);
        this.audioElement.addEventListener('error', onAudioError);
        this.videoElement.addEventListener('error', onVideoError);

        // Check if elements are already playing
        if (!this.audioElement.paused) {
          audioPlaying = true;
        }
        if (!this.videoElement.paused) {
          videoPlaying = true;
        }

        // If both are already playing, resolve immediately
        if (audioPlaying && videoPlaying) {
          this.audioElement.removeEventListener('playing', onAudioPlaying);
          this.videoElement.removeEventListener('playing', onVideoPlaying);
          this.audioElement.removeEventListener('error', onAudioError);
          this.videoElement.removeEventListener('error', onVideoError);
          resolve();
          return;
        }

        safePlay(this.audioElement)
          .then(() => safePlay(this.videoElement))
          .catch((error) => {
            // Clean up listeners
            this.audioElement.removeEventListener('playing', onAudioPlaying);
            this.videoElement.removeEventListener('playing', onVideoPlaying);
            this.audioElement.removeEventListener('error', onAudioError);
            this.videoElement.removeEventListener('error', onVideoError);

            logger.error('PlayerElement: Failed to initiate play', {
              error: error instanceof Error ? error.message : String(error),
              event_type: 'player_element_play_initiate_error',
            });
            reject(error);
          });

        // Clear timeout when completed
        const originalResolve = resolve;
        resolve = () => {
          originalResolve();
        };
      } catch (error) {
        logger.error('PlayerElement: Play method error', {
          error: error instanceof Error ? error.message : String(error),
          event_type: 'player_element_play_method_error',
        });
        reject(error);
      }
    });
  }

  pause() {
    this.audioElement.pause();
    this.videoElement.pause();
  }

  seekTo(targetTime: number): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.isCurrentlySeeking = true;
        this.syncAttempts = 0; // Reset on manual seek

        const seekTime = Math.max(0, targetTime); // Ensure non-negative

        let audioSeeked = false;
        let videoSeeked = false;
        let playbackResumed = false;

        const checkCompletion = () => {
          if (audioSeeked && videoSeeked && playbackResumed) {
            this.isCurrentlySeeking = false;
            resolve();
          }
        };

        // Set up seeked event listeners
        const onAudioSeeked = () => {
          this.audioElement.removeEventListener('seeked', onAudioSeeked);
          audioSeeked = true;
          checkCompletion();
        };

        const onVideoSeeked = () => {
          this.videoElement.removeEventListener('seeked', onVideoSeeked);
          videoSeeked = true;
          checkCompletion();
        };

        // Set up playback resume detection
        const onPlaybackResumed = () => {
          // Check if both elements are playing or ready to play
          const audioReady =
            !this.audioElement.paused || this.audioElement.readyState >= 3;
          const videoReady =
            !this.videoElement.paused || this.videoElement.readyState >= 3;

          if (audioReady && videoReady) {
            this.audioElement.removeEventListener('playing', onPlaybackResumed);
            this.audioElement.removeEventListener('canplay', onPlaybackResumed);
            this.videoElement.removeEventListener('playing', onPlaybackResumed);
            this.videoElement.removeEventListener('canplay', onPlaybackResumed);
            playbackResumed = true;
            checkCompletion();
          }
        };

        // Add event listeners
        this.audioElement.addEventListener('seeked', onAudioSeeked);
        this.videoElement.addEventListener('seeked', onVideoSeeked);

        // Listen for playback resumption
        this.audioElement.addEventListener('playing', onPlaybackResumed);
        this.audioElement.addEventListener('canplay', onPlaybackResumed);
        this.videoElement.addEventListener('playing', onPlaybackResumed);
        this.videoElement.addEventListener('canplay', onPlaybackResumed);

        // Set up timeout fallback
        const timeoutId = setTimeout(() => {
          // Clean up listeners
          this.audioElement.removeEventListener('seeked', onAudioSeeked);
          this.videoElement.removeEventListener('seeked', onVideoSeeked);
          this.audioElement.removeEventListener('playing', onPlaybackResumed);
          this.audioElement.removeEventListener('canplay', onPlaybackResumed);
          this.videoElement.removeEventListener('playing', onPlaybackResumed);
          this.videoElement.removeEventListener('canplay', onPlaybackResumed);

          this.isCurrentlySeeking = false;

          logger.warn('PlayerElement: Seek operation timed out', {
            target_time: targetTime,
            audio_seeked: audioSeeked,
            video_seeked: videoSeeked,
            playback_resumed: playbackResumed,
            event_type: 'player_element_seek_timeout',
          });

          resolve(); // Resolve anyway to prevent hanging
        }, 5000); // 5 second timeout

        // Perform the actual seeking
        this.audioElement.currentTime = seekTime;

        // Delay video seeking slightly to ensure audio goes first
        setTimeout(() => {
          this.videoElement.currentTime = seekTime;
        }, 10);

        // Clear timeout when completed
        const originalResolve = resolve;
        resolve = () => {
          clearTimeout(timeoutId);
          originalResolve();
        };
      } catch (error) {
        this.isCurrentlySeeking = false;
        logger.error('PlayerElement: Failed to seek', {
          target_time: targetTime,
          error: error instanceof Error ? error.message : String(error),
          event_type: 'player_element_seek_error',
        });
        reject(error);
      }
    });
  }

  getAudioElement(): HTMLAudioElement {
    return this.audioElement;
  }

  getVideoElement(): HTMLVideoElement {
    return this.videoElement;
  }

  destroy() {
    if (this.audioTimeUpdateHandler) {
      this.audioElement.removeEventListener(
        'timeupdate',
        this.audioTimeUpdateHandler,
      );
    }

    if (this.videoTimeUpdateHandler) {
      this.videoElement.removeEventListener(
        'timeupdate',
        this.videoTimeUpdateHandler,
      );
    }
  }
}
