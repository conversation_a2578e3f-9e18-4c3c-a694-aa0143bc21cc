import { logger } from '@/shared';
import { RecordingTrack, TrackTimelineItem } from '../types';
import Hls from 'hls.js';
import { PlayerElement } from './PlayerElement';
import { HlsPlayerConfig } from '../config';
import { AudioContextProvider } from '../../audio-context';

interface Subject {
  update: (time: number) => void;
}

export class ParticipantPlayer implements Subject {
  private audioContextProvider: AudioContextProvider;

  private globalTime: number = 0;
  private activeTrack: TrackTimelineItem | null = null;
  private tracks: TrackTimelineItem[] = [];

  private videoHls: Hls | null = null;
  private audioHls: Hls | null = null;
  private segmentMap: Map<string, string> = new Map();

  private player: PlayerElement;
  private audioSource!: MediaElementAudioSourceNode;
  private gainNode!: GainNode;
  private destination: AudioNode;

  // Sync tracking
  private lastSyncCheckTime: number = 0;
  private lastSyncAttemptTime: number = 0;
  private syncCheckInterval: number = 500; // Check every 500ms (not every 100ms to avoid performance issues)
  private syncTolerance: number = 0.3; // 300ms tolerance
  private maxSyncInterval: number = 2000; // Don't sync more often than every 2 seconds
  private isSyncing: boolean = false;

  constructor(
    tracks: TrackTimelineItem[],
    audioContextProvider: AudioContextProvider,
    deestination: AudioNode,
  ) {
    this.tracks = tracks;
    this.audioContextProvider = audioContextProvider;
    this.destination = deestination;

    this.player = new PlayerElement();
    this.setupHLS();
    this.setupAudioGraph();
  }

  private setupHLS() {
    // Setup Audio HLS (for Web Audio processing)
    this.audioHls = new Hls(this.getHLSConfig());
    this.audioHls.attachMedia(this.player.getAudioElement());

    // Setup Video HLS (for display)
    this.videoHls = new Hls(this.getHLSConfig());
    this.videoHls.attachMedia(this.player.getVideoElement());

    this.audioHls.on(Hls.Events.MEDIA_ENDED, () => {
      this.activeTrack = null;
      this.clearMediaSources();
    });

    // Add HLS events for better sync tracking
    this.audioHls.on(Hls.Events.FRAG_LOADED, () => {
      this.lastSyncCheckTime = 0;
    });
  }

  private setupAudioGraph() {
    try {
      if (this.audioSource) return;

      const audioContext = this.audioContextProvider.getAudioContext();
      this.audioSource = audioContext.createMediaElementSource(
        this.player.getAudioElement(),
      );
      this.gainNode = audioContext.createGain();
      this.gainNode.gain.value = 1.0;

      this.audioSource.connect(this.gainNode);
      this.gainNode.connect(this.destination);

      logger.info('ParticipantPlayer: Audio graph connected successfully');
    } catch (error) {
      logger.error('ParticipantPlayer: Failed to connect audio graph', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'participant_player_audio_graph_error',
      });
    }
  }

  private getHLSConfig() {
    return HlsPlayerConfig((xhr: XMLHttpRequest, url: string) => {
      if (url.includes('.m3u8')) {
        return;
      }

      const filename = url.split('/').pop()?.split('?')[0];
      const signedUrl = filename ? this.segmentMap.get(filename) : null;

      if (signedUrl) {
        xhr.open('GET', signedUrl, true);
        xhr.timeout = 30000;
      } else {
        logger.warn('ParticipantPlayer: No signed URL found for segment', {
          filename: filename ?? '',
          event_type: 'participant_player_missing_signed_url',
        });
      }
    });
  }

  // Helper for segment mapping
  private mapTrackSegments(track: RecordingTrack) {
    const segmentMap = new Map<string, string>();
    track.playlistItems.forEach((item) => {
      const url = new URL(item.uri);
      const filename = url.pathname.split('/').pop();
      if (filename) {
        segmentMap.set(filename, item.uri);
      }
    });
    this.segmentMap = segmentMap;
  }

  update(time: number) {
    if (!this.audioHls || !this.videoHls) return;
    this.globalTime = time;

    const newActiveTrack = this.tracks.find((item) => {
      const endTime = item.startOffset + item.duration;
      return item.startOffset <= this.globalTime && this.globalTime < endTime;
    });

    if (newActiveTrack && !this.activeTrack) {
      this.activeTrack = newActiveTrack;
      this.loadActiveTrack();
    } else if (!newActiveTrack && this.activeTrack) {
      this.activeTrack = null;
      this.clearMediaSources();
    }

    if (this.activeTrack) (async () => await this.checkGlobalSync())();
  }

  private async checkGlobalSync() {
    if (!this.activeTrack || this.isSyncing) return;

    const currentTime = Date.now();

    // Don't check sync too frequently
    if (currentTime - this.lastSyncCheckTime < this.syncCheckInterval) return;
    this.lastSyncCheckTime = currentTime;

    // Don't sync too frequently
    if (currentTime - this.lastSyncAttemptTime < this.maxSyncInterval) return;

    const audioElement = this.player.getAudioElement();

    // Skip if audio is not ready or has no duration
    if (audioElement.readyState < 2 || !audioElement.duration) return;

    // Calculate expected local time based on global time and track offset
    const expectedLocalTime = this.globalTime - this.activeTrack.startOffset;
    const actualLocalTime = audioElement.currentTime;
    const timeDifference = Math.abs(expectedLocalTime - actualLocalTime);

    // Check if we're within tolerance
    if (timeDifference <= this.syncTolerance) return;

    // Ensure expected time is valid
    if (expectedLocalTime < 0 || expectedLocalTime > audioElement.duration)
      return;

    logger.info('ParticipantPlayer: Global sync drift detected, correcting', {
      global_time: this.globalTime,
      track_start_offset: this.activeTrack.startOffset,
      expected_local_time: expectedLocalTime,
      actual_local_time: actualLocalTime,
      time_difference: timeDifference,
      tolerance: this.syncTolerance,
      track_id: this.activeTrack.id,
      event_type: 'participant_player_sync_correction',
    });

    // Perform sync correction
    await this.performSyncCorrection(expectedLocalTime);
  }

  private async performSyncCorrection(targetTime: number) {
    if (this.isSyncing) return;

    try {
      this.isSyncing = true;
      this.lastSyncAttemptTime = Date.now();

      // Perform the seek
      await this.player.seekTo(targetTime);

      logger.info('ParticipantPlayer: Global sync correction completed', {
        target_time: targetTime,
        actual_time: this.player.getAudioElement().currentTime,
        event_type: 'participant_player_sync_completed',
      });
    } catch (error) {
      logger.error('ParticipantPlayer: Global sync correction failed', {
        target_time: targetTime,
        error: error instanceof Error ? error.message : String(error),
        event_type: 'participant_player_sync_error',
      });
    } finally {
      this.isSyncing = false;
    }
  }

  private loadActiveTrack() {
    if (!this.activeTrack) return;

    if (!Hls.isSupported()) {
      this.player.getVideoElement().src = this.activeTrack.track.playlistUrl;
      this.player.getAudioElement().src = this.activeTrack.track.playlistUrl;
      return;
    }

    this.mapTrackSegments(this.activeTrack.track);
    if (this.videoHls && this.audioHls) {
      this.videoHls.loadSource(this.activeTrack.track.playlistUrl);
      this.audioHls.loadSource(this.activeTrack.track.playlistUrl);
    }
  }

  private clearMediaSources() {
    const videoElement = this.player.getVideoElement();
    const audioElement = this.player.getAudioElement();

    this.isSyncing = false;
    this.lastSyncCheckTime = 0;
    this.lastSyncAttemptTime = 0;

    this.player.pause();

    // Clear HLS sources
    if (this.videoHls) {
      this.videoHls.stopLoad();
      if (this.videoHls.media) {
        this.videoHls.detachMedia();
      }
    }

    if (this.audioHls) {
      this.audioHls.stopLoad();
      if (this.audioHls.media) {
        this.audioHls.detachMedia();
      }
    }

    // Clear media element sources
    videoElement.removeAttribute('src');
    videoElement.load(); // Reset the video element

    audioElement.removeAttribute('src');
    audioElement.load(); // Reset the audio element

    // Re-attach media elements to HLS instances for future use
    if (this.videoHls) {
      this.videoHls.attachMedia(videoElement);
    }

    if (this.audioHls) {
      this.audioHls.attachMedia(audioElement);
    }

    logger.info('ParticipantPlayer: Media sources cleared', {
      event_type: 'participant_player_sources_cleared',
    });
  }

  getActiveTrack(): TrackTimelineItem | null {
    return this.activeTrack;
  }

  getPlayer() {
    return this.player;
  }

  destroy() {
    this.player.destroy();

    if (this.audioHls) {
      this.audioHls.destroy();
      this.audioHls = null;
    }

    if (this.videoHls) {
      this.videoHls.destroy();
      this.videoHls = null;
    }

    if (this.gainNode) {
      this.gainNode.disconnect();
    }

    if (this.audioSource) {
      this.audioSource.disconnect();
    }
  }
}
