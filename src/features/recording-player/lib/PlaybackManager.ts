import { logger } from '@/shared';
import { getRecordingDetails, getRecordingHls } from '../api';
import { RecordingTrack, TrackTimelineItem } from '../types';
import { ParticipantPlayer } from './ParticipantPlayer';
import { AudioContextProvider } from '../../audio-context';

interface TrackManagerEvents {
  onParticipantSelected: (participant: string | null) => void;
  onShowPlayer: (value: boolean) => void;
  onActiveParticipantsChange: (participants: string[]) => void;
}

export class PlaybackManager {
  private events: TrackManagerEvents;
  private participants: Map<string, ParticipantPlayer> = new Map();

  private isPlaying: boolean = false;
  private isLoading: boolean = false;

  private tracks: RecordingTrack[] = [];
  private primaryParticipantId: string | null = null;

  private audioContextProvider!: AudioContextProvider;
  private masterGain!: GainNode;
  private destination!: AudioDestinationNode;

  // Global timeline control
  private currentGlobalTime: number = 0;
  private timelineInterval: NodeJS.Timeout | null = null;
  private globalStartTime: number = 0;
  private totalRecordingDuration: number = 0;

  private constructor(events: TrackManagerEvents) {
    this.events = events;
  }

  static async create(
    recordingId: string,
    events: TrackManagerEvents,
  ): Promise<PlaybackManager> {
    const manager = new PlaybackManager(events);
    await manager.initialize(recordingId);
    return manager;
  }

  private async initialize(recordingId: string): Promise<void> {
    try {
      logger.info('PlaybackManager: Starting initialization', {
        recording_id: recordingId,
        event_type: 'playback_manager_init_start',
      });

      const [, recordingHls] = await Promise.all([
        getRecordingDetails(recordingId),
        getRecordingHls(recordingId),
      ]);

      if (!recordingHls.tracks || recordingHls.tracks.length === 0) {
        throw new Error('No tracks found in recording');
      }

      // Initialize audio context
      this.audioContextProvider = await AudioContextProvider.create();
      const audioContext = this.audioContextProvider.getAudioContext();

      this.masterGain = audioContext.createGain();
      this.masterGain.gain.value = 1;

      this.destination = audioContext.destination;
      this.masterGain.connect(this.destination);

      // Sort tracks by start time
      this.tracks = recordingHls.tracks.sort(
        (a, b) => Date.parse(a.startTime) - Date.parse(b.startTime),
      );

      // Calculate global timeline parameters
      this.calculateGlobalTimeline(this.tracks);

      // Set first participant as primary (video display)
      this.primaryParticipantId = this.tracks[0].participantId;
      this.setVideoElement(this.primaryParticipantId);

      // Setup the global timeline controller
      this.setupGlobalTimelineControl();
      this.events.onShowPlayer(true);

      logger.info('PlaybackManager: Initialization completed successfully', {
        recording_id: recordingId,
        total_duration: this.totalRecordingDuration,
        participant_count: this.participants.size,
        primary_participant: this.primaryParticipantId,
        event_type: 'playback_manager_init_complete',
      });
    } catch (error) {
      logger.error('PlaybackManager: Initialization failed', {
        recording_id: recordingId,
        error: error instanceof Error ? error.message : String(error),
        event_type: 'playback_manager_init_error',
      });
      this.events.onShowPlayer(false);
    }
  }

  private calculateGlobalTimeline(sortedTracks: RecordingTrack[]) {
    if (sortedTracks.length === 0) return;

    // Global start time is the earliest track
    this.globalStartTime = Date.parse(sortedTracks[0].startTime);

    const particiapntTracks = new Map<string, TrackTimelineItem[]>();
    sortedTracks.forEach((track) => {
      const trackStartTime = Date.parse(track.startTime);

      const startOffset = (trackStartTime - this.globalStartTime) / 1000; // Convert to seconds
      const duration = (Date.parse(track.endTime) - trackStartTime) / 1000;

      const item = {
        id: track.participantId,
        startOffset,
        duration,
        track,
      };

      const tracks = particiapntTracks.get(track.participantId);
      particiapntTracks.set(
        track.participantId,
        tracks ? [...tracks, item] : [item],
      );
    });

    particiapntTracks.forEach((value, key) => {
      const participant = new ParticipantPlayer(
        value,
        this.audioContextProvider,
        this.masterGain,
      );

      this.participants.set(key, participant);
    });

    // Calculate total recording duration
    const latestEndTime = sortedTracks
      .map((track) => track.endTime)
      .sort((a, b) => Date.parse(b) - Date.parse(a))[0];

    this.totalRecordingDuration =
      (Date.parse(latestEndTime) - this.globalStartTime) / 1000;

    logger.info('PlaybackManager: Global timeline calculated', {
      total_duration: this.totalRecordingDuration,
      track_count: this.tracks.length,
      participant_count: particiapntTracks.size,
      event_type: 'playback_manager_timeline_calculated',
    });
  }

  private setupGlobalTimelineControl() {
    // Update global timeline every 100ms for smooth playback
    this.timelineInterval = setInterval(() => {
      if (this.isPlaying && !this.isLoading) {
        if (this.currentGlobalTime >= this.totalRecordingDuration) {
          logger.info('PlaybackManager: Reached end of recording, pausing', {
            current_time: this.currentGlobalTime,
            total_duration: this.totalRecordingDuration,
            event_type: 'playback_manager_end_reached',
          });

          this.pauseAll();

          this.events.onActiveParticipantsChange([]);
          this.events.onParticipantSelected(null);

          this.setVideoElement(this.tracks[0].participantId);
          this.currentGlobalTime = 0;
          return;
        }

        this.isLoading = true;
        this.currentGlobalTime += 0.1; // Advance 100ms

        const activeParticipants: string[] = [];
        this.participants.forEach(async (value, key) => {
          value.update(this.currentGlobalTime);
          if (value.getActiveTrack()) activeParticipants.push(key);
        });
        this.events.onActiveParticipantsChange(activeParticipants);

        if (
          this.primaryParticipantId &&
          !activeParticipants.includes(this.primaryParticipantId)
        ) {
          this.primaryParticipantId = activeParticipants[0];
          this.setVideoElement(this.primaryParticipantId);
        }

        this.isLoading = false;
      }
    }, 100);
  }

  // Centralized playback controls
  async playAll() {
    if (this.isPlaying) return;
    this.isLoading = true;

    try {
      const audioContext = this.audioContextProvider.getAudioContext();
      if (audioContext.state !== 'running') {
        await this.audioContextProvider.resume();
      }

      logger.info('PlaybackManager: Starting global timeline playback', {
        current_time: this.currentGlobalTime,
        total_duration: this.totalRecordingDuration,
        participant_count: this.participants.size,
        event_type: 'playback_manager_play_start',
      });

      // Play all participants
      this.participants.forEach(async (participant) => {
        participant.update(this.currentGlobalTime);
        await participant.getPlayer().play();
      });

      this.isLoading = false;
      this.isPlaying = true;
    } catch (error) {
      logger.error(
        'PlaybackManager: Failed to start global timeline playback',
        {
          current_time: this.currentGlobalTime,
          error: error instanceof Error ? error.message : String(error),
          event_type: 'playback_manager_play_error',
        },
      );
      this.isLoading = false;
      this.isPlaying = false;
    }
  }

  pauseAll() {
    if (!this.isPlaying) return;

    this.isPlaying = false;

    // Pause all participants
    this.participants.forEach((participant) => {
      participant.getPlayer().pause();
    });

    logger.info('PlaybackManager: Global timeline paused', {
      current_time: this.currentGlobalTime,
      total_duration: this.totalRecordingDuration,
      event_type: 'playback_manager_pause',
    });
  }

  async seekAll(targetTime: number) {
    this.isLoading = true;

    // Clamp target time to valid range
    const clampedTime = Math.max(
      0,
      Math.min(targetTime, this.totalRecordingDuration),
    );

    const previousTime = this.currentGlobalTime;
    this.currentGlobalTime = clampedTime;

    this.participants.forEach(async (participant) => {
      const activeTrack = participant.getActiveTrack();
      if (activeTrack) {
        const newTime = this.currentGlobalTime - activeTrack.startOffset;
        await participant.getPlayer().seekTo(newTime);
      }
    });

    this.isLoading = false;
    if (Math.abs(clampedTime - previousTime) > 1.0) {
      logger.info('PlaybackManager: Timeline seek performed', {
        from_time: previousTime,
        to_time: clampedTime,
        total_duration: this.totalRecordingDuration,
        event_type: 'playback_manager_seek',
      });
    }
  }

  // Master volume control
  setMasterVolume(volume: number) {
    if (this.masterGain) {
      const audioContext = this.audioContextProvider.getAudioContext();
      this.masterGain.gain.setValueAtTime(volume, audioContext.currentTime);
    }
  }

  // Switch primary video display
  setVideoElement(participantId: string) {
    const participant = this.participants.get(participantId);
    if (!participant) {
      logger.warn(
        'PlaybackManager: Attempted to set video for non-existent participant',
        {
          participant_id: participantId,
          event_type: 'playback_manager_invalid_participant',
        },
      );
      return;
    }

    const playerDiv = document.getElementById('video-player');
    if (!playerDiv) {
      logger.error('PlaybackManager: Video player container not found in DOM', {
        participant_id: participantId,
        event_type: 'playback_manager_no_video_container',
      });
      return;
    }

    // Remove current video
    if (playerDiv.childElementCount > 0) {
      const currentChild = playerDiv.children[0];
      playerDiv.removeChild(currentChild);
    }

    // Add new primary video
    playerDiv.appendChild(participant.getPlayer().getVideoElement());
    if (this.isPlaying) participant.getPlayer().play();

    const previousPrimary = this.primaryParticipantId;
    this.primaryParticipantId = participantId;
    this.events.onParticipantSelected(participantId);

    // Only log when actually switching, not initial setup
    if (previousPrimary && previousPrimary !== participantId) {
      logger.info('PlaybackManager: Primary video participant changed', {
        previous_participant: previousPrimary,
        new_participant: participantId,
        event_type: 'playback_manager_primary_video_change',
      });
    } else if (!previousPrimary) {
      logger.info('PlaybackManager: Initial primary video participant set', {
        participant_id: participantId,
        event_type: 'playback_manager_primary_video_init',
      });
    }
  }

  // Timeline information getters
  getCurrentTime(): number {
    return this.currentGlobalTime;
  }

  getTotalDuration(): number {
    return this.totalRecordingDuration;
  }

  getIsPlaying(): boolean {
    return this.isPlaying;
  }

  getStartTime(): number {
    return this.globalStartTime;
  }

  async destroy() {
    logger.info('PlaybackManager: Starting destruction', {
      participant_count: this.participants.size,
      current_time: this.currentGlobalTime,
      was_playing: this.isPlaying,
      event_type: 'playback_manager_destroy_start',
    });

    // Stop global timeline
    if (this.timelineInterval) {
      clearInterval(this.timelineInterval);
      this.timelineInterval = null;
    }

    // Destroy all participants
    for (const participant of this.participants.values()) {
      participant.destroy();
    }

    this.participants.clear();
    if (this.audioContextProvider) {
      await this.audioContextProvider.close();
    }

    logger.info('PlaybackManager: Destruction completed', {
      event_type: 'playback_manager_destroy_complete',
    });
  }
}
