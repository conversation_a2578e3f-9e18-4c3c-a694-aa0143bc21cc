export const HlsPlayerConfig = (
  callback: (xhr: XMLHttpRequest, url: string) => void,
) => {
  return {
    enableWorker: true,
    lowLatencyMode: false,

    // Gap handling - much more tolerant of timing issues
    maxBufferHole: 2.0, // Increased from 0.1 to 2 seconds
    nudgeMaxRetry: 10, // More retry attempts

    // Buffer settings - more forgiving
    backBufferLength: 90,
    maxBufferLength: 60, // Increased buffer
    maxMaxBufferLength: 600,
    maxBufferSize: 100 * 1000 * 1000, // 100MB buffer

    // Fragment loading - more resilient
    fragLoadingTimeOut: 30000, // 30 second timeout
    fragLoadingMaxRetry: 10, // More retries
    fragLoadingRetryDelay: 2000, // Longer delay between retries

    // Manifest settings
    manifestLoadingTimeOut: 15000,
    manifestLoadingMaxRetry: 10,

    // Error recovery settings
    startLevel: -1,
    capLevelToPlayerSize: true,

    // Additional gap handling
    maxStarvationDelay: 10, // Wait 10s before stalling
    maxLoadingDelay: 10, // Wait 10s for loading

    // Less strict about timing
    liveSyncDuration: undefined, // Disable live sync requirements
    liveMaxLatencyDuration: undefined,

    xhrSetup: callback,
  };
};
