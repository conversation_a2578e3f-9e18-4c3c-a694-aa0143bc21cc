/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock the shared api before importing the SUT
vi.mock('../../../shared', () => ({
  api: {
    get: vi.fn(),
  },
}));

import { api } from '../../../shared';
import { getRecordingHls } from './getRecordingHls';
import type { RecordingHlsResponse } from './getRecordingHls';

describe('getRecordingHls', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('calls the correct endpoint and returns unwrapped data', async () => {
    const id = 'rec-123';
    const payload: RecordingHlsResponse = {
      id,
      classId: 'class-1',
      classroomId: 'room-1',
      duration: 3600,
      trackCount: 2,
      tracks: [
        {
          participantId: 'p1',
          participantName: 'Alice',
          participantRole: 'student',
          participantFullName: '<PERSON>',
          participantAvatar: 'https://cdn.example.com/avatars/alice.png',
          playlistUrl: 'https://a.example/hls',
          duration: 1800,
          startTime: '2025-01-01T00:00:00.000Z',
          endTime: '2025-01-01T00:30:00.000Z',
          playlistItems: [],
        },
        {
          participantId: 'p2',
          participantName: 'Bob',
          participantRole: 'teacher',
          participantFullName: 'Bob Smith',
          participantAvatar: 'https://cdn.example.com/avatars/bob.png',
          playlistUrl: 'https://v.example/hls',
          duration: 3600,
          startTime: '2025-01-01T00:00:00.000Z',
          endTime: '2025-01-01T01:00:00.000Z',
          playlistItems: [],
        },
      ],
      createdAt: '2025-01-01T00:00:00.000Z',
      updatedAt: '2025-01-01T00:10:00.000Z',
    };

    (api.get as any).mockResolvedValueOnce({ data: { data: payload } });

    const result = await getRecordingHls(id);

    expect(api.get).toHaveBeenCalledWith(`/recordings/${id}/hls`);
    expect(result).toEqual(payload);
  });

  it('propagates errors from api.get', async () => {
    const id = 'rec-err';
    (api.get as any).mockRejectedValueOnce(new Error('network'));

    await expect(getRecordingHls(id)).rejects.toThrow('network');
    expect(api.get).toHaveBeenCalledWith(`/recordings/${id}/hls`);
  });
});
