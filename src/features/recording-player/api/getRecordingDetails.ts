import { api } from '../../../shared';

interface RecordingDetails {
  id: string;
  classId: string;
  classroomId: string;
  duration: number;
  fileSize: number;
  createdAt: number;
  updatedAt: number;
  activeSpeakers: { participantId: string; timestamp: string }[];
}

export async function getRecordingDetails(id: string) {
  const { data } = await api.get<{
    data: RecordingDetails;
  }>(`/recordings/${id}`);
  return data.data;
}
