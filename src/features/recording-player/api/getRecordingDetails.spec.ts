/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { getRecordingDetails } from './getRecordingDetails';
import { api } from '../../../shared';

// Mock the shared api client
vi.mock('../../../shared', () => ({
  api: {
    get: vi.fn(),
  },
}));

describe('getRecordingDetails', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('calls api.get with the correct URL and returns the nested data', async () => {
    const id = 'rec-123';
    const payload = {
      id,
      classId: 'class-1',
      classroomId: 'room-1',
      duration: 3600,
      fileSize: 123456789,
      createdAt: 1735689600000,
      updatedAt: 1735693200000,
      activeSpeakers: [
        { participantId: 'p1', timestamp: '2025-01-01T00:00:10.000Z' },
        { participantId: 'p2', timestamp: '2025-01-01T00:01:00.000Z' },
      ],
    };

    (api.get as any).mockResolvedValueOnce({ data: { data: payload } });

    const result = await getRecordingDetails(id);

    expect(api.get).toHaveBeenCalledWith(`/recordings/${id}`);
    expect(result).toEqual(payload);
  });

  it('bubbles errors from api.get', async () => {
    (api.get as any).mockRejectedValueOnce(new Error('network error'));

    await expect(getRecordingDetails('rec-err')).rejects.toThrow(
      'network error',
    );
    expect(api.get).toHaveBeenCalledWith('/recordings/rec-err');
  });
});
