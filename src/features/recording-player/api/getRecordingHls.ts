import { api } from '../../../shared';
import { RecordingTrack } from '../types';

export interface RecordingHlsResponse {
  id: string;
  classId: string;
  classroomId: string;
  duration: number;
  trackCount: number;
  tracks: RecordingTrack[];
  createdAt: string;
  updatedAt: string;
}

export async function getRecordingHls(id: string) {
  const { data } = await api.get<{ data: RecordingHlsResponse }>(
    `/recordings/${id}/hls`,
  );
  return data.data;
}
