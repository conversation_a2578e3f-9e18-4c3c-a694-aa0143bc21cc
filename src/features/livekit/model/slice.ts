import {
  createEntityAdapter,
  createSlice,
  PayloadAction,
} from '@reduxjs/toolkit';
import { ConnectionStatus } from '../lib';
import { ClassParticipant } from '@/entities';
import { RoomParticipant } from '../types';

interface LivekitConnectionState {
  localUser: RoomParticipant | null;
  joinStatus: 'joined' | 'left' | null;
  status: ConnectionStatus | null;
}

const roomUsersAdapter = createEntityAdapter<RoomParticipant, string>({
  selectId: (user) => user.sid,
  sortComparer: (a, b) => a.sid.localeCompare(b.sid),
});

const initialState = roomUsersAdapter.getInitialState<LivekitConnectionState>({
  localUser: null,
  joinStatus: null,
  status: null,
});

const livekitConnectionSlice = createSlice({
  name: 'conference/livekit',
  initialState,
  reducers: {
    setLocalUser: (state, action: PayloadAction<RoomParticipant | null>) => {
      state.localUser = action.payload;
    },
    setLocalUserMicrophoneOn: (state, action: PayloadAction<boolean>) => {
      if (state.localUser) state.localUser.isMicrophoneOn = action.payload;
    },
    setLocalUserVideoOn: (state, action: PayloadAction<boolean>) => {
      if (state.localUser) state.localUser.isVideoOn = action.payload;
    },

    upsertRoomUser: roomUsersAdapter.upsertOne,
    removeRoomUserBySid: roomUsersAdapter.removeOne,

    setRoomUserProfile: (
      state,
      action: PayloadAction<{ sid: string; profile: ClassParticipant }>,
    ) => {
      const { sid, profile } = action.payload;
      const user = state.entities[sid];
      if (user) user.profile = profile;
    },
    setRoomUserMicrophoneOn: (
      state,
      action: PayloadAction<{ sid: string; isMicrophoneOn: boolean }>,
    ) => {
      const { sid, isMicrophoneOn } = action.payload;
      const user = state.entities[sid];
      if (user) user.isMicrophoneOn = isMicrophoneOn;
    },
    setRoomUserVideoOn: (
      state,
      action: PayloadAction<{ sid: string; isVideoOn: boolean }>,
    ) => {
      const { sid, isVideoOn } = action.payload;
      const user = state.entities[sid];
      if (user) user.isVideoOn = isVideoOn;
    },
    setLivekitJoinStatus: (
      state,
      action: PayloadAction<'joined' | 'left' | null>,
    ) => {
      state.joinStatus = action.payload;
    },
    setLivekitConnectionStatus: (
      state,
      action: PayloadAction<ConnectionStatus | null>,
    ) => {
      state.status = action.payload;
    },
    resetLivekitConnection: (state) => {
      state.localUser = null;
      roomUsersAdapter.removeAll(state);
      state.joinStatus = null;
      state.status = null;
    },
  },
});

export const {
  setLocalUser,
  setLocalUserMicrophoneOn,
  setLocalUserVideoOn,
  upsertRoomUser,
  removeRoomUserBySid,
  setRoomUserProfile,
  setRoomUserMicrophoneOn,
  setRoomUserVideoOn,
  setLivekitJoinStatus,
  setLivekitConnectionStatus,
  resetLivekitConnection,
} = livekitConnectionSlice.actions;

export const {
  selectAll: selectAllRoomUsers,
  selectById: selectRoomUserBySid,
  selectIds: selectRoomUserSids,
} = roomUsersAdapter.getSelectors<{
  livekit: ReturnType<typeof livekitConnectionSlice.reducer>;
}>((state) => state.livekit);

export default livekitConnectionSlice.reducer;
