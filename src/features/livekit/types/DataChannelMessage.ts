import { ClassLoggerMessage } from '@/shared';
import { ActionBarMessage } from './ActionBarMessage';
import { HistoryMessage } from './HistoryMessage';
import { PresenceMessage } from './PresenceMessage';
import { ChatMessage } from '../../chat';

export type DataChannelMessage = {
  type: 'medias' | 'chat' | 'history' | 'presence' | 'participant-logs';
  message:
    | ActionBarMessage
    | ChatMessage
    | HistoryMessage
    | PresenceMessage
    | ClassLoggerMessage;
};
