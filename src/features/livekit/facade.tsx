import { useAppSelector, useUpdateReduxState } from '@/shared';
import { useEffect } from 'react';
import { ConnectionStatus } from './lib';
import {
  resetLivekitConnection,
  setLivekitConnectionStatus,
  setLivekitJoinStatus,
} from './model';
import {
  useLivekitActiveSpeaker,
  useLivekitDataChannel,
  useLivekitJoin,
  useLivekitLeave,
  useLivekitRemoteJoin,
  useLivekitRemoteLeave,
  useLivekitStreams,
} from './hooks';
import { ClassParticipant } from '@/entities';
import { ConferenceStatus } from '../conference';

export const useLivekit = (
  url: string,
  token: string,
  participants: ClassParticipant[],
) => {
  const updateReduxState = useUpdateReduxState();
  const { status } = useAppSelector((state) => state.conference);

  useEffect(() => {
    if (status === ConferenceStatus.Livekit) {
      updateReduxState(setLivekitConnectionStatus(ConnectionStatus.Connecting));
      updateReduxState(setLivekitJoinStatus('joined'));
    }
  }, [status]);

  useEffect(() => {
    return () => {
      updateReduxState(resetLivekitConnection());
    };
  }, []);

  useLivekitJoin(url, token, participants);
  useLivekitLeave();

  useLivekitRemoteJoin(participants);
  useLivekitRemoteLeave();

  useLivekitDataChannel();
  useLivekitStreams();

  useLivekitActiveSpeaker();
};
