import { Track, TrackPublishOptions } from 'livekit-client';
import { isLowEndDevice, isMobile } from '@/shared';

export const AUDIO_OPTIONS: TrackPublishOptions = {
  source: Track.Source.Microphone,
  forceStereo: true,
  dtx: false,
  red: false,
  audioPreset: {
    maxBitrate: 192000, // 192
    priority: 'high',
  },
};

const mobile = isMobile();
const lowEnd = isLowEndDevice();

export const VIDEO_OPTIONS: TrackPublishOptions = {
  videoEncoding: {
    maxBitrate: mobile ? (lowEnd ? 200000 : 400000) : 1000000, // 200KB-400KB for mobile vs 1MB desktop
    maxFramerate: mobile ? (lowEnd ? 15 : 24) : 30, // Lower FPS for mobile saves CPU cycles
    priority: 'low',
  },
  videoCodec: 'h264',
  simulcast: !mobile, // Disable simulcast on mobile - saves 30-40% CPU for encoding multiple streams
  degradationPreference: mobile ? 'maintain-resolution' : 'maintain-framerate',
};
