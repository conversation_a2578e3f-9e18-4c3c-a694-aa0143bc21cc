import { useEffect } from 'react';
import { RoomEvent } from 'livekit-client';
import {
  ClassLoggerMessage,
  useAppSelector,
  useUpdateReduxState,
} from '@/shared';
import { addManyMessages, selectAllMessages, upsertMessage } from '@/features';
import { useLivekitContext } from '../context';
import {
  removeRoomUserBySid,
  selectAllRoomUsers,
  setRoomUserMicrophoneOn,
  setRoomUserVideoOn,
  upsertRoomUser,
} from '../model';
import {
  ActionBarMessage,
  DataChannelMessage,
  HistoryMessage,
  PresenceMessage,
} from '../types';
import { sendHistoryData } from '../utils';
import {
  ParticipantLogs,
  updateRemoteParticipantLogs,
} from '../../class-logger';
import { ChatMessage } from '../../chat';

export const useLivekitDataChannel = () => {
  const updateReduxState = useUpdateReduxState();
  const { localParticipantLogs } = useAppSelector((state) => state.classLogger);

  const { room } = useLivekitContext();
  const { joinStatus, localUser } = useAppSelector((state) => state.livekit);
  const roomUsers = useAppSelector(selectAllRoomUsers);
  const messages = useAppSelector(selectAllMessages);

  useEffect(() => {
    if (joinStatus !== 'joined') return;
    const onDataReceived = (payload: Uint8Array) => {
      const message = JSON.parse(
        new TextDecoder().decode(payload),
      ) as DataChannelMessage;

      switch (message.type) {
        case 'medias': {
          const content = message.message as ActionBarMessage;
          switch (content.type) {
            case 'mic':
              updateReduxState(
                setRoomUserMicrophoneOn({
                  sid: content.sid,
                  isMicrophoneOn: content.state,
                }),
              );
              break;
            case 'video':
              updateReduxState(
                setRoomUserVideoOn({
                  sid: content.sid,
                  isVideoOn: content.state,
                }),
              );
              break;
          }
          break;
        }
        case 'chat': {
          const content = message.message as ChatMessage;
          updateReduxState(upsertMessage(content));
          break;
        }
        case 'history': {
          const content = message.message as HistoryMessage;
          switch (content.method) {
            case 'get': {
              sendHistoryData(room, localUser, messages, {
                sid: localUser?.sid ?? '',
                logs: localParticipantLogs,
              });
              break;
            }
            case 'post': {
              const content = message.message as HistoryMessage;
              const newMessages = (content.messages as ChatMessage[]) ?? [];
              updateReduxState(addManyMessages(newMessages));

              if (content.logs) {
                updateReduxState(updateRemoteParticipantLogs(content.logs));
              }

              break;
            }
          }
          break;
        }
        case 'presence': {
          const { sid, identity } = message.message as PresenceMessage;
          const existingRoomUser = roomUsers.find(
            (user) => user.sid !== sid && user.identity === identity,
          );

          if (existingRoomUser) {
            updateReduxState(removeRoomUserBySid(existingRoomUser.sid));
            updateReduxState(
              upsertRoomUser({
                sid: sid,
                identity: identity,
                role: existingRoomUser.role,
                roleType: existingRoomUser.roleType,
                profile: existingRoomUser.profile,
                isMicrophoneOn: existingRoomUser.isMicrophoneOn,
                isVideoOn: existingRoomUser.isVideoOn,
              }),
            );
          }

          break;
        }
        case 'participant-logs': {
          const { sid, message: logs } = message.message as ClassLoggerMessage;
          const parsedLogs = JSON.parse(logs) as ParticipantLogs;
          updateReduxState(
            updateRemoteParticipantLogs({ sid, logs: parsedLogs }),
          );
          break;
        }
      }
    };

    room.on(RoomEvent.DataReceived, onDataReceived);

    return () => {
      room.off(RoomEvent.DataReceived, onDataReceived);
    };
  }, [joinStatus, localUser, messages, roomUsers]);
};
