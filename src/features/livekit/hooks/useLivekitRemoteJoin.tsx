import { ParticipantEvent, RemoteParticipant, RoomEvent } from 'livekit-client';
import { useEffect, useRef } from 'react';
import {
  getParticipantProfile,
  isParticipantRemote,
  isParticipantWatcher,
  sendHistoryData,
} from '../utils';
import { logger, Role, useAppSelector, useUpdateReduxState } from '@/shared';
import { useLivekitContext } from '../context';
import { useQueryClient } from '@tanstack/react-query';
import { useParams } from 'react-router';
import { upsertRoomUser } from '../model';
import { ChatMessage, selectAllMessages } from '../../chat';
import { ClassParticipant } from '@/entities';
import { RoomParticipant } from '../types';

export const useLivekitRemoteJoin = (participants: ClassParticipant[]) => {
  const updateReduxState = useUpdateReduxState();
  const queryClient = useQueryClient();
  const { id: classroomId, classId } = useParams<{
    id: string;
    classId: string;
  }>();

  const { localParticipantLogs } = useAppSelector((state) => state.classLogger);
  const { room } = useLivekitContext();

  const { joinStatus, localUser } = useAppSelector((state) => state.livekit);
  const localUserRef = useRef<RoomParticipant | null>(null);
  useEffect(() => {
    localUserRef.current = localUser;
  }, [localUser]);

  const messages = useAppSelector(selectAllMessages);
  const messagesRef = useRef<ChatMessage[]>([]);
  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);

  // Handle remote user connecting to the livekit room
  useEffect(() => {
    if (joinStatus !== 'joined') return;
    const onParticipantConnected = (participant: RemoteParticipant) => {
      const profile = getParticipantProfile(participant.identity, participants);
      queryClient.invalidateQueries({
        queryKey: ['class', classId ?? '', classroomId ?? ''],
      });

      isParticipantRemote(room, participant, () => {
        logger.info('Party participant connected', {
          participant_sid: participant.sid,
          participant_identity: participant.identity,
          profile: JSON.stringify(profile),
          event_type: 'party_participant_connected',
        });

        updateReduxState(
          upsertRoomUser({
            sid: participant.sid,
            identity: participant.identity,
            role: profile?.role as Role,
            roleType: 'party',
            profile,
            isMicrophoneOn: true,
            isVideoOn: true,
          }),
        );
      });

      isParticipantWatcher(room, participant, () => {
        logger.info('Watcher participant connected', {
          participant_sid: participant.sid,
          participant_identity: participant.identity,
          profile: JSON.stringify(
            getParticipantProfile(participant.identity, participants),
          ),
          event_type: 'watcher_participant_connected',
        });

        updateReduxState(
          upsertRoomUser({
            sid: participant.sid,
            identity: participant.identity,
            role: profile?.role as Role,
            roleType: 'watcher',
            profile: getParticipantProfile(participant.identity, participants),
            isMicrophoneOn: null,
            isVideoOn: null,
          }),
        );

        participant.on(ParticipantEvent.Active, () =>
          sendHistoryData(room, localUserRef.current, messagesRef.current, {
            sid: localUserRef.current?.sid ?? '',
            logs: localParticipantLogs,
          }),
        );
      });

      const allRemotes = Array.from(room.remoteParticipants.values()).map(
        (p) => ({
          sid: p.sid,
          identity: p.identity,
          metadata: p.metadata,
        }),
      );

      const parties = allRemotes.filter((p) => {
        if (!p.metadata) return true;
        try {
          const metadata = JSON.parse(p.metadata);
          return metadata.role !== 'watcher' && metadata.role !== 'agent';
        } catch {
          return true;
        }
      });

      const watchers = allRemotes.filter((p) => {
        if (!p.metadata) return false;
        try {
          const metadata = JSON.parse(p.metadata);
          return metadata.role === 'watcher';
        } catch {
          return false;
        }
      });

      logger.info('Remote participant joined', {
        participant_sid: participant.sid,
        participant_identity: participant.identity,
        participant_metadata: participant.metadata ?? '',
        total_remotes: room.remoteParticipants.size,
        all_remotes: JSON.stringify(allRemotes),
        parties: JSON.stringify(parties),
        watchers: JSON.stringify(watchers),
        event_type: 'remote_participant_joined',
      });
    };

    room.on(RoomEvent.ParticipantConnected, onParticipantConnected);
    return () => {
      room.off(RoomEvent.ParticipantConnected, onParticipantConnected);
    };
  }, [joinStatus]);
};
