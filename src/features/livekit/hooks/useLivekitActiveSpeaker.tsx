import { useEffect, useRef } from 'react';
import { useLivekitContext } from '../context';
import { logger, useAppSelector } from '../../../shared';
import { selectAllRoomUsers } from '../model';
import { Participant, RoomEvent } from 'livekit-client';

export const useLivekitActiveSpeaker = () => {
  const { room, selectedRemoteSid, setSelectedRemoteSid } = useLivekitContext();
  const roomUsers = useAppSelector(selectAllRoomUsers);

  const debounceTimeoutRef = useRef<NodeJS.Timeout>(null);

  useEffect(() => {
    const onActiveSpeakersChanged = (speakers: Participant[]) => {
      logger.info('Active speaker detected', {
        active_speaker_sid: speakers[0] ? speakers[0].sid : 'none',
        active_speakers: speakers.length,
        event_type: 'livekit_active_speaker_detected',
      });

      if (speakers.length === 0) return;

      const newActiveSid = speakers[0].sid;
      if (
        newActiveSid !== selectedRemoteSid &&
        room.localParticipant.sid !== newActiveSid
      ) {
        if (debounceTimeoutRef.current)
          clearTimeout(debounceTimeoutRef.current);

        debounceTimeoutRef.current = setTimeout(
          () => setSelectedRemoteSid(newActiveSid),
          500,
        );
      }
    };

    room.on(RoomEvent.ActiveSpeakersChanged, onActiveSpeakersChanged);
    return () => {
      room.off(RoomEvent.ActiveSpeakersChanged, onActiveSpeakersChanged);
      if (debounceTimeoutRef.current) clearTimeout(debounceTimeoutRef.current);
    };
  }, [room, roomUsers]);
};
