import { useEffect } from 'react';
import { retry, Role, useAppSelector, useUpdateReduxState } from '@/shared';
import {
  setLivekitConnectionStatus,
  setLivekitJoinStatus,
  setLocalUser,
  upsertRoomUser,
} from '../model';
import { ConferenceStatus, setConferenceStatus } from '../../conference';
import {
  getParticipantProfile,
  isParticipantRemote,
  isParticipantWatcher,
  sendData,
} from '../utils';
import { useLivekitContext } from '../context';
import { ConnectionStatus } from '../lib';
import { networkAdaptiveJitterBuffer } from '../../audio-processing';
import { ClassParticipant, useUser } from '@/entities';

export const useLivekitJoin = (
  url: string,
  token: string,
  participants: ClassParticipant[],
) => {
  const updateReduxState = useUpdateReduxState();

  const { room } = useLivekitContext();
  const { role, roleType } = useUser();
  const { joinStatus } = useAppSelector((state) => state.livekit);

  useEffect(() => {
    if (joinStatus !== 'joined') return;
    (async () => {
      try {
        await retry(() =>
          room
            .connect(url, token, {
              autoSubscribe: true,
            })
            .then(async () => {
              const localParticipant = room.localParticipant;

              room.remoteParticipants.forEach((participant) => {
                const profile = getParticipantProfile(
                  participant.identity,
                  participants,
                );

                isParticipantRemote(room, participant, () => {
                  updateReduxState(
                    upsertRoomUser({
                      sid: participant.sid,
                      identity: participant.identity,
                      role: profile?.role as Role,
                      roleType: 'party',
                      profile: profile,
                      isMicrophoneOn: true,
                      isVideoOn: true,
                    }),
                  );
                });

                isParticipantWatcher(room, participant, () => {
                  updateReduxState(
                    upsertRoomUser({
                      sid: participant.sid,
                      identity: participant.identity,
                      role: profile?.role as Role,
                      roleType: 'watcher',
                      profile: getParticipantProfile(
                        participant.identity,
                        participants,
                      ),
                      isMicrophoneOn: null,
                      isVideoOn: null,
                    }),
                  );
                });
              });

              updateReduxState(
                setLocalUser({
                  sid: localParticipant.sid,
                  identity: localParticipant.identity,
                  role: role ?? Role.Guest,
                  roleType: roleType ?? 'watcher',
                  profile: getParticipantProfile(
                    room.localParticipant.identity,
                    participants,
                  ),
                  isMicrophoneOn: roleType === 'party' ? false : null,
                  isVideoOn: roleType === 'party' ? false : null,
                }),
              );

              sendData(room, { type: 'history', message: { method: 'get' } });
              sendData(room, {
                type: 'presence',
                message: {
                  sid: localParticipant.sid,
                  identity: localParticipant.identity,
                },
              });

              networkAdaptiveJitterBuffer.initialize(room);

              updateReduxState(
                setLivekitConnectionStatus(ConnectionStatus.Connected),
              );
              updateReduxState(
                setConferenceStatus(ConferenceStatus.AudioContext),
              );
            }),
        );
      } catch {
        updateReduxState(setLivekitJoinStatus('left'));
        updateReduxState(setLivekitConnectionStatus(ConnectionStatus.Failed));
      }
    })();

    return () => {
      (async () => {
        await room.disconnect(false);
        networkAdaptiveJitterBuffer.destroy();
      })();
    };
  }, [joinStatus]);
};
