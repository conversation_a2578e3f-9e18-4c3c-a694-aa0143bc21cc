import { useEffect } from 'react';
import { RemoteParticipant, RoomEvent } from 'livekit-client';
import { removeRoomUserBySid, selectAllRoomUsers } from '../model';
import { logger, useAppSelector, useUpdateReduxState } from '@/shared';
import { clearRemoteParticipantLogs } from '../../class-logger';
import { useLivekitContext } from '../context';

export const useLivekitRemoteLeave = () => {
  const updateReduxState = useUpdateReduxState();

  const { room } = useLivekitContext();
  const roomUsers = useAppSelector(selectAllRoomUsers);
  const { joinStatus } = useAppSelector((state) => state.livekit);

  // Handle remote user disconnecting from the livekit room
  useEffect(() => {
    if (joinStatus !== 'joined') return;
    const onParticipantDisconnected = (participant: RemoteParticipant) => {
      const roomParticipant = roomUsers.find(
        (remote) =>
          remote.sid === participant.sid ||
          remote.identity === participant.identity,
      );

      if (roomParticipant) {
        updateReduxState(removeRoomUserBySid(roomParticipant.sid));
        updateReduxState(clearRemoteParticipantLogs(roomParticipant.sid));
      }

      logger.info('Remote participant disconnected', {
        participant_sid: participant.sid,
        participant_identity: participant.identity,
        participant_role: JSON.stringify(roomParticipant?.role),
        remaining_participants_count: room.remoteParticipants.size,
        event_type: 'remote_participant_disconnected',
      });
    };

    room.on(RoomEvent.ParticipantDisconnected, onParticipantDisconnected);
    return () => {
      room.off(RoomEvent.ParticipantDisconnected, onParticipantDisconnected);
    };
  }, [joinStatus, roomUsers]);
};
