import {
  RemoteParticipant,
  RemoteTrack,
  RemoteTrackPublication,
  RoomEvent,
} from 'livekit-client';
import { useEffect, useRef, useState } from 'react';
import { logger, useAppSelector, useNotifications } from '@/shared';
import { useLivekitContext } from '../context';
import {
  isParticipantRemote,
  remoteAudioMixerTrackSubscribe,
  remoteAudioMixerTrackUnsubscribe,
} from '../utils';
import { selectAllRoomUsers } from '../model';
import { RoomParticipant } from '../types';

export const useLivekitStreams = () => {
  const { room, setRemoteVideoStreams } = useLivekitContext();

  const { joinStatus } = useAppSelector((state) => state.livekit);
  const { audioContextReady } = useAppSelector((state) => state.conference);

  const [rawAudioTracks, setRawAudioTracks] = useState<
    Map<string, MediaStreamTrack>
  >(new Map());

  const roomUsers = useAppSelector(selectAllRoomUsers);
  const reactiveRoomUsers = useRef<RoomParticipant[]>([]);

  const { addToast } = useNotifications();

  useEffect(() => {
    reactiveRoomUsers.current = roomUsers;
  }, [roomUsers]);

  useEffect(() => {
    if (!audioContextReady) return;
    rawAudioTracks.forEach((value, key) =>
      remoteAudioMixerTrackSubscribe(key, value),
    );
  }, [audioContextReady, rawAudioTracks]);

  useEffect(() => {
    if (joinStatus !== 'joined') return;

    const onTrackSubscribed = (
      track: RemoteTrack,
      _pub: RemoteTrackPublication,
      participant: RemoteParticipant,
    ) => {
      logger.info('Track subscribed', {
        track_kind: track.kind,
        track_sid: track.sid ?? '',
        participant_sid: participant.sid,
        participant_identity: participant.identity,
        track_muted: track.isMuted,
        track_source: _pub.source,
        publication_sid: _pub.trackSid,
        event_type: 'track_subscribed',
      });

      isParticipantRemote(room, participant, async () => {
        const sid = participant.sid;

        if (track.kind === 'audio') {
          if (!audioContextReady) {
            setRawAudioTracks((prev) => {
              prev.set(sid, track.mediaStreamTrack);
              return prev;
            });
          } else {
            const user = reactiveRoomUsers.current.find((u) => u.sid === sid);

            remoteAudioMixerTrackSubscribe(
              sid,
              track.mediaStreamTrack,
              user?.profile?.displayName,
              addToast,
            );
          }
          return;
        }

        setRemoteVideoStreams((prev) => {
          const sid = participant.sid;
          const idx = prev.findIndex((ws) => ws.sid === sid);
          if (idx === -1) {
            const newStream = new MediaStream([track.mediaStreamTrack]);
            return [...prev, { sid: sid, stream: newStream }];
          }

          const existingStreamData = prev[idx];
          if (!existingStreamData?.stream) {
            // Fallback: create new stream if existing is corrupted
            const newStream = new MediaStream([track.mediaStreamTrack]);
            const copy = [...prev];
            copy[idx] = { sid: participant.sid, stream: newStream };
            return copy;
          }

          const existingStream = existingStreamData.stream;
          existingStream.addTrack(track.mediaStreamTrack);
          const copy = [...prev];
          copy[idx] = { sid: participant.sid, stream: existingStream };
          return copy;
        });
      });
    };

    const onTrackUnsubscribed = (
      track: RemoteTrack,
      _pub: RemoteTrackPublication,
      participant: RemoteParticipant,
    ) => {
      logger.info('Track unsubscribed', {
        track_kind: track.kind,
        track_sid: track.sid ?? '',
        participant_sid: participant.sid,
        participant_identity: participant.identity,
        track_muted: track.isMuted,
        track_source: _pub.source,
        publication_sid: _pub.trackSid,
        track_data: JSON.stringify({
          id: track.mediaStreamTrack?.id,
          label: track.mediaStreamTrack?.label,
          readyState: track.mediaStreamTrack?.readyState,
          settings: track.mediaStreamTrack?.getSettings(),
        }),
        event_type: 'track_unsubscribed',
      });

      isParticipantRemote(room, participant, async () => {
        const oldTrack = track.mediaStreamTrack;
        const sid = participant.sid;

        if (track.kind === 'audio') {
          if (!audioContextReady) {
            setRawAudioTracks((prev) => {
              const copy = prev;
              copy.delete(sid);
              return copy;
            });
          } else {
            const user = reactiveRoomUsers.current.find((u) => u.sid === sid);

            remoteAudioMixerTrackUnsubscribe(
              sid,
              oldTrack,
              user?.profile?.displayName,
              addToast,
            );
          }
          return;
        }

        setRemoteVideoStreams((prev) => {
          return prev
            .map((ws) => {
              if (ws.sid !== participant.sid) return ws;
              ws.stream.getTracks().forEach((t) => {
                const oldId = t.getSettings().deviceId;
                const newId = oldTrack.getSettings().deviceId;
                if (
                  t.kind === oldTrack.kind &&
                  oldId === newId &&
                  t.id === oldTrack.id
                ) {
                  ws.stream.removeTrack(t);
                }
              });
              return { sid: ws.sid, stream: ws.stream };
            })
            .filter((ws) => ws.stream.getTracks().length > 0);
        });
      });
    };

    room.on(RoomEvent.TrackSubscribed, onTrackSubscribed);
    room.on(RoomEvent.TrackUnsubscribed, onTrackUnsubscribed);

    return () => {
      room.off(RoomEvent.TrackSubscribed, onTrackSubscribed);
      room.off(RoomEvent.TrackUnsubscribed, onTrackUnsubscribed);
    };
  }, [joinStatus, audioContextReady]);
};
