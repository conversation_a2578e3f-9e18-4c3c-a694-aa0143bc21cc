import { useEffect } from 'react';
import { logger, useAppSelector, useUpdateReduxState } from '@/shared';
import { useLivekitContext } from '../context';
import { selectAllRoomUsers, setLivekitJoinStatus } from '../model';
import { useNavigate, useParams } from 'react-router';
import { DisconnectReason, RoomEvent } from 'livekit-client';
import { ConnectionStatus } from '../lib';
import { useUser } from '@/entities';

export const useLivekitLeave = () => {
  const updateReduxState = useUpdateReduxState();
  const navigate = useNavigate();
  const { id: classroomId } = useParams<{ id: string }>();

  const { room } = useLivekitContext();
  const { role, roleType } = useUser();
  const roomUsers = useAppSelector(selectAllRoomUsers);
  const { localUser, status } = useAppSelector((state) => state.livekit);

  // Automatic watcher kick when class session ends and participant kick if he connects new device
  useEffect(() => {
    const remoteUser = roomUsers.filter((user) => user.roleType === 'party');

    if (
      status === ConnectionStatus.Connected &&
      remoteUser.length === 0 &&
      roleType === 'watcher'
    ) {
      logger.warn('Watcher kicked - class ended', {
        local_participant_sid: localUser?.sid ?? '',
        local_participant_identity: localUser?.identity ?? '',
        role: role as string,
        roleType: roleType,
        remaining_party_count: remoteUser.length,
        event_type: 'watcher_kicked_class_ended',
      });

      logger.info('Local participant leaving - class ended', {
        local_participant_sid: localUser?.sid ?? '',
        local_participant_identity: localUser?.identity ?? '',
        role: role as string,
        roleType: roleType,
        reason: 'class_ended',
        event_type: 'local_participant_leave',
      });

      updateReduxState(setLivekitJoinStatus('left'));
      navigate(`/classrooms/${classroomId}`);
    }

    const onDisconnected = (reason?: DisconnectReason) => {
      if (reason === DisconnectReason.DUPLICATE_IDENTITY) {
        logger.warn('User kicked - duplicate identity', {
          local_participant_sid: localUser?.sid ?? '',
          local_participant_identity: localUser?.identity ?? '',
          kick_reason: 'duplicate_identity',
          disconnect_reason: reason,
          event_type: 'user_kicked_duplicate_identity',
        });

        updateReduxState(setLivekitJoinStatus('left'));
        navigate(`/classrooms/${classroomId}`);
      }
    };

    room.on(RoomEvent.Disconnected, onDisconnected);
    return () => {
      room.off(RoomEvent.Disconnected, onDisconnected);
    };
  }, [localUser, status, role, roomUsers]);
};
