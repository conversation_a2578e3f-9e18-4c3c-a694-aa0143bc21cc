/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import type { Room, LocalTrackPublication } from 'livekit-client';
import {
  publishLocalTracks,
  remoteAudioMixerTrackSubscribe,
  remoteAudioMixerTrackUnsubscribe,
  updateLocalAudioTracks,
} from './mediaUtils';
import { AUDIO_OPTIONS, VIDEO_OPTIONS } from '../lib';
import { logger } from '@/shared';

// ---- Mocks ----
vi.mock('@/shared', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
  isMobile: vi.fn(() => false),
  isLowEndDevice: vi.fn(() => false),
}));

// remote audio mixer shared mocks
const getParticipant = vi.fn();
const addParticipant = vi.fn();
const updateParticipant = vi.fn();
const removeParticipant = vi.fn();
const mockRemoteAudioMixer = {
  getParticipant,
  addParticipant,
  updateParticipant,
  removeParticipant,
};

vi.mock('../../audio-processing', () => ({
  centralAudioProcessing: {
    getRemoteAudioMixer: () => mockRemoteAudioMixer,
  },
}));

// NEW: mock toggleVideo used by publishLocalTracks when a disabled video track is published
const toggleVideo = vi.fn();
vi.mock('../../local-media', () => ({
  toggleVideo: (...args: any[]) => toggleVideo(...args),
}));

// ---- Helpers ----
const makeTrack = (kind: 'audio' | 'video', id = '', enabled = true) =>
  ({ kind, id, label: id, enabled }) as unknown as MediaStreamTrack;

const makeRoom = (
  publishTrack = vi.fn(),
  unpublishTrack = vi.fn(),
  getTrackPublications = vi.fn(),
): Room =>
  ({
    localParticipant: {
      sid: 'local',
      identity: 'identity-123',
      publishTrack,
      unpublishTrack,
      getTrackPublications,
    },
  }) as any as Room;

describe('mediaUtils', () => {
  let infoSpy: ReturnType<typeof vi.spyOn>;
  let errorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    vi.clearAllMocks();
    infoSpy = vi.spyOn(logger, 'info').mockImplementation(() => {});
    errorSpy = vi.spyOn(logger, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('publishLocalTracks', () => {
    it('publishes audio and video tracks with correct options, logs, and does not toggle when video enabled', async () => {
      const audio = makeTrack('audio', 'a1', true);
      const video = makeTrack('video', 'v1', true);
      const publishTrack = vi.fn().mockResolvedValue(undefined);

      const room = makeRoom(publishTrack);
      const mediaStream = {
        id: 'stream-123',
        getTracks: () => [audio, video],
      } as MediaStream;

      await publishLocalTracks(room, mediaStream);

      expect(publishTrack).toHaveBeenCalledWith(audio, AUDIO_OPTIONS);
      expect(publishTrack).toHaveBeenCalledWith(video, VIDEO_OPTIONS);

      // start + per-track + complete logs
      expect(infoSpy).toHaveBeenCalledWith(
        'Publishing local tracks started',
        expect.objectContaining({
          stream_id: 'stream-123',
          event_type: 'local_tracks_publish_start',
        }),
      );
      expect(infoSpy).toHaveBeenCalledWith(
        'Local track published',
        expect.objectContaining({
          track_kind: 'audio',
          track_id: 'a1',
          options: JSON.stringify(AUDIO_OPTIONS),
          event_type: 'local_track_published',
        }),
      );
      expect(infoSpy).toHaveBeenCalledWith(
        'Local track published',
        expect.objectContaining({
          track_kind: 'video',
          track_id: 'v1',
          options: JSON.stringify(VIDEO_OPTIONS),
          event_type: 'local_track_published',
        }),
      );
      expect(infoSpy).toHaveBeenCalledWith(
        'All local tracks published',
        expect.objectContaining({
          event_type: 'local_tracks_publish_complete',
        }),
      );

      // no toggle when video track is enabled already
      expect(toggleVideo).not.toHaveBeenCalled();
    });

    it('calls toggleVideo for a disabled video track after publishing and logs error on failures', async () => {
      const audio = makeTrack('audio', 'a2', true);
      const disabledVideo = makeTrack('video', 'v2', false);

      const publishTrack = vi.fn().mockResolvedValue(undefined);
      const room = makeRoom(publishTrack);
      const mediaStream = {
        id: 'stream-456',
        getTracks: () => [audio, disabledVideo],
      } as MediaStream;

      await publishLocalTracks(room, mediaStream);

      // toggleVideo called for disabled video
      expect(toggleVideo).toHaveBeenCalledWith(
        room,
        'identity-123',
        false,
        mediaStream,
      );

      // If publish throws, error path:
      publishTrack.mockRejectedValueOnce(new Error('publish error'));
      const failingStream = {
        id: 'stream-789',
        getTracks: () => [makeTrack('audio', 'fail-a', true)],
      } as MediaStream;

      await publishLocalTracks(room, failingStream);

      expect(errorSpy).toHaveBeenCalledWith(
        'Failed to publish local track',
        expect.objectContaining({
          track_kind: 'audio',
          track_id: 'fail-a',
          error: 'publish error',
          event_type: 'local_track_publish_error',
        }),
      );
    });
  });

  describe('updateLocalAudioTracks', () => {
    it('unpublishes old audio tracks and publishes new ones, logs both', async () => {
      const oldAudioTrack = makeTrack('audio', 'old-audio');
      const oldPublication = {
        track: { mediaStreamTrack: oldAudioTrack },
      } as unknown as LocalTrackPublication;

      const unpublishTrack = vi.fn().mockResolvedValue(undefined);
      const publishTrack = vi.fn().mockResolvedValue(undefined);
      const getTrackPublications = vi.fn().mockReturnValue([oldPublication]);

      const room = makeRoom(publishTrack, unpublishTrack, getTrackPublications);

      const newAudioTrack = makeTrack('audio', 'new-audio');
      const newStream = {
        getAudioTracks: () => [newAudioTrack],
      } as MediaStream;

      await updateLocalAudioTracks(room, newStream);

      expect(unpublishTrack).toHaveBeenCalledWith(oldAudioTrack);
      expect(publishTrack).toHaveBeenCalledWith(newAudioTrack, AUDIO_OPTIONS);

      expect(logger.info).toHaveBeenCalledWith(
        'Local track unpublished',
        expect.objectContaining({
          track_kind: 'audio',
          track_id: 'old-audio',
          options: JSON.stringify(AUDIO_OPTIONS),
          event_type: 'local_track_unpublished',
        }),
      );
      expect(logger.info).toHaveBeenCalledWith(
        'Local track published',
        expect.objectContaining({
          track_kind: 'audio',
          track_id: 'new-audio',
          options: JSON.stringify(AUDIO_OPTIONS),
          event_type: 'local_track_published',
        }),
      );
    });

    it('unpublishes old tracks and publishes none if there are no new audio tracks', async () => {
      const oldAudioTrack = makeTrack('audio', 'old-only');
      const oldPublication = {
        track: { mediaStreamTrack: oldAudioTrack },
      } as unknown as LocalTrackPublication;

      const unpublishTrack = vi.fn().mockResolvedValue(undefined);
      const publishTrack = vi.fn();
      const getTrackPublications = vi.fn().mockReturnValue([oldPublication]);

      const room = makeRoom(publishTrack, unpublishTrack, getTrackPublications);

      const newStream = {
        getAudioTracks: () => [],
      } as unknown as MediaStream;

      await updateLocalAudioTracks(room, newStream);

      expect(unpublishTrack).toHaveBeenCalledWith(oldAudioTrack);
      expect(publishTrack).not.toHaveBeenCalled();
    });

    it('re-publishes when published tracks match new tracks (no skip/warn)', async () => {
      const sameTrack = makeTrack('audio', 'track-1');

      const oldPublication = {
        track: { mediaStreamTrack: sameTrack },
      } as unknown as LocalTrackPublication;

      const unpublishTrack = vi.fn().mockResolvedValue(undefined);
      const publishTrack = vi.fn().mockResolvedValue(undefined);
      const getTrackPublications = vi.fn().mockReturnValue([oldPublication]);

      const room = makeRoom(publishTrack, unpublishTrack, getTrackPublications);

      const newStream = {
        getAudioTracks: () => [sameTrack],
      } as MediaStream;

      const warnSpy = vi.spyOn(logger, 'warn');

      await updateLocalAudioTracks(room, newStream);

      expect(unpublishTrack).toHaveBeenCalledWith(sameTrack);
      expect(publishTrack).toHaveBeenCalledWith(sameTrack, AUDIO_OPTIONS);
      expect(warnSpy).not.toHaveBeenCalled();
    });
  });

  describe('remoteAudioMixerTrackSubscribe', () => {
    it('adds track to existing participant and updates', async () => {
      const sid = 'sid123';
      const track = { id: 'track123', kind: 'audio' } as MediaStreamTrack;

      const mockStream = {
        addTrack: vi.fn(),
      } as unknown as MediaStream;

      const mockParticipant = {
        getStream: vi.fn(() => mockStream),
      };

      getParticipant.mockReturnValue(mockParticipant);

      await remoteAudioMixerTrackSubscribe(sid, track);

      expect(mockStream.addTrack).toHaveBeenCalledWith(track);
      expect(updateParticipant).toHaveBeenCalledWith(sid, mockStream);
      expect(addParticipant).not.toHaveBeenCalled();
    });

    it('creates new participant if not existing (and can toast)', async () => {
      const sid = 'sid456';
      const track = { id: 'track456', kind: 'audio' } as MediaStreamTrack;

      getParticipant.mockReturnValue(undefined);

      const addToast = vi.fn();
      await remoteAudioMixerTrackSubscribe(sid, track, 'Alice', addToast);

      expect(addParticipant).toHaveBeenCalledWith(sid, expect.any(MediaStream));
      expect(updateParticipant).not.toHaveBeenCalled();
      expect(addToast).toHaveBeenCalledWith({
        text: 'notifications.success.joined',
        params: { displayName: 'Alice' },
      });
    });
  });

  describe('remoteAudioMixerTrackUnsubscribe', () => {
    it('removes track and updates participant if tracks remain', async () => {
      const sid = 'sid789';
      const track = { id: 'track789', kind: 'audio' } as MediaStreamTrack;

      const mockStream = {
        removeTrack: vi.fn(),
        getTracks: vi.fn(() => [{ id: 'another-track' }]),
      } as unknown as MediaStream;

      const mockParticipant = {
        getStream: vi.fn(() => mockStream),
      };

      getParticipant.mockReturnValue(mockParticipant);

      await remoteAudioMixerTrackUnsubscribe(sid, track);

      expect(mockStream.removeTrack).toHaveBeenCalledWith(track);
      expect(updateParticipant).toHaveBeenCalledWith(sid, mockStream);
      expect(removeParticipant).not.toHaveBeenCalled();
    });

    it('removes participant if no tracks remain (and can toast)', async () => {
      const sid = 'sid000';
      const track = { id: 'track000', kind: 'audio' } as MediaStreamTrack;

      const mockStream = {
        removeTrack: vi.fn(),
        getTracks: vi.fn(() => []),
      } as unknown as MediaStream;

      const mockParticipant = {
        getStream: vi.fn(() => mockStream),
      };

      getParticipant.mockReturnValue(mockParticipant);

      const addToast = vi.fn();
      await remoteAudioMixerTrackUnsubscribe(sid, track, 'Bob', addToast);

      expect(mockStream.removeTrack).toHaveBeenCalledWith(track);
      expect(removeParticipant).toHaveBeenCalledWith(sid);
      expect(updateParticipant).not.toHaveBeenCalled();
      expect(addToast).toHaveBeenCalledWith({
        text: 'notifications.success.left',
        params: { displayName: 'Bob' },
      });
    });

    it('does nothing if participant does not exist', async () => {
      const sid = 'sidXXX';
      const track = { id: 'trackXXX', kind: 'audio' } as MediaStreamTrack;

      getParticipant.mockReturnValue(undefined);

      await remoteAudioMixerTrackUnsubscribe(sid, track);

      expect(removeParticipant).not.toHaveBeenCalled();
      expect(updateParticipant).not.toHaveBeenCalled();
    });
  });
});
