/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import type { Room } from 'livekit-client';
import type { DataChannelMessage, RoomParticipant } from '../types';
import { Role } from '@/shared';
import { sendData, sendHistoryData } from './dataChannelUtils';
import { ChatMessage } from '../../chat';

// Mocks
const makeRoom = (publishData?: any): Room =>
  ({
    localParticipant: { sid: 'local', publishData },
  }) as unknown as Room;

const makeLogs = () => ({
  sid: 'log-sid',
  logs: {} as any,
});

describe('sendData', () => {
  let consoleSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('logs error if localParticipant is missing', () => {
    const room = {} as Room;
    sendData(room, { type: 'chat', message: 'hi' } as any);
    expect(consoleSpy).toHaveBeenCalledWith(
      'Cannot send message: not connected to LiveKit',
      'error',
    );
  });

  it('encodes and publishes data', () => {
    const publishData = vi.fn();
    const room = makeRoom(publishData);
    const message: DataChannelMessage = {
      type: 'chat',
      message: { text: 'hi' } as any,
    };

    sendData(room, message);
    expect(publishData).toHaveBeenCalledOnce();
    const [payload, opts] = publishData.mock.calls[0];
    expect(JSON.parse(new TextDecoder().decode(payload))).toEqual(message);
    expect(opts).toEqual({ reliable: true });
  });
});

describe('sendHistoryData', () => {
  let publishData: ReturnType<typeof vi.fn>;
  let room: Room;

  beforeEach(() => {
    publishData = vi.fn();
    room = makeRoom(publishData);
    vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('sends history and media states for a party user', () => {
    const localUser: RoomParticipant = {
      sid: 'user-1',
      identity: 'user-1',
      role: Role.Student,
      roleType: 'party',
      profile: null,
      isMicrophoneOn: true,
      isVideoOn: false,
    };
    const messages: ChatMessage[] = [
      {
        author: { sid: 'u1' } as any,
        message: 'hello',
        sendDate: 1,
        readDate: 123,
      },
    ];
    const logs = makeLogs();

    sendHistoryData(room, localUser, messages, logs);
    expect(publishData).toHaveBeenCalledTimes(3);
    const history = JSON.parse(
      new TextDecoder().decode(publishData.mock.calls[0][0]),
    );
    expect(history.type).toBe('history');
    expect(history.message.logs).toEqual(logs);
  });

  it('sends only history if localUser is null', () => {
    const messages: ChatMessage[] = [
      { author: { sid: 'u1' } as any, message: 'x', sendDate: 0, readDate: 10 },
    ];
    const logs = makeLogs();

    sendHistoryData(room, null, messages, logs);
    expect(publishData).toHaveBeenCalledTimes(1);
    const payload = JSON.parse(
      new TextDecoder().decode(publishData.mock.calls[0][0]),
    );
    expect(payload.type).toBe('history');
    expect(payload.message.logs).toEqual(logs);
  });

  it('defaults media state to true if null', () => {
    const localUser: RoomParticipant = {
      sid: 'user-2',
      identity: 'user-2',
      role: Role.Student,
      roleType: 'party',
      profile: null,
      isMicrophoneOn: null,
      isVideoOn: null,
    };
    const logs = makeLogs();

    sendHistoryData(room, localUser, [], logs);
    const mic = JSON.parse(
      new TextDecoder().decode(publishData.mock.calls[1][0]),
    );
    const video = JSON.parse(
      new TextDecoder().decode(publishData.mock.calls[2][0]),
    );
    expect(mic.message.state).toBe(true);
    expect(video.message.state).toBe(true);
  });
});
