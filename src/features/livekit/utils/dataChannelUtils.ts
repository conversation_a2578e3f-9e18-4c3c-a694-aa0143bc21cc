import { Room } from 'livekit-client';
import { DataChannelMessage, RoomParticipant } from '../types';
import { ParticipantLogs } from '../../class-logger';
import { ChatMessage } from '../../chat';

export const sendData = (room: Room, message: DataChannelMessage) => {
  if (!room || !room.localParticipant) {
    console.log('Cannot send message: not connected to LiveKit', 'error');
    return;
  }

  try {
    const payload = new TextEncoder().encode(JSON.stringify(message));
    room.localParticipant.publishData(payload, {
      reliable: true,
    });
  } catch (error) {
    console.log('Error sending data: ' + error, 'error');
  }
};

export const sendHistoryData = (
  room: Room,
  localUser: RoomParticipant | null,
  messages: ChatMessage[],
  logs: { sid: string; logs: ParticipantLogs },
) => {
  const unreadMessages: ChatMessage[] = messages.map((message) => ({
    ...message,
    readDate: null,
  }));

  sendData(room, {
    type: 'history',
    message: {
      method: 'post',
      messages: unreadMessages,
      logs,
    },
  });

  if (localUser && localUser.roleType === 'party') {
    sendData(room, {
      type: 'medias',
      message: {
        sid: localUser.sid,
        type: 'mic',
        state: localUser.isMicrophoneOn ?? true,
      },
    });

    sendData(room, {
      type: 'medias',
      message: {
        sid: localUser.sid,
        type: 'video',
        state: localUser.isVideoOn ?? true,
      },
    });
  }
};
