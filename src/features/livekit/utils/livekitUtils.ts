import { ClassParticipant } from '@/entities';
import { ParticipantKind, RemoteParticipant, Room } from 'livekit-client';

export const isParticipantRemote = (
  room: Room,
  participant: RemoteParticipant,
  onRemote: () => void,
) => {
  if (participant.sid === room.localParticipant.sid) {
    console.log(`Skipping self connection`);
    return;
  }

  if (participant.kind === ParticipantKind.AGENT) {
    console.log('Agent detected, not initiating P2P', 'info');
    return;
  }

  if (participant.metadata) {
    try {
      const metadata = JSON.parse(participant.metadata);
      if (metadata.role === 'agent') {
        console.log('Agent detected, not initiating P2P', 'info');
        return;
      }

      if (metadata.role === 'guest') {
        console.log('Watcher joined, not initiating P2P', 'info');
        return;
      }
    } catch (error) {
      console.log('Failed to parse participant metadata', error);
      return;
    }
  }

  onRemote();
};

export const isParticipantWatcher = (
  room: Room,
  participant: RemoteParticipant,
  onWatcher: () => void,
) => {
  if (participant.sid === room.localParticipant.sid) {
    console.log(`Skipping self connection`);
    return;
  }

  if (participant.kind === ParticipantKind.AGENT) {
    console.log('Agent detected, not initiating P2P', 'info');
    return;
  }

  if (participant.metadata) {
    try {
      const metadata = JSON.parse(participant.metadata);
      if (metadata.role === 'agent') {
        console.log('Agent detected, not initiating P2P', 'info');
        return;
      }

      if (metadata.role !== 'guest') {
        console.log('Party joined, not initiating P2P', 'info');
        return;
      }
    } catch (error) {
      console.log('Failed to parse participant metadata', error);
      return;
    }
  }

  onWatcher();
};

export const getParticipantProfile = (
  id: string,
  participants: ClassParticipant[],
) => {
  const classParticipant = participants.find((user) => user.userId === id);
  return classParticipant ?? null;
};
