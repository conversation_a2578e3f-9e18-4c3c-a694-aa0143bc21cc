import { Room } from 'livekit-client';
import { logger } from '@/shared';
import { AUDIO_OPTIONS, VIDEO_OPTIONS } from '../lib';
import { centralAudioProcessing } from '../../audio-processing';
import { toggleVideo } from '../../local-media';

export const publishLocalTracks = async (
  room: Room,
  mediaStream: MediaStream,
) => {
  logger.info('Publishing local tracks started', {
    stream_id: mediaStream.id,
    tracks_info: JSON.stringify(
      mediaStream.getTracks().map((t) => ({
        kind: t.kind,
        id: t.id,
        label: t.label,
        enabled: t.enabled,
      })),
    ),
    event_type: 'local_tracks_publish_start',
  });

  for (const track of mediaStream.getTracks()) {
    try {
      const enabled = track.enabled;
      await room.localParticipant.publishTrack(
        track,
        track.kind === 'audio' ? AUDIO_OPTIONS : VIDEO_OPTIONS,
      );

      if (!enabled && track.kind === 'video') {
        toggleVideo(room, room.localParticipant.identity, enabled, mediaStream);
      }

      logger.info('Local track published', {
        track_kind: track.kind,
        track_id: track.id,
        track_label: track.label,
        track_enabled: track.enabled,
        options:
          track.kind === 'audio'
            ? JSON.stringify(AUDIO_OPTIONS)
            : JSON.stringify(VIDEO_OPTIONS),
        event_type: 'local_track_published',
      });
    } catch (error) {
      logger.error('Failed to publish local track', {
        track_kind: track.kind,
        track_id: track.id,
        error: error instanceof Error ? error.message : String(error),
        event_type: 'local_track_publish_error',
      });
    }
  }

  logger.info('All local tracks published', {
    event_type: 'local_tracks_publish_complete',
  });
};

export const updateLocalAudioTracks = async (
  room: Room,
  newStream: MediaStream,
) => {
  const publishedAudioTracks = room.localParticipant
    .getTrackPublications()
    .map((pub) => pub.track?.mediaStreamTrack)
    .filter((t): t is MediaStreamTrack => !!t && t.kind === 'audio');

  const newAudioTracks = newStream.getAudioTracks();

  // Unpublish all old audio tracks
  for (const track of publishedAudioTracks) {
    await room.localParticipant.unpublishTrack(track);
    logger.info('Local track unpublished', {
      track_kind: track.kind,
      track_id: track.id,
      track_label: track.label,
      track_enabled: track.enabled,
      options: JSON.stringify(AUDIO_OPTIONS),
      event_type: 'local_track_unpublished',
    });
  }

  // Publish each track from the new stream
  for (const track of newAudioTracks) {
    await room.localParticipant.publishTrack(track, AUDIO_OPTIONS);
    logger.info('Local track published', {
      track_kind: track.kind,
      track_id: track.id,
      track_label: track.label,
      track_enabled: track.enabled,
      options: JSON.stringify(AUDIO_OPTIONS),
      event_type: 'local_track_published',
    });
  }
};

export const remoteAudioMixerTrackSubscribe = async (
  sid: string,
  track: MediaStreamTrack,
  name?: string,
  addToast?: ({
    text,
    params,
  }: {
    text: string;
    params?: Record<string, string>;
  }) => void,
) => {
  const remoteAudioMixer = centralAudioProcessing.getRemoteAudioMixer();
  const existingParticipant = remoteAudioMixer.getParticipant(sid);
  if (existingParticipant) {
    const existingStream = existingParticipant.getStream();
    existingStream.addTrack(track);
    await remoteAudioMixer.updateParticipant(sid, existingStream);
  } else {
    const newStream = new MediaStream([track]);
    await remoteAudioMixer.addParticipant(sid, newStream);

    if (name && addToast) {
      addToast?.({
        text: 'notifications.success.joined',
        params: {
          displayName: name,
        },
      });
    }
  }
};

export const remoteAudioMixerTrackUnsubscribe = async (
  sid: string,
  track: MediaStreamTrack,
  name?: string,
  addToast?: ({
    text,
    params,
  }: {
    text: string;
    params?: Record<string, string>;
  }) => void,
) => {
  const remoteAudioMixer = centralAudioProcessing.getRemoteAudioMixer();
  const existingParticipant = remoteAudioMixer.getParticipant(sid);
  if (existingParticipant) {
    const existingStream = existingParticipant.getStream();
    existingStream.removeTrack(track);
    if (existingStream.getTracks().length === 0) {
      await remoteAudioMixer.removeParticipant(sid);
      if (addToast && name) {
        addToast?.({
          text: 'notifications.success.left',
          params: {
            displayName: name,
          },
        });
      }
    } else {
      await remoteAudioMixer.updateParticipant(sid, existingStream);
    }
  }
};
