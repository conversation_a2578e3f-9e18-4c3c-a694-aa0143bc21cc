/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import type { RemoteParticipant, Room } from 'livekit-client';
import { isParticipantRemote, isParticipantWatcher } from './livekitUtils';

// Mocks
const makeRoom = (): Room =>
  ({
    localParticipant: { sid: 'local' },
  }) as unknown as Room;

describe('isParticipantRemote', () => {
  let consoleSpy: ReturnType<typeof vi.spyOn>;
  let onRemote: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    onRemote = vi.fn();
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('skips self', () => {
    const room = makeRoom();
    const participant = { sid: 'local' } as RemoteParticipant;
    isParticipantRemote(room, participant, onRemote);
    expect(consoleSpy).toHaveBeenCalledWith('Skipping self connection');
    expect(onRemote).not.toHaveBeenCalled();
  });

  it('skips agent and guest roles', () => {
    const room = makeRoom();
    ['agent', 'guest'].forEach((role) => {
      const participant = {
        sid: 'remote',
        metadata: JSON.stringify({ role }),
      } as RemoteParticipant;
      isParticipantRemote(room, participant, onRemote);
      expect(onRemote).not.toHaveBeenCalled();
    });
  });

  it('skips on malformed metadata', () => {
    const room = makeRoom();
    const participant = {
      sid: 'remote',
      metadata: 'bad-json',
    } as RemoteParticipant;
    isParticipantRemote(room, participant, onRemote);
    expect(onRemote).not.toHaveBeenCalled();
  });

  it('calls onRemote for valid non-agent/non-guest participant', () => {
    const room = makeRoom();
    const participant = {
      sid: 'remote',
      metadata: JSON.stringify({ role: 'party' }),
    } as RemoteParticipant;
    isParticipantRemote(room, participant, onRemote);
    expect(onRemote).toHaveBeenCalledOnce();
  });

  it('calls onRemote when metadata exists but has no relevant role', () => {
    const room = makeRoom();
    const participant = {
      sid: 'remote',
      metadata: JSON.stringify({ foo: 'bar' }),
    } as RemoteParticipant;
    isParticipantRemote(room, participant, onRemote);
    expect(onRemote).toHaveBeenCalledOnce();
  });
});

describe('isParticipantWatcher', () => {
  let consoleSpy: ReturnType<typeof vi.spyOn>;
  let onWatcher: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    onWatcher = vi.fn();
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('skips self', () => {
    const room = makeRoom();
    const participant = { sid: 'local' } as RemoteParticipant;
    isParticipantWatcher(room, participant, onWatcher);
    expect(consoleSpy).toHaveBeenCalledWith('Skipping self connection');
    expect(onWatcher).not.toHaveBeenCalled();
  });

  it('skips agent and any non-guest roles', () => {
    const room = makeRoom();

    // agent should be skipped
    let participant = {
      sid: 'remote',
      metadata: JSON.stringify({ role: 'agent' }),
    } as RemoteParticipant;
    isParticipantWatcher(room, participant, onWatcher);
    expect(onWatcher).not.toHaveBeenCalled();

    // party (or any role != guest) should be skipped
    participant = {
      sid: 'remote',
      metadata: JSON.stringify({ role: 'party' }),
    } as RemoteParticipant;
    isParticipantWatcher(room, participant, onWatcher);
    expect(onWatcher).not.toHaveBeenCalled();

    // arbitrary non-guest role
    participant = {
      sid: 'remote',
      metadata: JSON.stringify({ role: 'something-else' }),
    } as RemoteParticipant;
    isParticipantWatcher(room, participant, onWatcher);
    expect(onWatcher).not.toHaveBeenCalled();
  });

  it('skips on malformed metadata', () => {
    const room = makeRoom();
    const participant = {
      sid: 'remote',
      metadata: 'bad-json',
    } as RemoteParticipant;
    isParticipantWatcher(room, participant, onWatcher);
    expect(onWatcher).not.toHaveBeenCalled();
  });

  it('calls onWatcher only for guest role', () => {
    const room = makeRoom();
    const participant = {
      sid: 'remote',
      metadata: JSON.stringify({ role: 'guest' }),
    } as RemoteParticipant;
    isParticipantWatcher(room, participant, onWatcher);
    expect(onWatcher).toHaveBeenCalledOnce();
  });
});
