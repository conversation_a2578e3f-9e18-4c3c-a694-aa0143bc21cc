import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  Dispatch,
  SetStateAction,
} from 'react';
import { Room } from 'livekit-client';
import { RemoteStream } from '../types';
import { isMobile } from '@/shared';

interface LivekitContextValue {
  room: Room;
  remoteVideoStreams: RemoteStream[];
  setRemoteVideoStreams: Dispatch<SetStateAction<RemoteStream[]>>;
  selectedRemoteSid: string | null;
  setSelectedRemoteSid: (sid: string | null) => void;
}

const LivekitContext = createContext<LivekitContextValue | undefined>(
  undefined,
);

export const LivekitProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [room] = useState(
    () =>
      new Room({
        adaptiveStream: isMobile(),
        dynacast: isMobile(),
        stopLocalTrackOnUnpublish: false,
      }),
  );

  const [remoteVideoStreams, setRemoteVideoStreams] = useState<RemoteStream[]>(
    [],
  );
  const [selectedRemoteSid, setSelectedRemoteSid] = useState<string | null>(
    null,
  );

  return (
    <LivekitContext.Provider
      value={{
        room,
        remoteVideoStreams,
        setRemoteVideoStreams,
        selectedRemoteSid,
        setSelectedRemoteSid,
      }}
    >
      {children}
    </LivekitContext.Provider>
  );
};

export function useLivekitContext(): LivekitContextValue {
  const context = useContext(LivekitContext);
  if (!context) {
    throw new Error('useLivekitContext must be used within a LivekitProvider');
  }
  return context;
}
