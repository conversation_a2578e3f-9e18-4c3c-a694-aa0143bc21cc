import { AudioContextProvider } from '../audio-context';

export enum TimeSignature {
  TWO_FOUR = '2/4',
  THREE_FOUR = '3/4',
  FOUR_FOUR = '4/4',
  SIX_EIGHT = '6/8',
  NINE_EIGHT = '9/8',
  TWELVE_EIGHT = '12/8',
  FIVE_FOUR = '5/4',
  SEVEN_EIGHT = '7/8',
  THREE_EIGHT = '3/8',
  TWO_TWO = '2/2',
  FIVE_EIGHT = '5/8',
  SEVEN_FOUR = '7/4',
}

export interface TimeSignatureOption {
  value: TimeSignature;
  label: string;
}

export const TIME_SIGNATURES: TimeSignatureOption[] = [
  { value: TimeSignature.TWO_FOUR, label: 'class.tools.metronome.select.2/4' },
  {
    value: TimeSignature.THREE_FOUR,
    label: 'class.tools.metronome.select.3/4',
  },
  { value: TimeSignature.FOUR_FOUR, label: 'class.tools.metronome.select.4/4' },
  { value: TimeSignature.SIX_EIGHT, label: 'class.tools.metronome.select.6/8' },
  {
    value: TimeSignature.NINE_EIGHT,
    label: 'class.tools.metronome.select.9/8',
  },
  {
    value: TimeSignature.TWELVE_EIGHT,
    label: 'class.tools.metronome.select.12/8',
  },
  { value: TimeSignature.FIVE_FOUR, label: 'class.tools.metronome.select.5/4' },
  {
    value: TimeSignature.SEVEN_EIGHT,
    label: 'class.tools.metronome.select.7/8',
  },
  {
    value: TimeSignature.THREE_EIGHT,
    label: 'class.tools.metronome.select.3/8',
  },
  { value: TimeSignature.TWO_TWO, label: 'class.tools.metronome.select.2/2' },
  {
    value: TimeSignature.FIVE_EIGHT,
    label: 'class.tools.metronome.select.5/8',
  },
  {
    value: TimeSignature.SEVEN_FOUR,
    label: 'class.tools.metronome.select.7/4',
  },
];

export class Metronome {
  private audioContextProvider!: AudioContextProvider;
  private destination: AudioNode;

  private selectedSignature: TimeSignature = TimeSignature.FOUR_FOUR;
  private bpm: number = 120;
  private currentBeat: number = 0;
  private isPlaying: boolean = false;
  private volume: number = 100;

  private nextNoteTime: number = 0;
  private intervalId: NodeJS.Timeout | null = null;
  private beatCount: number = 0;

  private onBpmChange?: (bpm: number) => void;
  private onCurrentBeatChange?: (beat: number) => void;
  private onPlayingStateChange?: (isPlaying: boolean) => void;
  private onSignatureChange?: (signature: TimeSignature) => void;
  private onVolumeChange?: (volume: number) => void;

  private onMixingChange?: (enabled: boolean) => void;
  private mixingIntoLocal: boolean = true;

  constructor(
    audioContextProvider: AudioContextProvider,
    destination: AudioNode,
  ) {
    this.audioContextProvider = audioContextProvider;
    this.destination = destination;
  }

  onBpmChanged(callback: (bpm: number) => void) {
    this.onBpmChange = callback;
  }

  onCurrentBeatChanged(callback: (beat: number) => void) {
    this.onCurrentBeatChange = callback;
  }

  onPlayingStateChanged(callback: (isPlaying: boolean) => void) {
    this.onPlayingStateChange = callback;
  }

  onSignatureChanged(callback: (signature: TimeSignature) => void) {
    this.onSignatureChange = callback;
  }

  onVolumeChanged(callback: (volume: number) => void) {
    this.onVolumeChange = callback;
  }

  onMixingChanged(callback: (enabled: boolean) => void) {
    this.onMixingChange = callback;
  }

  getBpm(): number {
    return this.bpm;
  }

  getCurrentBeat(): number {
    return this.currentBeat;
  }

  getIsPlaying(): boolean {
    return this.isPlaying;
  }

  getSelectedSignature(): TimeSignature {
    return this.selectedSignature;
  }

  getVolume(): number {
    return this.volume;
  }

  increaseBpm(): void {
    const newBpm = Math.min(this.bpm + 1, 300);
    this.setBpm(newBpm);
  }

  decreaseBpm(): void {
    const newBpm = Math.max(this.bpm - 1, 40);
    this.setBpm(newBpm);
  }

  setBpm(newBpm: number): void {
    const wasPlaying = this.isPlaying;
    if (wasPlaying) {
      this.stop();
    }

    this.bpm = newBpm;
    this.onBpmChange?.(this.bpm);
  }

  setVolume(volume: number): void {
    this.volume = Math.max(0, Math.min(300, volume));
    this.onVolumeChange?.(this.volume);
  }

  setMixingIntoLocal(enabled: boolean) {
    this.mixingIntoLocal = enabled;
    this.onMixingChange?.(this.mixingIntoLocal);
  }

  increaseVolume(step: number = 10): void {
    this.setVolume(this.volume + step);
  }

  decreaseVolume(step: number = 10): void {
    this.setVolume(this.volume - step);
  }

  setTimeSignature(signature: TimeSignature): void {
    const wasPlaying = this.isPlaying;
    if (wasPlaying) {
      this.stop();
    }

    this.selectedSignature = signature;
    this.onSignatureChange?.(this.selectedSignature);
  }

  private getBeatsPerMeasure(): number {
    const [numerator] = this.selectedSignature.split('/');
    return Number(numerator);
  }

  private createClickSound(isDownbeat: boolean, startTime: number): void {
    const audioContext = this.audioContextProvider.getAudioContext();
    const osc = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    osc.connect(gainNode);
    gainNode.connect(audioContext.destination);
    if (this.mixingIntoLocal) {
      gainNode.connect(this.destination);
    }

    osc.start(startTime);

    const volumeGain = this.volume / 100;

    if (isDownbeat) {
      osc.frequency.value = 880;
      osc.type = 'sine';
      const finalGain = volumeGain;
      gainNode.gain.setValueAtTime(finalGain, startTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);
      osc.stop(startTime + 0.1);
    } else {
      osc.frequency.value = 660;
      osc.type = 'sine';
      const finalGain = 0.9 * volumeGain;
      gainNode.gain.setValueAtTime(finalGain, startTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.05);
      osc.stop(startTime + 0.05);
    }
  }

  private scheduleBeats = (): void => {
    const secondsPerBeat = 60 / this.bpm;
    const audioContext = this.audioContextProvider.getAudioContext();
    const currentTime = audioContext.currentTime;
    const beatsPerMeasure = this.getBeatsPerMeasure();

    while (this.nextNoteTime < currentTime + 0.1) {
      const beatInMeasure = this.beatCount % beatsPerMeasure;
      const isDownbeat = beatInMeasure === 0;

      this.createClickSound(isDownbeat, this.nextNoteTime);
      this.nextNoteTime += secondsPerBeat;
      this.beatCount++;

      this.currentBeat = beatInMeasure;
      this.onCurrentBeatChange?.(this.currentBeat);
    }
  };

  start(): void {
    if (this.isPlaying) return;

    const audioContext = this.audioContextProvider.getAudioContext();
    this.nextNoteTime = audioContext.currentTime;
    this.beatCount = 0;

    if (audioContext.state === 'suspended') {
      audioContext.resume();
    }

    this.intervalId = setInterval(this.scheduleBeats, 25);
    this.isPlaying = true;
    this.onPlayingStateChange?.(this.isPlaying);
  }

  stop(): void {
    if (!this.isPlaying) return;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    this.isPlaying = false;
    this.currentBeat = 0;
    this.beatCount = 0;

    this.onPlayingStateChange?.(this.isPlaying);
    this.onCurrentBeatChange?.(this.currentBeat);
  }

  setSinkId(sinkId: string): void {
    if (!this.audioContextProvider.setSinkId) return;
    this.audioContextProvider.setSinkId(sinkId);
  }

  toggle(): void {
    if (this.isPlaying) {
      this.stop();
    } else {
      this.start();
    }
  }
}
