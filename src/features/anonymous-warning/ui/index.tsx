import { useUser } from '@/entities';
import { motion, AnimatePresence } from 'framer-motion';
import { CircleAlert } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router';

export const AnonymousWarning = () => {
  const { user } = useUser();
  const [show, setShow] = useState(false);
  const location = useLocation();
  const { t } = useTranslation();

  const DELAY = 3000;

  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const startTimeRef = useRef<number>(0);
  const remainingRef = useRef<number>(DELAY);

  const clearTimer = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  };

  const startTimer = (duration: number) => {
    startTimeRef.current = Date.now();
    timerRef.current = setTimeout(() => {
      setShow(false);
      timerRef.current = null;
      remainingRef.current = DELAY;
    }, duration);
  };

  useEffect(() => {
    clearTimer();
    remainingRef.current = DELAY;

    if (
      location.pathname === '/' ||
      location.pathname === '/class' ||
      !user?.isAnonymous
    ) {
      setShow(false);
      return;
    }

    setShow(true);
    startTimer(DELAY);

    return () => clearTimer();
  }, [location.pathname, user]);

  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="w-fit fixed top-3 mx-auto z-50 flex items-center justify-between gap-2 max-[500px]:flex-col bg-[#FFAB00] p-4 rounded-lg transition-transform duration-300 hover:-translate-y-1 cursor-default min-[600px]:w-full min-[600px]:max-w-[500px]"
          style={{
            left: 'max(env(safe-area-inset-left), 1rem)',
            right: 'max(env(safe-area-inset-right), 1rem)',
          }}
          onMouseEnter={() => {
            clearTimer();
            const elapsed = Date.now() - startTimeRef.current;
            remainingRef.current = Math.max(0, remainingRef.current - elapsed);
          }}
          onMouseLeave={() => {
            if (remainingRef.current > 0) {
              startTimer(remainingRef.current);
            } else {
              setShow(false);
            }
          }}
        >
          <div 
            className="grid grid-cols-[20px_auto] items-center gap-2 text-sm font-semibold text-primary">
            <CircleAlert className="size-5" />
            {t('anonymousAlert.text')}
          </div>
          <Link
            data-testid="anonymous-warning-link"
            to="/?register=true"
            replace
            className="text-primary/90 transition-colors duration-300 hover:text-primary font-bold whitespace-nowrap"
          >
            {t('anonymousAlert.action')}
          </Link>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
