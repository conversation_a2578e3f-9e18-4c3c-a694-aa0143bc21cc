import { FC, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { PDFDocumentProxy, RenderTask } from 'pdfjs-dist';

interface IPageProps {
  pdf: PDFDocumentProxy;
  pageNumber: number;
  container: HTMLDivElement | null;
  scale: number;
}

export const Page: FC<IPageProps> = ({
  pdf,
  pageNumber,
  container,
  scale = 2,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [ratioString, setRatioString] = useState<string>('');
  const [rendered, setRendered] = useState(false);
  const renderTimeoutRef = useRef<number>(undefined);
  const renderTaskRef = useRef<RenderTask | null>(null);

  useEffect(() => {
    if (!container || !canvasRef.current || rendered) return;

    const renderPage = () => {
      pdf.getPage(pageNumber).then((page) => {
        const [xMin, yMin, xMax, yMax] = page.view;
        const docWidth = xMax - xMin;
        const docHeight = yMax - yMin;
        setRatioString(`${docWidth}/${docHeight}`);

        const viewport = page.getViewport({ scale });
        const canvas = canvasRef.current!;

        // Cancel any existing render task before starting a new one
        if (renderTaskRef.current) {
          renderTaskRef.current.cancel();
          renderTaskRef.current = null;
        }

        canvas.width = viewport.width;
        canvas.height = viewport.height;

        const renderTask = page.render({
          canvas,
          canvasContext: canvas.getContext('2d')!,
          viewport,
        } as Parameters<typeof page.render>[0]);

        // Store the render task reference
        renderTaskRef.current = renderTask;

        renderTask.promise
          .then(() => {
            setRendered(true);
            observer.disconnect();
            renderTaskRef.current = null;
          })
          .catch((error) => {
            // Handle cancellation gracefully
            if (error.name !== 'RenderingCancelledException') {
              console.error('PDF rendering error:', error);
            }
            renderTaskRef.current = null;
          });
      });
    };

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting) {
          renderPage();
        }
      },
      {
        root: container,
        threshold: 0.01,
        rootMargin: '50px',
      },
    );

    observer.observe(canvasRef.current);

    return () => {
      if (renderTaskRef.current) {
        renderTaskRef.current.cancel();
        renderTaskRef.current = null;
      }

      observer.disconnect();

      if (renderTimeoutRef.current) {
        clearTimeout(renderTimeoutRef.current);
      }
    };
  }, [container, pdf, pageNumber, rendered]);

  useLayoutEffect(() => {
    (async () => {
      const page = await pdf.getPage(pageNumber);

      if (!page || !canvasRef.current) return;

      const viewport = page.getViewport({ scale });
      const canvas = canvasRef.current;

      canvas.width = viewport.width;
      canvas.height = viewport.height;
    })();
  }, []);

  return (
    <canvas
      ref={canvasRef}
      data-page-number={pageNumber}
      style={{
        display: 'block',
        width: '100%',
        aspectRatio: ratioString,
        minHeight: '200px',
      }}
    />
  );
};
