import { FC, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { getDocument, type PDFDocumentProxy } from 'pdfjs-dist';
import { getFileSizeFromURL, Loader } from '@/shared';
import { Page } from './page';
import { AnimatePresence, motion } from 'framer-motion';
import {
  ArrowLeft,
  Download,
  Maximize,
  Minimize,
  PlusCircle,
} from 'lucide-react';
import { getFileSize } from '@/shared';
import { useTranslation } from 'react-i18next';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { getPDFScale } from '../utils';
import { useVirtualizer } from '@tanstack/react-virtual';
import { cn } from '@/shadcn/lib/utils';

interface IPDFViewerProps {
  url: string;
  name: string;
  download?: () => void;
  addToMaterials?: () => void;
  onClose?: () => void;
  type?: 'classroom' | 'class' | 'imslp';
}

async function fetchPdfBytes(url: string) {
  let res = await fetch(url, { method: 'GET' });

  if (res.status === 403) {
    // refresh the presigned URL once
    res = await fetch(url, { method: 'GET' });
  }
  if (!res.ok) throw new Error(`PDF fetch failed: ${res.status}`);

  const buf = await res.arrayBuffer();
  return new Uint8Array(buf);
}

export const PDFViewer: FC<IPDFViewerProps> = ({
  url,
  download,
  addToMaterials,
  type = 'classroom',
  name,
  onClose,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [pdf, setPdf] = useState<PDFDocumentProxy | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showOverlay, setShowOverlay] = useState(false);
  const [fileSize, setFileSize] = useState<string>('');
  const [pdfScale, setPdfScale] = useState<number>(2);
  const hideTimer = useRef<number | undefined>(undefined);
  const headerRef = useRef<HTMLDivElement>(null);
  const [fullscreen, setFullscreen] = useState(false);
  const [rangeString, setRangeString] = useState('0 / 0');
  const visiblePagesRef = useRef<Set<number>>(new Set());
  const { t } = useTranslation();

  const toggleFullscreen = () => {
    if (fullscreen) headerRef.current?.classList.remove('fullscreen');
    else headerRef.current?.classList.add('fullscreen');
    setFullscreen((prev) => !prev);
  };

  useEffect(() => {
    setIsLoading(true);
    fetchPdfBytes(url)
      .then((bytes) => getDocument({ data: bytes }).promise)
      .then(setPdf)
      .catch(() => {
        setPdf(null);
      })
      .finally(() => setIsLoading(false));
  }, [url]);

  const virtualizer = useVirtualizer({
    count: pdf ? pdf.numPages : 0,
    estimateSize: () => 100,
    getScrollElement: () => containerRef.current,
    overscan: 2,
  });

  useLayoutEffect(() => {
    if (!containerRef.current || !pdf) return;

    const observer = new IntersectionObserver(
      (entries) => {
        for (const entry of entries) {
          const index = Number((entry.target as HTMLDivElement).dataset.index);
          const pageNumber = index + 1; // Convert 0-based index to 1-based page number

          if (entry.intersectionRatio >= 0.1) {
            visiblePagesRef.current.add(pageNumber);
          } else {
            visiblePagesRef.current.delete(pageNumber);
          }
        }

        const pages = Array.from(visiblePagesRef.current).sort((a, b) => a - b);
        const first = pages[0] ?? 0;
        const last = pages[pages.length - 1] ?? 0;
        const total = pdf?.numPages ?? 0;

        if (first === last) setRangeString(`${first} / ${total}`);
        else setRangeString(`${first} - ${last} / ${total}`);
      },
      { root: containerRef.current, threshold: 0.1 },
    );

    const observeCurrentElements = () => {
      // Disconnect existing observations
      observer.disconnect();

      const elements =
        containerRef.current!.querySelectorAll<HTMLDivElement>(
          'div[data-index]',
        );

      elements.forEach((el) => {
        if (el.offsetHeight > 0) {
          observer.observe(el);
        }
      });
    };

    // Initial observation
    const tryObserve = (n = 0) => {
      const elements =
        containerRef.current!.querySelectorAll<HTMLDivElement>(
          'div[data-index]',
        );
      const ready = Array.from(elements).some((el) => el.offsetHeight > 0);
      if (ready || n > 10) {
        observeCurrentElements();
      } else {
        setTimeout(() => tryObserve(n + 1), 100);
      }
    };

    tryObserve();

    // Re-observe when virtual items change (when scrolling causes new pages to render)
    const handleScroll = () => {
      // Debounce the re-observation to avoid too frequent calls
      setTimeout(observeCurrentElements, 50);
    };

    containerRef.current.addEventListener('scroll', handleScroll, {
      passive: true,
    });

    return () => {
      observer.disconnect();
      visiblePagesRef.current.clear();
      containerRef.current?.removeEventListener('scroll', handleScroll);
    };
  }, [pdf]);

  useLayoutEffect(() => {
    if (!containerRef.current || !pdf) return;

    const calculateScale = async () => {
      try {
        const fileSizeBytes = await getFileSizeFromURL(url);
        const scale = getPDFScale(fileSizeBytes || 0);
        setPdfScale(scale);

        getFileSize(url)
          .then((size) => {
            if (size) {
              setFileSize(size);
            } else {
              setFileSize('0 B');
            }
          })
          .catch(() => setFileSize('0 B'));
      } catch (error) {
        console.warn('Error calculating scale:', error);
        setPdfScale(2);
        setFileSize('0 B');
      }
    };

    calculateScale();
  }, [pdf, url]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !pdf) return;

    const onScroll = () => {
      setShowOverlay(true);
      if (hideTimer.current) clearTimeout(hideTimer.current);
      hideTimer.current = window.setTimeout(() => setShowOverlay(false), 1000);
    };

    container.addEventListener('scroll', onScroll, { passive: true });

    return () => {
      container.removeEventListener('scroll', onScroll);
      if (hideTimer.current) clearTimeout(hideTimer.current);
    };
  }, [pdf]);

  return (
    <>
      <header
        className="shrink-0 flex items-center justify-between w-full gap-4 flex-wrap"
        ref={headerRef}
        data-testid="pdf-viewer-header"
      >
        <div className="flex-1 min-w-0 grid max-[450px]:grid-cols-[1rem_auto] grid-cols-[1.5rem_auto] items-center gap-4">
          <ArrowLeft
            className="max-[450px]:!size-4 opacity-80 transition-opacity duration-300 hover:opacity-100 cursor-pointer size-6"
            onClick={() => {
              if (fullscreen) {
                toggleFullscreen();
              }
              onClose?.();
            }}
            data-testid="pdf-viewer-back-button"
          />
          <div className="flex flex-col gap-1 min-w-0">
            <h1
              className="text-[17px] font-semibold max-[450px]:text-sm truncate"
              data-testid="pdf-viewer-title"
            >
              {name}
            </h1>
            <div className="flex items-center justify-start gap-2 max-[600px]:hidden">
              <div
                className={cn(
                  'text-xs p-2 font-medium rounded-2xl',
                  type === 'classroom' ? 'bg-primary/20' : 'bg-white/20',
                )}
              >
                PDF
              </div>
              <h3
                className={cn(
                  'text-sm capitalize',
                  type === 'classroom' ? 'text-primary' : 'text-white/70',
                )}
                data-testid="pdf-viewer-file-size"
              >
                {fileSize}
              </h3>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-6">
          {type === 'imslp' && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <PlusCircle
                    className="size-6 opacity-80 transition-opacity duration-300 hover:opacity-100 cursor-not-allowed max-[450px]:size-5"
                    onClick={addToMaterials}
                    data-testid="pdf-viewer-save-button"
                  />
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('class.tools.materials.pdf.saveIMSLP.tooltip')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Download
                  className="size-6 opacity-80 transition-opacity duration-300 hover:opacity-100 cursor-pointer max-[450px]:size-5"
                  onClick={download}
                  data-testid="pdf-viewer-download-button"
                />
              </TooltipTrigger>
              <TooltipContent>
                <p>{t('class.tools.materials.pdf.download.tooltip')}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                {fullscreen ? (
                  <Minimize
                    className={cn(
                      'size-6 opacity-80 transition-opacity duration-300 hover:opacity-100 cursor-pointer',
                      type === 'classroom'
                        ? 'max-[1050px]:hidden'
                        : 'max-[600px]:hidden',
                    )}
                    onClick={toggleFullscreen}
                    data-testid="pdf-viewer-minimize-button"
                  />
                ) : (
                  <Maximize
                    className={cn(
                      'size-6 opacity-80 transition-opacity duration-300 hover:opacity-100 cursor-pointer',
                      type === 'classroom'
                        ? 'max-[1050px]:hidden'
                        : 'max-[600px]:hidden',
                    )}
                    onClick={toggleFullscreen}
                    data-testid="pdf-viewer-maximize-button"
                  />
                )}
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {fullscreen
                    ? t('class.tools.materials.pdf.fullscreen.tooltip.exit')
                    : t('class.tools.materials.pdf.fullscreen.tooltip.enter')}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </header>

      <div
        className="w-full flex-1 flex flex-col min-h-0 gap-4 relative overflow-hidden"
        data-testid="pdf-viewer-container"
      >
        {pdf && (
          <AnimatePresence>
            {showOverlay && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="pointer-events-none absolute bg-black/60 text-white px-3 py-1 rounded text-sm font-medium w-fit z-50 backdrop-blur-2xl top-4 left-4"
                data-testid="pdf-viewer-page-indicator"
              >
                {rangeString}
              </motion.div>
            )}
          </AnimatePresence>
        )}

        <div
          className={cn(
            'w-full flex-1 min-h-0 overflow-y-auto overscroll-contain',
            type === 'classroom'
              ? 'scrollbar_primary'
              : 'transparent-scrollbar',
          )}
          ref={containerRef}
          data-testid="pdf-viewer-scroll-container"
        >
          {isLoading ? (
            <div
              className="h-full w-full flex items-center justify-center"
              data-testid="pdf-viewer-loading"
            >
              <Loader />
            </div>
          ) : pdf ? (
            <div
              className="w-full flex flex-col gap-2 pr-2 relative"
              style={{ height: `${virtualizer.getTotalSize()}px` }}
              data-testid="pdf-viewer-pages-container"
            >
              <div
                className="absolute top-0 left-0 w-full"
                style={{
                  transform: `translateY(${virtualizer.getVirtualItems()[0]?.start ?? 0}px)`,
                }}
              >
                {virtualizer.getVirtualItems().map((virtualItem) => (
                  <div
                    className="mb-2"
                    key={virtualItem.key}
                    data-index={virtualItem.index}
                    data-testid={`pdf-viewer-page-${virtualItem.index + 1}`}
                    ref={virtualizer.measureElement}
                  >
                    <Page
                      pageNumber={virtualItem.index + 1}
                      pdf={pdf}
                      container={containerRef.current!}
                      scale={pdfScale}
                    />
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div
              className="h-full w-full flex flex-col gap-2 items-center justify-center"
              data-testid="pdf-viewer-error"
            >
              <h1
                className={cn(
                  'text-2xl font-bold',
                  type === 'classroom' ? 'text-primary' : 'text-white',
                )}
              >
                {t('class.tools.materials.pdf.notFound.title')}
              </h1>
              <p
                className={cn(
                  'text-center',
                  type === 'classroom' ? 'text-primary/70' : 'text-white/70',
                )}
              >
                {t('class.tools.materials.pdf.notFound.description')}
              </p>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
