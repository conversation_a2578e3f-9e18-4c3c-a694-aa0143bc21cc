import { useEffect, useRef } from 'react';
import { useCookies } from 'react-cookie';
import { useLocation, useNavigate, useSearchParams } from 'react-router';
import { useAppDispatch } from '@/shared';
import { setUser, User, useUser } from '@/entities';

export const useOAuth2 = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [, setCookie] = useCookies(['token', 'refreshToken']);
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const { user: currentUser } = useUser();
  const hasProcessed = useRef(false);

  const data = JSON.parse(searchParams.get('auth') ?? '{}') as {
    user: User;
    token: string;
    refreshToken: string;
    expiresAt: number;
  };

  useEffect(() => {
    if (!searchParams.get('auth')) {
      navigate('/', { replace: true });
      return;
    }

    if (hasProcessed.current) return;
    hasProcessed.current = true;

    const from = localStorage.getItem('inviteRedirect');

    setCookie('token', data.token, {
      path: '/',
      maxAge: data.expiresAt,
      secure: true,
      sameSite: 'lax',
    });
    setCookie('refreshToken', data.refreshToken, {
      path: '/',
      maxAge: data.expiresAt,
      secure: true,
      sameSite: 'lax',
    });

    // Check if this is a guest-to-full-account conversion
    const wasAnonymous = currentUser?.isAnonymous === true;

    if (window.gtag) {
      // Track OAuth login/signup
      window.gtag('event', 'login', {
        method: 'oauth2',
        oauth_provider: 'none',
        user_id: data.user.id,
        is_anonymous: data.user.isAnonymous,
        page_path: location.pathname,
      });

      // Track guest-to-full-account conversion for OAuth
      if (wasAnonymous) {
        window.gtag('event', 'guest_conversion', {
          previous_user_id: currentUser.id,
          new_user_id: data.user.id,
          method: 'oauth2',
          oauth_provider: 'none',
          page_path: location.pathname,
        });
      }
    }

    dispatch(setUser(data.user));

    navigate(from !== 'undefined' && from ? from : '/classrooms', {
      replace: true,
    });
  }, [data, setCookie, navigate, location.pathname]);
};
