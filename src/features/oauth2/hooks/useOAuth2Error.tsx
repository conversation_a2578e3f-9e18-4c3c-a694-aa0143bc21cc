import { useNotifications } from '@/shared';
import { useEffect } from 'react';
import { useNavigate } from 'react-router';

export const useOAuth2Error = () => {
  const navigate = useNavigate();
  const { addNotification } = useNotifications();

  addNotification?.({
    title: 'notifications.errors.serverError.title',
    description: 'notifications.errors.serverError.description',
  });

  useEffect(() => {
    navigate('/', { replace: true });
  }, []);
};
