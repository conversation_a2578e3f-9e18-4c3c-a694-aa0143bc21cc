import { useDeleteClassroom } from '@/entities';
import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { Trash2 } from 'lucide-react';
import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';

export const DeleteClassroomDialog: FC<{ classroomId: string }> = ({
  classroomId,
}) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const { mutate: deleteClassroom } = useDeleteClassroom();

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <button
          className="p-2 flex items-center gap-2 rounded-md cursor-pointer bg-[#FFFDFA] transition-all duration-300 hover:bg-[##FFF6F1] active:bg-[#FFE6D8]"
          onClick={(event) => {
            event.preventDefault();
            event.stopPropagation();
            setOpen(!open);
          }}
        >
          <Trash2 className="text-[#FF4D65] size-5" />
          <span className="text-[#FF4D65] text-sm font-medium">
            {t('classrooms.dialogs.edit.triggers.delete')}
          </span>
        </button>
      </DialogTrigger>

      <DialogContent onClick={(e) => e.stopPropagation()}>
        <DialogHeader className="gap-0">
          <DialogTitle className="text-left">
            {t('classrooms.dialogs.edit.labels.delete')}
          </DialogTitle>
          <DialogDescription />
        </DialogHeader>

        <DialogFooter className="!justify-end gap-2.5 !flex-row">
          <Button size="lg" onClick={() => deleteClassroom(classroomId)}>
            {t('classrooms.dialogs.edit.buttons.delete')}
          </Button>
          <DialogClose asChild>
            <Button size="lg" variant="outline">
              {t('classrooms.dialogs.edit.buttons.cancel')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
