import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/shadcn/components/ui/popover';
import { Menu } from 'lucide-react';
import { FC, useState } from 'react';
import { DeleteClassroomDialog } from './delete';
import { EditClassroomTitleDialog } from './edit-title';
import { EditClassroomDescriptionDialog } from './edit-description';
import { useUser } from '@/entities';

interface IEditClassroomProps {
  classroomId: string;
  title: string;
  description: string;
  ownerId: string;
}

export const EditClassroom: FC<IEditClassroomProps> = ({
  classroomId,
  title,
  description,
  ownerId,
}) => {
  const { user } = useUser();
  const [open, setOpen] = useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <button
          className="w-8 h-8 rounded-full flex items-center justify-center bg-[#FFF2E51A] border-1 border-primary/10 cursor-pointer transition-all duration-300 hover:border-[#827062] hover:bg-[#FFF2E580] active:border-[#827062] active:bg-[#FFF2E5]"
          onClick={(event) => {
            event.preventDefault();
            event.stopPropagation();
            setOpen(!open);
          }}
        >
          <Menu className="size-4 text-primary" />
        </button>
      </PopoverTrigger>
      <PopoverContent
        side="bottom"
        align="end"
        className="p-0 bg-[#FFFDFA] rounded-lg"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-3 flex flex-col gap-2">
          <EditClassroomTitleDialog
            classroomId={classroomId}
            title={title}
            description={description}
            close={() => setOpen(false)}
          />
          <EditClassroomDescriptionDialog
            classroomId={classroomId}
            title={title}
            description={description}
            close={() => setOpen(false)}
          />
          {ownerId === user!.id && (
            <DeleteClassroomDialog classroomId={classroomId} />
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
};
