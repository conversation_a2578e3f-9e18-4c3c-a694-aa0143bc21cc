import { useUpdateClassroom } from '@/entities';
import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { Input } from '@/shadcn/components/ui/input';
import { Pencil } from 'lucide-react';
import { FC, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

type FormValues = {
  description: string;
};

interface IEditClassroomDescriptionDialogProps {
  classroomId: string;
  title: string;
  description: string;
  close: () => void;
}

export const EditClassroomDescriptionDialog: FC<
  IEditClassroomDescriptionDialogProps
> = ({ classroomId, title, description, close }) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const { mutate: updateClassroom } = useUpdateClassroom(
    classroomId,
    (open: boolean) => {
      setOpen(open);
      close();
    },
  );
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: { description: description.trim() },
  });

  const onSubmit = (data: FormValues) => {
    updateClassroom({ title, description: data.description });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <button
          className="p-2 flex items-center gap-2 rounded-md cursor-pointer bg-[#FFFDFA] transition-all duration-300 hover:bg-[##FFF6F1] active:bg-[#FFE6D8]"
          onClick={(event) => {
            event.preventDefault();
            event.stopPropagation();
            setOpen(!open);
          }}
        >
          <Pencil className="text-black/60 size-5" />
          <span className="text-primary text-sm font-medium">
            {t('classrooms.dialogs.edit.triggers.description')}
          </span>
        </button>
      </DialogTrigger>

      <DialogContent onClick={(e) => e.stopPropagation()}>
        <DialogHeader className="gap-0">
          <DialogTitle className="text-left">
            {t('classrooms.dialogs.edit.labels.description')}
          </DialogTitle>
          <DialogDescription />
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <Input
              className="border-primary/30"
              placeholder={t(
                'classrooms.dialogs.edit.placeholders.description',
              )}
              {...register('description', {
                maxLength: {
                  value: 200,
                  message: t(
                    'globalFields.classroomDescription.validation.maxLength',
                  ),
                },
              })}
            />

            {errors.description && (
              <p className="text-xs text-destructive px-1">
                {errors.description.message}
              </p>
            )}
          </div>

          <DialogFooter className="!justify-end gap-2.5">
            <Button size="lg" type="submit">
              {t('classrooms.dialogs.edit.buttons.save')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
