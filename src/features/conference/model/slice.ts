import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ClassParticipant } from '@/entities';
import { ConferenceStatus } from '../lib';

interface ConferenceState {
  audioContextReady: boolean | null;
  participant: ClassParticipant | null;
  status: ConferenceStatus;
}

const initialState: ConferenceState = {
  audioContextReady: null,
  participant: null,
  status: ConferenceStatus.Fetching,
};

const conferenceSlice = createSlice({
  name: 'conference',
  initialState,
  reducers: {
    setConferenceAudioContextReady: (state, action: PayloadAction<boolean>) => {
      state.audioContextReady = action.payload;
    },
    setConferenceParticipant: (
      state,
      action: PayloadAction<ClassParticipant>,
    ) => {
      state.participant = action.payload;
    },
    setConferenceStatus: (state, action: PayloadAction<ConferenceStatus>) => {
      state.status = action.payload;
    },
    resetConference: (state) => {
      state.audioContextReady = null;
      state.participant = null;
      state.status = ConferenceStatus.Fetching;
    },
  },
});

export const {
  setConferenceAudioContextReady,
  setConferenceParticipant,
  setConferenceStatus,
  resetConference,
} = conferenceSlice.actions;
export default conferenceSlice.reducer;
