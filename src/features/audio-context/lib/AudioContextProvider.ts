export class AudioContextProvider {
  private audioContext!: AudioContext;

  static async create(sinkId?: string): Promise<AudioContextProvider> {
    const provider = new AudioContextProvider();
    await provider.initialize(sinkId);
    return provider;
  }

  private async initialize(sinkId?: string): Promise<void> {
    const audioContextOptions: AudioContextOptions = {
      latencyHint: 'interactive',
      sampleRate: 48000,
      sinkId: sinkId ?? 'default',
    };

    const audioContext = new AudioContext(audioContextOptions);
    this.audioContext = audioContext;
  }

  async resume() {
    await this.audioContext.resume();
  }

  isValid(): boolean {
    return this.audioContext.state !== 'closed';
  }

  getAudioContext(): AudioContext {
    return this.audioContext;
  }

  getCurrentSinkId(): string {
    return this.audioContext.sinkId || '';
  }

  allowedSetSinkId(): boolean {
    return typeof this.audioContext.setSinkId === 'function';
  }

  async setSinkId(sinkId: string): Promise<void> {
    if (!this.isValid()) {
      throw new Error('AudioContext is not valid');
    }

    if (!this.allowedSetSinkId()) {
      throw new Error('setSinkId is not supported in this browser');
    }

    await this.audioContext.setSinkId(sinkId);
  }

  async close(): Promise<void> {
    if (this.audioContext && this.audioContext.state !== 'closed') {
      await this.audioContext.close();
    }
  }
}
