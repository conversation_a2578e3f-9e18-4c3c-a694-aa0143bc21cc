export declare global {
  interface AudioContext {
    /**
     * The identifier of the audio output device being used by this AudioContext.
     * Returns an empty string if using the default device.
     */
    readonly sinkId: string;

    /**
     * Sets the audio output device for this AudioContext.
     * @param sinkId - The identifier of the audio output device to use.
     *                 Use an empty string or "default" for the default device.
     *                 Use a device ID obtained from MediaDevices.enumerateDevices().
     * @returns A Promise that resolves when the audio output device has been changed.
     * @throws {NotAllowedError} If the user has not granted permission to access audio devices.
     * @throws {NotFoundError} If the specified device is not found.
     * @throws {AbortError} If the operation was aborted.
     * @throws {InvalidStateError} If the AudioContext is in an invalid state.
     */
    setSinkId(sinkId: string): Promise<void>;
  }

  // Augment AudioContextOptions to include sinkId
  interface AudioContextOptions {
    /**
     * The identifier of the audio output device to use for this AudioContext.
     * Use an empty string or "default" for the default device.
     * Use a device ID obtained from MediaDevices.enumerateDevices().
     *
     * Note: This is a newer feature and may not be supported in all browsers.
     * Check browser compatibility before using.
     */
    sinkId?: string;
  }

  // Augment OfflineAudioContextOptions for completeness
  interface OfflineAudioContextOptions {
    /**
     * The identifier of the audio output device to use for this OfflineAudioContext.
     * Note: This may not be applicable for offline contexts but included for consistency.
     */
    sinkId?: string;
  }
}
