import { useEffect } from 'react';
import { useAppSelector, useUpdateReduxState } from '@/shared';
import { setIOSAudioSession } from '../../local-media';
import { centralAudioProcessing } from '../../audio-processing';
import {
  ConferenceStatus,
  setConferenceAudioContextReady,
  setConferenceStatus,
} from '../../conference';

export const useAudioContext = () => {
  const updateReduxState = useUpdateReduxState();
  const { status, audioContextReady } = useAppSelector(
    (state) => state.conference,
  );

  useEffect(() => {
    if (status !== ConferenceStatus.AudioContext) return;
    (async () => {
      try {
        setIOSAudioSession('playback');
        const isContextRunning =
          await centralAudioProcessing.createAudioContext();

        if (isContextRunning) await centralAudioProcessing.initialize();
        updateReduxState(setConferenceAudioContextReady(isContextRunning));

        updateReduxState(setConferenceStatus(ConferenceStatus.Dialog));
      } catch {
        updateReduxState(setConferenceAudioContextReady(false));
        updateReduxState(setConferenceStatus(ConferenceStatus.Dialog));
      }
    })();
  }, [status]);

  //Fallback for user interaction
  const resumeAudioContext = async () => {
    if (!audioContextReady) {
      try {
        await centralAudioProcessing.resumeAudioContext();
        await centralAudioProcessing.initialize();

        updateReduxState(setConferenceAudioContextReady(true));
      } catch {
        updateReduxState(setConferenceAudioContextReady(false));
      }
    }
  };

  useEffect(() => {
    if (status !== ConferenceStatus.Dialog) return;
    window.addEventListener('click', resumeAudioContext);
    window.addEventListener('touchstart', resumeAudioContext);
    window.addEventListener('keydown', resumeAudioContext);

    return () => {
      window.removeEventListener('click', resumeAudioContext);
      window.removeEventListener('touchstart', resumeAudioContext);
      window.removeEventListener('keydown', resumeAudioContext);
    };
  }, [status, audioContextReady]);
};
