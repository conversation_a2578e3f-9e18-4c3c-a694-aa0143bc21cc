import { Paperclip, X } from 'lucide-react';
import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/shadcn/lib/utils';

interface IUploadFiles {
  onFileUpload: (file: File) => void;
  imageOnly?: boolean;
  maxSizeMb?: number;
  maxFiles?: number;
}

export const UploadFiles = ({
  onFileUpload,
  imageOnly = true,
  maxSizeMb = 10,
  maxFiles = 3,
}: IUploadFiles) => {
  const { t } = useTranslation();
  const [files, setFiles] = useState<File[]>([]);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFile = e.target.files?.[0];
    if (!newFile) return;

    if (!newFile.type.startsWith('image/') && imageOnly) {
      setError(t('globalFields.uploadFiles.validation.imageOnly'));
      return;
    }

    if (newFile.size > maxSizeMb * 1024 * 1024) {
      setError(
        t('globalFields.uploadFiles.validation.fileSize', {
          name: newFile.name,
          size: maxSizeMb,
        }),
      );
      return;
    }

    if (files.length >= maxFiles) {
      setError(
        t('globalFields.uploadFiles.validation.maxFiles', {
          number: maxFiles,
        }),
      );
      return;
    }

    setFiles((prev) => [...prev, newFile]);
    setError(null);

    onFileUpload(newFile);

    // Clear the input so the same file can be uploaded again if needed
    e.target.value = '';
  };

  const removeFile = (index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleClick = (e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    if (!target.closest('.uploaded-card')) {
      inputRef.current?.click();
    }
  };

  return (
    <div
      data-testid="upload-files-container"
      onClick={handleClick}
      className={cn(
        'flex flex-col gap-2 py-4 px-6 border-2 border-dashed rounded-lg transition-colors',
        error ? 'border-destructive/50 bg-destructive/5' : 'border-primary/20',
      )}
    >
      {files.length < maxFiles && (
        <>
          <div data-testid="upload-files-prompt" className="flex items-center justify-center gap-2 cursor-pointer">
            <Paperclip
              data-testid="upload-files-icon"
              className={`size-4 ${error ? 'text-destructive' : 'text-primary'}`}
            />
            <span
              data-testid="upload-files-text"
              className={`font-medium ${error ? 'text-destructive' : 'text-primary'}`}
            >
              {t(
                imageOnly
                  ? 'globalFields.uploadFiles.textImageOnly'
                  : 'globalFields.uploadFiles.text',
                {
                  size: maxSizeMb,
                },
              )}
            </span>
          </div>

          <input
            data-testid="upload-files-input"
            ref={inputRef}
            id="file-upload"
            type="file"
            accept={imageOnly ? 'image/*' : ' *'}
            multiple={false}
            onChange={handleFileChange}
            className="hidden"
          />
        </>
      )}

      {files.length > 0 && (
        <div data-testid="upload-files-list" className="mt-2 grid grid-cols-1 sm:grid-cols-2 gap-2 w-full">
          {files.map((file, index) => (
            <div
              key={index}
              data-testid={`upload-files-card-${index}`}
              className="uploaded-card relative border border-primary/15 rounded-sm p-2 flex items-center gap-3"
            >
              <img
                data-testid={`upload-files-image-${index}`}
                src={URL.createObjectURL(file)}
                alt={file.name}
                className="size-14 object-cover rounded-sm"
              />
              <div data-testid={`upload-files-info-${index}`} className="flex flex-col overflow-hidden">
                <span data-testid={`upload-files-name-${index}`} className="text-sm font-medium truncate max-w-[170px]">
                  {file.name}
                </span>
                <span data-testid={`upload-files-size-${index}`} className="text-xs text-primary/80">
                  {(file.size / (1024 * 1024)).toFixed(1)} MB
                </span>
              </div>
              <button
                data-testid={`upload-files-remove-${index}`}
                type="button"
                onClick={() => removeFile(index)}
                className="absolute top-1.5 right-1.5 text-primary/80 hover:text-destructive"
              >
                <X data-testid={`upload-files-remove-icon-${index}`} className="size-4" />
              </button>
            </div>
          ))}
        </div>
      )}

      {error && (
        <div data-testid="upload-files-error-container" className="w-full flex justify-center items-center">
          <p data-testid="upload-files-error-message" className="text-sm text-destructive -mt-1">{error}</p>
        </div>
      )}
    </div>
  );
};
