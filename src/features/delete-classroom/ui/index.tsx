import { useDeleteClassroom } from '@/entities';
import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { Loader2, Trash2 } from 'lucide-react';
import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface IDeleteClassroomDialogProps {
  id: string;
}

export const DeleteClassroomDialog: FC<IDeleteClassroomDialogProps> = ({
  id,
}) => {
  const { t } = useTranslation();
  const [show, setShow] = useState(false);
  const { mutate: deleteClassroom, isPending } = useDeleteClassroom();

  const onSubmit = () => {
    deleteClassroom(id);
  };

  return (
    <Dialog open={show} onOpenChange={setShow}>
      <DialogTrigger asChild>
        <Button
          data-testid="delete-classroom-button"
          variant="monochrome_outline"
          className="w-full gap-1 px-4 py-2 font-semibold !bg-[#fff2e51a] !border-[#8F7E72] hover:!bg-[#fff2e580] active:!bg-[#FFF2E5]"
          type="button"
        >
          <Trash2 className="max-[370px]:hidden" />
          <span className="max-[350px]:text-sm">
            {t('classroom.dialogs.delete.trigger')}
          </span>
        </Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">
            {t('classroom.dialogs.delete.title')}
          </DialogTitle>
          <DialogDescription>
            {t('classroom.dialogs.delete.description')}
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="mx-auto w-fit grid grid-cols-[auto_auto] gap-2 max-[360px]:flex flex-wrap items-center">
          <DialogClose asChild>
            <Button
              data-testid="delete-classroom-cancel-button"
              variant="destructive_outline"
              size="lg"
              type="button"
              className="w-fit max-[400px]:text-sm"
            >
              {t('classroom.dialogs.delete.buttons.cancel')}
            </Button>
          </DialogClose>

          <Button
            data-testid="delete-classroom-confirm-button"
            variant="destructive"
            size="lg"
            type="button"
            disabled={isPending}
            onClick={onSubmit}
            className="w-fit max-[400px]:text-sm"
          >
            {isPending ? (
              <Loader2 className="animate-spin h-5 w-5" />
            ) : (
              t('classroom.dialogs.delete.buttons.confirm')
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
