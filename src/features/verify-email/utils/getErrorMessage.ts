const ERROR_MESSAGES = {
  invalidOrExpired: {
    title: 'emailVerificationError.errors.invalidOrExpired.title',
    description: 'emailVerificationError.errors.invalidOrExpired.description',
  },
};

export const getErrorMessage = (error: string | null) => {
  switch (error) {
    case 'invalid or expired verification token':
      return ERROR_MESSAGES['invalidOrExpired'];
    default:
      return {
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      };
  }
};
