import { useEffect, useRef } from 'react';
import { useCookies } from 'react-cookie';
import { useLocation, useNavigate, useSearchParams } from 'react-router';
import { useAppDispatch } from '@/shared';
import { setUser, User } from '@/entities';

export const useVerifyEmail = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [, setCookie] = useCookies(['token', 'refreshToken']);
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const hasProcessed = useRef(false);

  const data = JSON.parse(searchParams.get('auth') ?? '{}') as {
    user: User;
    token: string;
    refreshToken: string;
    expiresAt: number;
  };

  useEffect(() => {
    if (!searchParams.get('auth')) {
      navigate('/', { replace: true });
      return;
    }

    if (hasProcessed.current) return;
    hasProcessed.current = true;

    const from = localStorage.getItem('inviteRedirect');

    setCookie('token', data.token, {
      path: '/',
      maxAge: data.expiresAt,
      secure: true,
      sameSite: 'lax',
    });
    setCookie('refreshToken', data.refreshToken, {
      path: '/',
      maxAge: data.expiresAt,
      secure: true,
      sameSite: 'lax',
    });

    dispatch(setUser(data.user));

    navigate(from !== 'undefined' && from ? from : '/?onboarding=true', {
      replace: true,
    });
  }, [data, setCookie, navigate, location.pathname]);
};
