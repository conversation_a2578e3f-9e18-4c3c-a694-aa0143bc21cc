import { useNotifications } from '@/shared';
import { useNavigate, useSearchParams } from 'react-router';
import { getErrorMessage } from '../utils';
import { useEffect } from 'react';

export const useVerifyEmailError = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { addNotification } = useNotifications();

  const message = searchParams.get('error');

  addNotification?.({
    title: getErrorMessage(message).title,
    description: getErrorMessage(message).description,
  });

  useEffect(() => {
    navigate('/', { replace: true });
  }, []);
};
