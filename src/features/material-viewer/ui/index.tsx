import { Material, useDownloadMaterial } from '@/entities';
import { PDFViewer } from '@/features/pdf-viewer';
import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { useNotifications } from '@/shared';
import { useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { Eye } from 'lucide-react';
import { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface IMaterialViewerProps {
  classroomId: string;
  material: Material & { type: 'pdf' | 'mp3' | 'mp4' };
}

const MaterialViewer: FC<
  IMaterialViewerProps & {
    setShow: (show: boolean) => void;
    setEnabled: (enabled: boolean) => void;
    enabled: boolean;
  }
> = ({ classroomId, material, setShow, setEnabled, enabled }) => {
  const { data, error } = useDownloadMaterial(
    classroomId,
    material.id,
    enabled,
  );
  const { addNotification } = useNotifications();
  const queryClient = useQueryClient();

  const handleDownload = () => {
    window.open(data ?? '', '_blank');
  };

  useEffect(() => {
    if (error && axios.isAxiosError(error) && error.response?.status === 404) {
      setEnabled(false);

      queryClient.removeQueries({
        queryKey: ['materials', classroomId, material.id],
      });

      setShow(false);
      addNotification?.({
        title: 'notifications.errors.materialNotFound.title',
        description: 'notifications.errors.materialNotFound.description',
      });

      queryClient.invalidateQueries({
        queryKey: ['materials', classroomId],
      });
    }
  }, [error]);

  if (data)
    return (
      <PDFViewer
        url={data}
        name={material.title}
        type="classroom"
        onClose={() => setShow(false)}
        download={handleDownload}
      />
    );
};

export const MaterialViewerWrapper: FC<IMaterialViewerProps> = ({
  classroomId,
  material,
}) => {
  const { t } = useTranslation();
  const [show, setShow] = useState(false);
  const [queryEnabled, setQueryEnabled] = useState(false);

  useEffect(() => {
    if (show) {
      setQueryEnabled(true);
    } else {
      setQueryEnabled(false);
    }
  }, [show]);

  return (
    <Dialog open={show} onOpenChange={setShow}>
      <DialogTrigger asChild>
        <Button
          data-testid={`material-view-button-${material.id}`}
          size="sm"
          variant="ghost"
          className="hover:bg-primary/10"
        >
          <Eye className="size-4 text-primary" />
          <span className="text-sm text-primary/80 font-bold">
            {t('classroom.materials.buttons.view')}
          </span>
        </Button>
      </DialogTrigger>
      <DialogContent className="min-w-full max-h-none h-[100dvh] rounded-none min-[1050px]:min-w-[1000px] min-[1050px]:h-[90vh] min-[1050px]:max-h-[900px] min-[1050px]:rounded-3xl [&>.dialog-close]:hidden p-0 flex flex-col has-[.fullscreen]:!min-w-screen has-[.fullscreen]:!min-h-screen has-[.fullscreen]:rounded-none">
        <DialogHeader className="hidden">
          <DialogTitle />
          <DialogDescription />
        </DialogHeader>

        <div className="h-full flex flex-col">
          <div className="flex-1 min-h-0 grid p-6 grid-rows-[auto_1fr] gap-2">
            <MaterialViewer
              setShow={setShow}
              classroomId={classroomId}
              material={material}
              setEnabled={setQueryEnabled}
              enabled={queryEnabled}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
