import { useRef } from 'react';
import { cn } from '@/shadcn/lib/utils';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/shadcn/components/ui/avatar';
import { getInitials, useAppSelector, useUpdateReduxState } from '@/shared';
import { useMediaStreamView, useWatcherMedia } from '../hooks';
import { RoomParticipant, setRoomUserVideoOn } from '@/features';

interface IMediaStreamViewProps {
  dataTestId: string;
  participant: RoomParticipant;
  mediaStream: MediaStream;
  selected?: boolean;
}

export const MediaStreamView = ({
  dataTestId,
  participant,
  mediaStream,
  selected = false,
}: IMediaStreamViewProps) => {
  const updateReduxState = useUpdateReduxState();
  const { localUser } = useAppSelector((state) => state.livekit);
  const isLocal = (localUser && localUser!.sid === participant.sid) ?? false;

  const videoRef = useRef<HTMLVideoElement>(null);
  useMediaStreamView(videoRef, mediaStream, participant);
  useWatcherMedia(videoRef, () => {
    updateReduxState(
      setRoomUserVideoOn({ sid: participant.sid, isVideoOn: false }),
    );
  });

  return (
    <div
      data-testid={`${dataTestId}-media-wrapper`}
      className="w-full h-full flex justify-center items-center overflow-hidden"
    >
      {!participant.isVideoOn && (
        <Avatar
          data-testid={`${dataTestId}-avatar`}
          className="w-30 h-auto aspect-square cursor-default max-[1600px]:w-20 max-[1000px]:landscape:w-18 max-[600px]:w-18"
        >
          <AvatarImage
            className="object-cover"
            src={participant.profile?.profileImage ?? undefined}
          />
          <AvatarFallback
            data-testid={`${dataTestId}-avatar-fallback`}
            className="text-4xl font-semibold max-[1600px]:text-2xl"
          >
            {getInitials(participant.profile?.displayName ?? 'Undefined')}
          </AvatarFallback>
        </Avatar>
      )}
      <video
        data-testid={`${dataTestId}-video`}
        autoPlay
        playsInline
        muted
        ref={videoRef}
        controls={false}
        disablePictureInPicture
        className={cn(
          'w-full h-full',
          isLocal && 'transform -scale-x-100',
          selected &&
            'portrait:h-[50%] portrait:object-cover lanscape:object-fit',
          !selected && 'object-cover',
          !participant.isVideoOn && 'hidden',
        )}
      />
    </div>
  );
};
