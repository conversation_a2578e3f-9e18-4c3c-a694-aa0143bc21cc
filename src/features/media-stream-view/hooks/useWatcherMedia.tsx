import { RefObject, useEffect } from 'react';
import { safePlay } from '@/shared';

export const useWatcherMedia = (
  element:
    | RefObject<HTMLAudioElement | null>
    | RefObject<HTMLVideoElement | null>,
  onAttemptUnlockFail?: () => void,
) => {
  useEffect(() => {
    if (!element.current) return;

    const attemptUnlock = () => {
      requestAnimationFrame(async () => {
        await safePlay(element.current!, onAttemptUnlockFail);
      });
    };

    window.addEventListener('click', attemptUnlock);
    window.addEventListener('touchstart', attemptUnlock);
    window.addEventListener('keydown', attemptUnlock);

    return () => {
      window.removeEventListener('click', attemptUnlock);
      window.removeEventListener('touchstart', attemptUnlock);
      window.removeEventListener('keydown', attemptUnlock);
    };
  }, []);
};
