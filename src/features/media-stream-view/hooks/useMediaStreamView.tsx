import { RefObject, useEffect, useLayoutEffect } from 'react';
import { safePlay, useUpdateReduxState } from '@/shared';
import { RoomParticipant, setRoomUserVideoOn } from '@/features';

export const useMediaStreamView = (
  videoRef: RefObject<HTMLVideoElement | null>,
  mediaStream: MediaStream | null,
  participant: RoomParticipant,
) => {
  const updateReduxState = useUpdateReduxState();

  useLayoutEffect(() => {
    const video = videoRef.current;

    if (!video || !mediaStream) return;
    if (video.srcObject !== null) return;

    video.srcObject = mediaStream;
    video.setAttribute('playsinline', 'true');
    video.setAttribute('webkit-playsinline', 'true');

    video.setAttribute('muted', 'true');
    video.defaultMuted = true;
    video.volume = 0;

    video.load();

    return () => {
      video.pause();
      video.srcObject = null;
    };
  }, [mediaStream, participant.isVideoOn]);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const attemptPlay = async () => {
      try {
        // Wait for video to be ready
        if (video.readyState < HTMLMediaElement.HAVE_CURRENT_DATA) {
          await new Promise((resolve) => {
            const handleCanPlay = () => {
              video.removeEventListener('canplay', handleCanPlay);
              resolve(undefined);
            };
            video.addEventListener('canplay', handleCanPlay);
          });
        }

        const playPromise = safePlay(video, () => {
          updateReduxState(
            setRoomUserVideoOn({ sid: participant.sid, isVideoOn: false }),
          );
        });

        if (playPromise !== undefined) {
          await playPromise;
        }
      } catch (error) {
        console.warn('Video play failed:', error);
        updateReduxState(
          setRoomUserVideoOn({ sid: participant.sid, isVideoOn: false }),
        );
      }
    };

    // Use setTimeout instead of requestAnimationFrame for better timing
    const timeoutId = setTimeout(attemptPlay, 100);

    return () => clearTimeout(timeoutId);
  }, [mediaStream, participant.isVideoOn]);

  useEffect(() => {
    if (!mediaStream) return;
    const [videoTrack] = mediaStream.getVideoTracks();
    if (!videoTrack) return;
    let timerId: number | null = null;

    const scheduleVideoUpdate = (newState: boolean) => {
      if (timerId !== null) {
        clearTimeout(timerId);
      }

      timerId = window.setTimeout(() => {
        if (videoTrack.enabled !== newState)
          updateReduxState(
            setRoomUserVideoOn({ sid: participant.sid, isVideoOn: newState }),
          );
        timerId = null;
      }, 100);
    };

    const onMute = () => {
      if (!videoTrack.enabled) {
        scheduleVideoUpdate(false);
      }
    };
    const onUnmute = () => {
      if (videoTrack.enabled) {
        scheduleVideoUpdate(true);
      }
    };

    videoTrack.addEventListener('mute', onMute);
    videoTrack.addEventListener('unmute', onUnmute);

    return () => {
      if (timerId !== null) {
        clearTimeout(timerId);
      }
      videoTrack.removeEventListener('mute', onMute);
      videoTrack.removeEventListener('unmute', onUnmute);
    };
  }, [mediaStream, participant.sid]);
};
