import { useDeleteUser, useUser } from '@/entities';
import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { Input } from '@/shadcn/components/ui/input';
import { LoaderCircle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router';

type FormValues = {
  confirmation: string;
};

export const DeleteAccountButton = () => {
  const { t } = useTranslation();
  const { user } = useUser();
  const { mutate: deleteUser, isPending } = useDeleteUser();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({ mode: 'onChange' });

  const onSubmit = (data: FormValues) => {
    deleteUser(data.confirmation);
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button 
          data-testid="delete-account-trigger-button"
          variant="destructive" 
          size="lg"
        >
          {t('profile.actions.delete.button')}
        </Button>
      </DialogTrigger>

      <DialogContent data-testid="delete-account-dialog">
        <DialogHeader>
          <DialogTitle data-testid="delete-account-dialog-title">
            {t('profile.dialogs.deleteAccount.title')}
          </DialogTitle>
          <DialogDescription data-testid="delete-account-dialog-description">
            {t('profile.dialogs.deleteAccount.description.text1')}
            <Link
              data-testid="delete-account-terms-link"
              to="/legal/terms-and-conditions"
              target="_blank"
              className="underline text-accent"
            >
              {t('profile.dialogs.deleteAccount.description.text2')}
            </Link>
            {t('profile.dialogs.deleteAccount.description.text3')}
          </DialogDescription>
        </DialogHeader>

        <form
          data-testid="delete-account-form"
          onSubmit={handleSubmit(onSubmit)}
          action=""
          className="flex flex-col gap-4"
        >
          <div className="flex flex-col gap-4">
            {user?.hasPassword ? (
              <>
                <p 
                  data-testid="delete-account-password-instruction"
                  className="text-sm text-card-foreground/80"
                >
                  {t('profile.dialogs.deleteAccount.confirmationPassword')}
                </p>

                <Input
                  data-testid="delete-account-password-input"
                  {...register('confirmation', { required: true })}
                  type="password"
                  placeholder={t(
                    'profile.dialogs.deleteAccount.fields.password',
                  )}
                  className="border-primary/20"
                />
              </>
            ) : (
              <>
                <p 
                  data-testid="delete-account-displayname-instruction"
                  className="text-sm text-card-foreground/80"
                >
                  {t('profile.dialogs.deleteAccount.confirmationDisplayName', {
                    displayName: user?.displayName,
                  })}
                </p>

                <Input
                  data-testid="delete-account-displayname-input"
                  {...register('confirmation', { required: true })}
                  type="text"
                  placeholder={t(
                    'profile.dialogs.deleteAccount.fields.displayName',
                  )}
                  className="border-primary/20"
                />
              </>
            )}
          </div>

          <DialogFooter 
            data-testid="delete-account-dialog-footer"
            className="grid grid-cols-2 max-[550px]:grid-cols-1"
          >
            <DialogClose asChild>
              <Button
                data-testid="delete-account-cancel-button"
                size="lg"
                variant="destructive_outline"
                className="w-full"
                type="button"
              >
                {t('profile.dialogs.deleteAccount.buttons.cancel')}
              </Button>
            </DialogClose>
            <Button
              data-testid="delete-account-confirm-button"
              size="lg"
              variant="destructive"
              className="w-full"
              disabled={isPending || !!errors.confirmation}
            >
              {isPending ? (
                <>
                  <LoaderCircle />
                  {t('profile.dialogs.deleteAccount.buttons.deleting')}
                </>
              ) : (
                t('profile.dialogs.deleteAccount.buttons.confirm')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
