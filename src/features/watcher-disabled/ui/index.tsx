import { Tooltip } from '@radix-ui/react-tooltip';
import {
  Toolt<PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import {
  FC,
  PropsWithChildren,
  ReactElement,
  cloneElement,
  Children,
  isValidElement,
} from 'react';
import { useTranslation } from 'react-i18next';
import { useUser } from '@/entities';

interface ElementWithChildren {
  children?: React.ReactNode;
  disabled?: boolean;
}

interface WatcherDisabledProps extends PropsWithChildren {
  'data-testid'?: string;
}

const DISABLEABLE_ELEMENTS = [
  'button',
  'input',
  'select',
  'textarea',
  'fieldset',
  'optgroup',
  'option',
];

const disableChildren = (children: React.ReactNode): React.ReactNode => {
  return Children.map(children, (child) => {
    if (!isValidElement(child)) {
      return child;
    }

    const element = child as ReactElement<ElementWithChildren>;

    const processedChildren = element.props.children
      ? disableChildren(element.props.children)
      : element.props.children;

    // Check if it's a native HTML element
    const isNativeElement = typeof element.type === 'string';
    const shouldDisableNative =
      isNativeElement &&
      DISABLEABLE_ELEMENTS.includes(
        typeof element.type === 'string' ? element.type.toLowerCase() : '',
      );

    // For React components, always try to add disabled prop
    const shouldDisableComponent = !isNativeElement;

    return cloneElement(element, {
      ...element.props,
      ...(shouldDisableNative && { disabled: true }),
      ...(shouldDisableComponent && { disabled: true }),
      children: processedChildren,
    } as ElementWithChildren);
  });
};

export const WatcherDisabled: FC<WatcherDisabledProps> = ({
  children,
  'data-testid': dataTestId,
}) => {
  const { roleType } = useUser();
  const { t } = useTranslation();

  const processedChildren =
    roleType === 'watcher' ? disableChildren(children) : children;

  return roleType === 'watcher' ? (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className="cursor-not-allowed inline-block"
            data-testid={
              dataTestId ? `watcher-disabled-${dataTestId}` : 'watcher-disabled'
            }
            style={{ pointerEvents: 'auto' }}
          >
            <div style={{ pointerEvents: 'none' }}>{processedChildren}</div>
          </div>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          <p className="text-center">{t('globals.watcherDisabled')}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  ) : (
    children
  );
};
