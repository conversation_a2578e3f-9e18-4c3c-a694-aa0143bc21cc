import {
  createEntityAdapter,
  createSlice,
  PayloadAction,
} from '@reduxjs/toolkit';
import { ChatMessage } from '../types';

const messagesAdapter = createEntityAdapter<ChatMessage, string>({
  selectId: (msg) => msg.author.sid + msg.sendDate.toString(),
  sortComparer: (a, b) => a.sendDate - b.sendDate,
});

const chatSlice = createSlice({
  name: 'conference/chat',
  initialState: messagesAdapter.getInitialState(),
  reducers: {
    upsertMessage: messagesAdapter.upsertOne,
    addManyMessages: messagesAdapter.addMany,
    updateMessage: messagesAdapter.updateOne,
    removeMessageById: messagesAdapter.removeOne,
    resetMessages: messagesAdapter.removeAll,

    markAllRead: {
      reducer: (state, action: PayloadAction<number>) => {
        const now = action.payload;
        const updates = state.ids.map((id) => ({
          id: id as string,
          changes: { readDate: now },
        }));
        messagesAdapter.updateMany(state, updates);
      },
      prepare: () => {
        return { payload: Date.now() };
      },
    },
  },
});

export const {
  upsertMessage,
  addManyMessages,
  updateMessage,
  removeMessageById,
  resetMessages,
  markAllRead,
} = chatSlice.actions;

export const {
  selectAll: selectAllMessages,
  selectById: selectMessageById,
  selectIds: selectMessageIds,
} = messagesAdapter.getSelectors<{
  chat: ReturnType<typeof chatSlice.reducer>;
}>((state) => state.chat);

export default chatSlice.reducer;
