import { SendHorizonal, Sparkles } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { cn } from '@/shadcn/lib/utils';
import { Message } from './message';
import { selectAllMessages, upsertMessage } from '../model';
import { useAppSelector, useUpdateReduxState } from '@/shared';
import { useLayoutEffect, useRef } from 'react';
import { sendData, useLivekitContext } from '../../livekit';
import { WatcherDisabled } from '../../watcher-disabled';
import { DisabledTooltip } from '../../disabled-tooltip';
import { ChatMessage } from '../types';

interface SearchFormValues {
  message: string;
}

export const Chat = () => {
  const { room } = useLivekitContext();
  const updateReduxState = useUpdateReduxState();
  const { t } = useTranslation();
  const { localUser } = useAppSelector((state) => state.livekit);

  const messages = useAppSelector(selectAllMessages);
  const containerRef = useRef<HTMLDivElement | null>(null);

  const { register, handleSubmit, reset } = useForm<SearchFormValues>({
    defaultValues: { message: '' },
  });

  const onSubmit = (data: SearchFormValues) => {
    const text = data.message.trim();
    if (!text || !localUser) return;

    const message: ChatMessage = {
      author: localUser,
      message: text,
      sendDate: Date.now(),
      readDate: null,
    };

    updateReduxState(upsertMessage({ ...message, readDate: Date.now() }));
    sendData(room, { type: 'chat', message: message });

    reset();
  };

  useLayoutEffect(() => {
    const el = containerRef.current;
    if (el) {
      el.scrollTo({ top: el.scrollHeight, behavior: 'smooth' });
    }
  }, [messages]);

  return (
    <div
      className="h-full grid grid-rows-[1fr_auto] gap-4 overflow-hidden"
      data-testid="chat-container"
    >
      <div
        className={cn(
          'h-full w-full border-y-1 border-white/10 px-2 py-4 flex flex-col items-center gap-4 overflow-y-auto scrollbar',
          messages.length === 0 ? 'justify-center' : 'justify-start',
        )}
        ref={containerRef}
        data-testid="chat-messages-container"
      >
        {messages.length === 0 && (
          <div
            className="flex flex-col justify-center items-center text-center gap-1.5 max-w-xs"
            data-testid="chat-empty-state"
          >
            <h5
              className="text-lg text-white/70"
              data-testid="chat-empty-title"
            >
              {t('class.tools.chat.noMessage.title')}
            </h5>
            <p
              className="text-sm text-white/50"
              data-testid="chat-empty-description"
            >
              {t('class.tools.chat.noMessage.description')}
            </p>
          </div>
        )}
        {messages.map((message, index) => {
          return <Message key={index} message={message} />;
        })}
      </div>
      <WatcherDisabled data-testid="chat-input">
        <div
          className="h-fit w-full grid grid-cols-[1fr_50px] gap-3"
          data-testid="chat-input-area"
        >
          <form
            className="w-full"
            onSubmit={handleSubmit(onSubmit)}
            data-testid="chat-message-form"
          >
            <div className="relative">
              <input
                type="text"
                placeholder={t('class.tools.chat.message.placeholder')}
                {...register('message')}
                data-testid="chat-message-input"
                className="w-full pl-5 pr-11 py-3 bg-transparent text-base text-white placeholder-white/50 rounded-full border border-white/20 hover:border-white/50 focus:border-white/50 transition-colors duration-300 outline-none"
                autoComplete="off"
              />
              <button
                type="submit"
                data-testid="chat-send-button"
                className="absolute right-4 top-1/2 transform -translate-y-1/2"
              >
                <SendHorizonal className="w-5 h-auto aspect-square text-white/70" />
              </button>
            </div>
          </form>

          <DisabledTooltip tooltipText="class.tools.chat.message.tooltip">
            <button
              className="w-12 h-auto aspect-square bg-accent rounded-full flex justify-center items-center disabled:opacity-50 disabled:pointer-events-none"
              disabled
              data-testid="chat-ai-button"
            >
              <Sparkles className="w-7 h-auto aspect-square text-white/70" />
            </button>
          </DisabledTooltip>
        </div>
      </WatcherDisabled>
    </div>
  );
};
