import { cn } from '@/shadcn/lib/utils';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/shadcn/components/ui/avatar';
import { getInitials, useAppSelector } from '@/shared';
import moment from 'moment';
import i18n from '@/app/i18n';
import { useTranslation } from 'react-i18next';
import { ChatMessage } from '../types';

interface IMessageProps {
  message: ChatMessage;
}

export const Message = ({ message }: IMessageProps) => {
  const { t } = useTranslation();
  const { localUser } = useAppSelector((state) => state.livekit);
  const isLocalMessage =
    localUser &&
    (localUser.sid === message.author.sid ||
      localUser.identity === message.author.identity);

  return (
    <div
      id="message"
      data-testid={`chat-message-${message.sendDate}`}
      className={cn(
        'h-fit w-full grid items-start gap-2.5',
        isLocalMessage
          ? 'grid-cols-[1fr_32px] justify-end'
          : 'grid-cols-[32px_1fr] justify-start',
      )}
    >
      <Avatar
        className={cn(
          'w-8 h-auto aspect-square cursor-default',
          isLocalMessage && 'order-2',
        )}
        data-testid={`chat-message-avatar-${message.sendDate}`}
      >
        <AvatarImage
          className="object-cover"
          src={message.author.profile?.profileImage ?? undefined}
        />
        <AvatarFallback className="text-base font-semibold max-[1600px]:text-sm">
          {getInitials(message.author.profile?.displayName ?? 'Undefined')}
        </AvatarFallback>
      </Avatar>
      <div
        className={cn(
          'h-fit w-full flex flex-col justify-center gap-2',
          isLocalMessage ? 'items-end order-1' : 'items-start',
        )}
        data-testid={`chat-message-content-${message.sendDate}`}
      >
        <p
          className="ml-1 mr-1 text-xs text-white/70"
          data-testid={`chat-message-author-${message.sendDate}`}
        >
          {isLocalMessage
            ? t('class.tools.chat.you', {
                name: message.author.profile?.displayName ?? '',
              })
            : message.author.profile?.displayName}
        </p>
        <div
          className={cn(
            'max-w-[80%] w-fit whitespace-pre-wrap break-all bg-white/10 py-2 px-4 rounded-b-lg flex flex-col items-start gap-1.5',
            isLocalMessage ? 'rounded-tl-lg' : 'rounded-tr-lg',
          )}
          data-testid={`chat-message-bubble-${message.sendDate}`}
        >
          <p
            className="text-sm text-white"
            data-testid={`chat-message-text-${message.sendDate}`}
          >
            {message.message}
          </p>
          <p
            className={cn(
              'w-full text-[10px] text-white/50',
              isLocalMessage ? 'text-end' : 'text-start',
            )}
            data-testid={`chat-message-timestamp-${message.sendDate}`}
          >
            {moment(message.sendDate)
              .locale(i18n.language)
              .format('MMMM D[,] HH:mm')}
          </p>
        </div>
      </div>
    </div>
  );
};
