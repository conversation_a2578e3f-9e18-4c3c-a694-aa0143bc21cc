import { useEffect, useRef } from 'react';
import { useAppSelector } from '@/shared';
import { AudioContextProvider } from '../../audio-context';

export const usePlayNotificationSound = () => {
  const audioContextRef = useRef<AudioContextProvider | null>(null);
  const { audioOutputDevice } = useAppSelector((state) => state.localMedia);

  const playSuccessNotification = (): void => {
    const config = {
      volume: 0.3,
      firstToneDuration: 0.15,
      secondToneDuration: 0.25,
      delay: 100,
    };

    const enabled = localStorage.getItem('enableChatNotificationSound');
    const audioContext = audioContextRef.current?.getAudioContext();

    if (
      enabled === 'false' ||
      !audioContext ||
      !audioContextRef.current?.isValid()
    ) {
      return;
    }

    if (audioContext.state === 'suspended') {
      audioContext.resume();
    }

    const oscillator1 = audioContext.createOscillator();
    const gainNode1 = audioContext.createGain();

    oscillator1.connect(gainNode1);
    gainNode1.connect(audioContext.destination);

    oscillator1.frequency.setValueAtTime(523, audioContext.currentTime);
    oscillator1.type = 'sine';

    gainNode1.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode1.gain.linearRampToValueAtTime(
      config.volume,
      audioContext.currentTime + 0.02,
    );
    gainNode1.gain.exponentialRampToValueAtTime(
      0.001,
      audioContext.currentTime + config.firstToneDuration,
    );

    oscillator1.start(audioContext.currentTime);
    oscillator1.stop(audioContext.currentTime + config.firstToneDuration);

    setTimeout(() => {
      const oscillator2 = audioContext.createOscillator();
      const gainNode2 = audioContext.createGain();

      oscillator2.connect(gainNode2);
      gainNode2.connect(audioContext.destination);

      oscillator2.frequency.setValueAtTime(784, audioContext.currentTime);
      oscillator2.type = 'sine';

      gainNode2.gain.setValueAtTime(0, audioContext.currentTime);
      gainNode2.gain.linearRampToValueAtTime(
        config.volume * 0.8,
        audioContext.currentTime + 0.02,
      );
      gainNode2.gain.exponentialRampToValueAtTime(
        0.001,
        audioContext.currentTime + config.secondToneDuration,
      );

      oscillator2.start(audioContext.currentTime);
      oscillator2.stop(audioContext.currentTime + config.secondToneDuration);
    }, config.delay);
  };

  useEffect(() => {
    (async () => {
      audioContextRef.current = await AudioContextProvider.create();
    })();
  }, []);

  useEffect(() => {
    if (audioContextRef.current && audioOutputDevice) {
      audioContextRef.current.setSinkId(audioOutputDevice);
    }
  }, [audioOutputDevice]);

  return playSuccessNotification;
};
