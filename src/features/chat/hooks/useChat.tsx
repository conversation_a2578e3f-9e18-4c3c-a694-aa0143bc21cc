import { markAllRead, selectAllMessages } from '../model';
import { useAppSelector, useUpdateReduxState } from '@/shared';
import { usePlayNotificationSound } from './usePlayNotificationSound';
import { useEffect } from 'react';

export const useChat = () => {
  const updateReduxState = useUpdateReduxState();

  const onSidebarOpen = (_: boolean) => {
    if (unreadMessages.length > 0) {
      updateReduxState(markAllRead());
    }
  };

  const messages = useAppSelector(selectAllMessages);
  const unreadMessages = messages.filter(
    (message) => message.readDate === null,
  );

  const playChatSound = usePlayNotificationSound();

  useEffect(() => {
    if (unreadMessages.length !== 0) {
      playChatSound();
    }
  }, [messages]);

  return { onSidebarOpen, unreadMessages } as const;
};
