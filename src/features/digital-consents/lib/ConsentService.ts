import { logger } from '../../../shared';
import { CONSENT_CONFIG, DEFAULT_CONSENT_CONFIG } from '../config';
import {
  ConsentConfig,
  ConsentResult,
  GeolocationApiResponse,
  IpApiResponse,
  IpGeolocationResponse,
  IpInfoResponse,
  UserLocation,
} from '../types';

class ConsentService {
  private static instance: ConsentService;

  private userLocation: UserLocation | null = null;
  private locationDetectionPromise: Promise<UserLocation> | null = null;

  private readonly CACHE_KEY_LOCATION = 'consent_user_location';
  private readonly CACHE_DURATION = 1000 * 60 * 60; // 1 hour in milliseconds

  public static getInstance(): ConsentService {
    if (!ConsentService.instance) {
      ConsentService.instance = new ConsentService();
    }
    return ConsentService.instance;
  }

  /**
   * Determine if cookie consent should be shown and
   * digital age of consent based on user location
   */
  public async getPersonalConsentConfig(
    userAge?: number,
  ): Promise<ConsentResult> {
    try {
      const location = await this.detectUserLocation();
      const config = this.getConsentConfig(location.countryCode);

      const isMinor =
        userAge !== undefined ? userAge < config.digitalAge : undefined;

      return {
        showCookieConsent: config.requiresCookieConsent,
        digitalAge: config.digitalAge,
        consentModel: config.consentModel,
        userAge,
        isMinor,
      };
    } catch (error) {
      logger.error('Error determining consent requirements', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'digital_consent_determining_error',
      });

      // Fallback to showing consent (safer approach)
      return {
        showCookieConsent: true,
        digitalAge: DEFAULT_CONSENT_CONFIG.digitalAge,
        consentModel: DEFAULT_CONSENT_CONFIG.consentModel,
        userAge,
        isMinor:
          userAge !== undefined
            ? userAge < DEFAULT_CONSENT_CONFIG.digitalAge
            : undefined,
      };
    }
  }

  /**
   * Detect user location using IP geolocation with caching
   */
  private async detectUserLocation(): Promise<UserLocation> {
    // First check in-memory cache
    if (this.userLocation) {
      return this.userLocation;
    }

    // Then check sessionStorage cache
    const cachedLocation = this.getSessionCache<UserLocation>(
      this.CACHE_KEY_LOCATION,
    );
    if (cachedLocation) {
      this.userLocation = cachedLocation;
      return this.userLocation;
    }

    // If already in progress, return the existing promise
    if (this.locationDetectionPromise) {
      return this.locationDetectionPromise;
    }

    this.locationDetectionPromise = this.performLocationDetection();

    try {
      this.userLocation = await this.locationDetectionPromise;

      // Cache the successful result
      this.setSessionCache(this.CACHE_KEY_LOCATION, this.userLocation);

      return this.userLocation;
    } catch (error) {
      logger.warn('Failed to detect user location', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'digital_consent_location_detection_error',
      });

      // Fallback to a safe default (EU/GDPR compliant)
      this.userLocation = {
        country: 'Unknown',
        countryCode: 'EU', // Default to EU for maximum privacy compliance
        region: 'Unknown',
        city: 'Unknown',
      };

      // Cache the fallback result as well
      this.setSessionCache(this.CACHE_KEY_LOCATION, this.userLocation);

      return this.userLocation;
    } finally {
      // Clear the promise so future calls can retry if needed
      this.locationDetectionPromise = null;
    }
  }

  private async performLocationDetection(): Promise<UserLocation> {
    // Try multiple IP geolocation services for reliability
    const services = [
      'https://ipapi.co/json/',
      'https://api.ipgeolocation.io/ipgeo?apiKey=free',
      'https://ipinfo.io/json',
    ];

    for (const serviceUrl of services) {
      try {
        const response = await fetch(serviceUrl);
        if (!response.ok) continue;

        const data = await response.json();

        // Handle different API response formats
        const location = this.normalizeLocationData(data, serviceUrl);
        if (location.countryCode) {
          return location;
        }
      } catch (error) {
        logger.warn(`Failed to fetch from ${serviceUrl}`, {
          service_url: serviceUrl,
          error: error instanceof Error ? error.message : String(error),
          event_type: 'digital_consent_location_fetch_error',
        });
        continue;
      }
    }

    throw new Error('All geolocation services failed');
  }

  private normalizeLocationData(
    data: GeolocationApiResponse,
    serviceUrl: string,
  ): UserLocation {
    let countryCode = '';
    let country = '';
    let region = '';
    let city = '';

    if (serviceUrl.includes('ipapi.co')) {
      const ipApiData = data as IpApiResponse;
      countryCode = ipApiData.country_code || ipApiData.country || '';
      country = ipApiData.country_name || ipApiData.country || '';
      region = ipApiData.region || '';
      city = ipApiData.city || '';
    } else if (serviceUrl.includes('ipgeolocation.io')) {
      const ipGeoData = data as IpGeolocationResponse;
      countryCode = ipGeoData.country_code2 || '';
      country = ipGeoData.country_name || '';
      region = ipGeoData.state_prov || '';
      city = ipGeoData.city || '';
    } else if (serviceUrl.includes('ipinfo.io')) {
      const ipInfoData = data as IpInfoResponse;
      countryCode = ipInfoData.country || '';
      country = ipInfoData.country || '';
      region = ipInfoData.region || '';
      city = ipInfoData.city || '';
    }

    return {
      country,
      countryCode: countryCode.toUpperCase(),
      region,
      city,
    };
  }

  /**
   * Get consent configuration for a specific country
   */
  private getConsentConfig(countryCode: string): ConsentConfig {
    return CONSENT_CONFIG[countryCode.toUpperCase()] || DEFAULT_CONSENT_CONFIG;
  }

  /**
   * Set data in sessionStorage with error handling
   */
  private setSessionCache(key: string, data: UserLocation): void {
    try {
      const cacheData = {
        data,
        timestamp: Date.now(),
        expires: Date.now() + this.CACHE_DURATION,
      };
      sessionStorage.setItem(key, JSON.stringify(cacheData));
    } catch (error) {
      logger.warn('Failed to set session cache', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'digital_consent_session_cache_set_error',
      });
    }
  }

  /**
   * Get data from sessionStorage with expiration check
   */
  private getSessionCache<T>(key: string): T | null {
    try {
      const cached = sessionStorage.getItem(key);
      if (!cached) return null;

      const cacheData = JSON.parse(cached);

      // Check if cache has expired
      if (Date.now() > cacheData.expires) {
        sessionStorage.removeItem(key);
        return null;
      }

      return cacheData.data;
    } catch (error) {
      logger.warn('Failed to get session cache', {
        error: error instanceof Error ? error.message : String(error),
        event_type: 'digital_consent_session_cache_get_error',
      });

      // Clean up corrupted cache
      try {
        sessionStorage.removeItem(key);
      } catch (cleanupError) {
        logger.warn('Failed to clean up corrupted cache', {
          error:
            cleanupError instanceof Error
              ? cleanupError.message
              : String(cleanupError),
          event_type: 'digital_consent_session_cache_clean_error',
        });
      }
      return null;
    }
  }
}

export const consentService = ConsentService.getInstance();
