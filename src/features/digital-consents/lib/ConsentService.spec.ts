/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// ---- <PERSON><PERSON> BEFORE importing the SUT ----
vi.mock('../config', () => ({
  CONSENT_CONFIG: {
    US: {
      requiresCookieConsent: false,
      digitalAge: 13,
      consentModel: 'opt-out',
    },
    // Intentionally omit EU here to ensure DEFAULT is used on EU fallback
  },
  DEFAULT_CONSENT_CONFIG: {
    requiresCookieConsent: true,
    digitalAge: 18,
    consentModel: 'opt-in',
  },
}));

vi.mock('../../../shared', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}));

// Import after mocks
import { logger } from '@/shared';
import { CONSENT_CONFIG, DEFAULT_CONSENT_CONFIG } from '../config';
import { consentService } from './ConsentService';

describe('ConsentService', () => {
  const store = new Map<string, string>();
  let fetchMock: ReturnType<typeof vi.fn>;

  const resetSingleton = () => {
    // Clear internal singleton state for isolation between tests
    (consentService as any).userLocation = null;
    (consentService as any).locationDetectionPromise = null;
  };

  beforeEach(() => {
    // Mock sessionStorage
    store.clear();
    vi.stubGlobal('sessionStorage', {
      getItem: (k: string) => store.get(k) ?? null,
      setItem: (k: string, v: string) => {
        store.set(k, v);
      },
      removeItem: (k: string) => {
        store.delete(k);
      },
      clear: () => store.clear(),
      key: (_: number) => null,
      length: 0,
    } as unknown as Storage);

    // Mock fetch
    fetchMock = vi.fn();
    vi.stubGlobal('fetch', fetchMock as unknown as typeof fetch);

    // Reset logger spies
    vi.clearAllMocks();
    resetSingleton();
  });

  afterEach(() => {
    vi.restoreAllMocks();
    resetSingleton();
  });

  it('returns consent config based on detected country (ipapi → US) and computes isMinor', async () => {
    // First geolocation service responds like ipapi.co
    fetchMock.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        country_code: 'US',
        country_name: 'United States',
        region: 'CA',
        city: 'SF',
      }),
    });

    const res = await (consentService as any).getPersonalConsentConfig(12); // userAge=12
    expect(res).toEqual({
      showCookieConsent: CONSENT_CONFIG.US.requiresCookieConsent,
      digitalAge: CONSENT_CONFIG.US.digitalAge,
      consentModel: CONSENT_CONFIG.US.consentModel,
      userAge: 12,
      isMinor: true, // 12 < 13
    });

    // Session cache should be written
    const cached = store.get('consent_user_location');
    expect(cached).toBeTruthy();
  });

  it('uses sessionStorage cache on subsequent calls and avoids fetch', async () => {
    // Prime the cache to simulate a previous successful detection
    const cached = {
      data: {
        country: 'United States',
        countryCode: 'US',
        region: 'CA',
        city: 'SF',
      },
      timestamp: Date.now(),
      expires: Date.now() + 60_000,
    };
    store.set('consent_user_location', JSON.stringify(cached));

    // Ensure there is no in-memory location yet
    resetSingleton();

    // No fetch responses provided; if code calls fetch it would crash this test
    const res = await (consentService as any).getPersonalConsentConfig(20);

    expect(fetchMock).not.toHaveBeenCalled();
    expect(res.digitalAge).toBe(CONSENT_CONFIG.US.digitalAge);
    expect(res.showCookieConsent).toBe(CONSENT_CONFIG.US.requiresCookieConsent);
    expect(res.isMinor).toBe(false);
  });

  it('falls back to EU-like default and logs a warning if all services fail', async () => {
    fetchMock
      .mockRejectedValueOnce(new Error('ipapi down'))
      .mockRejectedValueOnce(new Error('ipgeolocation down'))
      .mockRejectedValueOnce(new Error('ipinfo down'));

    const res = await (consentService as any).getPersonalConsentConfig(18);

    // detectUserLocation() warns and then uses EU fallback → DEFAULT config (EU not configured)
    expect(logger.warn).toHaveBeenCalledWith(
      'Failed to detect user location',
      expect.objectContaining({
        event_type: 'digital_consent_location_detection_error',
      }),
    );

    expect(res).toEqual({
      showCookieConsent: DEFAULT_CONSENT_CONFIG.requiresCookieConsent,
      digitalAge: DEFAULT_CONSENT_CONFIG.digitalAge,
      consentModel: DEFAULT_CONSENT_CONFIG.consentModel,
      userAge: 18,
      isMinor: false,
    });

    // Fallback should also be cached
    const cached = JSON.parse(store.get('consent_user_location')!);
    expect(cached.data.countryCode).toBe('EU');
  });

  it('normalizes different provider payloads (ipinfo lowercased country → uppercased)', async () => {
    // Make ipapi fail so code tries next, then ipgeolocation fail, then ipinfo ok
    fetchMock
      .mockResolvedValueOnce({ ok: false }) // ipapi
      .mockResolvedValueOnce({ ok: false }) // ipgeolocation
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          country: 'de', // ipinfo uses 'country' code
          region: 'BE',
          city: 'Berlin',
        }),
      });

    const res = await (consentService as any).getPersonalConsentConfig(10);

    // DE not in CONSENT_CONFIG → DEFAULTs
    expect(res.digitalAge).toBe(DEFAULT_CONSENT_CONFIG.digitalAge);
    expect(res.consentModel).toBe(DEFAULT_CONSENT_CONFIG.consentModel);
    expect(res.isMinor).toBe(true); // 10 < 16

    const cached = JSON.parse(store.get('consent_user_location')!);
    expect(cached.data.countryCode).toBe('DE'); // uppercased
  });

  it('ignores expired session cache and refetches', async () => {
    const expired = {
      data: {
        country: 'United States',
        countryCode: 'US',
        region: 'CA',
        city: 'SF',
      },
      timestamp: Date.now() - 2 * 60 * 60 * 1000,
      expires: Date.now() - 60 * 1000,
    };
    store.set('consent_user_location', JSON.stringify(expired));

    // First service (ipapi) returns its own shape
    fetchMock.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        country_code: 'US', // ipapi shape
        country_name: 'United States',
        region: 'CA',
        city: 'SF',
      }),
    });

    const res = await (consentService as any).getPersonalConsentConfig();
    expect(fetchMock).toHaveBeenCalled();
    expect(res.digitalAge).toBe(CONSENT_CONFIG.US.digitalAge); // 13
  });

  it('handles corrupted session cache by logging warn and clearing it', async () => {
    // Corrupted JSON in sessionStorage
    store.set('consent_user_location', '{bad json');

    fetchMock.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        country_code: 'US',
        country_name: 'United States',
        region: 'CA',
        city: 'SF',
      }),
    });

    const res = await (consentService as any).getPersonalConsentConfig();

    expect(logger.warn).toHaveBeenCalledWith(
      'Failed to get session cache',
      expect.objectContaining({
        event_type: 'digital_consent_session_cache_get_error',
      }),
    );
    // After successful detection it should set a proper cache again
    const cached = store.get('consent_user_location');
    expect(cached).toBeTruthy();
    expect(res.digitalAge).toBe(CONSENT_CONFIG.US.digitalAge);
  });

  it('logs error and returns DEFAULT when getPersonalConsentConfig throws (safety fallback)', async () => {
    // Force detectUserLocation to throw by temporarily clobbering fetch and the internal method
    const original = (consentService as any).detectUserLocation;
    (consentService as any).detectUserLocation = vi
      .fn()
      .mockRejectedValue(new Error('boom'));

    const res = await (consentService as any).getPersonalConsentConfig(15);

    expect(logger.error).toHaveBeenCalledWith(
      'Error determining consent requirements',
      expect.objectContaining({
        error: 'boom',
        event_type: 'digital_consent_determining_error',
      }),
    );

    expect(res).toEqual({
      showCookieConsent: DEFAULT_CONSENT_CONFIG.requiresCookieConsent,
      digitalAge: DEFAULT_CONSENT_CONFIG.digitalAge,
      consentModel: DEFAULT_CONSENT_CONFIG.consentModel,
      userAge: 15,
      isMinor: true, // 15 < 16
    });

    (consentService as any).detectUserLocation = original;
  });
});
