import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shadcn/components/ui/dialog';
import { initListeners, updateConsent } from '@/shared';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDigitalConsentContext } from '../context';
import { Link } from 'react-router';

declare global {
  interface Window {
    cookieConsentTest?: boolean;
  }
}

export const CookieConsent = () => {
  const [open, setOpen] = useState(false);
  const [consent, setConsent] = useState<boolean | null>(null);
  const { t } = useTranslation();
  const { config: consentConfig } = useDigitalConsentContext();

  useEffect(() => {
    if (!consentConfig) return;
    const userConsent = localStorage.getItem('userConsent');
    (async () => {
      const accepted =
        userConsent === 'true' || !consentConfig.showCookieConsent;

      setConsent(accepted);
      if (!accepted) {
        setOpen(true);
      }
    })();
  }, [consentConfig]);

  const handleConsent = (accepted: boolean) => {
    updateConsent(accepted);
    setConsent(accepted);
    setOpen(false);

    if (accepted) initListeners();
  };

  if (!import.meta.env.PROD && !window.cookieConsentTest) return;

  return (
    <Dialog
      open={open}
      onOpenChange={(value: boolean) => {
        if (!consent) handleConsent(false);
        setOpen(value);
      }}
      modal
      data-testid="cookie-consent-dialog"
    >
      <DialogContent className="gap-6" data-testid="cookie-consent-content">
        <div className="flex flex-col items-center gap-4">
          <img
            src="/assets/images/cookie.jpg"
            className="max-w-[150px]"
            alt="cookie"
            data-testid="cookie-consent-image"
          />
          <DialogHeader className="flex flex-col gap-2">
            <DialogTitle
              className="text-xl font-bold text-primary text-center"
              data-testid="cookie-consent-title"
            >
              {t('cookiesConsentDialog.title')}
            </DialogTitle>
            <DialogDescription
              className="text-sm text-center text-primary/80"
              data-testid="cookie-consent-description"
            >
              {t('cookiesConsentDialog.description.text1')}
              <Link
                to="/legal/privacy-policy"
                target="_blank"
                className="underline"
                data-testid="cookie-consent-privacy-link"
              >
                {t('cookiesConsentDialog.description.link')}
              </Link>
              {t('cookiesConsentDialog.description.text2')}
            </DialogDescription>
          </DialogHeader>
        </div>

        <DialogFooter className="w-full flex !flex-col gap-2 items-center">
          <Button
            onClick={() => handleConsent(true)}
            size="lg"
            data-testid="cookie-consent-accept-button"
          >
            {t('cookiesConsentDialog.buttons.accept')}
          </Button>
          <Button
            onClick={() => handleConsent(false)}
            variant="ghost"
            size="lg"
            className="hover:bg-primary/5 hover:text-primary/80 text-primary/80"
            data-testid="cookie-consent-decline-button"
          >
            {t('cookiesConsentDialog.buttons.decline')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
