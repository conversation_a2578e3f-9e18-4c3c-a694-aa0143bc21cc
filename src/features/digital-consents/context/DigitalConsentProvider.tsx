import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from 'react';
import { ConsentResult } from '../types';
import { consentService } from '../lib';

interface DigitalConsentContextValue {
  config: ConsentResult | null;
  setConfig: (value: ConsentResult) => void;
}

const DigitalConsentContext = createContext<
  DigitalConsentContextValue | undefined
>(undefined);

export const DigitalConsentProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [config, setConfig] = useState<ConsentResult | null>(null);

  useEffect(() => {
    (async () => {
      const config = await consentService.getPersonalConsentConfig();
      setConfig(config);
    })();
  }, []);

  return (
    <DigitalConsentContext.Provider
      value={{
        config,
        setConfig,
      }}
    >
      {children}
    </DigitalConsentContext.Provider>
  );
};

export function useDigitalConsentContext(): DigitalConsentContextValue {
  const context = useContext(DigitalConsentContext);
  if (!context) {
    throw new Error(
      'useDigitalConsentContext must be used within a DigitalConsentProvider',
    );
  }
  return context;
}
