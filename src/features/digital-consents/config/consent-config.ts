import { ConsentConfig } from '../types';

export const CONSENT_CONFIG: Record<string, ConsentConfig> = {
  // Countries requiring Opt-in (show cookie consent)
  AD: {
    // Andorra
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement:
      'Opt-in for non-essential cookies; inform about technical cookies.',
  },
  AR: {
    // Argentina
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  AM: {
    // Armenia
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  AT: {
    // Austria
    digitalAge: 14,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  BE: {
    // Belgium
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'Opt-in for non-essential cookies.',
  },
  BR: {
    // Brazil
    digitalAge: 18,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'Opt-in for non-essential cookies.',
  },
  CA: {
    // Canada
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in with exceptions',
    cookieRequirement: 'No specific cookie rules.',
  },
  CO: {
    // Colombia
    digitalAge: 18,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  HR: {
    // Croatia
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  CY: {
    // Cyprus
    digitalAge: 14,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  CZ: {
    // Czech Republic
    digitalAge: 15,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  DK: {
    // Denmark
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  EE: {
    // Estonia
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  FI: {
    // Finland
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  FR: {
    // France
    digitalAge: 15,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  DE: {
    // Germany
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  GR: {
    // Greece
    digitalAge: 15,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  HU: {
    // Hungary
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  IS: {
    // Iceland
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  IN: {
    // India
    digitalAge: 18,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  IE: {
    // Ireland
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  IL: {
    // Israel
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  IT: {
    // Italy
    digitalAge: 14,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  JP: {
    // Japan
    digitalAge: 15,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  KE: {
    // Kenya
    digitalAge: 18,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  LV: {
    // Latvia
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  LI: {
    // Liechtenstein
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  LT: {
    // Lithuania
    digitalAge: 14,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  LU: {
    // Luxembourg
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  MY: {
    // Malaysia
    digitalAge: 18,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  MT: {
    // Malta
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  NL: {
    // Netherlands
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  NZ: {
    // New Zealand
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  NO: {
    // Norway
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  PL: {
    // Poland
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  PT: {
    // Portugal
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  RO: {
    // Romania
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  RU: {
    // Russia
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  SK: {
    // Slovakia
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  SI: {
    // Slovenia
    digitalAge: 15,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  ZA: {
    // South Africa
    digitalAge: 18,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  KR: {
    // South Korea
    digitalAge: 14,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  ES: {
    // Spain
    digitalAge: 14,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  SE: {
    // Sweden
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  CH: {
    // Switzerland
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  TH: {
    // Thailand
    digitalAge: 20,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  TR: {
    // Turkey
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  UA: {
    // Ukraine
    digitalAge: 16,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  AE: {
    // United Arab Emirates
    digitalAge: 21,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  GB: {
    // United Kingdom
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No non-essential cookies before consent.',
  },
  UY: {
    // Uruguay
    digitalAge: 13,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },
  VE: {
    // Venezuela
    digitalAge: 18,
    requiresCookieConsent: true,
    consentModel: 'Opt-in',
    cookieRequirement: 'No specific cookie rules.',
  },

  // Countries with Opt-out model (do not show cookie consent)
  AU: {
    // Australia
    digitalAge: 13,
    requiresCookieConsent: false,
    consentModel: 'Opt-out with exceptions',
    cookieRequirement:
      'No specific cookie rules; must notify before/at time of cookie use.',
  },
  HK: {
    // Hong Kong
    digitalAge: 13,
    requiresCookieConsent: false,
    consentModel: 'Opt-out with exceptions',
    cookieRequirement: 'No specific cookie rules.',
  },
  MX: {
    // Mexico
    digitalAge: 12,
    requiresCookieConsent: false,
    consentModel: 'Opt-out with exceptions',
    cookieRequirement: 'No specific cookie rules.',
  },
  SG: {
    // Singapore
    digitalAge: 13,
    requiresCookieConsent: false,
    consentModel: 'Opt-out with exceptions',
    cookieRequirement: 'No specific cookie rules.',
  },
  US: {
    // United States
    digitalAge: 13,
    requiresCookieConsent: false,
    consentModel: 'Opt-out with exceptions',
    cookieRequirement: 'No specific cookie rules (varies by state).',
  },
};

// Default config for unknown countries
export const DEFAULT_CONSENT_CONFIG: ConsentConfig = {
  digitalAge: 18,
  requiresCookieConsent: true,
  consentModel: 'Opt-in',
  cookieRequirement: 'Opt-in for non-essential cookies.',
};
