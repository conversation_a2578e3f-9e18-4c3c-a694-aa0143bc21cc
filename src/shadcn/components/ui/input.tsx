import { cn } from '@/shadcn/lib/utils';
import { Eye, EyeOff } from 'lucide-react';
import { ComponentProps, forwardRef, useState } from 'react';

const Input = forwardRef<
  HTMLInputElement,
  ComponentProps<'input'> & { showPasswordToggle?: boolean }
>(({ className, type, showPasswordToggle = false, ...props }, ref) => {
  const [visible, setVisible] = useState(false);

  const isPassword = type === 'password';
  const inputType =
    isPassword && showPasswordToggle ? (visible ? 'text' : 'password') : type;

  return (
    <div className="relative w-full">
      <input
        type={inputType}
        data-slot="input"
        className={cn(
          'file:text-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
          'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive col-span-3 min-h-12 placeholder:text-card-foreground/60 border',
          className,
        )}
        ref={ref}
        {...props}
      />
      {isPassword && showPasswordToggle && (
        <button
          type="button"
          className="absolute inset-y-0 right-1 rounded-full top-0 bottom-0 my-auto pr-3 pl-1 flex items-center text-primary cursor-pointer bg-card h-[calc(100%-2px)]"
          onClick={() => setVisible(!visible)}
        >
          {visible ? <EyeOff size={18} /> : <Eye size={18} />}
        </button>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export { Input };
