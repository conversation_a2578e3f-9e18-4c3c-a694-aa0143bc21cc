import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/shadcn/components/ui/avatar';
import { cn } from '@/shadcn/lib/utils';
import React from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './tooltip';
import { getInitials } from '@/shared';

interface UserInfo {
  id: string | number;
  avatar?: string | null;
  name: string;
}

export interface GroupAvatarProps {
  users: UserInfo[];
  maxDisplayed?: number;
  size?: string;
  className?: string;
}

const palette = [
  'bg-[#7CA8F3]',
  'bg-[#E88CFF]',
  'bg-[#FD8E59]',
  'bg-[#A9A9A9]',
];

export const AvatarGroup: React.FC<GroupAvatarProps> = ({
  users = [],
  maxDisplayed = 3,
  size = 'h-8 w-8',
  className,
}) => {
  const displayedUsers = users.slice(0, maxDisplayed);
  const remainingCount = users.length - displayedUsers.length;
  const [open, setOpen] = React.useState(
    Array.from({ length: users.length }, () => false),
  );

  return (
    <div className={cn('flex items-center -space-x-2', className)}>
      {displayedUsers.map((user, index) => (
        <TooltipProvider key={user.id}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Avatar
                className={cn(size, 'border-card border-2 cursor-default')}
              >
                <AvatarImage src={user.avatar ?? undefined} alt={user.name} />
                <AvatarFallback
                  className={cn(
                    'text-sm font-semibold text-[#f8e9d6]',
                    palette[index % palette.length],
                  )}
                >
                  {getInitials(user.name)}
                </AvatarFallback>
              </Avatar>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>{user.name}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ))}
      {remainingCount > 0 && (
        <Avatar
          className={cn(
            size,
            'border-card border-2 dark:border-gray-800 cursor-default',
          )}
        >
          <AvatarFallback className="bg-[#7CA8F3] text-sm font-semibold text-[#f8e9d6]">
            +{remainingCount}
          </AvatarFallback>
        </Avatar>
      )}
    </div>
  );
};
