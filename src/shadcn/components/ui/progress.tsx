import * as React from 'react';
import * as ProgressPrimitive from '@radix-ui/react-progress';

import { cn } from '@/shadcn/lib/utils';

export function Progress({
  className,
  progressColorVar,
  value,
  ...props
}: React.ComponentProps<typeof ProgressPrimitive.Root> & {
  progressColorVar?: string;
}) {
  return (
    <ProgressPrimitive.Root
      className={cn(
        'bg-primary/20 relative h-2 w-full overflow-hidden rounded-full',
        className,
      )}
      value={value}
      {...props}
    >
      <ProgressPrimitive.Indicator
        className={cn('h-full w-full flex-1 transition-all')}
        style={{
          transform: `translateX(-${100 - (value || 0)}%)`,
          backgroundColor: `var(${progressColorVar || '--primary'})`,
        }}
      />
    </ProgressPrimitive.Root>
  );
}
