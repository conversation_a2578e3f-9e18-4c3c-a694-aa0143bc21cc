import { MessageSquareText } from 'lucide-react';
import { Chat, useChat } from '@/features';
import { useTranslation } from 'react-i18next';
import { Sidebar, useClassroomSettings } from '@/shared';

export const ClassChatWrapper = () => {
  const { t } = useTranslation();
  const { onSidebarOpen, unreadMessages } = useChat();
  const classroomSettings = useClassroomSettings();

  return (
    <Sidebar
      disabled={!classroomSettings || !classroomSettings.allowChat}
      title={t('class.tools.chat.title')}
      tooltip={t('class.tools.chat.tooltip')}
      triggerIcon={<MessageSquareText />}
      contentClassName="min-[1200px]:min-w-[550px]"
      indicator={unreadMessages.length}
      indicator-testid="sidebar-indicator-chat-messages"
      setOpen={onSidebarOpen}
      data-testid="action-bar-chat-button"
      contentContainerClassName="max-h-full"
    >
      <Chat />
    </Sidebar>
  );
};
