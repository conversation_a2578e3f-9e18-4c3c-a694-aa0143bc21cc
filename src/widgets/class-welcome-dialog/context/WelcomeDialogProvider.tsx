import {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from 'react';

type WelcomeDialogContextType = {
  show: boolean;
  setShow: (value: boolean) => void;
  step:
    | 'preview'
    | 'code-of-conduct'
    | 'audio-selection'
    | 'calibration'
    | 'finished';
  setStep: (
    step:
      | 'preview'
      | 'code-of-conduct'
      | 'audio-selection'
      | 'calibration'
      | 'finished',
  ) => void;
  status: 'initial' | 'recalibrate';
  setStatus: (status: 'initial' | 'recalibrate') => void;
  isCalibrating: boolean;
  setIsCalibrating: (value: boolean) => void;
  isPublishing: boolean;
  setIsPublishing: (value: boolean) => void;
};

const WelcomeDialogContext = createContext<
  WelcomeDialogContextType | undefined
>(undefined);

export const WelcomeDialogProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [show, setShow] = useState<boolean>(true);
  const [step, setStep] = useState<
    | 'preview'
    | 'code-of-conduct'
    | 'audio-selection'
    | 'calibration'
    | 'finished'
  >('preview');
  const [status, setStatus] = useState<'initial' | 'recalibrate'>('initial');
  const [isCalibrating, setIsCalibrating] = useState(true);
  const [isPublishing, setIsPublishing] = useState(false);

  useEffect(() => {
    if (!show) setStatus('recalibrate');
  }, [show]);

  return (
    <WelcomeDialogContext.Provider
      value={{
        show,
        setShow,
        step,
        setStep,
        status,
        setStatus,
        isCalibrating,
        setIsCalibrating,
        isPublishing,
        setIsPublishing,
      }}
    >
      {children}
    </WelcomeDialogContext.Provider>
  );
};

export const useWelcomeDialogContext = (): WelcomeDialogContextType => {
  const context = useContext(WelcomeDialogContext);
  if (!context) {
    throw new Error(
      'useWelcomeDialog must be used within a WelcomeDialogProvider',
    );
  }
  return context;
};
