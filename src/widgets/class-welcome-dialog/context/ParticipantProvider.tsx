import { ClassParticipant } from '@/entities';
import { createContext, useContext, useState, ReactNode } from 'react';

type ParticipantContextType = {
  participant: ClassParticipant | null;
  setParticipant: (participant: ClassParticipant | null) => void;
};

const ParticipantContext = createContext<ParticipantContextType | undefined>(
  undefined,
);

export const ParticipantProvider = ({ children }: { children: ReactNode }) => {
  const [participant, setParticipant] = useState<ClassParticipant | null>(null);

  return (
    <ParticipantContext.Provider
      value={{
        participant,
        setParticipant,
      }}
    >
      {children}
    </ParticipantContext.Provider>
  );
};

export const useParticipantContext = (): ParticipantContextType => {
  const context = useContext(ParticipantContext);
  if (!context) {
    throw new Error(
      'useParticipantContext must be used within a ParticipantProvider',
    );
  }
  return context;
};
