import { FC } from 'react';
import { Button } from '@/shadcn/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useWelcomeDialogContext } from '../../context';

interface IRespectAndListenStepProps {
  setStep: (step: 'respect' | 'checks' | 'report') => void;
}

export const RespectAndListenStep: FC<IRespectAndListenStepProps> = ({
  setStep,
}) => {
  const { t } = useTranslation();
  const { setStep: setGlobalStep } = useWelcomeDialogContext();

  return (
    <div
      data-testid="coc-respect-step"
      className="flex flex-col gap-9 items-center justify-center"
    >
      <img
        data-testid="coc-respect-image"
        src="/assets/images/coc-respect.jpg"
        alt="Respect and Listen"
        className="rounded-lg aspect-square max-w-[200px] max-h-[200px] h-full w-full"
      />
      <div
        data-testid="coc-respect-content"
        className="flex flex-col gap-4 items-center justify-center"
      >
        <h1
          data-testid="coc-respect-title"
          className="text-white font-[800] text-2xl"
        >
          {t('class.dialogs.welcome.codeOfConduct.respect.title')}
        </h1>
        <div
          data-testid="coc-respect-description"
          className="flex flex-col gap-3 text-white text-left"
        >
          <p data-testid="coc-respect-text1">
            {t('class.dialogs.welcome.codeOfConduct.respect.description.text1')}
          </p>
          <p
            data-testid="coc-respect-text2"
            dangerouslySetInnerHTML={{
              __html: t(
                'class.dialogs.welcome.codeOfConduct.respect.description.text2',
              ),
            }}
          />
          <p
            data-testid="coc-respect-text3"
            dangerouslySetInnerHTML={{
              __html: t(
                'class.dialogs.welcome.codeOfConduct.respect.description.text3',
              ),
            }}
          />
        </div>
      </div>

      <div
        data-testid="coc-respect-buttons"
        className="mt-2 flex items-center justify-center gap-3"
      >
        <Button
          data-testid="coc-respect-close-button"
          size="lg"
          variant="outline"
          onClick={() => setGlobalStep('audio-selection')}
        >
          {t('class.dialogs.welcome.codeOfConduct.buttons.close')}
        </Button>
        <Button
          data-testid="coc-respect-next-button"
          size="lg"
          onClick={() => setStep('checks')}
        >
          {t('class.dialogs.welcome.codeOfConduct.buttons.next')}
        </Button>
      </div>
    </div>
  );
};
