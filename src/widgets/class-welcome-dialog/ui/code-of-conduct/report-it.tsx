import { FC } from 'react';
import { Button } from '@/shadcn/components/ui/button';
import { Link } from 'react-router';
import { useTranslation } from 'react-i18next';
import { useWelcomeDialogContext } from '../../context';

interface IReportItStepProps {
  setStep: (step: 'respect' | 'checks' | 'report') => void;
}

export const ReportItStep: FC<IReportItStepProps> = ({ setStep }) => {
  const { t } = useTranslation();
  const { setStep: setGlobalStep } = useWelcomeDialogContext();

  return (
    <div
      data-testid="coc-report-step"
      className="flex flex-col gap-9 items-center justify-center"
    >
      <img
        data-testid="coc-report-image"
        src="/assets/images/coc-report.jpg"
        alt="See Something? Report It"
        className="rounded-lg aspect-square max-w-[200px] max-h-[200px] h-full w-full"
      />
      <div
        data-testid="coc-report-content"
        className="flex flex-col gap-4 items-center justify-center"
      >
        <h1
          data-testid="coc-report-title"
          className="text-white font-[800] text-2xl"
        >
          {t('class.dialogs.welcome.codeOfConduct.report.title')}
        </h1>
        <div
          data-testid="coc-report-description"
          className="flex flex-col gap-3 text-white text-left"
        >
          <div data-testid="coc-report-text1-container">
            <p
              data-testid="coc-report-text1"
              dangerouslySetInnerHTML={{
                __html: t(
                  'class.dialogs.welcome.codeOfConduct.report.description.text1',
                ),
              }}
            />
            <Link
              data-testid="coc-report-link"
              to={'/legal/report'}
              target="_blank"
              className="text-accent"
            >
              dev.virtuosohub.com/legal/report
            </Link>
          </div>
          <p
            data-testid="coc-report-text2"
            dangerouslySetInnerHTML={{
              __html: t(
                'class.dialogs.welcome.codeOfConduct.report.description.text2',
              ),
            }}
          />
        </div>
      </div>

      <Link
        data-testid="coc-report-full-link"
        to="/legal/code-of-conduct"
        target="_blank"
        className="underline mt-2 text-sm text-center"
      >
        {t('class.dialogs.welcome.codeOfConduct.report.full')}
      </Link>

      <div
        data-testid="coc-report-buttons"
        className="mt-2 flex items-center justify-center gap-3"
      >
        <Button
          data-testid="coc-report-back-button"
          size="lg"
          variant="outline"
          onClick={() => setStep('checks')}
        >
          {t('class.dialogs.welcome.codeOfConduct.buttons.back')}
        </Button>
        <Button
          data-testid="coc-report-close-button"
          size="lg"
          onClick={() => setGlobalStep('preview')}
        >
          {t('class.dialogs.welcome.codeOfConduct.buttons.close')}
        </Button>
      </div>
    </div>
  );
};
