import { useState } from 'react';
import { SmartSafetyChecksStep } from './smart-safety-checks';
import { ReportItStep } from './report-it';
import { RespectAndListenStep } from './respect-and-listen';
import { AnimatePresence, motion } from 'framer-motion';

export const CodeOfConduct = () => {
  const [step, setStep] = useState<'respect' | 'checks' | 'report'>('respect');

  const getCurrentStep = () => {
    switch (step) {
      case 'checks':
        return <SmartSafetyChecksStep setStep={setStep} />;
      case 'report':
        return <ReportItStep setStep={setStep} />;
      case 'respect':
      default:
        return <RespectAndListenStep setStep={setStep} />;
    }
  };

  return (
    <motion.div data-testid="coc-container" layout>
      <AnimatePresence mode="wait">
        <motion.div
          data-testid={`coc-step-${step}`}
          key={step}
          layout
          initial={{ opacity: 0, y: 8 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -8 }}
          transition={{ duration: 0.18 }}
        >
          {getCurrentStep()}
        </motion.div>
      </AnimatePresence>
    </motion.div>
  );
};
