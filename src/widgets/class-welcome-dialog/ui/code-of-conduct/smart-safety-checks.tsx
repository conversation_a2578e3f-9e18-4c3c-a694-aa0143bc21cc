import { FC } from 'react';
import { Button } from '@/shadcn/components/ui/button';
import { useTranslation } from 'react-i18next';

interface ISmartSafetyChecksStepProps {
  setStep: (step: 'respect' | 'checks' | 'report') => void;
}

export const SmartSafetyChecksStep: FC<ISmartSafetyChecksStepProps> = ({
  setStep,
}) => {
  const { t } = useTranslation();

  return (
    <div
      data-testid="coc-smart-step"
      className="flex flex-col gap-9 items-center justify-center"
    >
      <img
        data-testid="coc-smart-image"
        src="/assets/images/coc-smart.jpg"
        alt="Smart Safety Checks"
        className="rounded-lg aspect-square max-w-[200px] max-h-[200px] h-full w-full"
      />
      <div
        data-testid="coc-smart-content"
        className="flex flex-col gap-4 items-center justify-center"
      >
        <h1
          data-testid="coc-smart-title"
          className="text-white font-[800] text-2xl"
        >
          {t('class.dialogs.welcome.codeOfConduct.checks.title')}
        </h1>
        <div
          data-testid="coc-smart-description"
          className="flex flex-col gap-3 text-white text-left"
        >
          <p data-testid="coc-smart-text1">
            {t('class.dialogs.welcome.codeOfConduct.checks.description.text1')}
          </p>
          <p
            data-testid="coc-smart-text2"
            dangerouslySetInnerHTML={{
              __html: t(
                'class.dialogs.welcome.codeOfConduct.checks.description.text2',
              ),
            }}
          />
          <p data-testid="coc-smart-text3">
            {t('class.dialogs.welcome.codeOfConduct.checks.description.text3')}
          </p>
        </div>
      </div>

      <div
        data-testid="coc-smart-buttons"
        className="mt-2 flex items-center justify-center gap-3"
      >
        <Button
          data-testid="coc-smart-back-button"
          size="lg"
          variant="outline"
          onClick={() => setStep('respect')}
        >
          {t('class.dialogs.welcome.codeOfConduct.buttons.back')}
        </Button>
        <Button
          data-testid="coc-smart-next-button"
          size="lg"
          onClick={() => setStep('report')}
        >
          {t('class.dialogs.welcome.codeOfConduct.buttons.next')}
        </Button>
      </div>
    </div>
  );
};
