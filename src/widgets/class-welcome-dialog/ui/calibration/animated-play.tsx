import { motion } from 'framer-motion';

const OptimizedBarAnimation = () => {
  const bars = [
    { initial: 8, target: 20 },
    { initial: 20, target: 40 },
    { initial: 52, target: 20 },
    { initial: 20, target: 40 },
    { initial: 8, target: 20 },
  ];

  return (
    <div className="flex items-center justify-center gap-[8px]">
      {bars.map((bar, index) => (
        <motion.div
          key={index}
          className="w-[8px] bg-white"
          initial={{ height: bar.initial }}
          animate={{
            scaleY: [1, bar.target / bar.initial, 1],
            borderRadius: [10000, 10000, 10000],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: index * 0.05,
          }}
          style={{
            height: bar.initial,
            transformOrigin: 'center',
            borderRadius: 10000,
          }}
        />
      ))}
    </div>
  );
};

export default OptimizedBarAnimation;
