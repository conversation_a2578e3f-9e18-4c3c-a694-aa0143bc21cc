import {
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shadcn/components/ui/dialog';
import { useState } from 'react';
import AnimatedSvg from './animated-play';
import { centralAudioProcessing } from '@/features';
import { useWelcomeDialogContext } from '../../context';

export const Calibration = () => {
  const [isSoundPlay, setIsSoundPlay] = useState(false);
  const { setStep, setIsCalibrating } = useWelcomeDialogContext();

  return (
    <>
      <DialogHeader>
        <DialogTitle>Press play to colibrate</DialogTitle>
        <DialogDescription className="text-sm text-center text-white">
          Press play and be quiet. <br />
          This is necessary for sound customization.
        </DialogDescription>
      </DialogHeader>
      <div className="flex items-center justify-center">
        <button
          className="size-24 rounded-full flex items-center justify-center bg-accent border-2 border-accent cursor-pointer"
          data-testid="aec-play-button"
          disabled={isSoundPlay}
          onClick={() => {
            setIsSoundPlay(true);
            centralAudioProcessing.getAECController().startCalibration(
              () => {
                setStep('finished');
                setIsSoundPlay(false);
              },
              () => {
                setIsCalibrating(false);
              },
            );
          }}
        >
          {isSoundPlay ? (
            <AnimatedSvg />
          ) : (
            <img src="/assets/images/play.svg" />
          )}
        </button>
      </div>
    </>
  );
};
