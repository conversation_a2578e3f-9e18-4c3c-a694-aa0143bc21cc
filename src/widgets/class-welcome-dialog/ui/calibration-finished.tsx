import {
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shadcn/components/ui/dialog';
import { Button } from '@/shadcn/components/ui/button';
import { useLocalMediaContext } from '@/features';
import { Loader } from '@/shared';
import { useWelcomeDialogContext } from '../context';

export const CalibrationFinished = () => {
  const { setHasProcessing } = useLocalMediaContext();
  const { status, setShow, isCalibrating, setIsPublishing } =
    useWelcomeDialogContext();

  return (
    <>
      <DialogHeader>
        <DialogTitle>The sound is being tuned!</DialogTitle>
        <DialogDescription />
      </DialogHeader>

      <img src="/assets/images/success.svg" className="mx-auto" />

      <Button
        className="mx-auto"
        size="lg"
        disabled={isCalibrating}
        onClick={() => {
          setHasProcessing(true);
          if (status === 'initial') {
            setIsPublishing(true);
          } else {
            setShow(false);
          }
        }}
      >
        {isCalibrating ? <Loader color="white" /> : 'Done'}
      </Button>
    </>
  );
};
