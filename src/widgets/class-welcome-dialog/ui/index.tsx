import {
  Dialog,
  DialogContent,
  DialogPortal,
} from '@/shadcn/components/ui/dialog';
import { retry, useUpdateReduxState } from '@/shared';
import { Preview } from './preview';
import { AudioSelection } from './audio-selection';
import {
  centralAudioProcessing,
  LocalMediaStatus,
  useLocalMediaContext,
  publishLocalTracks,
  useLivekitContext,
  setConferenceStatus,
  ConferenceStatus,
} from '@/features';
import { useWelcomeDialogContext } from '../context';
import { useEffect } from 'react';
import { Calibration } from './calibration';
import { CalibrationFinished } from './calibration-finished';
import { useUser } from '@/entities';
import { CodeOfConduct } from './code-of-conduct';

export const WelcomeDialog = () => {
  const updateReduxState = useUpdateReduxState();

  const { roleType } = useUser();
  const { room } = useLivekitContext();
  const { localStream, status: localStreamStatus } = useLocalMediaContext();
  const { step, isPublishing, show, setShow, setIsPublishing } =
    useWelcomeDialogContext();

  const getCurrentStep = () => {
    switch (step) {
      case 'finished':
        return <CalibrationFinished />;
      case 'calibration':
        return <Calibration />;
      case 'audio-selection':
        return <AudioSelection />;
      case 'code-of-conduct':
        return <CodeOfConduct />;
      case 'preview':
      default:
        return <Preview />;
    }
  };

  useEffect(() => {
    if (
      !isPublishing ||
      !localStream ||
      localStreamStatus !== LocalMediaStatus.Ready ||
      !show
    )
      return;

    const videoTracks = localStream.getVideoTracks();
    const processedStream = centralAudioProcessing
      .getLocalAudioMixer()
      .getCurrentStream();

    videoTracks.map((track: MediaStreamTrack) =>
      processedStream.addTrack(track),
    );

    (async () => {
      await retry(() => publishLocalTracks(room, processedStream));
      centralAudioProcessing.getAECController().startProcessing();
      updateReduxState(setConferenceStatus(ConferenceStatus.Ready));

      setIsPublishing(false);
      setShow(false);
    })();
  }, [localStreamStatus, localStream]);

  return (
    <Dialog
      open={show}
      onOpenChange={(open) => {
        if (open && roleType !== 'party') setShow(open);
      }}
    >
      <DialogPortal>
        {/*isPublishing && (
          <div className="fixed inset-0 z-[9999] bg-black/60 flex items-center justify-center">
            <Loader />
          </div>
        )*/}
      </DialogPortal>
      <DialogContent
        className="w-full !max-w-[820px] max-[860px]:!max-w-[calc(100%-40px)] py-10 items-center justify-center min-w-[calc(100%-2rem)] md:min-w-[80%] lg:min-w-[50%] [&>.dialog-close]:hidden !text-white"
        variant="dark"
        onOpenAutoFocus={(e) => e.preventDefault()}
        onCloseAutoFocus={(e) => e.preventDefault()}
      >
        {getCurrentStep()}
      </DialogContent>
    </Dialog>
  );
};
