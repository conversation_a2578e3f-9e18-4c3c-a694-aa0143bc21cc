import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/shadcn/components/ui/dialog';
import { useTranslation } from 'react-i18next';
import { Button } from '@/shadcn/components/ui/button';
import { ReactNode } from 'react';
import { Headphones, Laptop } from 'lucide-react';
import { useLocalMediaContext } from '@/features';
import { useWelcomeDialogContext } from '../context';

export const AudioSelection = () => {
  const { t } = useTranslation();

  const { setHasProcessing } = useLocalMediaContext();
  const { status, setShow, setIsPublishing } = useWelcomeDialogContext();

  const types: {
    title: string;
    description: string;
    icon: ReactNode;
    onClick: () => void;
  }[] = [
    {
      title: 'class.dialogs.welcome.audioSelection.types.computer.title',
      description:
        'class.dialogs.welcome.audioSelection.types.computer.description',
      icon: <Laptop className="size-20 text-accent" />,
      onClick: () => {
        setHasProcessing(true);
        if (status === 'initial') {
          setIsPublishing(true);
        } else {
          setShow(false);
        }
      },
    },
    {
      title: 'class.dialogs.welcome.audioSelection.types.headphones.title',
      description:
        'class.dialogs.welcome.audioSelection.types.headphones.description',
      icon: <Headphones className="size-20 text-accent" />,
      onClick: () => {
        setHasProcessing(false);
        if (status === 'initial') {
          setIsPublishing(true);
        } else {
          setShow(false);
        }
      },
    },
  ];

  return (
    <>
      <DialogHeader
        className="items-center"
        data-testid="audio-selection-header"
      >
        <DialogTitle data-testid="audio-selection-title">
          {t('class.dialogs.welcome.audioSelection.title')}
        </DialogTitle>
      </DialogHeader>

      <div
        className="flex justify-center items-start gap-4 mb-4 max-[450px]:items-center max-[450px]:flex-col"
        data-testid="audio-selection-container"
      >
        {types.map((value, index) => {
          const optionType = index === 0 ? 'computer' : 'headphones';
          return (
            <div
              key={index}
              className="flex flex-col justify-center items-center gap-2 max-w-50"
              onClick={value.onClick}
              data-testid={`audio-selection-${optionType}-option`}
            >
              <h3
                className="text-lg font-bold text-white text-center"
                data-testid={`audio-selection-${optionType}-title`}
              >
                {t(value.title)}
              </h3>
              <div
                className="aspect-square h-35 border-2 border-accent/50 bg-accent/10 flex justify-center items-center rounded-lg"
                data-testid={`audio-selection-${optionType}-icon`}
              >
                {value.icon}
              </div>
              <p
                className="text-sm text-center text-white/70"
                data-testid={`audio-selection-${optionType}-description`}
              >
                {t(value.description)}
              </p>
            </div>
          );
        })}
      </div>

      {status === 'recalibrate' && (
        <DialogFooter
          className="w-full flex flex-row"
          data-testid="audio-selection-footer"
        >
          <Button
            variant="outline"
            onClick={() => setShow(false)}
            data-testid="audio-selection-close-button"
          >
            {t('class.dialogs.welcome.audioSelection.close')}
          </Button>
        </DialogFooter>
      )}
    </>
  );
};
