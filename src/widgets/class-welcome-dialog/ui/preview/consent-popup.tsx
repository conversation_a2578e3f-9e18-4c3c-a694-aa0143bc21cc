import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { Switch } from '@/shadcn/components/ui/switch';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface IConsentPopupProps {
  consent: boolean;
  setConsent: (value: boolean) => void;
}

export const ConsentPopup = ({ consent, setConsent }: IConsentPopupProps) => {
  const { t } = useTranslation();
  const [show, setShow] = useState<boolean>(false);

  return (
    <Dialog
      open={show}
      onOpenChange={(value) => {
        if (!value) setShow(value);
      }}
    >
      <DialogTrigger asChild>
        <div className="flex items-center justify-center gap-3">
          <Switch
            checked={consent}
            onCheckedChange={(value) => {
              if (!value) {
                setShow(true);
              } else {
                setConsent(value);
              }
            }}
            className="data-[state=unchecked]:!bg-white/20"
          />
          <label htmlFor="" className="text-sm text-white">
            {t('class.dialogs.welcome.preview.party.consent.trigger')}
          </label>
        </div>
      </DialogTrigger>
      <DialogContent
        className="px-5 py-10 items-center justify-center !text-white"
        variant="dark"
      >
        <DialogHeader className="items-center gap-3">
          <DialogTitle className="text-2xl max-[450px]:text-xl text-white font-extrabold">
            {t('class.dialogs.welcome.preview.party.consent.dialog.title')}
          </DialogTitle>
          <DialogDescription className="text-sm text-white text-center max-[450px]:text-xs">
            {t(
              'class.dialogs.welcome.preview.party.consent.dialog.description',
            )}
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="w-full mt-3 flex flex-row gap-3 !justify-center items-center">
          <Button
            data-testid="welcome-dialog-leave-button"
            onClick={() => {
              setShow(!show);
            }}
            size="lg"
          >
            {t('class.dialogs.welcome.preview.party.consent.dialog.buttons.no')}
          </Button>
          <Button
            data-testid="welcome-dialog-continue-button"
            variant="outline"
            onClick={() => {
              setConsent(!consent);
              setShow(!show);
            }}
            size="lg"
          >
            {t(
              'class.dialogs.welcome.preview.party.consent.dialog.buttons.yes',
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
