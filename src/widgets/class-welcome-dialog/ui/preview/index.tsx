import {
  DialogD<PERSON><PERSON>,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shadcn/components/ui/dialog';
import { useMemo, useState } from 'react';
import { Button } from '@/shadcn/components/ui/button';
import { Loader, useUpdateReduxState } from '@/shared';
import { useTranslation } from 'react-i18next';
import {
  MediaStreamView,
  setLivekitJoinStatus,
  setLocalUserMicrophoneOn,
  setLocalUserVideoOn,
  toggleMute,
  toggleVideo,
  useLivekitContext,
  useLocalMediaContext,
  useLocalMediaVideoStream,
} from '@/features';
import { useNavigate, useParams } from 'react-router';
import { useUpdateClassroomConsent, useUser } from '@/entities';
import { useAppSelector } from '@/shared';
import { useWelcomeDialogContext } from '../../context';
import { ActionButton } from './action-button';
import { MicIcon, MicOffIcon, VideoIcon, VideoOffIcon } from 'lucide-react';
import { ConsentPopup } from './consent-popup';

export const Preview = () => {
  const navigate = useNavigate();
  const updateReduxState = useUpdateReduxState();
  const { t } = useTranslation();

  const { id: classroomId } = useParams<{ id: string }>();

  const { roleType } = useUser();
  const { room } = useLivekitContext();
  const { localStream, setHasProcessing } = useLocalMediaContext();
  const { localUser } = useAppSelector((state) => state.livekit);

  const { localVideoStream } = useLocalMediaVideoStream();
  const { isPublishing, setShow, setStep, setIsPublishing } =
    useWelcomeDialogContext();

  const video = useMemo(
    () =>
      localVideoStream && (
        <div className="z-0 aspect-video overflow-hidden w-[450px] max-[800px]:w-[330px] max-[400px]:w-[300px] rounded-[1.2rem] border border-white/20">
          <MediaStreamView
            dataTestId=""
            participant={localUser!}
            mediaStream={localVideoStream}
          />
        </div>
      ),
    [localVideoStream, localUser],
  );

  const [consent, setConsent] = useState<boolean>(true);
  const { mutate: updateConsent, isPending } = useUpdateClassroomConsent(() => {
    if (roleType === 'party') {
      setHasProcessing(true);
      setIsPublishing(true);
    } else {
      setShow(false);
    }
  });

  return (
    <div className="gap-[1.5rem] flex flex-col items-center justify-center">
      <DialogHeader className="items-center gap-[1.5rem]">
        <DialogTitle className="text-[2.25rem] max-[450px]:text-[1.5rem] text-white font-extrabold">
          {t(
            roleType === 'party'
              ? 'class.dialogs.welcome.preview.party.title'
              : 'class.dialogs.welcome.preview.watcher.title',
          )}
        </DialogTitle>
        <DialogDescription
          className="text-sm text-white text-center max-w-[468px] max-[450px]:text-xs"
          dangerouslySetInnerHTML={{
            __html: t(
              roleType === 'party'
                ? 'class.dialogs.welcome.preview.party.description'
                : 'class.dialogs.welcome.preview.watcher.description',
            ),
          }}
        />
      </DialogHeader>

      {roleType === 'party' && localUser && (
        <div id="preview-video" className="relative">
          {video}
          <div className="absolute z-10 w-full bottom-2 flex justify-center items-center gap-2 pointer-events-auto">
            <ActionButton
              data-testid="welcome-dialog-party-camera-button"
              active={localUser.isVideoOn ?? false}
              icon={localUser.isVideoOn ? <VideoIcon /> : <VideoOffIcon />}
              tooltip={t(
                'class.dialogs.welcome.preview.party.tooltips.camera.' +
                  (localUser.isVideoOn ? 'off' : 'on'),
              )}
              onClick={() => {
                const enabled = localStream
                  ? toggleVideo(
                      room,
                      localUser.sid,
                      !localUser.isVideoOn,
                      localStream,
                    )
                  : false;
                updateReduxState(setLocalUserVideoOn(enabled));
              }}
            />
            <ActionButton
              data-testid="welcome-dialog-party-microphone-button"
              active={localUser.isMicrophoneOn ?? false}
              icon={localUser.isMicrophoneOn ? <MicIcon /> : <MicOffIcon />}
              tooltip={t(
                'class.dialogs.welcome.preview.party.tooltips.microphone.' +
                  (localUser.isMicrophoneOn ? 'off' : 'on'),
              )}
              onClick={() => {
                const enabled = localStream
                  ? toggleMute(
                      room,
                      localUser.sid,
                      !localUser.isMicrophoneOn,
                      localStream,
                    )
                  : false;
                updateReduxState(setLocalUserMicrophoneOn(enabled));
              }}
            />
          </div>
        </div>
      )}

      {roleType === 'party' && (
        <ConsentPopup consent={consent} setConsent={setConsent} />
      )}

      <DialogFooter className="w-full mt-[11px] flex flex-row !justify-center items-center">
        <Button
          variant="outline"
          data-testid="welcome-dialog-leave-button"
          onClick={() => {
            updateReduxState(setLivekitJoinStatus('left'));
            navigate(`/classrooms/${classroomId}`);
          }}
          size="lg"
        >
          {t('class.dialogs.welcome.preview.buttons.leave')}
        </Button>
        <Button
          data-testid="welcome-dialog-continue-button"
          onClick={() =>
            updateConsent({
              classroomId: classroomId ?? '',
              recording: consent,
              aiAgent: consent,
            })
          }
          size="lg"
          disabled={isPending || isPublishing}
        >
          {isPending || isPublishing ? (
            <Loader color="white" />
          ) : (
            t('class.dialogs.welcome.preview.buttons.continue')
          )}
        </Button>
      </DialogFooter>

      <div
        data-testid="welcome-dialog-code-of-conduct-link"
        className="mx-auto text-center w-fit underline text-sm text-white cursor-pointer"
        onClick={() => setStep('code-of-conduct')}
      >
        {t('legal.codeOfConduct')}
      </div>
    </div>
  );
};
