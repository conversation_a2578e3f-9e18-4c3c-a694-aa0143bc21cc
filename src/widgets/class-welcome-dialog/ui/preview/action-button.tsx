import { cloneElement, ReactElement } from 'react';
import { cn } from '@/shadcn/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';

interface IActionButtonProps {
  active: boolean;
  icon: ReactElement<React.SVGProps<SVGSVGElement>>;
  tooltip: string;
  onClick?: () => void;
  'data-testid'?: string;
}

export const ActionButton = ({
  active,
  icon,
  tooltip,
  onClick,
  'data-testid': testId,
}: IActionButtonProps) => {
  const iconStyle = 'w-5 h-5 text-primary-foreground';
  const StyledIcon = cloneElement(icon, {
    className: iconStyle,
  });

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            className={cn(
              'flex justify-center items-center p-4 rounded-full text-primary-foreground max-[1000px]:p-3',
              active
                ? 'bg-black/40 hover:bg-black/30'
                : 'bg-accent hover:bg-accent/80',
            )}
            onClick={onClick}
            data-testid={testId}
          >
            {StyledIcon}
          </button>
        </TooltipTrigger>
        <TooltipContent
          container={document.querySelector('#preview-video') ?? undefined}
        >
          <p className="text-center">{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
