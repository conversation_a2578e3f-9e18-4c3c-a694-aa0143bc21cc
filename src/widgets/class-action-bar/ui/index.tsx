import {
  CaptionsIcon,
  CaptionsOffIcon,
  Headphones,
  Laptop,
  MicIcon,
  MicOffIcon,
  Phone,
  UserPlus,
  VideoIcon,
  VideoOffIcon,
} from 'lucide-react';
import { ActionButton } from './action-button';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router';
import {
  useACL,
  useAppSelector,
  useClassroomSettings,
  useUpdateReduxState,
} from '@/shared';
import { WatcherCount } from './watcher-count';
import { useTranslation } from 'react-i18next';
import {
  DisabledTooltip,
  InviteDialog,
  isMicrophoneEnabled,
  isVideoEnabled,
  setLivekitJoinStatus,
  setLocalUserMicrophoneOn,
  setLocalUserVideoOn,
  toggleMute,
  toggleVideo,
  useLivekitContext,
  useLocalMediaContext,
  useTranslationContext,
} from '@/features';
import { useUser } from '@/entities';
import { FullscreenButton } from './fullscreen-button';

interface IActionBarProps {
  name: string;
}

export const ActionBar = ({ name }: IActionBarProps) => {
  const { room } = useLivekitContext();

  const { t } = useTranslation();
  const updateReduxState = useUpdateReduxState();
  const navigate = useNavigate();
  const { id: classroomId } = useParams<{
    id: string;
  }>();
  const { roleType } = useUser();
  const { localUser } = useAppSelector((state) => state.livekit);
  const acl = useACL();
  const classroomSettings = useClassroomSettings();
  const { localStream, hasProcessing, setHasProcessing } =
    useLocalMediaContext();
  const { enabled: transaltionOn, setEnabled: setTranslationOn } =
    useTranslationContext();

  useEffect(() => {
    if (localStream && localUser) {
      updateReduxState(
        setLocalUserMicrophoneOn(isMicrophoneEnabled(localStream)),
      );
      updateReduxState(setLocalUserVideoOn(isVideoEnabled(localStream)));
    }
  }, [localStream, localUser]);

  return (
    <div className="backdrop-blur-md optimized-blur w-full h-fit bg-black/30 flex justify-center items-center gap-3.5 max-[1200px]:gap-1.5 action-bar">
      {roleType === 'party' && localUser && (
        <>
          <ActionButton
            data-testid="action-bar-camera-button"
            active={localUser!.isVideoOn ?? false}
            icon={localUser!.isVideoOn ? <VideoIcon /> : <VideoOffIcon />}
            name={t('class.actionBar.camera.text')}
            tooltip={
              localUser!.isVideoOn
                ? t('class.actionBar.camera.tooltip.off')
                : t('class.actionBar.camera.tooltip.on')
            }
            onClick={() => {
              const enabled = localStream
                ? toggleVideo(
                    room,
                    localUser!.sid,
                    !localUser!.isVideoOn,
                    localStream,
                  )
                : false;
              updateReduxState(setLocalUserVideoOn(enabled));
            }}
          />
          <ActionButton
            data-testid="action-bar-mic-button"
            active={localUser!.isMicrophoneOn ?? false}
            icon={localUser!.isMicrophoneOn ? <MicIcon /> : <MicOffIcon />}
            name={t('class.actionBar.mic.text')}
            tooltip={
              localUser!.isMicrophoneOn
                ? t('class.actionBar.mic.tooltip.off')
                : t('class.actionBar.mic.tooltip.on')
            }
            onClick={() => {
              const enabled = localStream
                ? toggleMute(
                    room,
                    localUser!.sid,
                    !localUser!.isMicrophoneOn,
                    localStream,
                  )
                : false;
              updateReduxState(setLocalUserMicrophoneOn(enabled));
            }}
          />
          <ActionButton
            active={hasProcessing ?? false}
            icon={hasProcessing ? <Laptop /> : <Headphones />}
            name={t(
              'class.actionBar.processing.' +
                (hasProcessing ? 'computer' : 'headphones'),
            )}
            tooltip={t(
              'class.actionBar.processing.tooltip.' +
                (hasProcessing ? 'off' : 'on'),
            )}
            onClick={() => {
              setHasProcessing(!hasProcessing);
            }}
          />
        </>
      )}

      <ActionButton
        data-testid="action-bar-translate-button"
        active={transaltionOn}
        icon={transaltionOn ? <CaptionsIcon /> : <CaptionsOffIcon />}
        tooltip={
          transaltionOn
            ? t('class.actionBar.translate.tooltip.off')
            : t('class.actionBar.translate.tooltip.on')
        }
        name={t('class.actionBar.translate.text')}
        onClick={() => setTranslationOn(!transaltionOn)}
      />

      {roleType === 'party' && (
        <DisabledTooltip
          tooltipText={`globals.permissions.canParticipantInvite`}
          disabled={
            acl && classroomSettings
              ? !acl.canParticipantInvite &&
                !(classroomSettings.allowInviteGuests && acl.canGuestInvite)
              : true
          }
        >
          <InviteDialog
            id={classroomId ?? ''}
            name={name}
            toClass
            defaultRole={acl && acl.canParticipantInvite ? 'student' : 'guest'}
            trigger={
              <ActionButton
                data-testid="action-bar-invite-button"
                onClick={() => {}}
                icon={<UserPlus />}
                name={t('class.actionBar.invite.text')}
                tooltip={
                  acl
                    ? acl.canParticipantInvite && acl.canGuestInvite
                      ? t('class.actionBar.invite.tooltip')
                      : acl.canParticipantInvite
                        ? t('class.actionBar.invite.tooltipParticipant')
                        : acl.canGuestInvite
                          ? t('class.actionBar.invite.tooltipGuest')
                          : ''
                    : ''
                }
                disabled={
                  acl ? !acl.canParticipantInvite && !acl.canGuestInvite : true
                }
              />
            }
          />
        </DisabledTooltip>
      )}

      <ActionButton
        data-testid="action-bar-exit-button"
        icon={<Phone />}
        name={t('class.actionBar.exit.text')}
        tooltip={t('class.actionBar.exit.tooltip')}
        variant="destructive"
        onClick={() => {
          updateReduxState(setLivekitJoinStatus('left'));
          navigate(`/classrooms/${classroomId}`);
        }}
      />

      <FullscreenButton />
      {roleType === 'party' && <WatcherCount />}
    </div>
  );
};
