import { cloneElement, forwardRef, ReactElement, RefObject } from 'react';
import { cn } from '@/shadcn/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';

interface IActionButtonProps {
  active?: boolean;
  icon: ReactElement<React.SVGProps<SVGSVGElement>>;
  name: string;
  tooltip: string;
  variant?: 'default' | 'destructive';
  disabled?: boolean;
  onClick?: () => void;
  ref?: RefObject<HTMLButtonElement | null>;
  'data-testid'?: string;
}

export const ActionButton = forwardRef<HTMLButtonElement, IActionButtonProps>(
  (
    {
      active = true,
      icon,
      name,
      tooltip,
      variant = 'default',
      disabled,
      onClick,
      'data-testid': dataTestId,
    },
    ref,
  ) => {
    const iconStyle = 'w-5 h-5 text-primary-foreground';
    const StyledIcon = cloneElement(icon, {
      className: iconStyle,
    });

    const button = (
      <button
        className={cn(
          'disabled:opacity-50 disabled:cursor-not-allowed flex justify-center items-center gap-1.5 py-4 px-5 rounded-full text-primary-foreground max-[1000px]:landscape:p-3 max-[600px]:p-3 disabled:pointer-events-none',
          variant === 'default'
            ? active
              ? 'bg-black/30 hover:bg-black/20 disabled:hover:bg-black/30'
              : 'bg-accent hover:bg-accent/80 disabled:hover:bg-accent'
            : 'bg-accent hover:bg-accent/80 disabled:hover:bg-accent',
        )}
        disabled={disabled}
        onClick={onClick}
        ref={ref}
        data-testid={dataTestId}
      >
        {StyledIcon}
        <span className="inline text-base font-semibold text-primary-foreground max-[1200px]:hidden">
          {name}
        </span>
      </button>
    );

    return disabled ? (
      button
    ) : (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>{button}</TooltipTrigger>
          <TooltipContent side="bottom">
            <p className="text-center">{tooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  },
);

ActionButton.displayName = 'ActionButton';
