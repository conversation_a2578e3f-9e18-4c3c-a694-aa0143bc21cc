import { Maximize, Minimize } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { useTranslation } from 'react-i18next';
import { useFullscreen, usePlatform } from '@/shared';

export const FullscreenButton = () => {
  const { t } = useTranslation();
  const { supportsFs, toggle, isFullscreen } =
    useFullscreen('#main-class-area');
  const { type, os } = usePlatform();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            data-testid="action-bar-fullscreen-button"
            onClick={toggle}
            className="absolute top-1/2 -translate-y-1/2 p-2 hover:bg-white/10 rounded-full max-[600px]:hidden disabled:hidden"
            style={{
              right: 'max(env(safe-area-inset-right), .75rem)',
            }}
            disabled={
              !supportsFs ||
              (type === 'mobile' && os === 'iOS') ||
              (type === 'mobile' && os === 'macOS')
            }
          >
            {isFullscreen ? (
              <Minimize className="text-primary-foreground w-5 h-5" />
            ) : (
              <Maximize className="text-primary-foreground w-5 h-5" />
            )}
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-center">
            {isFullscreen
              ? t('class.actionBar.fullscreen.exit')
              : t('class.actionBar.fullscreen.enter')}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
