import {
  Toolt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { Eye } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { selectAllRoomUsers } from '@/features';
import { useAppSelector } from '@/shared';

export const WatcherCount = () => {
  const roomUsers = useAppSelector(selectAllRoomUsers);
  const { t } = useTranslation();

  const watchers = roomUsers.filter((user) => user.roleType === 'watcher');

  return (
    watchers.length !== 0 && (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="absolute left-5 top-1/2 -translate-y-1/2 flex justify-center items-center gap-2 max-[400px]:gap-0.5 max-[400px]:left-2">
              <Eye className="text-white w-5 h-auto aspect-squares max-[400px]:w-4" />
              <span
                data-testid="watcher-count"
                className="text-white text-md max-[400px]:text-sm"
              >
                {watchers.length}
              </span>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p data-testid="watcher-count" className="text-center">
              {t('class.watcherCount', { count: watchers.length })}
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  );
};
