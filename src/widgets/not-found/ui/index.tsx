import { Button } from '@/shadcn/components/ui/button';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router';

export const NotFound = () => {
  const { t } = useTranslation();

  return (
    <div className="p-4 absolute top-0 left-0 w-full h-full flex items-center justify-center flex-col gap-6">
      <img src="/assets/images/404.jpg" className="w-80 rounded-2xl" />
      <div className="text-primary/70 font-medium text-lg">
        {t('notFound.text')}
      </div>
      <Link to="/">
        <Button data-testid="not-found-button" size="lg">
          {t('notFound.button')}
        </Button>
      </Link>
    </div>
  );
};
