import { Slider } from '@/shadcn/components/ui/slider';
import { Minus, Play, Plus, Square, TimerIcon } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shadcn/components/ui/select';
import { useEffect, useState } from 'react';
import { Button } from '@/shadcn/components/ui/button';
import { cn } from '@/shadcn/lib/utils';
import { useTranslation } from 'react-i18next';
import {
  TimeSignature,
  TIME_SIGNATURES,
  centralAudioProcessing,
} from '@/features';
import { Sidebar, Switch, useAppSelector } from '@/shared';

export const ClassMetronome = () => {
  const { t } = useTranslation();
  const { audioContextReady } = useAppSelector((state) => state.conference);

  const [selectedSignature, setSelectedSignature] = useState<TimeSignature>(
    TimeSignature.FOUR_FOUR,
  );
  const [bpm, setBpm] = useState(120);
  const [currentBeat, setCurrentBeat] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(100);
  const [isInitialized, setIsInitialized] = useState(false);
  const [localMixing, setLocalMixing] = useState(true);

  useEffect(() => {
    if (!centralAudioProcessing.isInitialized() || !audioContextReady) return;
    const metronome = centralAudioProcessing
      .getLocalAudioMixer()
      .getMetronome();

    try {
      metronome.onBpmChanged(setBpm);
      metronome.onCurrentBeatChanged(setCurrentBeat);
      metronome.onPlayingStateChanged(setIsPlaying);
      metronome.onSignatureChanged(setSelectedSignature);
      metronome.onVolumeChanged(setVolume);
      metronome.onMixingChanged(setLocalMixing);

      setBpm(metronome.getBpm());
      setCurrentBeat(metronome.getCurrentBeat());
      setIsPlaying(metronome.getIsPlaying());
      setSelectedSignature(metronome.getSelectedSignature());
      setVolume(metronome.getVolume());

      setIsInitialized(true);
    } catch (error) {
      console.error('Failed to initialize metronome:', error);
    }

    return () => metronome.stop();
  }, [audioContextReady]);

  const increaseBpm = () => {
    if (isInitialized) {
      centralAudioProcessing.getLocalAudioMixer().getMetronome().increaseBpm();
    }
  };

  const decreaseBpm = () => {
    if (isInitialized) {
      centralAudioProcessing.getLocalAudioMixer().getMetronome().decreaseBpm();
    }
  };

  const getBeatsPerMeasure = () => {
    const [numerator] = selectedSignature.split('/');
    return Number(numerator);
  };

  const toggleMetronome = () => {
    if (isInitialized) {
      centralAudioProcessing.getLocalAudioMixer().getMetronome().toggle();
    }
  };

  const handleBpmChange = (value: number[]) => {
    if (isInitialized) {
      centralAudioProcessing
        .getLocalAudioMixer()
        .getMetronome()
        .setBpm(value[0]);
    }
  };

  const handleSignatureChange = (value: string) => {
    if (isInitialized) {
      centralAudioProcessing
        .getLocalAudioMixer()
        .getMetronome()
        .setTimeSignature(value as TimeSignature);
    }
  };

  const handleVolumeChange = (value: number[]) => {
    if (isInitialized) {
      centralAudioProcessing
        .getLocalAudioMixer()
        .getMetronome()
        .setVolume(value[0]);
    }
  };

  const handleMixingEnabled = (enabled: boolean) => {
    if (isInitialized) {
      centralAudioProcessing
        .getLocalAudioMixer()
        .getMetronome()
        .setMixingIntoLocal(enabled);
    }
  };

  if (!isInitialized) {
    return (
      <Sidebar
        disabled={false}
        title={t('class.tools.metronome.title')}
        tooltip={t('class.tools.metronome.tooltip')}
        triggerIcon={<TimerIcon />}
        contentClassName="min-[1200px]:min-w-[300px]"
      >
        <div className="w-full h-full flex items-center justify-center">
          <div className="text-white">Initializing metronome...</div>
        </div>
      </Sidebar>
    );
  }

  return (
    <Sidebar
      disabled={false}
      title={t('class.tools.metronome.title')}
      tooltip={t('class.tools.metronome.tooltip')}
      triggerIcon={<TimerIcon />}
      contentClassName="min-[1200px]:min-w-[300px]"
      data-testid="action-bar-metronome-button"
    >
      <div className="w-full h-full flex items-center justify-center">
        <div className="flex items-center justify-center flex-col gap-8 max-w-[250px] w-full">
          <div className="flex flex-col items-center justify-center gap-2">
            <h1
              className="text-white font-bold text-[2.5rem]"
              data-testid="metronome-bpm-display"
            >
              {bpm}
            </h1>
            <h2 className="text-sm text-white/70">
              {t('class.tools.metronome.bpm')}
            </h2>
          </div>

          <div
            className="flex items-center justify-center gap-2"
            data-testid="metronome-beat-indicators"
          >
            {Array.from({
              length: getBeatsPerMeasure(),
            }).map((_, index) => (
              <div
                key={index}
                data-testid={`metronome-beat-${index}`}
                className={cn(
                  'size-3 rounded-full',
                  isPlaying && currentBeat === index
                    ? index === 0
                      ? 'bg-destructive'
                      : 'bg-white/40'
                    : 'bg-white/20',
                )}
              />
            ))}
          </div>

          <div className="flex items-center justify-center gap-4">
            <div
              className="size-11 rounded-full flex items-center justify-center border border-white/30 cursor-pointer hover:bg-white/10 transition-colors"
              onClick={decreaseBpm}
              data-testid="metronome-decrease-bpm-button"
            >
              <Minus className="size-6" />
            </div>

            <Button
              className={cn(
                isPlaying
                  ? 'bg-destructive/90 hover:bg-destructive'
                  : 'bg-primary/90 hover:bg-primary',
              )}
              size="lg"
              onClick={toggleMetronome}
              data-testid="metronome-toggle-button"
            >
              {isPlaying ? <Square /> : <Play />}
              {isPlaying
                ? t('class.tools.metronome.buttons.stop')
                : t('class.tools.metronome.buttons.start')}
            </Button>

            <div
              className="size-11 rounded-full flex items-center justify-center border border-white/30 cursor-pointer hover:bg-white/10 transition-colors"
              onClick={increaseBpm}
              data-testid="metronome-increase-bpm-button"
            >
              <Plus className="size-6" />
            </div>
          </div>

          <div className="w-full flex flex-col gap-4">
            <h1 className="text-white/70 text-base font-medium">
              {t('class.tools.metronome.bpmLabel')}
            </h1>

            <Slider
              min={40}
              max={300}
              value={[bpm]}
              onValueChange={handleBpmChange}
              data-testid="metronome-bpm-slider"
            />
          </div>

          <Select
            onValueChange={handleSignatureChange}
            value={selectedSignature}
          >
            <SelectTrigger
              className="w-full border-white/20 !text-white [&>svg]:!text-white"
              data-testid="metronome-time-signature-select"
            >
              <SelectValue />
            </SelectTrigger>
            <SelectContent
              container={document.querySelector('#main-class-area')}
            >
              {TIME_SIGNATURES.map((signature, index) => (
                <SelectItem
                  key={index}
                  value={signature.value}
                  data-testid={`metronome-time-signature-${signature.value}`}
                >
                  {t(signature.label)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="w-full flex flex-col gap-4">
            <h1 className="text-white/70 text-base font-medium">
              {t('class.tools.metronome.volume')}
            </h1>
            <Slider
              min={0}
              max={300}
              value={[volume]}
              onValueChange={handleVolumeChange}
              data-testid="metronome-volume-slider"
            />
          </div>

          <Switch
            id="track-mixing"
            data-testid="metronome-mix-switch"
            title={t('class.tools.metronome.mixing.title')}
            description={t('class.tools.metronome.mixing.description')}
            value={localMixing}
            setValue={handleMixingEnabled}
          />
        </div>
      </div>
    </Sidebar>
  );
};
