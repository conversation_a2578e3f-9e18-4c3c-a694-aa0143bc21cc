import { useUser } from '@/entities';
import { Avatar, AvatarFallback } from '@/shadcn/components/ui/avatar';
import { getInitials } from '@/shared';

export const ProfileHeader = () => {
  const { user } = useUser();

  return (
    <div className="px-2 flex items-center gap-9">
      <Avatar className="size-36">
        <AvatarFallback className="bg-[#FAD1AE] text-white font-semibold text-5xl">
          {getInitials(user!.displayName || '')}
        </AvatarFallback>
      </Avatar>

      <h1 className="text-[#242426] font-bold text-[2rem]">
        {user!.displayName}
      </h1>
    </div>
  );
};
