import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateClassroomSettings } from '../api';
import { useNotifications } from '@/shared';
import { useNavigate } from 'react-router';

export const useUpdateClassroomSettings = (classroomId: string) => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { addNotification } = useNotifications();

  return useMutation({
    mutationFn: async (data: {
      title?: string;
      description?: string;
      canAnonymousJoin: boolean;
      allowInviteGuests: boolean;
      allowRecording: boolean;
      allowSharing: boolean;
      allowComments: boolean;
      allowTranscription: boolean;
      allowMaterialsUpload: boolean;
      allowMaterialsDownload: boolean;
      allowChat: boolean;
    }) => updateClassroomSettings(classroomId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classrooms'] });
      queryClient.invalidateQueries({ queryKey: ['classroom', classroomId] });
    },
    onError: (error) => {
      if (error.message === 'classroom not found') {
        navigate('/classrooms');
        addNotification?.({
          title: 'notifications.errors.classroomNotFound.title',
          description: 'notifications.errors.classroomNotFound.description',
        });
      } else
        addNotification?.({
          title: 'notifications.errors.serverError.title',
          description: 'notifications.errors.serverError.description',
        });
    },
  });
};
