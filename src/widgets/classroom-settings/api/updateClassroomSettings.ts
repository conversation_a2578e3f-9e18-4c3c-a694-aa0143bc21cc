import { api } from '@/shared';
import { Classroom } from '@/entities';
import axios from 'axios';

export const updateClassroomSettings = async (
  id: string,
  data: {
    title?: string;
    description?: string;
    canAnonymousJoin: boolean;
    allowInviteGuests: boolean;
    allowRecording: boolean;
    allowSharing: boolean;
    allowComments: boolean;
    allowTranscription: boolean;
    allowMaterialsUpload: boolean;
    allowMaterialsDownload: boolean;
    allowChat: boolean;
  },
): Promise<Classroom> => {
  try {
    const response = await api.put<Classroom>(`/classrooms/${id}`, {
      title: data.title,
      description: data.description ? data.description : ' ',
      settings: {
        defaultLanguage: 'en',
        translationLanguages: ['en', 'es', 'fr'],
        enableTranscription: true,
        enableTranslation: true,
        enableRecording: true,
        maxParties: 2,
        maxWatchers: 10,
        egressLayout: 'grid',
        canAnonymousJoin: data.canAnonymousJoin,
      },
      defaultClassSettings: {
        allowInviteGuests: data.allowInviteGuests,
        allowRecording: data.allowRecording,
        allowSharing: data.allowSharing,
        allowComments: data.allowComments,
        allowTranscription: data.allowTranscription,
        allowMaterialsUpload: data.allowMaterialsUpload,
        allowMaterialsDownload: data.allowMaterialsDownload,
        allowChat: data.allowChat,
      },
    });

    return response.data;
  } catch (error) {
    let errorMessage = 'Failed to update classroom settings!';
    if (axios.isAxiosError(error) && error.response) {
      errorMessage = error.response.data.error || errorMessage;
    }
    throw new Error(errorMessage);
  }
};
