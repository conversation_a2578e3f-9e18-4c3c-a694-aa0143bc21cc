import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api, Role } from '@/shared';
import { updateClassroomSettings } from './updateClassroomSettings';
import type { Classroom } from '@/entities';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { put: vi.fn() },
}));

describe('Update Classroom Settings API', () => {
  let mockPut: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockPut = api.put as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' && error !== null && 'response' in error,
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  describe('updateClassroomSettings', () => {
    const mockClassroom: Classroom = {
      id: 'classroom-123',
      title: 'Updated Classroom',
      description: 'Updated description',
      createdBy: 'user-456',
      creator: 'John Doe',
      status: 'active',
      settings: {
        defaultLanguage: 'en',
        translationLanguages: ['en', 'es', 'fr'],
        enableTranscription: true,
        enableTranslation: true,
        enableRecording: true,
        maxParties: 2,
        maxWatchers: 10,
        egressLayout: 'grid',
        canAnonymousJoin: true,
      },
      defaultClassSettings: {
        allowRecording: true,
        allowTranscription: true,
        allowTranslation: true,
        allowCaptions: true,
        allowSharing: true,
        allowMaterialsUpload: true,
        allowMaterialsDownload: true,
        allowChat: true,
        allowAnonymousJoin: true,
        allowInviteGuests: true,
        allowMultipleTeachers: true,
        allowMultipleStudents: true,
        allowComments: true,
      },
      participants: [],
      activeClass: 'class-789',
      createdAt: 1704067200000,
      updatedAt: 1705276800000,
      isAnonymous: false,
      activeClasses: ['class-789', 'class-101'],
    };

    const basicUpdateData = {
      canAnonymousJoin: true,
      allowInviteGuests: true,
      allowRecording: true,
      allowSharing: true,
      allowComments: true,
      allowTranscription: true,
      allowMaterialsUpload: true,
      allowMaterialsDownload: true,
      allowChat: true,
    };

    // Note: The service function doesn't handle all the interface fields
    // It only maps to a subset of defaultClassSettings

    it('should update classroom settings successfully and return updated classroom', async () => {
      mockPut.mockResolvedValueOnce({ data: mockClassroom });

      const updateData = {
        title: 'New Classroom Title',
        description: 'New classroom description',
        ...basicUpdateData,
      };

      const result = await updateClassroomSettings('classroom-123', updateData);

      expect(mockPut).toHaveBeenCalledWith('/classrooms/classroom-123', {
        title: 'New Classroom Title',
        description: 'New classroom description',
        settings: {
          defaultLanguage: 'en',
          translationLanguages: ['en', 'es', 'fr'],
          enableTranscription: true,
          enableTranslation: true,
          enableRecording: true,
          maxParties: 2,
          maxWatchers: 10,
          egressLayout: 'grid',
          canAnonymousJoin: true,
        },
        defaultClassSettings: {
          allowInviteGuests: true,
          allowRecording: true,
          allowSharing: true,
          allowComments: true,
          allowTranscription: true,
          allowMaterialsUpload: true,
          allowMaterialsDownload: true,
          allowChat: true,
        },
      });
      expect(result).toEqual(mockClassroom);
    });

    it('should work with different classroom IDs', async () => {
      const testCases = [
        'abc-123',
        'classroom-xyz-789',
        '12345',
        'special-chars_classroom',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      ];

      for (const classroomId of testCases) {
        const updatedClassroom = { ...mockClassroom, id: classroomId };
        mockPut.mockResolvedValueOnce({ data: updatedClassroom });

        const result = await updateClassroomSettings(
          classroomId,
          basicUpdateData,
        );

        expect(mockPut).toHaveBeenCalledWith(
          `/classrooms/${classroomId}`,
          expect.any(Object),
        );
        expect(result.id).toBe(classroomId);
      }
    });

    describe('title and description handling', () => {
      it('should handle title updates', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          title: 'Updated Title',
          ...basicUpdateData,
        };

        await updateClassroomSettings('classroom-123', updateData);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({ title: 'Updated Title' }),
        );
      });

      it('should handle undefined title', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          title: undefined,
          description: 'Some description',
          ...basicUpdateData,
        };

        await updateClassroomSettings('classroom-123', updateData);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({ title: undefined }),
        );
      });

      it('should handle description updates', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          description: 'Updated description',
          ...basicUpdateData,
        };

        await updateClassroomSettings('classroom-123', updateData);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({ description: 'Updated description' }),
        );
      });

      it('should set description to space when undefined', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          description: undefined,
          ...basicUpdateData,
        };

        await updateClassroomSettings('classroom-123', updateData);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({ description: ' ' }),
        );
      });

      it('should handle empty string description', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          description: '',
          ...basicUpdateData,
        };

        await updateClassroomSettings('classroom-123', updateData);

        // Empty string is falsy, so it should be replaced with ' '
        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({ description: ' ' }),
        );
      });

      it('should handle long titles and descriptions', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const longTitle = 'A'.repeat(500);
        const longDescription = 'B'.repeat(2000);

        const updateData = {
          title: longTitle,
          description: longDescription,
          ...basicUpdateData,
        };

        await updateClassroomSettings('classroom-123', updateData);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({
            title: longTitle,
            description: longDescription,
          }),
        );
      });
    });

    describe('settings object', () => {
      it('should always include default settings with canAnonymousJoin', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          ...basicUpdateData,
          canAnonymousJoin: false,
        };

        await updateClassroomSettings('classroom-123', updateData);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({
            settings: {
              defaultLanguage: 'en',
              translationLanguages: ['en', 'es', 'fr'],
              enableTranscription: true,
              enableTranslation: true,
              enableRecording: true,
              maxParties: 2,
              maxWatchers: 10,
              egressLayout: 'grid',
              canAnonymousJoin: false,
            },
          }),
        );
      });

      it('should handle canAnonymousJoin: true', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          ...basicUpdateData,
          canAnonymousJoin: true,
        };

        await updateClassroomSettings('classroom-123', updateData);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({
            settings: expect.objectContaining({ canAnonymousJoin: true }),
          }),
        );
      });

      it('should handle canAnonymousJoin: false', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          ...basicUpdateData,
          canAnonymousJoin: false,
        };

        await updateClassroomSettings('classroom-123', updateData);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({
            settings: expect.objectContaining({ canAnonymousJoin: false }),
          }),
        );
      });
    });

    describe('defaultClassSettings object', () => {
      it('should include all permission flags', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          canAnonymousJoin: true,
          allowInviteGuests: false,
          allowRecording: true,
          allowSharing: false,
          allowComments: true,
          allowTranscription: false,
          allowMaterialsUpload: true,
          allowMaterialsDownload: false,
          allowChat: true,
        };

        await updateClassroomSettings('classroom-123', updateData);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({
            defaultClassSettings: {
              allowInviteGuests: false,
              allowRecording: true,
              allowSharing: false,
              allowComments: true,
              allowTranscription: false,
              allowMaterialsUpload: true,
              allowMaterialsDownload: false,
              allowChat: true,
            },
          }),
        );
      });

      it('should handle all permissions enabled', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          canAnonymousJoin: true,
          allowInviteGuests: true,
          allowRecording: true,
          allowSharing: true,
          allowComments: true,
          allowTranscription: true,
          allowMaterialsUpload: true,
          allowMaterialsDownload: true,
          allowChat: true,
        };

        await updateClassroomSettings('classroom-123', updateData);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({
            defaultClassSettings: {
              allowInviteGuests: true,
              allowRecording: true,
              allowSharing: true,
              allowComments: true,
              allowTranscription: true,
              allowMaterialsUpload: true,
              allowMaterialsDownload: true,
              allowChat: true,
            },
          }),
        );
      });

      it('should handle all permissions disabled', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          canAnonymousJoin: false,
          allowInviteGuests: false,
          allowRecording: false,
          allowSharing: false,
          allowComments: false,
          allowTranscription: false,
          allowMaterialsUpload: false,
          allowMaterialsDownload: false,
          allowChat: false,
        };

        await updateClassroomSettings('classroom-123', updateData);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({
            defaultClassSettings: {
              allowInviteGuests: false,
              allowRecording: false,
              allowSharing: false,
              allowComments: false,
              allowTranscription: false,
              allowMaterialsUpload: false,
              allowMaterialsDownload: false,
              allowChat: false,
            },
          }),
        );
      });
    });

    describe('request body structure', () => {
      it('should send complete request with all required fields', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          title: 'Test Classroom',
          description: 'Test description',
          canAnonymousJoin: true,
          allowInviteGuests: false,
          allowRecording: true,
          allowSharing: false,
          allowComments: true,
          allowTranscription: false,
          allowMaterialsUpload: true,
          allowMaterialsDownload: false,
          allowChat: true,
        };

        await updateClassroomSettings('classroom-123', updateData);

        expect(mockPut).toHaveBeenCalledWith('/classrooms/classroom-123', {
          title: 'Test Classroom',
          description: 'Test description',
          settings: {
            defaultLanguage: 'en',
            translationLanguages: ['en', 'es', 'fr'],
            enableTranscription: true,
            enableTranslation: true,
            enableRecording: true,
            maxParties: 2,
            maxWatchers: 10,
            egressLayout: 'grid',
            canAnonymousJoin: true,
          },
          defaultClassSettings: {
            allowInviteGuests: false,
            allowRecording: true,
            allowSharing: false,
            allowComments: true,
            allowTranscription: false,
            allowMaterialsUpload: true,
            allowMaterialsDownload: false,
            allowChat: true,
          },
        });
      });

      it('should handle minimal update (only required boolean fields)', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        await updateClassroomSettings('classroom-123', basicUpdateData);

        expect(mockPut).toHaveBeenCalledWith('/classrooms/classroom-123', {
          title: undefined,
          description: ' ',
          settings: expect.any(Object),
          defaultClassSettings: expect.any(Object),
        });
      });
    });

    describe('response handling', () => {
      it('should return the complete classroom object', async () => {
        const expectedClassroom: Classroom = {
          id: 'classroom-123',
          title: 'My Classroom',
          description: 'A test classroom',
          createdBy: 'user-789',
          creator: 'Jane Smith',
          status: 'active',
          settings: {
            defaultLanguage: 'en',
            translationLanguages: ['en', 'es'],
            enableTranscription: true,
            enableTranslation: false,
            enableRecording: true,
            maxParties: 5,
            maxWatchers: 20,
            egressLayout: 'speaker',
            canAnonymousJoin: false,
          },
          defaultClassSettings: {
            allowRecording: false,
            allowTranscription: true,
            allowTranslation: false,
            allowCaptions: true,
            allowSharing: true,
            allowMaterialsUpload: false,
            allowMaterialsDownload: true,
            allowChat: false,
            allowAnonymousJoin: false,
            allowInviteGuests: true,
            allowMultipleTeachers: true,
            allowMultipleStudents: false,
            allowComments: false,
          },
          participants: [
            {
              userId: 'user-001',
              displayName: 'Test User',
              profileImage: '',
              role: 'student' as Role,
              status: 'active',
              joinedAt: 1704067200000,
              leftAt: null,
              isAnonymous: false,
              isConnected: true,
              acl: {
                canCommentOnRecordings: true,
                canDownloadMaterials: true,
                canGuestInvite: false,
                canKickParticipant: false,
                canParticipantInvite: false,
                canStartClass: false,
                canUpdateClass: false,
                canUpdateParticipant: false,
                canUploadMaterials: false,
                canViewRecordings: true,
              },
            },
          ],
          activeClass: 'class-456',
          createdAt: 1704067200000,
          updatedAt: 1705190400000,
          isAnonymous: false,
          activeClasses: ['class-456'],
        };

        mockPut.mockResolvedValueOnce({ data: expectedClassroom });

        const result = await updateClassroomSettings(
          'classroom-123',
          basicUpdateData,
        );

        expect(result).toEqual(expectedClassroom);
        expect(result).toHaveProperty('id');
        expect(result).toHaveProperty('title');
        expect(result).toHaveProperty('settings');
        expect(result).toHaveProperty('defaultClassSettings');
        expect(result).toHaveProperty('participants');
        expect(result).toHaveProperty('createdBy');
        expect(result).toHaveProperty('status');
      });

      it('should preserve all classroom properties', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const result = await updateClassroomSettings(
          'classroom-123',
          basicUpdateData,
        );

        expect(result.id).toBe('classroom-123');
        expect(result.createdAt).toBe(1704067200000);
        expect(result.updatedAt).toBe(1705276800000);
        expect(result.createdBy).toBe('user-456');
        expect(result.creator).toBe('John Doe');
        expect(result.status).toBe('active');
        expect(result.activeClass).toBe('class-789');
        expect(result.isAnonymous).toBe(false);
        expect(result.participants).toEqual([]);
        expect(result.settings.defaultLanguage).toBe('en');
        expect(result.defaultClassSettings.allowChat).toBe(true);
        expect(result.activeClasses).toEqual(['class-789', 'class-101']);
      });
    });

    describe('error handling', () => {
      it('should throw provided error message for classroom not found', async () => {
        const errorResponse = {
          response: { data: { error: 'Classroom not found' } },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassroomSettings('nonexistent-classroom', basicUpdateData),
        ).rejects.toThrow('Classroom not found');
      });

      it('should throw provided error message for insufficient permissions', async () => {
        const errorResponse = {
          response: {
            data: {
              error: 'Insufficient permissions to update classroom settings',
            },
          },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassroomSettings('classroom-123', basicUpdateData),
        ).rejects.toThrow(
          'Insufficient permissions to update classroom settings',
        );
      });

      it('should throw provided error message for invalid settings', async () => {
        const errorResponse = {
          response: { data: { error: 'Invalid classroom settings provided' } },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassroomSettings('classroom-123', basicUpdateData),
        ).rejects.toThrow('Invalid classroom settings provided');
      });

      it('should throw provided error message for validation errors', async () => {
        const errorResponse = {
          response: { data: { error: 'Title cannot be empty' } },
        };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassroomSettings('classroom-123', {
            ...basicUpdateData,
            title: '',
          }),
        ).rejects.toThrow('Title cannot be empty');
      });

      it('should throw default message when API error has no error field', async () => {
        const errorResponse = { response: { data: {} } };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassroomSettings('classroom-123', basicUpdateData),
        ).rejects.toThrow('Failed to update classroom settings!');
      });

      it('should throw default message when API error has null error', async () => {
        const errorResponse = { response: { data: { error: null } } };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassroomSettings('classroom-123', basicUpdateData),
        ).rejects.toThrow('Failed to update classroom settings!');
      });

      it('should throw default message when API error has undefined error', async () => {
        const errorResponse = { response: { data: { error: undefined } } };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassroomSettings('classroom-123', basicUpdateData),
        ).rejects.toThrow('Failed to update classroom settings!');
      });

      it('should throw default message when API error has no response', async () => {
        const errorResponse = { response: undefined };
        mockPut.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          updateClassroomSettings('classroom-123', basicUpdateData),
        ).rejects.toThrow('Failed to update classroom settings!');
      });

      it('should throw default message when error is not axios error', async () => {
        isAxiosErrorSpy.mockReturnValueOnce(false);
        mockPut.mockRejectedValueOnce(new Error('Network failure'));

        await expect(
          updateClassroomSettings('classroom-123', basicUpdateData),
        ).rejects.toThrow('Failed to update classroom settings!');
      });

      it('should handle generic error correctly', async () => {
        mockPut.mockRejectedValueOnce(new Error('Some generic error'));

        await expect(
          updateClassroomSettings('classroom-123', basicUpdateData),
        ).rejects.toThrow('Failed to update classroom settings!');
      });

      it('should handle timeout error', async () => {
        const timeoutError = {
          response: {
            data: {
              error: 'Request timeout while updating classroom settings',
            },
          },
        };
        mockPut.mockRejectedValueOnce(timeoutError as AxiosError);

        await expect(
          updateClassroomSettings('classroom-123', basicUpdateData),
        ).rejects.toThrow('Request timeout while updating classroom settings');
      });

      it('should handle server error', async () => {
        const serverError = {
          response: {
            data: { error: 'Internal server error during settings update' },
          },
        };
        mockPut.mockRejectedValueOnce(serverError as AxiosError);

        await expect(
          updateClassroomSettings('classroom-123', basicUpdateData),
        ).rejects.toThrow('Internal server error during settings update');
      });
    });

    describe('edge cases', () => {
      it('should handle special characters in title and description', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          title: 'Título con émojis 📚 and símbolos @#$%',
          description: 'Description with "quotes", \'apostrophes\', and <tags>',
          ...basicUpdateData,
        };

        await updateClassroomSettings('classroom-123', updateData);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({
            title: 'Título con émojis 📚 and símbolos @#$%',
            description:
              'Description with "quotes", \'apostrophes\', and <tags>',
          }),
        );
      });

      it('should handle whitespace-only description', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const updateData = {
          description: '   \n\t   ',
          ...basicUpdateData,
        };

        await updateClassroomSettings('classroom-123', updateData);

        // Whitespace-only string is truthy, so it should be preserved
        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({ description: '   \n\t   ' }),
        );
      });

      it('should handle multiple rapid updates', async () => {
        const updates = [
          { ...basicUpdateData, title: 'Update 1' },
          { ...basicUpdateData, title: 'Update 2' },
          { ...basicUpdateData, title: 'Update 3' },
        ];

        updates.forEach((_, index) => {
          mockPut.mockResolvedValueOnce({
            data: { ...mockClassroom, title: `Update ${index + 1}` },
          });
        });

        const results = [];
        for (const updateData of updates) {
          const result = await updateClassroomSettings(
            'classroom-123',
            updateData,
          );
          results.push(result);
        }

        expect(results).toHaveLength(3);
        expect(mockPut).toHaveBeenCalledTimes(3);
      });

      it('should handle mixed permission configurations', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const mixedUpdateData = {
          canAnonymousJoin: true,
          allowInviteGuests: false,
          allowRecording: true,
          allowSharing: false,
          allowComments: true,
          allowTranscription: false,
          allowMaterialsUpload: true,
          allowMaterialsDownload: false,
          allowChat: true,
        };

        await updateClassroomSettings('classroom-123', mixedUpdateData);

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({
            settings: expect.objectContaining({ canAnonymousJoin: true }),
            defaultClassSettings: {
              allowInviteGuests: false,
              allowRecording: true,
              allowSharing: false,
              allowComments: true,
              allowTranscription: false,
              allowMaterialsUpload: true,
              allowMaterialsDownload: false,
              allowChat: true,
            },
          }),
        );
      });
    });

    describe('business logic scenarios', () => {
      it('should handle restrictive classroom settings', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const restrictiveSettings = {
          title: 'Restricted Classroom',
          description: 'High security classroom',
          canAnonymousJoin: false,
          allowInviteGuests: false,
          allowRecording: false,
          allowSharing: false,
          allowComments: false,
          allowTranscription: false,
          allowMaterialsUpload: false,
          allowMaterialsDownload: false,
          allowChat: false,
        };

        const result = await updateClassroomSettings(
          'classroom-123',
          restrictiveSettings,
        );

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({
            settings: expect.objectContaining({ canAnonymousJoin: false }),
            defaultClassSettings: expect.objectContaining({
              allowInviteGuests: false,
              allowRecording: false,
              allowSharing: false,
              allowComments: false,
            }),
          }),
        );
        expect(result).toEqual(mockClassroom);
      });

      it('should handle open classroom settings', async () => {
        mockPut.mockResolvedValueOnce({ data: mockClassroom });

        const openSettings = {
          title: 'Open Classroom',
          description: 'Public learning space',
          canAnonymousJoin: true,
          allowInviteGuests: true,
          allowRecording: true,
          allowSharing: true,
          allowComments: true,
          allowTranscription: true,
          allowMaterialsUpload: true,
          allowMaterialsDownload: true,
          allowChat: true,
        };

        const result = await updateClassroomSettings(
          'classroom-123',
          openSettings,
        );

        expect(mockPut).toHaveBeenCalledWith(
          '/classrooms/classroom-123',
          expect.objectContaining({
            settings: expect.objectContaining({ canAnonymousJoin: true }),
            defaultClassSettings: expect.objectContaining({
              allowInviteGuests: true,
              allowRecording: true,
              allowSharing: true,
              allowComments: true,
            }),
          }),
        );
        expect(result).toEqual(mockClassroom);
      });
    });
  });
});
