import { z } from 'zod';

export const ClassroomTitleSchema = z
  .string({ error: 'globalFields.classroomName.validation.required' })
  .min(3, { error: 'globalFields.classroomName.validation.minLength' })
  .max(30, {
    error: 'globalFields.classroomName.validation.maxLength',
  });
export const ClassroomDescriptionSchema = z.string().max(200, {
  error: 'globalFields.classroomDescription.validation.maxLength',
});
