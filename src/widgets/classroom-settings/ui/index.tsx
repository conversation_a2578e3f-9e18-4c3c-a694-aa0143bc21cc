import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { useMediaQuery } from '@/shared';
import { Settings } from 'lucide-react';
import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ClassroomSettingsDialogContent } from './dialog-content';
import {
  Drawer,
  DrawerContent,
  DrawerTrigger,
} from '@/shadcn/components/ui/drawer';
import { ClassroomSettingsDrawerContent } from './drawer-content';

interface IClassroomSettingsProps {
  classroomId: string;
  classroomTitle: string;
  classroomDescription: string;
}

export const ClassroomSettings: FC<IClassroomSettingsProps> = ({
  classroomId,
  classroomTitle,
  classroomDescription,
}) => {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();
  const isDesktop = useMediaQuery('(min-width: 600px)');

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button
            data-testid="settings-dialog-button"
            variant="monochrome_outline"
            size="lg"
            className="font-bold max-[650px]:!w-full"
            type="button"
          >
            <Settings className="size-5 text-primary" />
            {t('classroom.dialogs.settings.trigger')}
          </Button>
        </DialogTrigger>

        <DialogContent 
          data-testid="classroom-settings-dialog"
          className="flex flex-col gap-4 bg-[#FFFAF1] px-6 pt-6 pb-[43px] max-[761px]:!max-w-[calc(100%-2rem)] !max-w-[729px]"
        >
          <ClassroomSettingsDialogContent
            classroomId={classroomId}
            classroomTitle={classroomTitle}
            classroomDescription={classroomDescription}
            open={open}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button
          data-testid="settings-dialog-button"
          variant="monochrome_outline"
          size="lg"
          className="font-bold max-[650px]:!w-full"
          type="button"
        >
          <Settings className="size-5 text-primary" />
          {t('classroom.dialogs.settings.trigger')}
        </Button>
      </DrawerTrigger>

      <DrawerContent 
        data-testid="classroom-settings-drawer"
        className="bg-[#FFFAF1]"
      >
        <ClassroomSettingsDrawerContent
          classroomId={classroomId}
          classroomTitle={classroomTitle}
          classroomDescription={classroomDescription}
          open={open}
        />
      </DrawerContent>
    </Drawer>
  );
};
