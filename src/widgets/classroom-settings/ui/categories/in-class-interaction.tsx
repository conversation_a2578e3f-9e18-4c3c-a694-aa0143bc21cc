import { FC } from 'react';
import { Switch } from '../switch';
import { useTranslation } from 'react-i18next';

interface IInClassInteractionProps {
  allowChat: boolean;
  setAllowChat: (value: boolean) => void;
  hideHeader?: boolean;
}

export const InClassInteraction: FC<IInClassInteractionProps> = ({
  allowChat,
  setAllowChat,
  hideHeader = false,
}) => {
  const { t } = useTranslation();

  return (
    <div data-testid="classroom-settings-in-class-interaction" className="flex flex-col gap-3">
      {!hideHeader && (
        <div data-testid="classroom-settings-in-class-interaction-header" className="flex flex-col gap-3 pb-2">
          <div className="flex flex-col">
            <h1 className="text-lg font-bold">
              {t('classroom.dialogs.settings.in-class.title')}
            </h1>
            <p className="text-primary/70 font-medium text-xs">
              {t('classroom.dialogs.settings.in-class.description')}
            </p>
          </div>

          <div className="w-full h-px bg-black/10" />
        </div>
      )}

      <div data-testid="classroom-settings-in-class-interaction-content" className="flex flex-col gap-6">
        <Switch
          title={t(
            'classroom.dialogs.settings.in-class.fields.allowChat.title',
          )}
          description={t(
            'classroom.dialogs.settings.in-class.fields.allowChat.description',
          )}
          value={allowChat}
          setValue={setAllowChat}
        />
      </div>
    </div>
  );
};
