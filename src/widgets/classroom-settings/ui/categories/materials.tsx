import { FC } from 'react';
import { Switch } from '../switch';
import { useTranslation } from 'react-i18next';

interface IMaterialsProps {
  allowMaterialsUpload: boolean;
  setAllowMaterialsUpload: (value: boolean) => void;
  allowMaterialsDownload: boolean;
  setAllowMaterialsDownload: (value: boolean) => void;
  hideHeader?: boolean;
}

export const Materials: FC<IMaterialsProps> = ({
  allowMaterialsUpload,
  setAllowMaterialsUpload,
  allowMaterialsDownload,
  setAllowMaterialsDownload,
  hideHeader = false,
}) => {
  const { t } = useTranslation();

  return (
    <div data-testid="classroom-settings-materials" className="flex flex-col gap-3">
      {!hideHeader && (
        <div data-testid="classroom-settings-materials-header" className="flex flex-col gap-3 pb-2">
          <div className="flex flex-col">
            <h1 className="text-lg font-bold">
              {t('classroom.dialogs.settings.materials.title')}
            </h1>
            <p className="text-primary/70 font-medium text-xs">
              {t('classroom.dialogs.settings.materials.description')}
            </p>
          </div>

          <div className="w-full h-px bg-black/10" />
        </div>
      )}

      <div data-testid="classroom-settings-materials-content" className="flex flex-col gap-6">
        <Switch
          title={t(
            'classroom.dialogs.settings.materials.fields.allowMaterialsUpload.title',
          )}
          description={t(
            'classroom.dialogs.settings.materials.fields.allowMaterialsUpload.description',
          )}
          value={allowMaterialsUpload}
          setValue={setAllowMaterialsUpload}
        />

        <Switch
          title={t(
            'classroom.dialogs.settings.materials.fields.allowMaterialsDownload.title',
          )}
          description={t(
            'classroom.dialogs.settings.materials.fields.allowMaterialsDownload.description',
          )}
          value={allowMaterialsDownload}
          setValue={setAllowMaterialsDownload}
        />
      </div>
    </div>
  );
};
