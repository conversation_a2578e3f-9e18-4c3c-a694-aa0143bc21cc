import { FC } from 'react';
import { Switch } from '../switch';
import { useTranslation } from 'react-i18next';

interface ITranslationProps {
  allowTranscription: boolean;
  setAllowTranscription: (value: boolean) => void;
  hideHeader?: boolean;
}

export const Translation: FC<ITranslationProps> = ({
  allowTranscription,
  setAllowTranscription,
  hideHeader = false,
}) => {
  const { t } = useTranslation();

  return (
    <div data-testid="classroom-settings-translation" className="flex flex-col gap-3">
      {!hideHeader && (
        <div data-testid="classroom-settings-translation-header" className="flex flex-col gap-3 pb-2">
          <div className="flex flex-col">
            <h1 className="text-lg font-bold">
              {t('classroom.dialogs.settings.translation.title')}
            </h1>
            <p className="text-primary/70 font-medium text-xs">
              {t('classroom.dialogs.settings.translation.description')}
            </p>
          </div>

          <div className="w-full h-px bg-black/10" />
        </div>
      )}

      <div data-testid="classroom-settings-translation-content" className="flex flex-col gap-6">
        <Switch
          title={t(
            'classroom.dialogs.settings.translation.fields.allowTranscription.title',
          )}
          description={t(
            'classroom.dialogs.settings.translation.fields.allowTranscription.description',
          )}
          value={allowTranscription}
          setValue={setAllowTranscription}
        />
      </div>
    </div>
  );
};
