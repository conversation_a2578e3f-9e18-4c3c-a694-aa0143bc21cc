import { FC } from 'react';
import { Switch } from '../switch';
import { useTranslation } from 'react-i18next';

interface IRecordingAndSharingProps {
  allowRecording: boolean;
  allowSharing: boolean;
  allowComments: boolean;
  setAllowRecording: (value: boolean) => void;
  setAllowSharing: (value: boolean) => void;
  setAllowComments: (value: boolean) => void;
  hideHeader?: boolean;
}

export const RecordingAndSharing: FC<IRecordingAndSharingProps> = ({
  allowRecording,
  allowSharing,
  allowComments,
  setAllowRecording,
  setAllowSharing,
  setAllowComments,
  hideHeader = false,
}) => {
  const { t } = useTranslation();

  return (
    <div data-testid="classroom-settings-recording-sharing" className="flex flex-col gap-3">
      {!hideHeader && (
        <div data-testid="classroom-settings-recording-sharing-header" className="flex flex-col gap-3 pb-2">
          <div className="flex flex-col">
            <h1 className="text-lg font-bold">
              {t('classroom.dialogs.settings.recording.title')}
            </h1>
            <p className="text-primary/70 font-medium text-xs">
              {t('classroom.dialogs.settings.recording.description')}
            </p>
          </div>

          <div className="w-full h-px bg-black/10" />
        </div>
      )}

      <div data-testid="classroom-settings-recording-sharing-content" className="flex flex-col gap-6">
        <Switch
          title={t(
            'classroom.dialogs.settings.recording.fields.allowRecording.title',
          )}
          description={t(
            'classroom.dialogs.settings.recording.fields.allowRecording.description',
          )}
          value={allowRecording}
          setValue={setAllowRecording}
        />

        <Switch
          title={t(
            'classroom.dialogs.settings.recording.fields.allowSharing.title',
          )}
          description={t(
            'classroom.dialogs.settings.recording.fields.allowSharing.description',
          )}
          value={allowSharing}
          setValue={setAllowSharing}
        />

        <Switch
          title={t(
            'classroom.dialogs.settings.recording.fields.allowComments.title',
          )}
          description={t(
            'classroom.dialogs.settings.recording.fields.allowComments.description',
          )}
          value={allowComments}
          setValue={setAllowComments}
        />
      </div>
    </div>
  );
};
