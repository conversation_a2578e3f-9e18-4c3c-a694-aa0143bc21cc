import { DeleteClassroomDialog } from '@/features';
import { Input } from '@/shadcn/components/ui/input';
import { Textarea } from '@/shadcn/components/ui/textarea';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ClassroomDescriptionSchema, ClassroomTitleSchema } from '../../model';

interface IGeneralProps {
  id: string;
  title: string;
  description: string;
  setTitle: (title: string) => void;
  setDescription: (description: string) => void;
  hideHeader?: boolean;
}

export const General = ({
  id,
  title,
  description,
  setTitle,
  setDescription,
  hideHeader = false,
}: IGeneralProps) => {
  const { t } = useTranslation();
  const [errors, setErrors] = useState<{
    title?: string;
    description?: string;
  }>({});

  useEffect(() => {
    const result = ClassroomTitleSchema.safeParse(title);
    setErrors((prev) => ({
      ...prev,
      title: result.success ? undefined : result.error.issues[0]?.message,
    }));
  }, [title]);

  useEffect(() => {
    const result = ClassroomDescriptionSchema.safeParse(description);
    setErrors((prev) => ({
      ...prev,
      description: result.success ? undefined : result.error.issues[0]?.message,
    }));
  }, [description]);

  return (
    <div data-testid="classroom-settings-general" className="flex flex-col gap-3">
      {!hideHeader && (
        <div data-testid="classroom-settings-general-header" className="flex flex-col gap-3 pb-2">
          <div className="flex flex-col">
            <h1 className="text-lg font-bold">
              {t('classroom.dialogs.settings.general.title')}
            </h1>
            <p className="text-primary/70 font-medium text-xs">
              {t('classroom.dialogs.settings.general.description')}
            </p>
          </div>

          <div className="w-full h-px bg-black/10" />
        </div>
      )}

      <form data-testid="classroom-settings-general-form" className="flex flex-col gap-6">
        <div data-testid="classroom-settings-title-field" className="flex flex-col gap-2">
          <p className="text-xs text-card-foreground/80 px-1">
            {t('globalFields.classroomName.label')}
          </p>
          <Input
            data-testid="classroom-settings-title-input"
            id="title"
            placeholder={t('globalFields.classroomName.label')}
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className={
              errors.title ? 'border-destructive' : 'border-primary/30'
            }
          />
          {errors.title && (
            <p data-testid="classroom-settings-title-error" className="text-xs text-destructive px-1">{t(errors.title)}</p>
          )}
        </div>

        <div data-testid="classroom-settings-description-field" className="flex flex-col gap-2">
          <p className="text-xs text-card-foreground/80 px-1">
            {t('globalFields.classroomDescription.label')}
          </p>
          <Textarea
            data-testid="classroom-settings-description-input"
            id="description"
            rows={3}
            placeholder={t('globalFields.classroomDescription.label')}
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className={`${errors.description ? 'border-destructive' : 'border-primary/30'} scrollbar resize-none`}
          />
          {errors.description && (
            <p data-testid="classroom-settings-description-error" className="text-xs text-destructive px-1">
              {t(errors.description)}
            </p>
          )}
        </div>

        <DeleteClassroomDialog id={id} />
      </form>
    </div>
  );
};
