import { FC } from 'react';
import { Switch } from '../switch';
import { useTranslation } from 'react-i18next';

interface IAccessAndParticipationProps {
  canAnonymousJoin: boolean;
  allowInviteGuests: boolean;
  setCanAnonymousJoin: (value: boolean) => void;
  setAllowInviteGuests: (value: boolean) => void;
  hideHeader?: boolean;
}

export const AccessAndParticipation: FC<IAccessAndParticipationProps> = ({
  canAnonymousJoin,
  allowInviteGuests,
  setCanAnonymousJoin,
  setAllowInviteGuests,
  hideHeader = false,
}) => {
  const { t } = useTranslation();

  return (
    <div data-testid="classroom-settings-access-participation" className="flex flex-col gap-3">
      {!hideHeader && (
        <div data-testid="classroom-settings-access-participation-header" className="flex flex-col gap-3 pb-2">
          <div className="flex flex-col">
            <h1 className="text-lg font-bold">
              {t('classroom.dialogs.settings.access.title')}
            </h1>
            <p className="text-primary/70 font-medium text-xs">
              {t('classroom.dialogs.settings.access.description')}
            </p>
          </div>

          <div className="w-full h-px bg-black/10" />
        </div>
      )}

      <div data-testid="classroom-settings-access-participation-content" className="flex flex-col gap-6">
        <Switch
          title={t(
            'classroom.dialogs.settings.access.fields.canAnonymousJoin.title',
          )}
          description={t(
            'classroom.dialogs.settings.access.fields.canAnonymousJoin.description',
          )}
          value={canAnonymousJoin}
          setValue={setCanAnonymousJoin}
        />

        <Switch
          title={t(
            'classroom.dialogs.settings.access.fields.allowInviteGuests.title',
          )}
          description={t(
            'classroom.dialogs.settings.access.fields.allowInviteGuests.description',
          )}
          value={allowInviteGuests}
          setValue={setAllowInviteGuests}
        />
      </div>
    </div>
  );
};
