import { Switch as ShadcnSwitch } from '@/shadcn/components/ui/switch';
import { FC } from 'react';

interface ISwitchProps {
  title: string;
  description: string;
  value: boolean;
  setValue: (value: boolean) => void;
}

export const Switch: FC<ISwitchProps> = ({
  title,
  description,
  value,
  setValue,
}) => {
  return (
    <div data-testid="classroom-settings-switch" className="flex flex-col w-full">
      <div className="flex items-center justify-between gap-2">
        <h1 className="text-primary font-semibold text-sm">{title}</h1>
        <ShadcnSwitch
          data-testid="classroom-settings-switch-toggle"
          checked={value ?? true}
          onCheckedChange={(value) => setValue?.(value as boolean)}
        />
      </div>
      <p className="font-medium text-xs text-primary/70">{description}</p>
    </div>
  );
};
