import { FC, useEffect, useState } from 'react';
import { useUpdateClassroomSettings } from '../hooks';
import { useClassroomSettings } from '@/shared';
import {
  DrawerClose,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@/shadcn/components/ui/drawer';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/shadcn/components/ui/accordion';
import { AccessAndParticipation } from './categories/access-and-participation';
import { RecordingAndSharing } from './categories/recording-and-sharing';
import { Translation } from './categories/translation';
import { Materials } from './categories/materials';
import { InClassInteraction } from './categories/in-class-interaction';
import { Button } from '@/shadcn/components/ui/button';
import { General } from './categories/general';
import { useTranslation } from 'react-i18next';

interface IClassroomSettingsDrawerContentProps {
  classroomId: string;
  classroomTitle: string;
  classroomDescription: string;
  open: boolean;
}

export const ClassroomSettingsDrawerContent: FC<
  IClassroomSettingsDrawerContentProps
> = ({ classroomId, classroomTitle, classroomDescription, open }) => {
  const { mutate: updateClassroomSettings } =
    useUpdateClassroomSettings(classroomId);
  const { t } = useTranslation();
  const classroomSettings = useClassroomSettings();

  const [title, setTitle] = useState(classroomTitle);
  const [description, setDescription] = useState(classroomDescription.trim());
  const [canAnonymousJoin, setCanAnonymousJoin] = useState(
    classroomSettings ? classroomSettings.canAnonymousJoin : false,
  );
  const [allowInviteGuests, setAllowInviteGuests] = useState(
    classroomSettings ? classroomSettings.allowInviteGuests : false,
  );
  const [allowRecording, setAllowRecording] = useState(
    classroomSettings ? classroomSettings.allowRecording : false,
  );
  const [allowSharing, setAllowSharing] = useState(
    classroomSettings ? classroomSettings.allowSharing : false,
  );
  const [allowComments, setAllowComments] = useState(
    classroomSettings ? classroomSettings.allowComments : false,
  );
  const [allowTranscription, setAllowTranscription] = useState(
    classroomSettings ? classroomSettings.allowTranscription : false,
  );
  const [allowMaterialsUpload, setAllowMaterialsUpload] = useState(
    classroomSettings ? classroomSettings.allowMaterialsUpload : false,
  );
  const [allowMaterialsDownload, setAllowMaterialsDownload] = useState(
    classroomSettings ? classroomSettings.allowMaterialsDownload : false,
  );
  const [allowChat, setAllowChat] = useState(
    classroomSettings ? classroomSettings.allowChat : false,
  );

  useEffect(() => {
    if (open) {
      setTitle(classroomTitle);
      setDescription(classroomDescription.trim());
    }

    if (!open) {
      updateClassroomSettings({
        title: title.length >= 3 && title.length <= 30 ? title : undefined,
        description: description.length <= 200 ? description : undefined,
        canAnonymousJoin,
        allowInviteGuests,
        allowRecording,
        allowSharing,
        allowComments,
        allowTranscription,
        allowMaterialsUpload,
        allowMaterialsDownload,
        allowChat,
      });
    }
  }, [open]);

  return (
    <>
      <div data-testid="classroom-settings-drawer-content" className="px-4 pb-[90px]">
        <DrawerHeader data-testid="classroom-settings-drawer-header" className="px-0 pt-4">
          <DrawerTitle className="text-left text-xl font-bold">
            {t('classroom.dialogs.settings.title')}
          </DrawerTitle>
          <DrawerDescription />
        </DrawerHeader>

        <Accordion
          data-testid="classroom-settings-accordion"
          type="single"
          collapsible
          className="bg-[#FFFDFA] rounded-lg shadow-[0_0_12px_0_rgba(88,63,52,0.04)] flex flex-col h-fit gap-2"
        >
          <AccordionItem
            data-testid="classroom-settings-accordion-general"
            value="general"
            className="rounded-lg shadow-[0,0,12px,0,rgba(88,63,52,0.04)] bg-white border-0"
          >
            <AccordionTrigger className="font-semibold text-sm text-primary p-3 leading-7">
              {t('classroom.dialogs.settings.general.title')}
            </AccordionTrigger>
            <AccordionContent className="flex flex-col gap-4 text-balance">
              <div className="px-3">
                <General
                  hideHeader
                  id={classroomId}
                  title={title}
                  description={description}
                  setTitle={setTitle}
                  setDescription={setDescription}
                />
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem
            data-testid="classroom-settings-accordion-access"
            value="access"
            className="rounded-lg shadow-[0,0,12px,0,rgba(88,63,52,0.04)] bg-white border-0"
          >
            <AccordionTrigger className="font-semibold text-sm text-primary p-3 leading-7">
              {t('classroom.dialogs.settings.access.title')}
            </AccordionTrigger>
            <AccordionContent className="flex flex-col gap-4 text-balance">
              <div className="px-3">
                <AccessAndParticipation
                  canAnonymousJoin={canAnonymousJoin}
                  allowInviteGuests={allowInviteGuests}
                  setCanAnonymousJoin={setCanAnonymousJoin}
                  setAllowInviteGuests={setAllowInviteGuests}
                  hideHeader
                />
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem
            data-testid="classroom-settings-accordion-recording"
            value="recording"
            className="rounded-lg shadow-[0,0,12px,0,rgba(88,63,52,0.04)] bg-white border-0"
          >
            <AccordionTrigger className="font-semibold text-sm text-primary p-3 leading-7">
              {t('classroom.dialogs.settings.recording.title')}
            </AccordionTrigger>
            <AccordionContent className="flex flex-col gap-4 text-balance">
              <div className="px-3">
                <RecordingAndSharing
                  allowRecording={allowRecording}
                  allowSharing={allowSharing}
                  allowComments={allowComments}
                  setAllowRecording={setAllowRecording}
                  setAllowSharing={setAllowSharing}
                  setAllowComments={setAllowComments}
                  hideHeader
                />
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem
            data-testid="classroom-settings-accordion-translation"
            value="translation"
            className="rounded-lg shadow-[0,0,12px,0,rgba(88,63,52,0.04)] bg-white border-0"
          >
            <AccordionTrigger className="font-semibold text-sm text-primary p-3 leading-7">
              {t('classroom.dialogs.settings.translation.title')}
            </AccordionTrigger>
            <AccordionContent className="flex flex-col gap-4 text-balance">
              <div className="px-3">
                <Translation
                  allowTranscription={allowTranscription}
                  setAllowTranscription={setAllowTranscription}
                  hideHeader
                />
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem
            data-testid="classroom-settings-accordion-materials"
            value="materials"
            className="rounded-lg shadow-[0,0,12px,0,rgba(88,63,52,0.04)] bg-white border-0"
          >
            <AccordionTrigger className="font-semibold text-sm text-primary p-3 leading-7">
              {t('classroom.dialogs.settings.materials.title')}
            </AccordionTrigger>
            <AccordionContent className="flex flex-col gap-4 text-balance">
              <div className="px-3">
                <Materials
                  allowMaterialsUpload={allowMaterialsUpload}
                  setAllowMaterialsUpload={setAllowMaterialsUpload}
                  allowMaterialsDownload={allowMaterialsDownload}
                  setAllowMaterialsDownload={setAllowMaterialsDownload}
                  hideHeader
                />
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem
            data-testid="classroom-settings-accordion-inclass"
            value="inclass"
            className="rounded-lg shadow-[0,0,12px,0,rgba(88,63,52,0.04)] bg-white border-0"
          >
            <AccordionTrigger className="font-semibold text-sm text-primary p-3 leading-7">
              {t('classroom.dialogs.settings.in-class.title')}
            </AccordionTrigger>
            <AccordionContent className="flex flex-col gap-4 text-balance">
              <div className="px-3">
                <InClassInteraction
                  allowChat={allowChat}
                  setAllowChat={setAllowChat}
                  hideHeader
                />
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      <DrawerFooter data-testid="classroom-settings-drawer-footer" className="fixed w-full bottom-0 p-5 bg-[#FFFDFA] shadow-[0,0,12px,0,rgba(88,63,52,0.18)]">
        <DrawerClose asChild>
          <Button data-testid="classroom-settings-save-button" size="lg" className="w-full !rounded-md">
            {t('classroom.dialogs.settings.buttons.save')}
          </Button>
        </DrawerClose>
      </DrawerFooter>
    </>
  );
};
