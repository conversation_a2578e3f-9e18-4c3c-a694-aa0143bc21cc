import { FC, useEffect, useState } from 'react';
import { useUpdateClassroomSettings } from '../hooks';
import { useClassroomSettings } from '@/shared';
import { General } from './categories/general';
import { AccessAndParticipation } from './categories/access-and-participation';
import { RecordingAndSharing } from './categories/recording-and-sharing';
import { Translation } from './categories/translation';
import { Materials } from './categories/materials';
import { InClassInteraction } from './categories/in-class-interaction';
import {
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shadcn/components/ui/dialog';
import { cn } from '@/shadcn/lib/utils';
import { useTranslation } from 'react-i18next';

interface IClassroomSettingsDialogContentProps {
  classroomId: string;
  classroomTitle: string;
  classroomDescription: string;
  open: boolean;
}

export const ClassroomSettingsDialogContent: FC<
  IClassroomSettingsDialogContentProps
> = ({ classroomId, classroomTitle, classroomDescription, open }) => {
  const { t } = useTranslation();

  const categories = [
    t('classroom.dialogs.settings.categories.general'),
    t('classroom.dialogs.settings.categories.access'),
    t('classroom.dialogs.settings.categories.recording'),
    t('classroom.dialogs.settings.categories.translation'),
    t('classroom.dialogs.settings.categories.materials'),
    t('classroom.dialogs.settings.categories.in-class'),
  ];

  const [activeCategory, setActiveCategory] = useState(0);
  const { mutate: updateClassroomSettings } =
    useUpdateClassroomSettings(classroomId);
  const classroomSettings = useClassroomSettings();

  const [title, setTitle] = useState(classroomTitle);
  const [description, setDescription] = useState(classroomDescription.trim());
  const [canAnonymousJoin, setCanAnonymousJoin] = useState(
    classroomSettings ? classroomSettings.canAnonymousJoin : false,
  );
  const [allowInviteGuests, setAllowInviteGuests] = useState(
    classroomSettings ? classroomSettings.allowInviteGuests : false,
  );
  const [allowRecording, setAllowRecording] = useState(
    classroomSettings ? classroomSettings.allowRecording : false,
  );
  const [allowSharing, setAllowSharing] = useState(
    classroomSettings ? classroomSettings.allowSharing : false,
  );
  const [allowComments, setAllowComments] = useState(
    classroomSettings ? classroomSettings.allowComments : false,
  );
  const [allowTranscription, setAllowTranscription] = useState(
    classroomSettings ? classroomSettings.allowTranscription : false,
  );
  const [allowMaterialsUpload, setAllowMaterialsUpload] = useState(
    classroomSettings ? classroomSettings.allowMaterialsUpload : false,
  );
  const [allowMaterialsDownload, setAllowMaterialsDownload] = useState(
    classroomSettings ? classroomSettings.allowMaterialsDownload : false,
  );
  const [allowChat, setAllowChat] = useState(
    classroomSettings ? classroomSettings.allowChat : false,
  );

  const getCurrentCategory = () => {
    switch (activeCategory) {
      case 0:
        return (
          <General
            id={classroomId}
            title={title}
            description={description}
            setTitle={setTitle}
            setDescription={setDescription}
          />
        );
      case 1:
        return (
          <AccessAndParticipation
            canAnonymousJoin={canAnonymousJoin}
            allowInviteGuests={allowInviteGuests}
            setCanAnonymousJoin={setCanAnonymousJoin}
            setAllowInviteGuests={setAllowInviteGuests}
          />
        );
      case 2:
        return (
          <RecordingAndSharing
            allowRecording={allowRecording}
            allowSharing={allowSharing}
            allowComments={allowComments}
            setAllowRecording={setAllowRecording}
            setAllowSharing={setAllowSharing}
            setAllowComments={setAllowComments}
          />
        );
      case 3:
        return (
          <Translation
            allowTranscription={allowTranscription}
            setAllowTranscription={setAllowTranscription}
          />
        );
      case 4:
        return (
          <Materials
            allowMaterialsUpload={allowMaterialsUpload}
            setAllowMaterialsUpload={setAllowMaterialsUpload}
            allowMaterialsDownload={allowMaterialsDownload}
            setAllowMaterialsDownload={setAllowMaterialsDownload}
          />
        );
      case 5:
        return (
          <InClassInteraction
            allowChat={allowChat}
            setAllowChat={setAllowChat}
          />
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    if (open) {
      setTitle(classroomTitle);
      setDescription(classroomDescription.trim());
    }

    if (!open) {
      updateClassroomSettings({
        title: title.length >= 3 && title.length <= 30 ? title : undefined,
        description: description.length <= 200 ? description : undefined,
        canAnonymousJoin,
        allowInviteGuests,
        allowRecording,
        allowSharing,
        allowComments,
        allowTranscription,
        allowMaterialsUpload,
        allowMaterialsDownload,
        allowChat,
      });
    }
  }, [open]);

  return (
    <>
      <DialogHeader data-testid="classroom-settings-dialog-header" className="gap-0">
        <DialogTitle className="text-left">
          {t('classroom.dialogs.settings.title')}
        </DialogTitle>
        <DialogDescription />
      </DialogHeader>

      <div data-testid="classroom-settings-content" className="grid grid-cols-[auto_1fr] gap-3">
        <div data-testid="classroom-settings-categories" className="bg-[#FFFDFA] rounded-3xl shadow-[0_0_12px_0_rgba(88,63,52,0.04)] flex flex-col gap-1 px-2 pt-2 pb-8 h-fit">
          {categories.map((category, index) => (
            <div
              key={index}
              data-testid={`classroom-settings-category-${index}`}
              className={cn(
                'py-2 px-3 rounded-lg text-primary text-sm font-medium transition-all duration-300 cursor-pointer',
                activeCategory === index
                  ? 'bg-[#FFEDE3] text-accent'
                  : 'text-primary hover:bg-[#ffede384]',
              )}
              onClick={() => setActiveCategory(index)}
            >
              {category}
            </div>
          ))}
        </div>

        <div data-testid="classroom-settings-category-content" className="bg-[#FFFDFA] rounded-3xl shadow-[0_0_12px_0_rgba(88,63,52,0.04)] flex flex-col gap-1 pl-5 pr-3 pt-4 pb-8 h-fit">
          {getCurrentCategory()}
        </div>
      </div>
    </>
  );
};
