import { User } from 'lucide-react';
import { Button } from '@main/ui-kit';

export const ProfileDetails = () => {
  return (
    <div className="w-full py-14 px-[60px] bg-white rounded-4xl shadow-[0_0_12px_0_rgba(88,63,52,0.18)] flex flex-col gap-7">
      <Button
        icon={User}
        size="medium"
        label="Test Button"
        variant={'monochrome-outline'}
        disabled
      />
      <div className="flex items-center gap-2">
        <User className="size-6 text-[#434347]" strokeWidth={2} />
        <h1 className="text-[#242426] text-2xl font-bold">Account details</h1>
      </div>
    </div>
  );
};
