import { Header } from './header';
import { Participants } from './participants';
import { Summary } from './summary';
import { useLayoutEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/shadcn/components/ui/button';
import { ParticipantsSkeleton } from './fallbacks/participants';
import { PlayerSkeleton } from './fallbacks/player';
import { useClass } from '@/entities';
import { Player } from './player';
import { Feedback } from './feedback';
import { KeyMoments } from './key-moments';
import { PlaybackManager } from '@/features';

interface IContentProps {
  id: string;
  classId: string;
  setShow: (show: boolean) => void;
}

export const Content = ({ id, classId, setShow }: IContentProps) => {
  const { t } = useTranslation();
  const { data, isError, isPending } = useClass(classId, id);

  const playbackManagerRef = useRef<PlaybackManager | null>(null);

  const [showPlayer, setShowPlayer] = useState(true);
  const [participant, setParticipant] = useState<string | null>(null);
  const [activeParticipants, setActiveParticipants] = useState<string[]>([]);

  useLayoutEffect(() => {
    if (!isError && data && data.recordings && data.recordings.length > 0) {
      const firstRecording = data.recordings[0];
      (async () => {
        playbackManagerRef.current = await PlaybackManager.create(
          firstRecording,
          {
            onParticipantSelected: setParticipant,
            onShowPlayer: setShowPlayer,
            onActiveParticipantsChange: setActiveParticipants,
          },
        );
      })();
    }

    return () => {
      if (playbackManagerRef.current) {
        (async () => {
          await playbackManagerRef.current!.destroy();
        })();
      }
    };
  }, [data]);

  return (
    <div className="flex flex-col overflow-y-auto themed-scrollbar max-h-[70dvh] max-[900px]:landscape:overflow-hidden max-[900px]:landscape:max-h-[100dvh] max-[900px]:max-h-none h-full">
      {data && <Header title={data.title} createdAt={data.createdAt} />}

      {!isPending ? (
        data && data.recordings && data.recordings.length > 0 && showPlayer ? (
          <Player playbackManager={playbackManagerRef.current} />
        ) : (
          <div className="w-fit max-[900px]:landscape:w-full flex flex-col gap-4 items-center justify-center max-[900px]:landscape:h-[100dvh]">
            <h1 className="px-6 pt-6 text-lg text-primary font-semibold">
              {t('classroom.dialogs.history.noRecording')}
            </h1>
            <Button
              className="hidden max-[900px]:landscape:inline-flex"
              size="lg"
              onClick={() => setShow(false)}
            >
              Close
            </Button>
          </div>
        )
      ) : (
        <PlayerSkeleton />
      )}

      <div className="flex flex-col gap-6 p-6 max-[900px]:landscape:hidden">
        {!isPending ? (
          data &&
          data.participants && (
            <Participants
              participants={data.participants}
              selectedParticipant={participant}
              activeParticipants={activeParticipants}
              onParticipantClick={(id) => {
                if (!playbackManagerRef.current) return;
                playbackManagerRef.current.setVideoElement(id);

                if (!playbackManagerRef.current.getIsPlaying()) {
                  (async () => {
                    await playbackManagerRef.current!.playAll();
                  })();
                }
              }}
            />
          )
        ) : (
          <ParticipantsSkeleton count={6} />
        )}

        <KeyMoments
          playbackManager={playbackManagerRef.current}
          keyMoments={data?.keyMoments}
        />
        <Summary text={data?.summary} />
        <Feedback text={data?.feedback} />

        {/* <Transcript seekTo={seekTo} /> */}
      </div>
    </div>
  );
};
