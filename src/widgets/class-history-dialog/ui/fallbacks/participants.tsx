import { FC, useState, useEffect } from 'react';
import { Skeleton } from '@/shadcn/components/ui/skeleton';

interface IParticipantsSkeletonProps {
  count?: number;
}

export const ParticipantsSkeleton: FC<IParticipantsSkeletonProps> = ({
  count,
}) => {
  const [cols, setCols] = useState(6);

  useEffect(() => {
    function updateCols() {
      const w = window.innerWidth;
      if (w < 470) {
        setCols(2);
      } else if (w < 600) {
        setCols(3);
      } else if (w < 800) {
        setCols(4);
      } else if (w < 950) {
        setCols(5);
      } else {
        setCols(6);
      }
    }

    updateCols();
    window.addEventListener('resize', updateCols);
    return () => window.removeEventListener('resize', updateCols);
  }, []);

  const itemsCount = count ?? cols * 2;
  const items = Array.from({ length: itemsCount });

  return (
    <div className="flex flex-col gap-3">
      <div className="h-6 w-48">
        <Skeleton />
      </div>

      <div
        className="grid gap-4"
        style={{ gridTemplateColumns: `repeat(${cols}, minmax(0, 1fr))` }}
      >
        {items.map((_, index) => (
          <div
            key={index}
            className="w-full rounded-xl overflow-hidden flex flex-col bg-card border border-primary/10"
          >
            <div className="relative">
              <div className="flex items-center justify-center w-full aspect-square">
                <Skeleton className="w-full h-full" />
              </div>
            </div>

            <div className="px-4 pb-4 pt-2 flex flex-col gap-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
