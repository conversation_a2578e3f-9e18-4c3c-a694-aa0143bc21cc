import { ClassParticipant } from '@/entities';
import { getInitials } from '@/shared';
import { Play } from 'lucide-react';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/shadcn/lib/utils';

interface IParticipantsProps {
  participants: ClassParticipant[];
  selectedParticipant: string | null;
  activeParticipants: string[];
  onParticipantClick: (id: string) => void;
}

export const Participants: FC<IParticipantsProps> = ({
  participants,
  selectedParticipant,
  activeParticipants,
  onParticipantClick,
}) => {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col gap-3">
      <h1
        data-testid="history-dialog-participants-title"
        className="text-xl text-primary font-bold"
      >
        {t('classroom.dialogs.history.participants.title')}
      </h1>
      <div className="grid gap-4 grid-cols-6 max-[950px]:grid-cols-5 max-[800px]:grid-cols-4 max-[600px]:grid-cols-3 max-[470px]:grid-cols-2">
        {participants.map((item, index) => {
          const disabled = !activeParticipants.includes(item.userId);
          return (
            <button
              key={index}
              className={cn(
                'w-full rounded-xl overflow-hidden flex flex-col bg-card border border-primary/10 duration-300 transition-colors hover:border-primary/20 group disabled:opacity-50',
                selectedParticipant &&
                  selectedParticipant === item.userId &&
                  'border-accent/50 border-2 hover:border-accent/70',
              )}
              disabled={disabled}
              onClick={async () => onParticipantClick(item.userId)}
            >
              <div className="relative">
                <div className="flex items-center justify-center w-full aspect-square bg-orange-200 text-2xl text-primary font-bold">
                  {getInitials(item.displayName)}
                </div>
                <div
                  className={cn(
                    'size-10 rounded-full bg-card absolute top-0 right-0 left-0 bottom-0 m-auto flex items-center justify-center opacity-0 duration-300 transition-opacity',
                    !disabled && 'group-hover:opacity-100',
                  )}
                >
                  <Play className="size-4 text-primary" />
                </div>
              </div>

              <div className="px-4 pb-4 pt-2 flex flex-col items-start text-start justify-between h-full">
                <h1
                  data-testid="history-dialog-participant-name"
                  className="text-primary/80 font-bold text-base break-all"
                >
                  {item.displayName}
                </h1>
                <h3
                  data-testid="history-dialog-participant-role"
                  className="text-sm font-medium text-primary/60 capitalize"
                >
                  {t(
                    `classroom.dialogs.history.participants.roles.${item.role || 'guest'}`,
                  )}
                </h3>
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
};
