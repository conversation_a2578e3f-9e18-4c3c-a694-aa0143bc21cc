import { PlaybackManager } from '@/features';
import { Link2 } from 'lucide-react';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';

interface IKeyMomentsProps {
  playbackManager: PlaybackManager | null;
  keyMoments:
    | { timestamp: string; description: Record<string, string> }[]
    | undefined;
}

export const KeyMoments: FC<IKeyMomentsProps> = ({
  playbackManager,
  keyMoments,
}: IKeyMomentsProps) => {
  const { t, i18n } = useTranslation();

  // Convert absolute ISO timestamp → relative mm:ss
  const formatRelativeTime = (
    timestamp: string,
  ): { seconds: number; label: string } => {
    if (!playbackManager) return { seconds: 0, label: '00:00' };

    const start = playbackManager.getStartTime(); // number (epoch ms)
    const current = new Date(timestamp).getTime();
    const diffSeconds = Math.max(0, Math.floor((current - start) / 1000));

    const minutes = Math.floor(diffSeconds / 60);
    const seconds = diffSeconds % 60;

    return {
      seconds: diffSeconds,
      label: `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`,
    };
  };

  const handleSeek = async (seconds: number) => {
    if (playbackManager) {
      await playbackManager.seekAll(seconds);
      const playerContainer = document.getElementById('video-player-container');
      if (playerContainer) {
        playerContainer.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
    }
  };

  return (
    <div className="flex flex-col gap-3">
      <h1 className="text-xl text-primary font-bold">
        {t('classroom.dialogs.history.keyMoments.title')}
      </h1>
      <div className="flex flex-col">
        {keyMoments && keyMoments.length > 0 ? (
          keyMoments.map((m, index) => {
            const { seconds, label } = formatRelativeTime(m.timestamp);

            // Pick description in current language, or fallback to first available
            const desc =
              m.description[i18n.language as keyof typeof m.description] ??
              Object.values(m.description)[0] ??
              '';

            return (
              <div
                className="grid grid-cols-[auto_1fr] gap-4 first:border-t-0 border-t border-t-primary/20 py-1"
                key={index}
              >
                <div
                  className="flex items-center gap-2 cursor-pointer"
                  onClick={async () => await handleSeek(seconds)}
                >
                  <Link2 className="size-4 text-primary" />
                  <h4 className="text-primary font-bold text-sm">
                    {t('classroom.dialogs.history.keyMoments.time', {
                      time: label,
                    })}
                  </h4>
                </div>
                <h3 className="text-primary/80 text-sm">
                  {desc
                    ? t('classroom.dialogs.history.keyMoments.description', {
                        description: desc,
                      })
                    : t('classroom.dialogs.history.keyMoments.noKeyMoments')}
                </h3>
              </div>
            );
          })
        ) : (
          <h3 className="text-primary/80 text-sm">
            {t('classroom.dialogs.history.keyMoments.noKeyMoments')}
          </h3>
        )}
      </div>
    </div>
  );
};
