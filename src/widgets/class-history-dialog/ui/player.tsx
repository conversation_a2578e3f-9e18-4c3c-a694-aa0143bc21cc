import { FC, useState, useEffect, useRef } from 'react';
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  Minimize,
} from 'lucide-react';
import { cn } from '@/shadcn/lib/utils';
import { useFullscreen } from '@/shared';
import { PlaybackManager } from '@/features';

interface IPlayerProps {
  playbackManager: PlaybackManager | null;
}

export const Player: FC<IPlayerProps> = ({ playbackManager }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const {
    isFullscreen,
    supportsFs,
    toggle: toggleFullscreen,
  } = useFullscreen('#video-player-container');

  // Auto-hide controls state
  const [showControls, setShowControls] = useState(true);
  const [isMobileLandscape, setIsMobileLandscape] = useState(false);
  const [isOverlayMode, setIsOverlayMode] = useState(false);

  const progressRef = useRef<HTMLDivElement>(null);
  const playerContainerRef = useRef<HTMLDivElement>(null);
  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const hideControlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Detect mobile landscape and overlay conditions
  useEffect(() => {
    const checkLayout = () => {
      const isMobile = /Mobi|Android|iPhone|iPad|iPod/i.test(
        navigator.userAgent,
      );
      const isLandscape = window.innerWidth > window.innerHeight;
      const isSmallHeight = window.innerHeight < 500; // Less than 500px height

      const mobileLandscape = isMobile && isLandscape;
      const needsOverlay = mobileLandscape || isFullscreen || isSmallHeight;

      setIsMobileLandscape(mobileLandscape);
      setIsOverlayMode(needsOverlay);

      // Auto-hide controls in overlay mode
      if (needsOverlay) {
        scheduleControlsHide();
      } else {
        setShowControls(true);
        clearControlsTimeout();
      }
    };

    checkLayout();
    window.addEventListener('resize', checkLayout);
    window.addEventListener('orientationchange', checkLayout);

    return () => {
      window.removeEventListener('resize', checkLayout);
      window.removeEventListener('orientationchange', checkLayout);
    };
  }, [isFullscreen]);

  // Schedule controls to hide after delay
  const scheduleControlsHide = () => {
    clearControlsTimeout();

    if (isOverlayMode && isPlaying) {
      hideControlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 3000); // Hide after 3 seconds of inactivity
    }
  };

  // Clear hide timeout
  const clearControlsTimeout = () => {
    if (hideControlsTimeoutRef.current) {
      clearTimeout(hideControlsTimeoutRef.current);
      hideControlsTimeoutRef.current = null;
    }
  };

  // Show controls and schedule hide
  const showControlsTemporarily = () => {
    setShowControls(true);
    scheduleControlsHide();
  };

  // Handle user interaction (tap/click to show controls)
  const handlePlayerInteraction = () => {
    if (isOverlayMode) {
      if (!showControls) {
        showControlsTemporarily();
      }
    }
  };

  // Schedule hide when playback starts
  useEffect(() => {
    if (isPlaying && isOverlayMode) {
      scheduleControlsHide();
    } else {
      clearControlsTimeout();
    }
  }, [isPlaying, isOverlayMode]);

  // Update time and duration periodically
  useEffect(() => {
    if (!playbackManager) return;

    const updateTimeInfo = () => {
      if (!isDragging && playbackManager) {
        const current = playbackManager.getCurrentTime();
        const total = playbackManager.getTotalDuration();

        setCurrentTime(current);
        if (total && total !== duration) {
          setDuration(total);
        }

        // Sync isPlaying state
        setIsPlaying(playbackManager.getIsPlaying());
      }
    };

    updateTimeInfo(); // Initial update
    updateIntervalRef.current = setInterval(updateTimeInfo, 100);

    return () => {
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
      }
    };
  }, [playbackManager, duration, isDragging]);

  // Cleanup timeouts
  useEffect(() => {
    return () => {
      clearControlsTimeout();
    };
  }, []);

  const handlePlayPause = async () => {
    if (!playbackManager) return;

    if (isPlaying) {
      playbackManager.pauseAll();
    } else {
      await playbackManager.playAll();
    }

    // Keep controls visible briefly after interaction
    if (isOverlayMode) {
      showControlsTemporarily();
    }
  };

  const handleSeek = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!playbackManager || !progressRef.current || duration === 0) return;

    const rect = progressRef.current.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, clickX / rect.width));
    const targetTime = percentage * duration;

    playbackManager.seekAll(targetTime);
    setCurrentTime(targetTime);

    // Keep controls visible briefly after interaction
    if (isOverlayMode) {
      showControlsTemporarily();
    }
  };

  const handleProgressMouseDown = (event: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(true);
    handleSeek(event);

    const handleMouseMove = (e: MouseEvent) => {
      if (!playbackManager || !progressRef.current || duration === 0) return;

      const rect = progressRef.current.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const percentage = Math.max(0, Math.min(1, clickX / rect.width));
      const targetTime = percentage * duration;

      setCurrentTime(targetTime);
    };

    const handleMouseUp = (e: MouseEvent) => {
      if (!playbackManager || !progressRef.current || duration === 0) return;

      const rect = progressRef.current.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const percentage = Math.max(0, Math.min(1, clickX / rect.width));
      const targetTime = percentage * duration;

      playbackManager.seekAll(targetTime);
      setIsDragging(false);

      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // Keep controls visible briefly after interaction
      if (isOverlayMode) {
        showControlsTemporarily();
      }
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    setVolume(newVolume);
    setIsMuted(newVolume === 0);

    if (playbackManager) {
      playbackManager.setMasterVolume(newVolume);
    }

    // Keep controls visible briefly after interaction
    if (isOverlayMode) {
      showControlsTemporarily();
    }
  };

  const toggleMute = () => {
    if (!playbackManager) return;

    if (isMuted) {
      const volumeToRestore = volume > 0 ? volume : 0.5;
      playbackManager.setMasterVolume(volumeToRestore);
      setVolume(volumeToRestore);
      setIsMuted(false);
    } else {
      playbackManager.setMasterVolume(0);
      setIsMuted(true);
    }

    // Keep controls visible briefly after interaction
    if (isOverlayMode) {
      showControlsTemporarily();
    }
  };

  const formatTime = (seconds: number): string => {
    if (!isFinite(seconds)) return '00:00';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <div
      id="video-player-container"
      ref={playerContainerRef}
      className={cn(
        'bg-black/90 text-white relative',
        isOverlayMode ? 'h-full' : 'grid grid-rows-[1fr_auto]',
        isFullscreen && 'h-full',
      )}
      onClick={handlePlayerInteraction}
      onTouchEnd={handlePlayerInteraction}
    >
      {/* Video Player Area */}
      <div
        id="video-player"
        className={cn(
          'relative w-full bg-black flex items-center justify-center',
          isOverlayMode ? 'h-full' : 'rounded-t-lg aspect-video',
        )}
      >
        <div className="text-white/50 pointer-events-none">
          Video will appear here
        </div>
      </div>

      {/* Controls - Overlay or Fixed */}
      <div
        className={cn(
          'transition-all duration-300 ease-in-out',
          isOverlayMode
            ? [
                'absolute inset-x-0 bottom-0 z-10',
                'bg-gradient-to-t from-black/90 via-black/60 to-transparent',
                'p-6 pt-20',
                showControls
                  ? 'translate-y-0 opacity-100'
                  : 'translate-y-full opacity-0',
              ]
            : 'bg-transparent',
        )}
      >
        {/* Progress Bar */}
        <div className={cn('w-full', isOverlayMode ? 'mb-4' : '')}>
          <div
            className={cn(
              'grid items-center gap-4',
              isMobileLandscape ? 'grid-cols-1' : 'grid-cols-[auto_1fr]',
              !isOverlayMode && 'p-4',
            )}
          >
            {!isMobileLandscape && (
              <button
                onClick={handlePlayPause}
                className="flex items-center justify-center w-10 h-10 bg-accent hover:bg-accent/90 rounded-full transition-colors focus:outline-none"
                aria-label={isPlaying ? 'Pause' : 'Play'}
              >
                {isPlaying ? (
                  <Pause className="w-5 h-5" />
                ) : (
                  <Play className="w-5 h-5" />
                )}
              </button>
            )}

            <div className="flex flex-col">
              <div
                ref={progressRef}
                className="w-full h-2 bg-foreground rounded-full cursor-pointer relative group"
                onClick={handleSeek}
                onMouseDown={handleProgressMouseDown}
              >
                <div
                  className="h-full bg-accent rounded-full transition-all duration-100 ease-out"
                  style={{ width: `${progressPercentage}%` }}
                />
                <div
                  className="absolute top-1/2 transform -translate-y-1/2 w-4 h-4 bg-accent rounded-full opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing"
                  style={{ left: `calc(${progressPercentage}% - 8px)` }}
                />
              </div>
              <div className="flex justify-between text-sm text-white/50 mt-1">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Controls */}
        <div
          className={cn(
            'flex items-center justify-between',
            !isOverlayMode && 'p-4 pt-0',
          )}
        >
          <div className="flex items-center space-x-2.5">
            {isMobileLandscape && (
              <button
                onClick={handlePlayPause}
                className="flex items-center justify-center w-10 h-10 bg-accent hover:bg-accent/90 rounded-full transition-colors focus:outline-none mr-2"
                aria-label={isPlaying ? 'Pause' : 'Play'}
              >
                {isPlaying ? (
                  <Pause className="w-5 h-5" />
                ) : (
                  <Play className="w-5 h-5" />
                )}
              </button>
            )}

            <button
              onClick={toggleMute}
              className="p-2 hover:bg-accent/70 rounded-[100%] focus:outline-none"
              aria-label={isMuted ? 'Unmute' : 'Mute'}
            >
              {isMuted || volume === 0 ? (
                <VolumeX className="w-5 h-5" />
              ) : (
                <Volume2 className="w-5 h-5" />
              )}
            </button>

            <div className="flex items-center space-x-2">
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={isMuted ? 0 : volume}
                onChange={handleVolumeChange}
                className="w-24 h-2 bg-foreground rounded-lg appearance-none cursor-pointer volume-slider"
              />
              <span className="text-sm text-white/50 min-w-[3rem]">
                {Math.round((isMuted ? 0 : volume) * 100)}%
              </span>
            </div>
          </div>

          {supportsFs && (
            <button
              onClick={toggleFullscreen}
              className="p-2.5 hover:bg-accent/70 rounded-[100%] transition-colors focus:outline-none"
              aria-label={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
            >
              {isFullscreen ? (
                <Minimize className="w-5 h-5" />
              ) : (
                <Maximize className="w-5 h-5" />
              )}
            </button>
          )}
        </div>
      </div>

      <style>{`
        .volume-slider::-webkit-slider-thumb {
          appearance: none;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: var(--accent);
          cursor: pointer;
        }

        .volume-slider::-moz-range-thumb {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: var(--accent);
          cursor: pointer;
          border: none;
        }

        /* Fullscreen styles */
        :fullscreen {
          background: black;
        }

        /* Hide volume slider on very small screens */
        @media (max-width: 480px) {
          .volume-controls {
            display: none;
          }
        }
      `}</style>
    </div>
  );
};
