import {
  Pagination,
  PaginationContent,
  <PERSON><PERSON>ation<PERSON><PERSON>psis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/shadcn/components/ui/pagination';
import { Link2 } from 'lucide-react';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';

interface ITranscriptProps {
  seekTo: (seconds: number) => void;
}

export const Transcript: FC<ITranscriptProps> = ({ seekTo }) => {
  const { t } = useTranslation();

  const segments = Array.from({ length: 10 }, (_) => ({
    time: '00:00',
    text: '',
  }));

  return (
    <div className="flex flex-col gap-3">
      <h1 className="text-xl text-primary font-bold">
        {t('classroom.dialogs.history.transcript.title')}
      </h1>
      <div className="flex flex-col">
        {segments.length > 0 ? (
          segments.map((seg, index) => (
            <div
              className="grid grid-cols-[auto_1fr] gap-4 first:border-t-0 border-t border-t-primary/20 py-1"
              key={index}
            >
              <div
                className="flex items-center gap-2 cursor-pointer"
                onClick={() =>
                  seekTo(
                    parseInt(seg.time.split(':')[0], 10) * 60 +
                      parseInt(seg.time.split(':')[1], 10),
                  )
                }
              >
                <Link2 className="size-4 text-primary" />
                <h4 className="text-primary font-bold text-sm">
                  {t('classroom.dialogs.history.transcript.time', {
                    time: seg.time,
                  })}
                </h4>
              </div>

              <h3 className="text-primary/80 text-sm">
                {seg.text ||
                  t('classroom.dialogs.history.transcript.noTranscript')}
              </h3>
            </div>
          ))
        ) : (
          <h3 className="text-primary/80 text-sm">
            {t('classroom.dialogs.history.transcript.noTranscript')}
          </h3>
        )}

        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious text="Previous">
                {t('classroom.dialogs.history.transcript.pagination.previous')}
              </PaginationPrevious>
            </PaginationItem>
            <PaginationItem>
              <PaginationLink href="#">1</PaginationLink>
            </PaginationItem>
            <PaginationItem>
              <PaginationLink href="#">2</PaginationLink>
            </PaginationItem>
            <PaginationItem>
              <PaginationEllipsis />
            </PaginationItem>
            <PaginationItem>
              <PaginationNext text="Next">
                {t('classroom.dialogs.history.transcript.pagination.next')}
              </PaginationNext>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
};
