import { useTranslation } from 'react-i18next';

interface IFeedbackProps {
  text: string | undefined;
}

export const Feedback = ({ text }: IFeedbackProps) => {
  const { t } = useTranslation();

  // Split into [bulletsBlock, recommendationBlock]
  const [bulletsBlock, recommendationBlock] = text ? text.split(/\n\s*\n/) : [];

  // Split bullets block into individual lines starting with "-"
  const feedbackTexts = bulletsBlock
    ? bulletsBlock
        .split(/\n-\s*/g)
        .map((s) => s.replace(/^-/, '').trim())
        .filter(Boolean)
    : [];

  return (
    <div className="flex flex-col gap-3">
      <h1 className="text-xl text-primary font-bold">
        {t('classroom.dialogs.history.feedback.title')}
      </h1>

      {feedbackTexts.length > 0 ? (
        <ul className="list-disc list-inside text-primary/80 space-y-2">
          {feedbackTexts.map((value, index) => (
            <li
              key={index}
              className="leading-relaxed marker:text-primary font-medium"
            >
              {value}
            </li>
          ))}
        </ul>
      ) : (
        <div className="text-primary/80">
          {t('classroom.dialogs.history.feedback.noFeedback')}
        </div>
      )}

      {recommendationBlock && (
        <div className="text-primary/70 italic leading-relaxed">
          {recommendationBlock.trim()}
        </div>
      )}
    </div>
  );
};
