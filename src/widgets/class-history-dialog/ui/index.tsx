import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { ChevronRight } from 'lucide-react';
import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Content } from './content';

interface IHistoryDialogProps {
  id: string;
  classId: string;
}

export const ClassHistoryDialog: FC<IHistoryDialogProps> = ({
  id,
  classId,
}) => {
  const { t } = useTranslation();
  const [show, setShow] = useState(false);

  return (
    <Dialog open={show} onOpenChange={setShow}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          className="hover:bg-primary/10 hover:text-primary transition-colors duration-300 text-primary cursor-pointer text-base disabled:opacity-50 disabled:!cursor-not-allowed"
        >
          {t('classroom.dialogs.history.trigger')}
          <ChevronRight />
        </Button>
      </DialogTrigger>

      <DialogContent
        className="min-[1232px]:!max-w-[1200px] max-[900px]:rounded-none max-[900px]:!max-w-[100dvw] max-[900px]:h-[100dvh] max-[900px]:max-h-[100dvh] min-[900px]:!max-w-[calc(100%-32px)] h-auto p-0 gap-0 grid grid-rows-[auto_1fr_auto] border-0"
        style={{
          paddingLeft: 'env(safe-area-inset-left)',
          paddingRight: 'env(safe-area-inset-right)',
        }}
      >
        <DialogHeader className="px-6 py-4 max-[900px]:landscape:!hidden">
          <DialogTitle className="text-start">
            {t('classroom.dialogs.history.title')}
          </DialogTitle>
          <DialogDescription className="hidden" />
        </DialogHeader>
        <Content id={id} classId={classId} setShow={setShow} />
        <DialogFooter className="p-6 py-4 max-[900px]:landscape:hidden items-end">
          <DialogClose asChild>
            <Button data-testid="history-dialog-close-button" size="lg">
              {t('classroom.dialogs.history.buttons.close')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
