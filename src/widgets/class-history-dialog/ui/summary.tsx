import { FC } from 'react';
import { useTranslation } from 'react-i18next';

interface ISummaryProps {
  text: string | undefined;
}

export const Summary: FC<ISummaryProps> = ({ text }) => {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col gap-3">
      <h1 className="text-xl text-primary font-bold">
        {t('classroom.dialogs.history.summary.title')}
      </h1>
      <div className="text-primary/80">
        {text ?? t('classroom.dialogs.history.summary.noSummary')}
      </div>
    </div>
  );
};
