import { Button } from '@/shadcn/components/ui/button';
import { DisabledTooltip } from '@/features';
import { Copy } from 'lucide-react';
import moment from 'moment';
import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface IHeaderProps {
  title: string;
  createdAt: number;
}

export const Header: FC<IHeaderProps> = ({ title, createdAt }) => {
  const { t, i18n } = useTranslation();

  const [formattedDate] = useState(
    moment(createdAt * 1000)
      .locale(i18n.language)
      .format('dddd, MMMM D [at] HH:mm'),
  );

  return (
    <div className="w-full flex items-center justify-between gap-6 p-6 bg-black/5 max-[500px]:flex-col max-[900px]:landscape:!hidden">
      <div className="flex flex-col gap-1">
        <h1
          data-testid="history-dialog-title"
          className="text-2xl text-primary font-bold"
        >
          {title}
        </h1>
        <h3
          data-testid="history-dialog-date"
          className="text-base font-semibold text-primary/80"
        >
          {formattedDate}
        </h3>
      </div>

      <DisabledTooltip tooltipText="classroom.dialogs.history.header.copyLink.tooltip">
        <Button
          variant="monochrome_outline"
          className="border-primary/50 text-primary max-[500px]:w-full disabled:opacity-50"
          size="lg"
          disabled
        >
          <Copy className="text-primary" />
          {t('classroom.dialogs.history.header.copyLink.button')}
        </Button>
      </DisabledTooltip>
    </div>
  );
};
