import { AuthResponse } from '@/features';
import { SERVER_URL } from '@/shared';

export const registerAnonymous = async (
  nickname: string,
): Promise<AuthResponse> => {
  const response = await fetch(SERVER_URL + '/auth/register/anonymous', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ nickname }),
  });

  if (!response.ok) {
    let errorMessage = 'Failed to register an anonymous user!';
    try {
      const errorData = await response.json();
      errorMessage = errorData.error || errorMessage;
    } catch (parseError) {
      console.log(parseError);
    }
    throw new Error(errorMessage);
  }

  try {
    const result = await response.json();
    return result as AuthResponse;
  } catch (error) {
    throw new Error('Failed to parse the response: ' + error);
  }
};
