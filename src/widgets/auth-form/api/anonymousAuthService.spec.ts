import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { registerAnonymous } from '../api';
import { SERVER_URL } from '@/shared';
import { AuthResponse } from '@/features';

describe('registerAnonymous', () => {
  let originalFetch: typeof fetch;
  let consoleLogSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    originalFetch = global.fetch;
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.clearAllMocks();
  });

  afterEach(() => {
    global.fetch = originalFetch;
    consoleLogSpy.mockRestore();
  });

  it('should register anonymously and return AuthResponse on success', async () => {
    const fakeResponse: AuthResponse = {
      data: {
        user: {
          id: '10',
          email: '<EMAIL>',
          displayName: 'Anonymous',
          displayNameWithTitle: 'Anonymous',
          firstName: 'Anonymous',
          lastName: '',
          preferences: {
            locale: 'en',
            defaultClassroomSettings: {
              speakingLanguage: 'en',
              translationLanguage: 'en',
              autoRecording: false,
              autoTranscribe: false,
              autoTranslateOtherLanguages: false,
              preferedAudioInputDevices: null,
              preferedVideoInputDevices: null,
              preferedAudioOutputDevices: null,
              autoShowCaptions: false,
            },
            receiveNotifications: false,
            participantConsent: {
              canRecord: false,
              canShareClassRecordings: false,
              canShareLiveStream: false,
              canAIProcess: false,
              canTeacherInviteGuests: false,
              canStudentInviteGuests: false,
              canAnonymousJoin: true,
              canPartyChat: false,
              canInviteMultipleTeachers: false,
              canInviteMultipleStudents: false,
            },
          },
          isAnonymous: true,
          createdAt: '2021-05-03T12:00:00Z',
          emailVerified: false,
          hasPassword: false,
        },
        token: 'anonToken',
        refreshToken: 'anonRefresh',
        expiresAt: 1620025200000,
      },
    };
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(fakeResponse),
      } as Response),
    );

    const result = await registerAnonymous('TestAnon');

    expect(global.fetch).toHaveBeenCalledWith(
      `${SERVER_URL}/auth/register/anonymous`,
      expect.objectContaining({
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ nickname: 'TestAnon' }),
      }),
    );
    expect(result).toEqual(fakeResponse);
  });

  it('should throw provided error message when response is not ok', async () => {
    const errorData = { error: 'Registration error' };
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: false,
        json: () => Promise.resolve(errorData),
      } as Response),
    );

    await expect(registerAnonymous('TestAnon')).rejects.toThrow(
      'Registration error',
    );
  });

  it('should throw fallback message when response not ok and JSON parse fails', async () => {
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: false,
        json: () => Promise.reject(new Error('Parse error')),
      } as Response),
    );

    await expect(registerAnonymous('TestAnon')).rejects.toThrow(
      'Failed to register an anonymous user!',
    );
  });

  it('should throw parse error when successful response JSON parsing fails', async () => {
    const parseErr = new Error('Unexpected token');
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.reject(parseErr),
      } as Response),
    );

    await expect(registerAnonymous('TestAnon')).rejects.toThrow(
      `Failed to parse the response: ${parseErr}`,
    );
  });
});
