import { AuthResponse } from '@/features';
import { api, Consents, SERVER_URL } from '@/shared';
import axios from 'axios';

export const login = async ({
  email,
  password,
}: {
  email: string;
  password: string;
}): Promise<AuthResponse> => {
  const response = await fetch(SERVER_URL + '/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email,
      password,
    }),
  });

  if (!response.ok) {
    let errorMessage = 'Failed to sign in the user!';
    try {
      const errorData = await response.json();
      errorMessage = errorData.error?.message || errorMessage;
    } catch (parseError) {
      console.log(parseError);
    }

    throw new Error(errorMessage);
  }

  try {
    const result = await response.json();
    return result as AuthResponse;
  } catch (error) {
    throw new Error('Failed to parse the response: ' + error);
  }
};

export const register = async ({
  fullName,
  email,
  password,
  consents,
  locale = 'en',
}: {
  fullName: string;
  email: string;
  password: string;
  consents: Consents;
  locale?: string;
}): Promise<{ data: { userId: string } }> => {
  const names = fullName.split(' ');

  const response = await fetch(SERVER_URL + '/auth/register', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email,
      password,
      displayName: fullName,
      firstName: names[0],
      lastName: names[names.length - 1],
      locale,
      participantConsent: consents,
    }),
  });

  if (!response.ok) {
    let errorMessage = 'Failed to register the user!';
    try {
      const errorData = await response.json();
      errorMessage = errorData.error?.message || errorMessage;
    } catch (parseError) {
      console.log(parseError);
    }

    throw new Error(errorMessage);
  }

  try {
    const result = await response.json();
    return result as { data: { userId: string } };
  } catch (error) {
    throw new Error('Failed to parse the response: ' + error);
  }
};

export const convertGuest = async ({
  fullName,
  email,
  password,
  consents,
}: {
  fullName: string;
  email: string;
  password: string;
  consents: Consents;
}): Promise<{ data: { userId: string } }> => {
  const names = fullName.split(' ');

  try {
    const response = await api.post<{ data: { userId: string } }>(
      SERVER_URL + '/users/me/convert',
      {
        email,
        password,
        firstName: names[0],
        lastName: names[names.length - 1],
        participantConsent: consents,
      },
    );

    return response.data;
  } catch (error) {
    let errorMessage = 'Failed to register the user!';

    if (axios.isAxiosError(error) && error.response?.data?.error?.message) {
      errorMessage = error.response.data.error.message;
    }

    throw new Error(errorMessage);
  }
};

export const resendVerificationEmail = async ({
  email,
}: {
  email: string;
}): Promise<{
  success: boolean;
  message: string;
}> => {
  const response = await fetch(SERVER_URL + '/auth/resend-verification', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email,
    }),
  });

  if (!response.ok) {
    let errorMessage = 'Failed to resend verification email!';
    try {
      const errorData = await response.json();
      errorMessage = errorData.error?.message || errorMessage;
    } catch (parseError) {
      console.log(parseError);
    }

    throw new Error(errorMessage);
  }

  try {
    const result = await response.json();
    return {
      success: result.success,
      message: result.message || 'Verification email resent successfully!',
    };
  } catch (error) {
    throw new Error('Failed to parse the response: ' + error);
  }
};
