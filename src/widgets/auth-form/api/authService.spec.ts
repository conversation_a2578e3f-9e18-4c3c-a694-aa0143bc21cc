import {
  describe,
  it,
  expect,
  beforeEach,
  afterEach,
  vi,
  MockedFunction,
} from 'vitest';
import {
  login,
  register,
  resendVerificationEmail,
  convertGuest,
} from './authService';
import { SERVER_URL, Consents, api } from '@/shared';
import { AuthResponse } from '@/features';
import axios from 'axios';

// Mock the api module
vi.mock('@/shared', () => ({
  api: {
    post: vi.fn(),
  },
  SERVER_URL: 'http://localhost:3000',
}));

const mockedApiPost = api.post as MockedFunction<typeof api.post>;

describe('Authentication API', () => {
  let originalFetch: typeof fetch;
  let consoleLogSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    originalFetch = global.fetch;
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.clearAllMocks();
  });

  afterEach(() => {
    global.fetch = originalFetch;
    consoleLogSpy.mockRestore();
  });

  const mockConsents: Consents = {
    canRecord: true,
    canShareClassRecordings: false,
    canShareLiveStream: true,
    canAIProcess: true,
    canTeacherInviteGuests: true,
    canStudentInviteGuests: false,
    canAnonymousJoin: true,
    canPartyChat: true,
    canInviteMultipleTeachers: false,
    canInviteMultipleStudents: true,
  };

  describe('login', () => {
    it('should return AuthResponse on successful login', async () => {
      const fakeResponse: AuthResponse = {
        data: {
          user: {
            id: '1',
            email: '<EMAIL>',
            displayName: 'John Doe',
            displayNameWithTitle: 'Mr. John Doe',
            firstName: 'John',
            lastName: 'Doe',
            preferences: {
              locale: 'en',
              defaultClassroomSettings: {
                speakingLanguage: 'en',
                translationLanguage: 'en',
                autoRecording: false,
                autoTranscribe: true,
                autoTranslateOtherLanguages: false,
                preferedAudioInputDevices: null,
                preferedVideoInputDevices: null,
                preferedAudioOutputDevices: null,
                autoShowCaptions: true,
              },
              receiveNotifications: true,
              participantConsent: {
                canRecord: true,
                canShareClassRecordings: true,
                canShareLiveStream: false,
                canAIProcess: true,
                canTeacherInviteGuests: true,
                canStudentInviteGuests: false,
                canAnonymousJoin: false,
                canPartyChat: true,
                canInviteMultipleTeachers: false,
                canInviteMultipleStudents: true,
              },
            },
            isAnonymous: false,
            createdAt: '2021-05-03T12:00:00Z',
            emailVerified: true,
            hasPassword: true,
          },
          token: 'abc123',
          refreshToken: 'refresh123',
          expiresAt: 1620003600000,
        },
      };
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve(fakeResponse),
        } as Response),
      );

      const result = await login({
        email: '<EMAIL>',
        password: 'pass',
      });

      expect(global.fetch).toHaveBeenCalledWith(
        `${SERVER_URL}/auth/login`,
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: '<EMAIL>', password: 'pass' }),
        }),
      );
      expect(result).toEqual(fakeResponse);
    });

    it('should throw provided error message when response is not ok', async () => {
      const errorData = {
        error: {
          code: 'PERMISSION_DENIED',
          message: 'Email not verified',
          request_id: '226252ef-a7e1-4584-a0ae-598d7ae9cf32',
        },
      };
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: false,
          json: () => Promise.resolve(errorData),
        } as Response),
      );

      await expect(
        login({ email: '<EMAIL>', password: 'wrong' }),
      ).rejects.toThrow('Email not verified');
    });

    it('should throw fallback message when response not ok and JSON parse fails', async () => {
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: false,
          json: () => Promise.reject(new Error('Parse error')),
        } as Response),
      );

      await expect(
        login({ email: '<EMAIL>', password: 'wrong' }),
      ).rejects.toThrow('Failed to sign in the user!');
    });

    it('should throw default error when no error message provided', async () => {
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: false,
          json: () => Promise.resolve({}),
        } as Response),
      );

      await expect(
        login({ email: '<EMAIL>', password: 'wrong' }),
      ).rejects.toThrow('Failed to sign in the user!');
    });

    it('should throw parse error when response JSON parsing fails', async () => {
      const parseErr = new Error('Unexpected token');
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.reject(parseErr),
        } as Response),
      );

      await expect(
        login({ email: '<EMAIL>', password: 'pass' }),
      ).rejects.toThrow(`Failed to parse the response: ${parseErr}`);
    });
  });

  describe('register', () => {
    it('should return RegisterResponse on successful registration', async () => {
      const fakeResponse = {
        data: {
          userId: '6425a9b8e55b3a8a7c9c1d3f',
        },
      };
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve(fakeResponse),
        } as Response),
      );

      const result = await register({
        fullName: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        consents: mockConsents,
        locale: 'en',
      });

      expect(global.fetch).toHaveBeenCalledWith(
        `${SERVER_URL}/auth/register`,
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123',
            displayName: 'Test User',
            firstName: 'Test',
            lastName: 'User',
            locale: 'en',
            participantConsent: mockConsents,
          }),
        }),
      );
      expect(result).toEqual(fakeResponse);
    });

    it('should use default locale when not provided', async () => {
      const fakeResponse = {
        data: {
          userId: '6425a9b8e55b3a8a7c9c1d3f',
        },
      };
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve(fakeResponse),
        } as Response),
      );

      await register({
        fullName: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        consents: mockConsents,
      });

      expect(global.fetch).toHaveBeenCalledWith(
        `${SERVER_URL}/auth/register`,
        expect.objectContaining({
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123',
            displayName: 'Test User',
            firstName: 'Test',
            lastName: 'User',
            locale: 'en',
            participantConsent: mockConsents,
          }),
        }),
      );
    });

    it('should handle multi-word names correctly', async () => {
      const fakeResponse = {
        data: {
          userId: '6425a9b8e55b3a8a7c9c1d3f',
        },
      };
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve(fakeResponse),
        } as Response),
      );

      await register({
        fullName: 'John Michael Smith',
        email: '<EMAIL>',
        password: 'password123',
        consents: mockConsents,
      });

      expect(global.fetch).toHaveBeenCalledWith(
        `${SERVER_URL}/auth/register`,
        expect.objectContaining({
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123',
            displayName: 'John Michael Smith',
            firstName: 'John',
            lastName: 'Smith',
            locale: 'en',
            participantConsent: mockConsents,
          }),
        }),
      );
    });

    it('should throw provided error message when response is not ok', async () => {
      const errorData = {
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Email already exists',
          request_id: '123456789',
        },
      };
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: false,
          json: () => Promise.resolve(errorData),
        } as Response),
      );

      await expect(
        register({
          fullName: 'Test User',
          email: '<EMAIL>',
          password: 'pwd',
          consents: mockConsents,
        }),
      ).rejects.toThrow('Email already exists');
    });

    it('should throw fallback message when response not ok and JSON parse fails', async () => {
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: false,
          json: () => Promise.reject(new Error('Parse error')),
        } as Response),
      );

      await expect(
        register({
          fullName: 'Test User',
          email: '<EMAIL>',
          password: 'pwd2',
          consents: mockConsents,
        }),
      ).rejects.toThrow('Failed to register the user!');
    });

    it('should throw default error when no error message provided', async () => {
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: false,
          json: () => Promise.resolve({}),
        } as Response),
      );

      await expect(
        register({
          fullName: 'Test User',
          email: '<EMAIL>',
          password: 'pwd3',
          consents: mockConsents,
        }),
      ).rejects.toThrow('Failed to register the user!');
    });

    it('should throw parse error when response JSON parsing fails', async () => {
      const parseErr = new Error('Bad JSON');
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.reject(parseErr),
        } as Response),
      );

      await expect(
        register({
          fullName: 'Test User',
          email: '<EMAIL>',
          password: 'pwd4',
          consents: mockConsents,
        }),
      ).rejects.toThrow(`Failed to parse the response: ${parseErr}`);
    });
  });

  describe('convertGuest', () => {
    it('should return user data on successful conversion', async () => {
      const fakeResponse = {
        data: {
          data: {
            userId: '6425a9b8e55b3a8a7c9c1d3f',
          },
        },
      };

      mockedApiPost.mockResolvedValue(fakeResponse);

      const result = await convertGuest({
        fullName: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        consents: mockConsents,
      });

      expect(api.post).toHaveBeenCalledWith(
        'http://localhost:3000/users/me/convert',
        {
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'Test',
          lastName: 'User',
          participantConsent: mockConsents,
        },
      );
      expect(result).toEqual(fakeResponse.data);
    });

    it('should handle multi-word names correctly', async () => {
      const fakeResponse = {
        data: {
          data: {
            userId: '6425a9b8e55b3a8a7c9c1d3f',
          },
        },
      };

      mockedApiPost.mockResolvedValue(fakeResponse);

      await convertGuest({
        fullName: 'John Michael Smith',
        email: '<EMAIL>',
        password: 'password123',
        consents: mockConsents,
      });

      expect(api.post).toHaveBeenCalledWith(
        'http://localhost:3000/users/me/convert',
        {
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'John',
          lastName: 'Smith',
          participantConsent: mockConsents,
        },
      );
    });

    it('should throw provided error message when axios error occurs', async () => {
      const axiosError = {
        response: {
          data: {
            error: {
              code: 'VALIDATION_ERROR',
              message: 'User already exists',
              request_id: '123456789',
            },
          },
        },
      };

      vi.spyOn(axios, 'isAxiosError').mockReturnValue(true);
      mockedApiPost.mockRejectedValue(axiosError);

      await expect(
        convertGuest({
          fullName: 'Test User',
          email: '<EMAIL>',
          password: 'password123',
          consents: mockConsents,
        }),
      ).rejects.toThrow('User already exists');
    });

    it('should throw default error message when no error message provided', async () => {
      const axiosError = {
        response: {
          data: {},
        },
      };

      vi.spyOn(axios, 'isAxiosError').mockReturnValue(true);
      mockedApiPost.mockRejectedValue(axiosError);

      await expect(
        convertGuest({
          fullName: 'Test User',
          email: '<EMAIL>',
          password: 'password123',
          consents: mockConsents,
        }),
      ).rejects.toThrow('Failed to register the user!');
    });

    it('should throw default error message when not an axios error', async () => {
      const genericError = new Error('Network error');

      vi.spyOn(axios, 'isAxiosError').mockReturnValue(false);
      mockedApiPost.mockRejectedValue(genericError);

      await expect(
        convertGuest({
          fullName: 'Test User',
          email: '<EMAIL>',
          password: 'password123',
          consents: mockConsents,
        }),
      ).rejects.toThrow('Failed to register the user!');
    });

    it('should throw default error when axios error has no response', async () => {
      const axiosError = {
        message: 'Request failed',
      };

      vi.spyOn(axios, 'isAxiosError').mockReturnValue(true);
      mockedApiPost.mockRejectedValue(axiosError);

      await expect(
        convertGuest({
          fullName: 'Test User',
          email: '<EMAIL>',
          password: 'password123',
          consents: mockConsents,
        }),
      ).rejects.toThrow('Failed to register the user!');
    });
  });

  describe('resendVerificationEmail', () => {
    it('should return success and message on successful resend', async () => {
      const fakeResponse = { success: true, message: 'Email sent' };
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve(fakeResponse),
        } as Response),
      );

      const result = await resendVerificationEmail({
        email: '<EMAIL>',
      });

      expect(global.fetch).toHaveBeenCalledWith(
        `${SERVER_URL}/auth/resend-verification`,
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: '<EMAIL>' }),
        }),
      );
      expect(result).toEqual({ success: true, message: 'Email sent' });
    });

    it('should return default message when no message provided', async () => {
      const fakeResponse = { success: true };
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve(fakeResponse),
        } as Response),
      );

      const result = await resendVerificationEmail({
        email: '<EMAIL>',
      });

      expect(result).toEqual({
        success: true,
        message: 'Verification email resent successfully!',
      });
    });

    it('should throw provided error message when response is not ok', async () => {
      const errorData = {
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests',
          request_id: '987654321',
        },
      };
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: false,
          json: () => Promise.resolve(errorData),
        } as Response),
      );

      await expect(
        resendVerificationEmail({ email: '<EMAIL>' }),
      ).rejects.toThrow('Too many requests');
    });

    it('should throw fallback message when response not ok and JSON parse fails', async () => {
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: false,
          json: () => Promise.reject(new Error('Parse error')),
        } as Response),
      );

      await expect(
        resendVerificationEmail({ email: '<EMAIL>' }),
      ).rejects.toThrow('Failed to resend verification email!');
    });

    it('should throw default error when no error message provided', async () => {
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: false,
          json: () => Promise.resolve({}),
        } as Response),
      );

      await expect(
        resendVerificationEmail({ email: '<EMAIL>' }),
      ).rejects.toThrow('Failed to resend verification email!');
    });

    it('should throw parse error when response JSON parsing fails', async () => {
      const parseErr = new Error('Bad JSON');
      global.fetch = vi.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.reject(parseErr),
        } as Response),
      );

      await expect(
        resendVerificationEmail({ email: '<EMAIL>' }),
      ).rejects.toThrow(`Failed to parse the response: ${parseErr}`);
    });
  });
});
