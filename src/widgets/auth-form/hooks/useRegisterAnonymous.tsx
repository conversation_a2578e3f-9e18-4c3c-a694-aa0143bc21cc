import { useMutation } from '@tanstack/react-query';
import { useCookies } from 'react-cookie';
import { registerAnonymous } from '../api';
import { useNotifications } from '@/shared';
import { useLocation } from 'react-router';
import { AuthResponse } from '@/features';

export const useRegisterAnonymous = (
  onSuccessfulAuth: (data: AuthResponse) => void,
) => {
  const [, setCookie] = useCookies(['anonymousUserId', 'anonymousDisplayName']);
  const { addNotification } = useNotifications();
  const location = useLocation();

  return useMutation<AuthResponse, Error, string>({
    mutationFn: registerAnonymous,
    onSuccess: (data) => {
      setCookie('anonymousUserId', data.data.user.id, {
        path: '/',
        secure: true,
        sameSite: 'lax',
      });
      setCookie('anonymousDisplayName', data.data.user.displayName, {
        path: '/',
        secure: true,
        sameSite: 'lax',
      });

      // Track guest account creation with Google Analytics
      if (window.gtag) {
        window.gtag('event', 'sign_up', {
          method: 'guest',
          user_id: data.data.user.id,
          display_name: data.data.user.displayName,
          page_path: location.pathname,
        });
      }

      onSuccessfulAuth(data);
    },
    onError: () => {
      addNotification?.({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
