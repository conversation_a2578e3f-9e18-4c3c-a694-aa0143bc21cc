import { useMutation } from '@tanstack/react-query';
import { register } from '../api';
import { Consents, useNotifications } from '@/shared';

export const useRegister = (onSuccessfulAuth: () => void) => {
  const { addNotification } = useNotifications();

  return useMutation<
    { data: { userId: string } },
    Error,
    {
      fullName: string;
      email: string;
      password: string;
      consents: Consents;
      locale: string;
    }
  >({
    mutationFn: register,
    onSuccess: (data: { data: { userId: string } }) => {
      if (window.gtag) {
        window.gtag('event', 'sign-up form completed', {
          method: 'email',
          user_id: data.data.userId,
          page_path: location.pathname,
        });
      }

      onSuccessfulAuth();
    },
    onError: () => {
      addNotification?.({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
