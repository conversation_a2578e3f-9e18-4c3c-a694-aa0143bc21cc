import { useMutation } from '@tanstack/react-query';
import { convertGuest } from '../api';
import { Consents, useAppDispatch, useNotifications } from '@/shared';
import { useCookies } from 'react-cookie';
import { setUser } from '@/entities';

export const useConvertGuest = (onSuccessfulAuth: () => void) => {
  const { addNotification } = useNotifications();
  const dispatch = useAppDispatch();
  const [, , removeCookie] = useCookies([
    'token',
    'refreshToken',
    'anonymousDisplayName',
    'anonymousUserId',
  ]);

  return useMutation<
    { data: { userId: string } },
    Error,
    {
      fullName: string;
      email: string;
      password: string;
      consents: Consents;
    }
  >({
    mutationFn: convertGuest,
    onSuccess: (data: { data: { userId: string } }) => {
      if (window.gtag) {
        window.gtag('event', 'sign-up form completed (guest converted)', {
          method: 'email',
          user_id: data.data.userId,
          page_path: location.pathname,
        });
      }

      removeCookie('token', { path: '/' });
      removeCookie('refreshToken', { path: '/' });
      removeCookie('anonymousDisplayName', { path: '/' });
      removeCookie('anonymousUserId', { path: '/' });

      dispatch(setUser(null));

      onSuccessfulAuth();
    },
    onError: () => {
      addNotification?.({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
