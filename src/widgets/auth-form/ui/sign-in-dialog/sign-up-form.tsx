import { Input } from '@/shadcn/components/ui/input';
import { Step } from './step';
import {
  Dispatch,
  FC,
  SetStateAction,
  useEffect,
  useState,
  useMemo,
} from 'react';
import { Checkbox } from '@/shadcn/components/ui/checkbox';
import { Link } from 'react-router';
import { useForm, Controller, Control } from 'react-hook-form';
import { useConvertGuest, useRegister } from '../../hooks';
import { Trans, useTranslation } from 'react-i18next';
import { Progress } from '@/shadcn/components/ui/progress';
import { Info } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { Consents, getProviderLink, LANGUAGES } from '@/shared';
import {
  calculatePasswordStrength,
  PASSWORD_MIN_LENGTH,
  PASSWORD_PATTERN,
} from '@/shared';
import { ResendButton } from '../resend-button';
import { ConsentCheckboxList } from '@/shared';
import { useUser } from '@/entities';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shadcn/components/ui/select';
import { supportedLanguages } from '@/app/i18n';
import { useDigitalConsentContext } from '@/features';

type FormValues = {
  fullName: string;
  email: string;
  password: string;
  confirmPassword: string;
  terms: boolean;
  locale: string;
} & Consents;

interface ISignUpFormProps {
  activeStep: number;
  setActiveStep: Dispatch<SetStateAction<number>>;
}

export const SignUpForm: FC<ISignUpFormProps> = ({
  activeStep,
  setActiveStep,
}) => {
  const { t, i18n } = useTranslation();
  const [isLocaleTooltipOpen, setIsLocaleTooltipOpen] = useState(false);
  const { config: consentConfig } = useDigitalConsentContext();
  const { mutate: registerMutation, isPending: isRegisterLoading } =
    useRegister(() => setActiveStep(5));
  const { mutate: convertGuestMutation, isPending: isConvertGuestLoading } =
    useConvertGuest(() => {
      setActiveStep(5);
    });

  const {
    register,
    control,
    trigger,
    watch,
    setValue,
    formState: { errors },
  } = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: {
      fullName: '',
      email: '',
      password: '',
      confirmPassword: '',
      terms: false,
      canRecord: true,
      canShareClassRecordings: true,
      canShareLiveStream: true,
      canAIProcess: true,
      canTeacherInviteGuests: true,
      canStudentInviteGuests: true,
      canAnonymousJoin: true,
      canPartyChat: true,
      canInviteMultipleTeachers: true,
      canInviteMultipleStudents: true,
      locale: i18n.language,
    },
  });
  const password = watch('password');
  const confirmPassword = watch('confirmPassword');
  const email = watch('email');
  const [showPasswordStrengthTooltip, setShowPasswordStrengthTooltip] =
    useState(false);
  const { percentage, strength, colorClass } = useMemo(
    () => calculatePasswordStrength(password ?? ''),
    [password],
  );

  const allValues = watch();
  const { user } = useUser();
  const onSubmit = (data: FormValues) => {
    if (user?.isAnonymous) {
      convertGuestMutation({
        fullName: data.fullName,
        email: data.email,
        password: data.password,
        consents: {
          canRecord: data.canRecord,
          canShareClassRecordings: data.canShareClassRecordings,
          canShareLiveStream: data.canShareLiveStream,
          canAIProcess: data.canAIProcess,
          canTeacherInviteGuests: data.canTeacherInviteGuests,
          canStudentInviteGuests: data.canStudentInviteGuests,
          canAnonymousJoin: data.canAnonymousJoin,
          canPartyChat: data.canPartyChat,
          canInviteMultipleTeachers: data.canInviteMultipleTeachers,
          canInviteMultipleStudents: data.canInviteMultipleStudents,
        },
      });
    } else {
      registerMutation({
        fullName: data.fullName,
        email: data.email,
        password: data.password,
        consents: {
          canRecord: data.canRecord,
          canShareClassRecordings: data.canShareClassRecordings,
          canShareLiveStream: data.canShareLiveStream,
          canAIProcess: data.canAIProcess,
          canTeacherInviteGuests: data.canTeacherInviteGuests,
          canStudentInviteGuests: data.canStudentInviteGuests,
          canAnonymousJoin: data.canAnonymousJoin,
          canPartyChat: data.canPartyChat,
          canInviteMultipleTeachers: data.canInviteMultipleTeachers,
          canInviteMultipleStudents: data.canInviteMultipleStudents,
        },
        locale: data.locale,
      });
    }
  };

  useEffect(() => {
    if (password && confirmPassword && confirmPassword.length > 0) {
      trigger('confirmPassword');
    }
  }, [password, confirmPassword, trigger]);

  const next = async () => {
    if (activeStep === 1) {
      const ok = await trigger([
        'fullName',
        'email',
        'password',
        'confirmPassword',
      ]);
      if (!ok) return;
    }

    if (activeStep === 2) {
      const ok = await trigger('terms');
      if (!ok) return;
    }

    if (activeStep === 4) {
      const link = getProviderLink(email ?? '');
      if (link) window.open(link, '_blank');
      return onSubmit(allValues);
    }

    setActiveStep((s: number) => Math.min(s + 1, steps.length));
  };

  const prev = () => setActiveStep((s) => Math.max(s - 1, 1));

  const steps = [
    {
      text: t('home.dialogs.signUp.steps.create'),
      content: (
        <>
          <div data-testid="sign-up-step-1-content">
            <Input
              data-testid="sign-up-full-name-input"
              className={
                errors?.fullName ? 'border-destructive' : 'border-[#00000030]'
              }
              placeholder={t('globalFields.displayName.label')}
              {...register('fullName', {
                required: t('globalFields.displayName.validation.required'),
                minLength: {
                  value: 3,
                  message: t('globalFields.displayName.validation.minLength'),
                },
                maxLength: {
                  value: 20,
                  message: t('globalFields.displayName.validation.maxLength'),
                },
              })}
            />
            {errors.fullName && (
              <p
                data-testid="sign-up-full-name-error"
                className="text-destructive text-sm"
              >
                {errors.fullName.message}
              </p>
            )}
          </div>
          <div>
            <Input
              data-testid="sign-up-email-input"
              placeholder={t('globalFields.email.label')}
              className={
                errors?.email ? 'border-destructive' : 'border-[#00000030]'
              }
              {...register('email', {
                required: t('globalFields.email.validation.required'),
                onChange: (e) => {
                  setValue('email', e.target.value.replace(/\s+/g, ''));
                },
                pattern: {
                  value: /^[^@\s]+@(?:[A-Za-z0-9-]+\.)+[A-Za-z0-9-]+$/,
                  message: t('globalFields.email.validation.invalid'),
                },
                minLength: {
                  value: 3,
                  message: t('globalFields.email.validation.minLength'),
                },
                maxLength: {
                  value: 50,
                  message: t('globalFields.email.validation.maxLength'),
                },
              })}
              inputMode="email"
            />
            {errors.email && (
              <p
                data-testid="sign-up-email-error"
                className="text-destructive text-sm"
              >
                {errors.email.message}
              </p>
            )}
          </div>
          <div>
            <Input
              data-testid="sign-up-password-input"
              type="password"
              showPasswordToggle
              className={
                errors?.password ? 'border-destructive' : 'border-[#00000030]'
              }
              placeholder={t('globalFields.password.label')}
              {...register('password', {
                required: t('globalFields.password.validation.required'),
                minLength: {
                  value: PASSWORD_MIN_LENGTH,
                  message: t('globalFields.password.validation.minLength'),
                },
                maxLength: {
                  value: 50,
                  message: t('globalFields.password.validation.maxLength'),
                },
                pattern: {
                  value: PASSWORD_PATTERN,
                  message: t('globalFields.password.validation.pattern'),
                },
                validate: (v: string) =>
                  calculatePasswordStrength(v).percentage >=
                    Math.round((3 / 6) * 100) ||
                  t('globalFields.password.validation.weakStrength'),
              })}
            />
            {errors.password && (
              <p
                data-testid="sign-up-password-error"
                className="text-destructive text-sm"
              >
                {errors.password.message}
              </p>
            )}
          </div>
          <div>
            <Input
              data-testid="sign-up-confirm-password-input"
              type="password"
              showPasswordToggle
              className={
                errors?.confirmPassword
                  ? 'border-destructive'
                  : 'border-[#00000030]'
              }
              placeholder={t('globalFields.confirmPassword.label')}
              {...register('confirmPassword', {
                required: t('globalFields.confirmPassword.validation.required'),
                validate: (v) =>
                  v === watch('password') ||
                  t('globalFields.confirmPassword.validation.match'),
              })}
            />
            {errors.confirmPassword && (
              <p
                data-testid="sign-up-confirm-password-error"
                className="text-destructive text-sm"
              >
                {errors.confirmPassword.message}
              </p>
            )}
          </div>

          <div className="flex flex-col gap-4">
            <TooltipProvider>
              <Tooltip
                open={isLocaleTooltipOpen}
                onOpenChange={setIsLocaleTooltipOpen}
              >
                <TooltipTrigger
                  asChild
                  onClick={() => setIsLocaleTooltipOpen((prev) => !prev)}
                >
                  <div className="w-fit flex items-center justify-start gap-1.5 px-1 cursor-default">
                    <Info className="size-4 text-card-foreground/50" />
                    <p className="text-xs text-card-foreground/80">
                      {t('globalFields.languages.locale.label')}
                    </p>
                  </div>
                </TooltipTrigger>

                <TooltipContent align="start">
                  <p>{t('globalFields.languages.locale.tooltip')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <Controller
              control={control}
              name="locale"
              rules={{ required: true }}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger
                    data-testid="sign-up-locale-trigger"
                    className="w-full"
                  >
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent data-testid="sign-up-locale-content">
                    {Object.entries(LANGUAGES).map(([key, label]) => {
                      if (supportedLanguages.includes(key))
                        return (
                          <SelectItem
                            key={key}
                            value={key}
                            data-testid={`sign-up-locale-option-${key}`}
                          >
                            {label}
                          </SelectItem>
                        );
                    })}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {password && (
            <div
              data-testid="sign-up-password-strength"
              className="flex flex-col gap-2"
            >
              <div className="flex items-center justify-between max-[350px]:flex-col max-[350px]:items-start gap-2">
                <TooltipProvider>
                  <Tooltip
                    open={showPasswordStrengthTooltip}
                    onOpenChange={setShowPasswordStrengthTooltip}
                  >
                    <TooltipTrigger asChild>
                      <div
                        data-testid="sign-up-password-strength-info"
                        className="flex items-center justify-center gap-2"
                        onClick={() => setShowPasswordStrengthTooltip(true)}
                      >
                        <Info className="size-4 text-primary/60 " />
                        <h3 className="text-primary/60 text-xs">
                          {t('globals.passwordStrength.title')}
                        </h3>
                      </div>
                    </TooltipTrigger>

                    <TooltipContent align="start" className="max-w-[300px]">
                      <p>{t('globals.passwordStrength.tooltip')}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <h4
                  className="text-xs font-medium"
                  style={{ color: `var(--${colorClass})` }}
                >
                  {t(strength)}
                </h4>
              </div>
              <Progress
                data-testid="sign-up-password-strength-progress"
                value={percentage}
                className="w-full h-[6px]"
                progressColorVar={`--${colorClass}`}
              />
            </div>
          )}
        </>
      ),
    },
    {
      text: t('home.dialogs.signUp.steps.terms'),
      content: (
        <>
          <h3 className="text-sm">
            {t('home.dialogs.signUp.fields.terms.text1')}
            <Link
              data-testid="sign-up-terms-link"
              to="/legal/terms-and-conditions"
              target="_blank"
              className="text-accent underline"
            >
              {t('home.dialogs.signUp.fields.terms.text2')}
            </Link>
            {t('home.dialogs.signUp.fields.terms.text3')}
            <Link
              data-testid="sign-up-terms-link"
              to="/legal/privacy-policy"
              target="_blank"
              className="text-accent underline"
            >
              {t('home.dialogs.signUp.fields.terms.text4')}
            </Link>
            {t('home.dialogs.signUp.fields.terms.text5')}
          </h3>
          <div className="flex items-center space-x-2">
            <Controller
              name="terms"
              control={control}
              rules={{
                validate: (v) =>
                  v ||
                  t('home.dialogs.signUp.fields.terms.validation.required'),
              }}
              render={({ field }) => (
                <Checkbox
                  data-testid="sign-up-terms-checkbox"
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  id="terms"
                  className="size-5 data-[state=checked]:bg-accent data-[state=checked]:border-transparent border-primary/40 cursor-pointer"
                />
              )}
            />
            <label
              htmlFor="terms"
              className="text-sm font-medium text-card-foreground/80 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              <Trans
                i18nKey="home.dialogs.signUp.fields.terms.checkbox"
                values={{ age: consentConfig?.digitalAge ?? 18 }}
              />
            </label>
          </div>
          {errors.terms && (
            <p
              data-testid="sign-up-terms-error"
              className="text-destructive text-sm"
            >
              {errors.terms.message}
            </p>
          )}
        </>
      ),
    },
    {
      text: t('home.dialogs.signUp.steps.consent'),
      content: (
        <ConsentCheckboxList
          control={
            control as unknown as Control<{ [K in keyof Consents]?: boolean }>
          }
        />
      ),
    },
    {
      text: t('home.dialogs.signUp.steps.verify'),
      content: (
        <h2 className="text-sm">
          {t('home.dialogs.signUp.fields.verify.label')}
          <span className="font-semibold break-all">{email}</span>
        </h2>
      ),
    },
    {
      text: t('home.dialogs.signUp.steps.complete'),
      content: (
        <div className="flex flex-col gap-4">
          <h3 className="text-sm break-all">
            {t('home.dialogs.signUp.fields.complete.label', {
              email,
            })}
          </h3>

          <div className="flex items-center justify-between gap-2 max-[480px]:flex-col max-[480px]:items-start">
            <h2 className="text-sm opacity-70">
              {t('home.dialogs.signUp.fields.complete.didntReceive')}
            </h2>
            <ResendButton email={email ?? ''} activeStep={activeStep} />
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className="grid gap-4 text-card-foreground overflow-auto min-[450px]:px-[20px]">
      <div className="flex flex-col gap-1">
        {steps.map((step, index) => (
          <Step
            key={index}
            step={index + 1}
            text={step.text}
            active={activeStep}
            last={index + 1 === steps.length}
            next={next}
            prev={prev}
            loading={isRegisterLoading || isConvertGuestLoading}
          >
            {step.content}
          </Step>
        ))}
      </div>
    </div>
  );
};
