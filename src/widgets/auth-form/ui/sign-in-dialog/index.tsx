import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { useEffect, useState } from 'react';
import { SignInForm } from './sign-in-form';
import { SignUpForm } from './sign-up-form';
import { DialogDescription } from '@radix-ui/react-dialog';
import { Button } from '@/shadcn/components/ui/button';
import { useNavigate, useSearchParams } from 'react-router';
import { useTranslation } from 'react-i18next';
import { AuthResponse } from '@/features';

interface ISignInDialogProps {
  onSuccessfulAuth: (data: AuthResponse) => void;
  setShowGuestDialog: (show: boolean) => void;
}

export const SignInDialog = ({
  onSuccessfulAuth,
  setShowGuestDialog,
}: ISignInDialogProps) => {
  const [isSignUp, setIsSignUp] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const login = searchParams.get('login');
  const register = searchParams.get('register');
  const [activeStep, setActiveStep] = useState(1);
  const { t } = useTranslation();

  const openGuestDialog = () => {
    navigate('/', { replace: true });
    setShowGuestDialog(true);
  };

  useEffect(() => {
    if (login === 'true') {
      setIsSignUp(false);
    }

    if (register === 'true') {
      setIsSignUp(true);
      setActiveStep(1);
    }
  }, [login, register]);

  return (
    <Dialog
      open={login === 'true' || register === 'true'}
      onOpenChange={(param) => {
        if (!param) {
          navigate('/', { replace: true });
        } else {
          setActiveStep(1);
          navigate(`/?login=true`, { replace: true });
        }
      }}
    >
      <DialogTrigger asChild>
        <Button
          data-testid="sign-in-button"
          className="w-full font-semibold !h-14"
          size="lg"
        >
          {t('home.buttons.signIn')}
        </Button>
      </DialogTrigger>

      <DialogContent
        className="sm:max-w-[600px] bg-card"
        onOpenAutoFocus={(e) => e.preventDefault()}
        data-testid={isSignUp ? 'sign-up-dialog' : 'sign-in-dialog'}
      >
        <DialogHeader className="mb-4">
          <DialogTitle>
            {isSignUp
              ? t('home.dialogs.signUp.title')
              : t('home.dialogs.signIn.title')}
          </DialogTitle>
          <DialogDescription />
        </DialogHeader>

        {isSignUp ? (
          <SignUpForm activeStep={activeStep} setActiveStep={setActiveStep} />
        ) : (
          <SignInForm
            onSuccessfulAuth={onSuccessfulAuth}
            openGuestDialog={openGuestDialog}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};
