import { Input } from '@/shadcn/components/ui/input';
import { Google, Microsoft } from '../icons';
import { FC } from 'react';
import { useLogin, useOAuth2Link } from '../../hooks';
import { useForm } from 'react-hook-form';
import { Loader2 } from 'lucide-react';
import { Button } from '@/shadcn/components/ui/button';
import { useTranslation } from 'react-i18next';
import { AnimatePresence, motion } from 'framer-motion';
import { useNavigate } from 'react-router';
import { ResendButton } from '../resend-button';
import { AuthResponse } from '@/features';
import { useUser } from '@/entities';

const LOGIN_ERRORS: Record<string, string> = {
  'invalid email or password': 'home.dialogs.signIn.errors.invalidCredentials',
};

interface ISignInFormProps {
  onSuccessfulAuth: (data: AuthResponse) => void;
  openGuestDialog: () => void;
}

interface FormValues {
  email: string;
  password: string;
}

export const SignInForm: FC<ISignInFormProps> = ({
  onSuccessfulAuth,
  openGuestDialog,
}) => {
  const navigate = useNavigate();
  const getLink = useOAuth2Link();
  const { mutate: login, isPending, error } = useLogin(onSuccessfulAuth);
  const loginError = error?.message
    ? (LOGIN_ERRORS[error.message.toLowerCase()] ??
      'home.dialogs.signIn.errors.unexpected')
    : null;
  const { t } = useTranslation();
  const { user } = useUser();

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
  } = useForm<FormValues>({ mode: 'onChange' });

  const email = watch('email');

  const onSubmit = (data: FormValues) => {
    login(data);
  };

  return (
    <form
      className="grid gap-4 text-card-foreground"
      onSubmit={handleSubmit(onSubmit)}
    >
      <div className="flex flex-col items-start gap-2">
        <Input
          data-testid="sign-in-email-input"
          id="email"
          className={
            errors.email || error?.message === 'invalid email or password'
              ? 'border-destructive'
              : 'border-[#00000030]'
          }
          placeholder={t('globalFields.email.label')}
          {...register('email', {
            required: t('globalFields.email.validation.required'),
          })}
          inputMode="email"
        />
        {errors.email && (
          <p className="text-destructive text-sm">{errors.email.message}</p>
        )}
      </div>

      <div className="flex flex-col items-start gap-2">
        <Input
          data-testid="sign-in-password-input"
          id="password"
          type="password"
          showPasswordToggle
          className={
            errors.password || error?.message === 'invalid email or password'
              ? 'border-destructive'
              : 'border-[#00000030]'
          }
          placeholder={t('globalFields.password.label')}
          {...register('password', {
            required: t('globalFields.password.validation.required'),
          })}
        />
        {errors.password && (
          <p className="text-destructive text-sm">{errors.password.message}</p>
        )}
      </div>

      <AnimatePresence>
        {error && error.message === 'Email not verified' ? (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="flex gap-2 items-center justify-between w-full max-[450px]:flex-col max-[450px]:items-start"
          >
            <div className="flex flex-col gap-1">
              <h1
                className="text-sm font-medium"
                dangerouslySetInnerHTML={{
                  __html: t(
                    'home.dialogs.signIn.errors.emailVerification.text1',
                    { email },
                  ),
                }}
              />
              <h3 className="text-sm text-primary/60">
                {t('home.dialogs.signIn.errors.emailVerification.text2')}
              </h3>
            </div>

            <ResendButton email={email ?? ''} />
          </motion.div>
        ) : (
          loginError && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="text-sm font-medium text-destructive text-center"
            >
              {t(loginError)}
            </motion.div>
          )
        )}
      </AnimatePresence>

      <div
        data-testid="sign-in-forgot-password-button"
        className="ml-auto w-fit text-sm font-medium opacity-80 transition-opacity duration-300 hover:opacity-100 cursor-pointer"
        onClick={() => {
          navigate('/?reset-password=true');
        }}
      >
        {t('home.dialogs.signIn.buttons.forgotPassword')}
      </div>

      <div className="w-full grid grid-cols-2 max-[500px]:grid-rows-2 max-[500px]:grid-cols-1 items-center gap-2">
        <Button
          data-testid="dialog-sign-in-button"
          type="submit"
          disabled={
            isPending ||
            !isValid ||
            errors.email !== undefined ||
            errors.password !== undefined
          }
          size="lg"
          className="w-full"
        >
          {isPending ? (
            <Loader2 className="animate-spin h-5 w-5" />
          ) : (
            t('home.dialogs.signIn.buttons.signIn')
          )}
        </Button>

        <Button
          type="button"
          data-testid="dialog-sign-up-button"
          size="lg"
          variant="outline"
          className="w-full"
          onClick={() => {
            navigate('/?register=true', { replace: true });
          }}
        >
          {t('home.dialogs.signIn.buttons.signUp')}
        </Button>
      </div>

      <div className="grid grid-cols-[1fr_auto_1fr] gap-2 items-center my-4">
        <div className="w-full h-px bg-black/20 rounded-full" />
        <h4 className="text-xs text-black/50">
          {t('home.dialogs.signIn.buttons.or')}
        </h4>
        <div className="w-full h-px bg-black/20 rounded-full" />
      </div>

      <div className="grid grid-cols-2 items-center justify-center gap-2">
        <Button
          data-testid="dialog-google-button"
          type="button"
          variant="monochrome_outline"
          className="w-full"
          size="lg"
          onClick={() => {
            window.location.href = getLink('google');
          }}
        >
          <Google className="fill-primary/80 size-4" />
          Google
        </Button>

        <Button
          data-testid="dialog-microsoft-button"
          type="button"
          variant="monochrome_outline"
          className="w-full"
          size="lg"
          onClick={() => {
            window.location.href = getLink('microsoft');
          }}
        >
          <Microsoft className="fill-primary/80 size-4" />
          Microsoft
        </Button>
      </div>

      {!user && (
        <div className="flex flex-col gap-2 mt-4">
          <h1
            data-testid="sign-in-guest-button"
            className="text-center text-primary/90 transition-colors duration-300 hover:text-primary cursor-pointer font-medium text-[15px]"
            onClick={openGuestDialog}
          >
            {t('home.dialogs.signIn.buttons.guest.text')}
          </h1>
          <p className="text-xs text-primary/80 text-center">
            {t('home.dialogs.signIn.buttons.guest.warning')}
          </p>
        </div>
      )}
    </form>
  );
};
