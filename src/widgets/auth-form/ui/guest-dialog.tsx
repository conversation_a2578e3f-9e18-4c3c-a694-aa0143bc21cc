import { Checkbox } from '@/shadcn/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { Input } from '@/shadcn/components/ui/input';
import { Button } from '@/shadcn/components/ui/button';
import { Loader2 } from 'lucide-react';
import { Link } from 'react-router';
import { useForm, Controller } from 'react-hook-form';
import { useRegisterAnonymous } from '../hooks';
import { Trans, useTranslation } from 'react-i18next';
import { useEffect } from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { AuthResponse, useDigitalConsentContext } from '@/features';

interface FormValues {
  nickname: string;
  language: string;
  terms: boolean;
}

interface IGuestDialogProps {
  onSuccessfulAuth: (data: AuthResponse) => void;
  show: boolean;
  setShow: (show: boolean) => void;
  instantClass: boolean;
}

export const GuestDialog = ({
  onSuccessfulAuth,
  show,
  setShow,
  instantClass,
}: IGuestDialogProps) => {
  const {
    handleSubmit,
    control,
    register,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: {
      nickname: '',
      terms: false,
    },
  });
  const { t } = useTranslation();
  const { mutate: anonymousRegister } = useRegisterAnonymous(onSuccessfulAuth);
  const { config: consentConfig } = useDigitalConsentContext();

  const onSubmit = (data: FormValues) => {
    anonymousRegister(data.nickname.trim());
  };

  useEffect(() => {
    if (show) reset();
  }, [show, reset]);

  const triggerButton = instantClass ? (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            data-testid="instant-class-button"
            className="w-full font-semibold !h-14"
            size="lg"
            onClick={() => setShow(true)}
          >
            {t('home.buttons.instant.text')}
          </Button>
        </TooltipTrigger>

        <TooltipContent className="max-w-[300px]">
          <p className="text-center">{t('home.buttons.instant.tooltip')}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  ) : (
    <Button
      data-testid="guest-login-button"
      className="w-full bg-card/90 hover:bg-card disabled:bg-card/60 disabled:hover:bg-card/60 text-card-foreground disabled:text-card-foreground/60 font-semibold !h-14"
      size="lg"
    >
      {t('home.buttons.guest')}
    </Button>
  );

  return (
    <Dialog open={show} onOpenChange={setShow}>
      <DialogTrigger asChild>{triggerButton}</DialogTrigger>

      <DialogContent
        className="sm:max-w-[450px] bg-card"
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <DialogHeader className="mb-4">
          <DialogTitle>
            {instantClass
              ? t('home.dialogs.instant.title')
              : t('home.dialogs.guest.title')}
          </DialogTitle>
          <DialogDescription>
            {instantClass
              ? t('home.dialogs.instant.description')
              : t('home.dialogs.guest.description')}
          </DialogDescription>
        </DialogHeader>

        <form className="grid gap-4" onSubmit={handleSubmit(onSubmit)}>
          <div className="flex flex-col gap-1">
            <Input
              data-testid="guest-display-name-input"
              id="nickname"
              placeholder={t('globalFields.displayName.label')}
              {...register('nickname', {
                required: t('globalFields.displayName.validation.required'),
                minLength: {
                  value: 3,
                  message: t('globalFields.displayName.validation.minLength'),
                },
                maxLength: {
                  value: 20,
                  message: t('globalFields.displayName.validation.maxLength'),
                },
              })}
              className={
                errors.nickname ? 'border-destructive' : 'border-[#00000030]'
              }
            />
            {errors.nickname && (
              <p
                data-testid="guest-display-name-error"
                className="text-xs text-destructive px-1"
              >
                {errors.nickname.message}
              </p>
            )}
          </div>

          <div className="flex flex-col items-start gap-1.5">
            <div className="flex items-center space-x-2 mt-2">
              <Controller
                control={control}
                name="terms"
                rules={{
                  validate: (v) =>
                    v ||
                    t('home.dialogs.guest.fields.terms.validation.required'),
                }}
                render={({ field }) => (
                  <Checkbox
                    data-testid="guest-terms-checkbox"
                    id="terms"
                    checked={field.value}
                    onCheckedChange={(checked) => field.onChange(checked)}
                    className="h-5 w-5 border-1 border-accent data-[state=checked]:bg-accent data-[state=checked]:border-transparent"
                  />
                )}
              />
              <label
                htmlFor="terms"
                className="text-sm font-medium text-card-foreground/80"
              >
                {t('home.dialogs.guest.fields.terms.text1')}

                <Link
                  to="/legal/terms-and-conditions"
                  target="_blank"
                  className="text-accent underline"
                >
                  {t('home.dialogs.guest.fields.terms.text2')}
                </Link>
                {t('home.dialogs.guest.fields.terms.text3')}
                <Link
                  to="/legal/privacy-policy"
                  target="_blank"
                  className="text-accent underline"
                >
                  {t('home.dialogs.guest.fields.terms.text4')}
                </Link>
                <Trans
                  i18nKey="home.dialogs.guest.fields.terms.text5"
                  values={{ age: consentConfig?.digitalAge ?? 18 }}
                />
              </label>
            </div>
            {errors.terms && (
              <p
                data-testid="guest-terms-error"
                className="text-xs text-destructive px-1.5"
              >
                {errors.terms.message}
              </p>
            )}
          </div>

          <DialogFooter className="mt-4 w-full">
            <Button
              data-testid="guest-submit-button"
              type="submit"
              variant="default"
              size="lg"
              disabled={isSubmitting}
              className="max-[450px]:text-sm w-full mx-auto !px-[32px] !py-4 whitespace-break-spaces"
            >
              {isSubmitting ? (
                <Loader2 className="animate-spin h-5 w-5" />
              ) : instantClass ? (
                t('home.dialogs.instant.buttons.continue')
              ) : (
                t('home.dialogs.guest.buttons.continue')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
