import { GuestDialog } from './guest-dialog';
import { SignInDialog } from './sign-in-dialog';
import { useCookies } from 'react-cookie';
import { Link, useNavigate } from 'react-router';
import { useAppDispatch } from '@/shared';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { setUser, useUser } from '@/entities';
import { OnboardingDialog } from './onboarding-dialog';
import { useLocation } from 'react-router';
import { useCreateClass, useCreateClassroom } from '@/entities';
import {
  AuthResponse,
  RequestResetPasswordDialog,
  ResetPasswordDialog,
} from '@/features';

export const AuthForm = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const from = localStorage.getItem('inviteRedirect');
  const location = useLocation();
  const [showGuestDialog, setShowGuestDialog] = useState(false);
  const [showInstantClassDialog, setShowInstantClassDialog] = useState(false);
  const dispatch = useAppDispatch();

  const { mutate: createClass } = useCreateClass(true);
  const { mutate: createClassroom } = useCreateClassroom((id: string) => {
    createClass({ id, title: '' });
  });

  const { user } = useUser();
  const [, setCookie] = useCookies(['token', 'refreshToken']);

  const onSuccessfulAuth = (authData: AuthResponse) => {
    const { data } = authData;

    setCookie('token', data.token, {
      path: '/',
      maxAge: data.expiresAt,
      secure: true,
      sameSite: 'lax',
    });
    setCookie('refreshToken', data.refreshToken, {
      path: '/',
      maxAge: data.expiresAt,
      secure: true,
      sameSite: 'lax',
    });

    dispatch(setUser(data.user));

    // Track successful login with Google Analytics
    // Note: Guest accounts are tracked as sign_up events, not login events
    if (window.gtag && !data.user.isAnonymous) {
      window.gtag('event', 'login', {
        method: 'email',
        user_id: data.user.id,
        page_path: location.pathname,
      });
    }

    navigate(from && from !== 'undefined' ? from : '/classrooms', {
      replace: true,
    });
  };

  const onSuccessfulInstantClassAuth = (authData: AuthResponse) => {
    const { data } = authData;

    setCookie('token', data.token, {
      path: '/',
      maxAge: data.expiresAt,
      secure: true,
      sameSite: 'lax',
    });
    setCookie('refreshToken', data.refreshToken, {
      path: '/',
      maxAge: data.expiresAt,
      secure: true,
      sameSite: 'lax',
    });

    dispatch(setUser(data.user));

    createClassroom({
      title: t('classrooms.dialogs.create.titlePlaceholder', {
        username: data.user.displayName,
      }),
      description: ' ',
    });
  };

  return (
    <div
      className="m-auto w-fit h-fit z-[6] flex items-center flex-col gap-[8px] p-[calc(env(safe-area-inset-*))]"
      style={{
        paddingTop: 'max(env(safe-area-inset-top), 1rem)',
        paddingLeft: 'max(env(safe-area-inset-left), 1rem)',
        paddingRight: 'max(env(safe-area-inset-right), 1rem)',
        paddingBottom: 'max(env(safe-area-inset-bottom), 1rem)',
      }}
    >
      <h1 className="text-[56px] max-[800px]:text-[40px] font-bold text-primary-foreground">
        VirtuosoHub
      </h1>
      <h3 className="text-xl max-[430px]:text-[18px] px-5 text-semibold text-primary-foreground text-center">
        {t('home.description')}
      </h3>
      <div className="flex flex-col items-center justify-stretch gap-4 mt-[32px] max-w-[20rem] w-full">
        {user && <OnboardingDialog />}

        {!user || user.isAnonymous ? (
          <SignInDialog
            setShowGuestDialog={setShowGuestDialog}
            onSuccessfulAuth={onSuccessfulAuth}
          />
        ) : null}
        {!user && (
          <>
            <GuestDialog
              setShow={setShowGuestDialog}
              show={showGuestDialog}
              onSuccessfulAuth={onSuccessfulAuth}
              instantClass={false}
            />

            <RequestResetPasswordDialog />
            <ResetPasswordDialog />
          </>
        )}

        {!user || user.isAnonymous ? (
          <>
            <GuestDialog
              setShow={setShowInstantClassDialog}
              show={showInstantClassDialog}
              onSuccessfulAuth={onSuccessfulInstantClassAuth}
              instantClass={true}
            />

            <Link
              to="/?register=true"
              className="text-white mt-2 group cursor-pointer"
            >
              {t('home.buttons.noAccount.text')}
              <span className="underline group-hover:opacity-100 opacity-80 transition-opacity duration-300">
                {t('home.buttons.noAccount.link')}
              </span>
            </Link>
          </>
        ) : null}
      </div>
    </div>
  );
};
