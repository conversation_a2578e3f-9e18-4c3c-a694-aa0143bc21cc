import { getProviderLink } from '@/shared';
import { useResendVerificationEmail } from '../hooks';
import { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';

interface IResendButtonProps {
  email: string;
  activeStep?: number;
}

export const ResendButton: FC<IResendButtonProps> = ({ email, activeStep }) => {
  const { t } = useTranslation();
  const [resendTimeout, setResendTimeout] = useState(activeStep ? 30 : 0);
  const [resendAllowed, setResendAllowed] = useState(activeStep ? false : true);
  const { mutate: resendVerificationEmail, isPending: isResendLoading } =
    useResendVerificationEmail();

  const startResendInterval = () => {
    if ((activeStep === 5 && resendTimeout === 30) || !activeStep) {
      setResendAllowed(false);
      setResendTimeout(30);
      const resendInterval = setInterval(() => {
        setResendTimeout((prev) => {
          if (prev <= 1) {
            clearInterval(resendInterval);
            setResendAllowed(true);
            return 30;
          }
          return prev - 1;
        });
      }, 1000);
    }
  };

  useEffect(() => {
    if (activeStep) startResendInterval();
  }, [activeStep]);

  const resendButton = (
    <button
      data-testid="resend-button"
      className="w-fit px-4 py-2 rounded-full border border-primary/60 text-sm cursor-pointer transition-colors duration-300 hover:bg-primary/10 text-primary disabled:opacity-20 disabled:cursor-not-allowed whitespace-nowrap"
      onClick={() => {
        const link = getProviderLink(email);
        if (link) window.open(link, '_blank');
        resendVerificationEmail(email);
        startResendInterval();
      }}
      disabled={isResendLoading || !resendAllowed}
      type="button"
    >
      {resendAllowed
        ? t('home.dialogs.signUp.fields.complete.resend.button')
        : t('home.dialogs.signUp.fields.complete.resend.disabled', {
            time: resendTimeout,
          })}
    </button>
  );

  if (resendAllowed) return resendButton;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span>{resendButton}</span>
        </TooltipTrigger>
        <TooltipContent>
          <p>
            {t('home.dialogs.signUp.fields.complete.resend.tooltip', {
              time: resendTimeout,
            })}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
