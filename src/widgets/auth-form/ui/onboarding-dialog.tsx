import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shadcn/components/ui/dialog';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router';

export const OnboardingDialog = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const onboarding = searchParams.get('onboarding');
  const { t } = useTranslation();

  return (
    <Dialog
      open={onboarding === 'true'}
      onOpenChange={(param) => {
        navigate(`/${param ? '?onboarding=true' : ''}`, { replace: true });
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('home.dialogs.onboarding.title')}</DialogTitle>
          <DialogDescription>
            {t('home.dialogs.onboarding.text')}
          </DialogDescription>
        </DialogHeader>

        <div className="w-full grid grid-cols-2 mt-4 gap-4 max-[500px]:grid-cols-1">
          <Button
            data-testid="onboarding-dialog-dashboard-button"
            size="lg"
            variant="outline"
            className="w-full"
            onClick={() => navigate('/classrooms', { replace: true })}
          >
            {t('home.dialogs.onboarding.buttons.dashboard')}
          </Button>
          <Button
            data-testid="onboarding-dialog-create-button"
            size="lg"
            className="w-full"
            onClick={() =>
              navigate('/classrooms?create=true', { replace: true })
            }
          >
            {t('home.dialogs.onboarding.buttons.create')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
