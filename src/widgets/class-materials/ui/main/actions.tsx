import { Button } from '@/shadcn/components/ui/button';
import { DisabledTooltip, UploadMaterialDialog } from '@/features';
import { Music } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router';
import { useACL, useClassroomSettings } from '@/shared';
import { useTabsContext } from '../../context';

export const Actions = () => {
  const { setActiveTab } = useTabsContext();
  const { t } = useTranslation();
  const { id: classroomId } = useParams<{
    id: string;
  }>();
  const acl = useACL();
  const classroomSettings = useClassroomSettings();

  return (
    <div
      className="w-full grid grid-cols-2 gap-4 max-[500px]:grid-cols-1"
      data-testid="materials-actions-container"
    >
      <DisabledTooltip
        tooltipText="globals.permissions.canDownloadMaterials"
        disabled={
          acl && classroomSettings
            ? !(
                acl.canUploadMaterials && classroomSettings.allowMaterialsUpload
              )
            : true
        }
      >
        <UploadMaterialDialog classroomId={classroomId ?? ''} type="class" />
      </DisabledTooltip>
      <Button
        size="lg"
        className="w-full border border-white/30 text-white bg-white/5 hover:bg-white/10"
        onClick={() => setActiveTab?.('imslpSearch')}
        data-testid="materials-search-scores-button"
      >
        <Music />
        {t('class.tools.materials.main.actions.scores')}
      </Button>
    </div>
  );
};
