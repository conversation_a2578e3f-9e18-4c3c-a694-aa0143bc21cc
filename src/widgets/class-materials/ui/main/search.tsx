import { DisabledTooltip } from '@/features';
import { Search as SearchIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';

export const Search = () => {
  const { t } = useTranslation();

  return (
    <DisabledTooltip tooltipText={'class.tools.materials.main.search.disabled'}>
      <label
        htmlFor="search"
        id="search"
        className="flex items-center gap-2 px-3.5 border rounded-xl border-white/30 opacity-50 cursor-not-allowed"
      >
        <SearchIcon className="size-5 text-white/70" />
        <input
          disabled
          name="search"
          placeholder={t('class.tools.materials.main.search.placeholder')}
          className="py-2 outline-none border-none w-full text-white !bg-transparent placeholder:text-white/70 cursor-not-allowed"
          autoComplete="off"
        />
      </label>
    </DisabledTooltip>
  );
};
