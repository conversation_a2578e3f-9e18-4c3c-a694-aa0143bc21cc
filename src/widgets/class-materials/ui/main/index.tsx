import { Header } from './header';
import { Search } from './search';
import { Actions } from './actions';
import { useACL } from '@/shared';
import { useParams } from 'react-router';
import { useEffect, useState } from 'react';
import { useMaterials } from '@/entities';
import { Pagination } from '../../../pagination';
import { ClassMaterialsList } from './class-material-list';
import { ClassMaterialsListSkeleton } from './class-material-list/skeleton';
import { useTabsContext } from '../../context';

export const Main = () => {
  const { id: classroomId } = useParams<{
    id: string;
  }>();
  const { setActiveTab, activeTabParams, setActiveTabParams, setViewerType } =
    useTabsContext();
  const [fetchMaterialsEnabled, setFetchMaterialsEnabled] = useState(false);

  const acl = useACL();
  const {
    materials,
    currentPage,
    totalPages,
    totalItems,
    handlePageChange,
    isPending,
  } = useMaterials(classroomId ?? '', { limit: 8 }, fetchMaterialsEnabled);

  useEffect(() => {
    if (acl) {
      if (acl.canDownloadMaterials) setFetchMaterialsEnabled(true);
    }
  }, [acl]);

  return (
    <>
      <Header fileCount={totalItems} />
      <Search />
      <Actions />

      {isPending && fetchMaterialsEnabled ? (
        <ClassMaterialsListSkeleton />
      ) : (
        <div className="space-y-8 grid grid-rows-[1fr_auto] h-full">
          <ClassMaterialsList
            materials={materials}
            enabled={fetchMaterialsEnabled}
            onClick={(title, url) => {
              setViewerType?.('uploaded');
              setActiveTabParams?.({
                title: activeTabParams?.title || '',
                itemTitle: title,
                url,
              });
              setActiveTab?.('viewer');
            }}
          />
          {materials.length > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              handlePageChange={handlePageChange}
            />
          )}
        </div>
      )}
    </>
  );
};
