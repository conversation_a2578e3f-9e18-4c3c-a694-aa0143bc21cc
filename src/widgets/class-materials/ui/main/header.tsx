import {
  SheetClose,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/shadcn/components/ui/sheet';
import { DisabledTooltip } from '@/features';
import { ArrowDownAZ, ListFilter, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface IHeaderProps {
  fileCount: number;
}

export const Header = ({ fileCount }: IHeaderProps) => {
  const { t } = useTranslation();

  return (
    <SheetHeader className="p-0 mb-2 flex flex-row items-center justify-between gap-4 flex-wrap">
      <div className="flex items-center justify-center gap-4">
        <SheetTitle className="text-white text-[17.1px] font-semibold">
          {t('class.tools.materials.main.header.title')}
        </SheetTitle>

        <div className="text-xs font-medium text-white p-2 bg-white/10 rounded-2xl">
          {t('class.tools.materials.main.header.files', { count: fileCount })}
        </div>
      </div>

      <SheetDescription />

      <div className="p-1 flex items-center justify-center gap-6">
        <DisabledTooltip
          tooltipText={'class.tools.materials.main.header.sorting.disabled'}
        >
          <ArrowDownAZ className="size-6 text-white opacity-50 cursor-not-allowed" />
        </DisabledTooltip>

        <DisabledTooltip
          tooltipText={'class.tools.materials.main.header.filter.disabled'}
        >
          <ListFilter className="size-6 text-white opacity-50 cursor-not-allowed" />
        </DisabledTooltip>

        <SheetClose className="cursor-pointer" asChild>
          <X className="size-6 text-white hover:opacity-100 opacity-90 transition-opacity duration-300" />
        </SheetClose>
      </div>
    </SheetHeader>
  );
};
