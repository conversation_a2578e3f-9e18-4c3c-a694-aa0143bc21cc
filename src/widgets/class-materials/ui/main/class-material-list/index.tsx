import { cn } from '@/shadcn/lib/utils';
import { formatFileSize } from '@/shared';
import { cva } from 'class-variance-authority';
import { FileText } from 'lucide-react';
import moment from 'moment';
import { useTranslation } from 'react-i18next';
import { Material, useDownloadMaterial } from '@/entities';
import { DeleteMaterialDialog } from '@/features';
import { useParams } from 'react-router';

interface IClassMaterialsListProps {
  materials: Material[];
  enabled: boolean;
  onClick: (title: string, url: string) => void;
}

export const ClassMaterialsList = ({
  materials,
  enabled,
  onClick,
}: IClassMaterialsListProps) => {
  const { t } = useTranslation();
  const { id: classroomId } = useParams<{ id: string }>();

  if (!enabled) {
    return (
      <div
        className="flex flex-col gap-2 items-center justify-center"
        data-testid="class-materials-empty"
      >
        <h1
          className="font-medium text-white text-lg mt-4"
          data-testid="class-materials-empty-title"
        >
          {t('class.tools.materials.main.noAccess.title')}
        </h1>
        <h3
          className="text-white/70 text-sm text-center"
          data-testid="class-materials-empty-description"
        >
          {t('class.tools.materials.main.noAccess.description')}
        </h3>
      </div>
    );
  }

  if (!materials.length && enabled) {
    return (
      <div
        className="flex flex-col gap-2 items-center justify-center"
        data-testid="class-materials-empty"
      >
        <h1
          className="font-medium text-white text-lg mt-4"
          data-testid="class-materials-empty-title"
        >
          {t('class.tools.materials.main.empty.title')}
        </h1>
        <h3
          className="text-white/70 text-sm text-center"
          data-testid="class-materials-empty-description"
        >
          {t('class.tools.materials.main.empty.description')}
        </h3>
      </div>
    );
  }

  return (
    <div
      className="flex flex-col gap-2 mt-2"
      data-testid="class-materials-container"
    >
      {materials.map((m) => (
        <MaterialRow
          key={m.id}
          classroomId={classroomId ?? ''}
          material={m}
          onOpen={(url) => onClick(m.title, url)}
        />
      ))}
    </div>
  );
};

function MaterialRow({
  classroomId,
  material,
  onOpen,
}: {
  classroomId: string;
  material: Material;
  onOpen: (url: string) => void;
}) {
  const { i18n } = useTranslation();

  const { refetch, isFetching } = useDownloadMaterial(
    classroomId,
    material.id,
    false,
  );

  const previewVariants = cva(
    'size-10 rounded-full flex items-center justify-center',
    {
      variants: {
        type: {
          pdf: 'bg-red-500/40',
          audio: 'bg-green-500/40',
          video: 'bg-blue-500/40',
        },
      },
      defaultVariants: { type: 'pdf' },
    },
  );

  const handleClick = async () => {
    const { data: url } = await refetch();
    if (url) onOpen(url);
  };

  return (
    <div
      className="p-3 border border-white/15 bg-white/8 hover:bg-white/10 hover:border-white/20
                 grid grid-cols-[1fr_auto] gap-4 rounded-xl cursor-pointer transition-colors duration-300 w-full"
      data-testid={`class-material-row-${material.id}`}
    >
      <div
        className="grid grid-cols-[auto_1fr] gap-3 w-full min-w-0"
        onClick={handleClick}
        data-testid={`class-material-content-${material.id}`}
      >
        <div
          className={cn(previewVariants({ type: 'pdf' }))}
          data-testid={`class-material-icon-${material.id}`}
        >
          <FileText className="size-4" />
        </div>

        <div
          className="flex flex-col gap-1 min-w-0"
          data-testid={`class-material-info-${material.id}`}
        >
          <h1
            className="text-sm text-white font-medium truncate"
            data-testid={`class-material-title-${material.id}`}
          >
            {material.title}
          </h1>
          <div
            className="text-xs text-white/50 capitalize flex items-center gap-1"
            data-testid={`class-material-metadata-${material.id}`}
          >
            <h4 className="uppercase">{'pdf'}</h4>
            <h4>{' • '}</h4>
            <h4 className="capitalize">
              {moment(material.updatedAt * 1000)
                .locale(i18n.language)
                .format('MMM D, YYYY')}
            </h4>
            <h4>{' • '}</h4>
            <h4>{formatFileSize(material.fileSize)}</h4>
          </div>
        </div>
      </div>

      <div
        className="flex items-center gap-2"
        data-testid={`class-material-actions-${material.id}`}
      >
        {isFetching && (
          <span
            className="text-xs text-white/60"
            data-testid={`class-material-loading-${material.id}`}
          >
            …
          </span>
        )}
        <DeleteMaterialDialog
          classroomId={classroomId}
          material={material}
          type="class"
        />
      </div>
    </div>
  );
}
