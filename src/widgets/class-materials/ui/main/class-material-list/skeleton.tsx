export const ClassMaterialsListSkeleton = () => {
  return (
    <>
      {Array.from({ length: 8 }).map((_, i) => (
        <div
          key={i}
          className="p-3 border border-white/15 bg-white/5 flex items-center justify-between gap-4 rounded-xl animate-pulse"
        >
          <div className="flex items-center gap-3">
            <div className="size-10 rounded-full bg-white/10" />
            <div className="flex flex-col gap-2">
              <div className="h-4 w-32 bg-white/10 rounded" />
              <div className="h-3 w-48 bg-white/10 rounded" />
            </div>
          </div>
          <div className="size-5 rounded bg-white/10" />
        </div>
      ))}
    </>
  );
};
