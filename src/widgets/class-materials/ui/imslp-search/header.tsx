import {
  SheetDes<PERSON>,
  SheetHeader,
  SheetTitle,
} from '@/shadcn/components/ui/sheet';
import { ArrowLeft } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useTabsContext } from '../../context';

export const Header = () => {
  const { setActiveTab } = useTabsContext();
  const { t } = useTranslation();

  return (
    <SheetHeader
      className="p-0 mb-2 flex flex-row items-center justify-between gap-4"
      data-testid="imslp-search-header"
    >
      <div className="flex items-center justify-center gap-4">
        <ArrowLeft
          onClick={() => setActiveTab?.('main')}
          className="opaccity-90 transition-opacity duration-300 hover:opacity-100 cursor-pointer"
          data-testid="imslp-search-back-button"
        />

        <SheetTitle
          className="text-white text-[17.1px] font-semibold"
          data-testid="imslp-search-title"
        >
          {t('class.tools.materials.imslpSearch.header.title')}
        </SheetTitle>
      </div>

      <SheetDescription />
    </SheetHeader>
  );
};
