import { Info } from 'lucide-react';
import { useTranslation } from 'react-i18next';

export const Alert = () => {
  const { t } = useTranslation();

  return (
    <div className="w-full grid grid-cols-[auto_1fr] gap-3 px-4 py-3.5 bg-[#2196F3]/10 rounded-2xl mb-2">
      <Info className="size-5 text-[#9EC1CC]" />
      <p className="text-[#9EC1CC] text-sm">
        {t('class.tools.materials.imslpSearch.alert')}
      </p>
    </div>
  );
};
