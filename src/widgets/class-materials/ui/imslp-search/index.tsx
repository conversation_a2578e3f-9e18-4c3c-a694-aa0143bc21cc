import { useEffect, useState } from 'react';
import { Alert } from './alert';
import { Header } from './header';
import { Search } from './search';
import { SearchResults } from './search-results';
import { useTabsContext } from '../../context';
import { usePages } from '../../hooks';

export const IMSLPSearch = () => {
  const { query } = useTabsContext();
  const [error, setError] = useState<string | undefined>();
  const { isPending, data, mutate } = usePages();

  useEffect(() => {
    if (query) mutate(query);
  }, [query, mutate]);

  return (
    <>
      <Header />
      <Alert />
      <Search isLoading={isPending} mutate={mutate} setError={setError} />
      <SearchResults
        isLoading={isPending}
        searchResults={query === '' ? undefined : data}
        error={error}
      />
    </>
  );
};
