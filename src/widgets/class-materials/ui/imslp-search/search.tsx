import { Search as SearchIcon } from 'lucide-react';
import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTabsContext } from '../../context';

interface ISearchProps {
  isLoading: boolean;
  mutate: (query: string) => void;
  setError: (error: string | undefined) => void;
}

export const Search: FC<ISearchProps> = ({ isLoading, mutate, setError }) => {
  const { t } = useTranslation();
  const { query, setQuery } = useTabsContext();
  const [inputValue, setInputValue] = useState(query ? query : '');
  const englishWithSymbols =
    /^[a-zA-Z0-9\s.,!?;:'"()\-_@#$%&*+=[\]{}|\\/<>~`]+$/;

  const handleSearch = () => {
    if (
      !inputValue ||
      inputValue.trim() === '' ||
      !englishWithSymbols.test(inputValue.trim())
    )
      return;
    mutate(inputValue.trim());
    setQuery?.(inputValue.trim());
  };

  return (
    <div
      className="w-full grid grid-cols-[1fr_auto] items-center gap-2 mb-2"
      data-testid="imslp-search-container"
    >
      <input
        disabled={isLoading}
        type="text"
        placeholder={t('class.tools.materials.imslpSearch.search.placeholder')}
        className="p-3.5 rounded-xl border-white/15 outline-none border w-full text-white !bg-transparent placeholder:text-white/50 focus:border-white/30 transition-colors duration-300"
        value={inputValue}
        data-testid="imslp-search-input"
        onChange={(e) => {
          if (
            e.target.value !== '' &&
            !englishWithSymbols.test(e.target.value)
          ) {
            setError(
              'class.tools.materials.imslpSearch.searchResults.errors.onlyEnglish',
            );
          } else {
            setError(undefined);
          }
          if (e.target.value.length === 0) setQuery?.('');
          setInputValue(e.target.value);
        }}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            handleSearch();
          }
        }}
      />
      <button
        className="size-13.5 flex items-center justify-center bg-transparent transition-all duration-300 hover:bg-white/5 border border-white/15 hover:border-white/20 disabled:text-white/50 disabled:border-white/10 disabled:hover:!border-white/10 disabled:hover:!bg-transparent text-white rounded-full cursor-pointer disabled:cursor-not-allowed"
        disabled={!inputValue || inputValue.trim() === '' || isLoading}
        onClick={handleSearch}
        data-testid="imslp-search-button"
      >
        <SearchIcon className="size-4" />
      </button>
    </div>
  );
};
