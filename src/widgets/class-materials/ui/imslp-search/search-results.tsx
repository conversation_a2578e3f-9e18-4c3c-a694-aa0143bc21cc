import { Loader } from '@/shared';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useTabsContext } from '../../context';

interface ISearchResultsProps {
  searchResults?: {
    title: string;
  }[];
  isLoading: boolean;
  error: string | undefined;
}

const extractAuthorFromTitle = (
  title: string,
): { title: string; author: string | null } => {
  const match = title.match(/\(([^()]+)\)$/);
  if (match) {
    const author = match[1].trim();
    const titleWithoutAuthor = title.replace(/\s*\([^()]+\)$/, '').trim();
    return { title: titleWithoutAuthor, author };
  }
  return { title, author: null };
};

export const SearchResults: FC<ISearchResultsProps> = ({
  searchResults,
  isLoading,
  error,
}) => {
  const { t } = useTranslation();
  const { setActiveTab, setActiveTabParams } = useTabsContext();

  if (error)
    return (
      <div className="flex flex-col items-center justify-center gap-2 h-full">
        <h1 className="font-medium text-white text-lg mt-4 text-center">
          {t('class.tools.materials.imslpSearch.searchResults.errors.title')}
        </h1>
        <h3 className="text-white/70 text-sm text-center">{t(error)}</h3>
      </div>
    );

  return (
    <div
      className="flex flex-col gap-2 h-full"
      data-testid="imslp-search-results-container"
    >
      {isLoading ? (
        <Loader />
      ) : !searchResults ? (
        <div
          className="h-full flex flex-col gap-2 items-center justify-center"
          data-testid="imslp-search-empty-state"
        >
          <h1 className="font-medium text-white text-lg mt-4">
            {t('class.tools.materials.imslpSearch.searchResults.empty.title')}
          </h1>
          <h3 className="text-white/70 text-sm text-center">
            {t(
              'class.tools.materials.imslpSearch.searchResults.empty.description',
            )}
          </h3>
        </div>
      ) : searchResults.length > 0 ? (
        searchResults.map((result, index) => {
          const { title, author } = extractAuthorFromTitle(result.title);

          return (
            <div
              className="p-3 border border-white/15 bg-white/8 hover:bg-white/10 hover:border-white/20 flex flex-col gap-1 rounded-xl cursor-pointer transition-colors duration-300"
              key={index}
              data-testid={`imslp-search-result-${index}`}
              onClick={() => {
                setActiveTabParams?.({ title: result.title });
                setActiveTab?.('imslpSearchResult');
              }}
            >
              <h3
                className="text-sm text-white/70"
                data-testid={`imslp-search-result-author-${index}`}
              >
                {author}
              </h3>
              <h1
                className="text-base text-white font-medium"
                data-testid={`imslp-search-result-title-${index}`}
              >
                {title}
              </h1>
            </div>
          );
        })
      ) : (
        <h1
          className="text-white/70 text-sm text-center"
          data-testid="imslp-search-no-results"
        >
          {t('class.tools.materials.imslpSearch.searchResults.notFound')}
        </h1>
      )}
    </div>
  );
};
