import { PDFViewer } from '@/features';
import { useTabsContext } from '../context';

export const Viewer = () => {
  const { activeTabParams, viewerType, setActiveTab } = useTabsContext();

  const handleDownload = () => {
    window.open(activeTabParams?.url ?? '', '_blank');
  };

  if (activeTabParams && activeTabParams.url)
    return (
      <PDFViewer
        data-testid="materials-pdf-viewer"
        url={activeTabParams?.url ?? ''}
        name={activeTabParams?.itemTitle ?? ''}
        type={viewerType === 'uploaded' ? 'class' : 'imslp'}
        download={handleDownload}
        onClose={() => {
          setActiveTab?.(
            viewerType === 'uploaded' ? 'main' : 'imslpSearchResult',
          );
        }}
      />
    );
  else return <></>;
};
