import { Folder } from 'lucide-react';
import { motion } from 'framer-motion';
import { cva } from 'class-variance-authority';
import { useTranslation } from 'react-i18next';
import { Sidebar, useACL, useClassroomSettings } from '@/shared';
import { TabsProvider, useTabsContext } from '../context';
import { Main } from './main';
import { IMSLPSearch } from './imslp-search';
import { IMSLPSearchResult } from './imslp-search-result';
import { Viewer } from './viewer';

const ClassMaterials = () => {
  const { t } = useTranslation();
  const { activeTab } = useTabsContext();
  const tabs = [
    {
      name: 'main',
      component: Main,
    },
    {
      name: 'imslpSearch',
      component: IMSLPSearch,
    },
    {
      name: 'imslpSearchResult',
      component: IMSLPSearchResult,
    },
    {
      name: 'viewer',
      component: Viewer,
    },
  ];
  const acl = useACL();
  const classroomSettings = useClassroomSettings();

  const sidebarStyle = cva('', {
    variants: {
      activeTab: {
        main: 'min-[1200px]:min-w-[550px]',
        imslpSearch: 'min-[1200px]:min-w-[50%]',
        imslpSearchResult: 'min-[1200px]:min-w-[50%]',
        viewer:
          'min-[1200px]:min-w-[550px] [&>#sidebar-content-container]:overflow-hidden',
      },
    },
    defaultVariants: {
      activeTab: 'main',
    },
  });

  return (
    <Sidebar
      disabled={
        !acl ||
        !classroomSettings ||
        !classroomSettings.allowMaterialsDownload ||
        !acl.canDownloadMaterials
      }
      title={t('class.tools.materials.title')}
      tooltip={t('class.tools.materials.tooltip')}
      triggerIcon={<Folder />}
      contentClassName={sidebarStyle({ activeTab })}
      hideHeader
      data-testid="action-bar-materials-button"
    >
      <div className="h-full relative flex-1">
        {tabs.map(({ component: TabComponent, name }) => {
          const isActive = activeTab === name;

          return (
            <motion.div
              key={name}
              initial={false}
              animate={{
                opacity: isActive ? 1 : 0,
                pointerEvents: isActive ? 'auto' : 'none',
                transition: { duration: 0.3, ease: 'easeInOut' },
              }}
              className={`${
                isActive ? 'flex' : 'hidden'
              } flex-col flex-1 overflow-auto gap-4 transparent-scrollbar h-full px-2`}
            >
              <TabComponent />
            </motion.div>
          );
        })}
      </div>
    </Sidebar>
  );
};

export const ClassMaterialsWrapper = () => {
  return (
    <TabsProvider>
      <ClassMaterials />
    </TabsProvider>
  );
};
