import {
  SheetDes<PERSON>,
  SheetHeader,
  SheetTitle,
} from '@/shadcn/components/ui/sheet';
import { ArrowLeft } from 'lucide-react';
import { FC } from 'react';
import { useTabsContext } from '../../context';

interface IHeaderProps {
  title: string;
}

export const Header: FC<IHeaderProps> = ({ title }) => {
  const { setActiveTab } = useTabsContext();

  return (
    <SheetHeader
      className="p-0 mb-2 flex flex-row items-center justify-between gap-4"
      data-testid="imslp-result-header"
    >
      <div className="grid grid-cols-[1.5rem_auto] items-center justify-center gap-4">
        <ArrowLeft
          onClick={() => setActiveTab?.('imslpSearch')}
          className="opaccity-90 transition-opacity duration-300 hover:opacity-100 cursor-pointer size-6"
          data-testid="imslp-result-back-button"
        />

        <SheetTitle
          className="text-white text-[17.1px] font-semibold"
          data-testid="imslp-result-title"
        >
          {title}
        </SheetTitle>
      </div>

      <SheetDescription />
    </SheetHeader>
  );
};
