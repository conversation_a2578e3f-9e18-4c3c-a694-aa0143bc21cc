import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/shadcn/components/ui/accordion';
import { cn } from '@/shadcn/lib/utils';
import { Loader, useNotifications } from '@/shared';
import { cva } from 'class-variance-authority';
import { Copy, Download, FileText } from 'lucide-react';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useTabsContext } from '../../context';
import { parseIMSLPWikitext } from '../../utils';

interface IResultsProps {
  isLoading: boolean;
  wikitext: string;
}

export const Results: FC<IResultsProps> = ({ isLoading, wikitext }) => {
  const { t } = useTranslation();
  const { setActiveTab, activeTabParams, setActiveTabParams, setViewerType } =
    useTabsContext();
  const { addToast } = useNotifications();
  const parsedData = parseIMSLPWikitext(wikitext);

  const previewVariants = cva(
    'size-10 rounded-full flex items-center justify-center',
    {
      variants: {
        type: {
          pdf: 'bg-red-500/40',
          audio: 'bg-green-500/40',
          video: 'bg-blue-500/40',
        },
      },
      defaultVariants: {
        type: 'pdf',
      },
    },
  );

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <Loader />
      </div>
    );
  }

  if (Object.values(parsedData.workInfo).every((value) => value === null)) {
    return (
      <div className="h-full flex flex-col gap-2 items-center justify-center">
        <h1 className="font-medium text-white text-lg mt-4">
          {t('class.tools.materials.imslpSearchResults.results.notFound.title')}
        </h1>
        <h3 className="text-white/70 text-sm text-center">
          {t(
            'class.tools.materials.imslpSearchResults.results.notFound.description',
          )}
        </h3>
      </div>
    );
  }

  return (
    <div
      className="flex flex-col gap-4"
      data-testid="imslp-result-details-container"
    >
      <h1
        className="text-base uppercase font-semibold"
        data-testid="imslp-work-info-header"
      >
        {t(
          'class.tools.materials.imslpSearchResults.results.workInformation.title',
        )}
      </h1>

      <div
        className="flex flex-col gap-4 p-6 border border-white/15 bg-white/8 shadow-[0_2px_8px_rgba(0,0,0,0.04)] rounded-xl"
        data-testid="imslp-work-info-section"
      >
        {parsedData.workInfo.workTitle && (
          <WorkInfoItem
            label={t(
              'class.tools.materials.imslpSearchResults.results.workInformation.labels.title',
            )}
            value={parsedData.workInfo.workTitle}
          />
        )}

        {parsedData.workInfo.alternativeTitle && (
          <WorkInfoItem
            label={t(
              'class.tools.materials.imslpSearchResults.results.workInformation.labels.altTitle',
            )}
            value={parsedData.workInfo.alternativeTitle}
          />
        )}

        {parsedData.workInfo.opusNumber && (
          <WorkInfoItem
            label={t(
              'class.tools.materials.imslpSearchResults.results.workInformation.labels.opus',
            )}
            value={parsedData.workInfo.opusNumber}
          />
        )}

        {parsedData.workInfo.yearOfComposition && (
          <WorkInfoItem
            label={t(
              'class.tools.materials.imslpSearchResults.results.workInformation.labels.year',
            )}
            value={parsedData.workInfo.yearOfComposition}
          />
        )}

        <div className="flex items-start gap-6">
          {parsedData.workInfo.style && (
            <WorkInfoItem
              label={t(
                'class.tools.materials.imslpSearchResults.results.workInformation.labels.style',
              )}
              value={parsedData.workInfo.style}
            />
          )}

          {parsedData.workInfo.instrumentation && (
            <WorkInfoItem
              label={t(
                'class.tools.materials.imslpSearchResults.results.workInformation.labels.instrumentation',
              )}
              value={parsedData.workInfo.instrumentation}
            />
          )}
        </div>
      </div>

      <h1
        className="text-base uppercase font-semibold"
        data-testid="imslp-editions-header"
      >
        {' '}
        {t('class.tools.materials.imslpSearchResults.results.editions.title')}
      </h1>

      {parsedData.editionGroups.length > 0 ? (
        parsedData.editionGroups.map((editionGroup, index) => (
          <div
            key={index}
            className='className="flex flex-col gap-4 p-4 border border-white/15 bg-white/8 shadow-[0_2px_8px_rgba(0,0,0,0.04)] rounded-xl'
            data-testid={`imslp-edition-group-${index}`}
          >
            <Accordion collapsible type="single">
              <AccordionItem value="main">
                <AccordionTrigger
                  className="[&>svg]:text-white items-center p-0"
                  data-testid={`imslp-edition-trigger-${index}`}
                >
                  <div className="flex flex-col gap-1">
                    <h1
                      className="text-base text-white font-medium"
                      data-testid={`imslp-edition-editor-${index}`}
                    >
                      {editionGroup.editor !== ''
                        ? editionGroup.editor
                        : t(
                            'class.tools.materials.imslpSearchResults.results.editions.unknowns.editor',
                          )}
                    </h1>
                    <h2
                      className="text-sm text-white/70"
                      data-testid={`imslp-edition-publisher-${index}`}
                    >
                      {editionGroup.publisherInfo !== ''
                        ? editionGroup.publisherInfo
                        : t(
                            'class.tools.materials.imslpSearchResults.results.editions.unknowns.publisher',
                          )}
                    </h2>
                    <h3
                      className="text-xs text-white/50 mt-1"
                      data-testid={`imslp-edition-copyright-${index}`}
                    >
                      {editionGroup.copyrightInfo !== ''
                        ? editionGroup.copyrightInfo
                        : t(
                            'class.tools.materials.imslpSearchResults.results.editions.unknowns.copyright',
                          )}
                    </h3>
                  </div>
                </AccordionTrigger>
                <AccordionContent
                  data-testid={`imslp-edition-content-${index}`}
                  className="p-0"
                >
                  <div>
                    <div className="my-4 w-full h-px bg-white/10" />
                    <div className="flex flex-col gap-2">
                      {editionGroup.editions.map((edition, editionIndex) => (
                        <div
                          key={editionIndex}
                          data-testid={`imslp-edition-file-${index}-${editionIndex}`}
                          className="p-3 border border-white/15 bg-white/8 hover:bg-white/10 hover:border-white/20 flex items-center justify-between gap-4 rounded-xl cursor-pointer transition-colors duration-300"
                          onClick={() => {
                            setViewerType?.('imslp');
                            setActiveTabParams?.({
                              title: activeTabParams?.title || '',
                              itemTitle: edition.title,
                              url: edition.link,
                            });
                            setActiveTab?.('viewer');
                          }}
                        >
                          <div className="flex items-center gap-3">
                            <div
                              className={cn(previewVariants({ type: 'pdf' }))}
                            >
                              <FileText className="size-4" />
                            </div>

                            <div className="flex flex-col gap-1">
                              <h1 className="text-sm text-white font-medium">
                                {edition.title}
                              </h1>
                            </div>
                          </div>

                          <div className="flex items-center gap-4">
                            <Download
                              className="opacity-70 transition-opacity duration-300 hover:opacity-100 cursor-pointer"
                              data-testid={`imslp-edition-download-${index}-${editionIndex}`}
                              onClick={(event) => {
                                event.stopPropagation();
                                window.open(edition.link, '_blank');
                              }}
                            />
                            <Copy
                              className="opacity-70 transition-opacity duration-300 hover:opacity-100 cursor-pointer"
                              data-testid={`imslp-edition-copy-${index}-${editionIndex}`}
                              onClick={(event) => {
                                event.stopPropagation();
                                navigator.clipboard
                                  .writeText(edition.link)
                                  .then(() => {
                                    addToast?.({
                                      text: t(
                                        'notifications.success.linkCopied',
                                      ),
                                    });
                                  });
                              }}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        ))
      ) : (
        <div
          className="flex flex-col gap-2 items-center justify-center"
          data-testid="imslp-no-editions"
        >
          <h1 className="font-medium text-white text-lg mt-4">
            {t(
              'class.tools.materials.imslpSearchResults.results.noEditionsFound.title',
            )}
          </h1>
          <h3 className="text-white/70 text-sm text-center">
            {t(
              'class.tools.materials.imslpSearchResults.results.noEditionsFound.title',
            )}
          </h3>
        </div>
      )}
    </div>
  );
};

const WorkInfoItem: FC<{ label: string; value: string }> = ({
  label,
  value,
}) => (
  <div className="flex flex-col gap-1">
    <h2 className="text-white/60 text-xs">{label}</h2>
    <h1 className="text-white text-sm">{value}</h1>
  </div>
);
