import { useEffect } from 'react';
import { Header } from './header';
import { Results } from './results';
import { useTranslation } from 'react-i18next';
import { useTabsContext } from '../../context';
import { usePageDetails } from '../../hooks';

export const IMSLPSearchResult = () => {
  const { t } = useTranslation();
  const { activeTabParams } = useTabsContext();
  const { data, mutate, isPending } = usePageDetails();

  useEffect(() => {
    if (activeTabParams?.title) {
      mutate(activeTabParams.title);
    }
  }, [activeTabParams, mutate]);

  return (
    <>
      <Header
        title={
          activeTabParams?.title ||
          t('class.tools.materials.imslpSearchResults.header.title')
        }
      />
      <Results
        isLoading={isPending}
        wikitext={
          data && data.parse && data.parse.wikitext
            ? data.parse.wikitext['*']
            : ''
        }
      />
    </>
  );
};
