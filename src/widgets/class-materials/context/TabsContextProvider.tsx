import { FC, PropsWithChildren, useContext, useState } from 'react';
import { createContext } from 'react';

export type TabType = 'main' | 'imslpSearch' | 'imslpSearchResult' | 'viewer';

interface TabsContextValue {
  activeTab: TabType;
  activeTabParams?: Record<string, string>;
  query?: string;
  viewerType?: 'uploaded' | 'imslp';
  setViewerType?: (value: 'uploaded' | 'imslp') => void;
  setQuery?: (query: string) => void;
  setActiveTab?: (tab: TabType) => void;
  setActiveTabParams?: (params: Record<string, string>) => void;
}

const TabsContext = createContext<TabsContextValue>({
  activeTab: 'main',
});

export const TabsProvider: FC<PropsWithChildren> = ({ children }) => {
  const [activeTab, setActiveTab] = useState<TabType>('main');
  const [activeTabParams, setActiveTabParams] = useState<
    Record<string, string>
  >({});
  const [viewerType, setViewerType] = useState<'uploaded' | 'imslp'>(
    'uploaded',
  );
  const [query, setQuery] = useState<string>('');

  return (
    <TabsContext.Provider
      value={{
        activeTab,
        setActiveTab,
        activeTabParams,
        setActiveTabParams,
        viewerType,
        setViewerType,
        query,
        setQuery,
      }}
    >
      {children}
    </TabsContext.Provider>
  );
};

export function useTabsContext(): TabsContextValue {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error('useTabsContext must be used within a TabsProvider');
  }
  return context;
}
