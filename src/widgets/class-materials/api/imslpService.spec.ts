import { describe, it, expect, vi, beforeEach } from 'vitest';
import { getPages, getPageDetails } from './imslpService';
import { IMSLP_PROXY_URL } from '@/shared';

// Helper to create a mock Response
function createMockResponse<T>(data: T, ok = true): Response {
  return {
    ok,
    status: ok ? 200 : 500,
    statusText: ok ? 'OK' : 'Error',
    headers: new Headers(),
    url: '',
    redirected: false,
    type: 'basic',
    clone: () => createMockResponse(data, ok),
    body: null,
    bodyUsed: false,
    arrayBuffer: async () => new ArrayBuffer(0),
    blob: async () => new Blob(),
    formData: async () => new FormData(),
    json: async () => data,
    text: async () => JSON.stringify(data),
  } as Response;
}

describe('imslpService', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('getPages()', () => {
    const query = 'Beethoven Sonata';
    const base = IMSLP_PROXY_URL + '/imslp/search?srsearch=';

    it('returns search results when response is ok', async () => {
      const mockData = { query: { search: [{ title: 'Sonata No.14' }] } };
      vi.stubGlobal(
        'fetch',
        vi.fn().mockResolvedValue(createMockResponse(mockData)),
      );

      const pages = await getPages(query);
      expect(pages).toEqual(mockData.query.search);
      expect(fetch).toHaveBeenCalledWith(`${base}${encodeURIComponent(query)}`);
    });

    it('returns empty array if search field is missing', async () => {
      const mockData = { query: {} };
      vi.stubGlobal(
        'fetch',
        vi.fn().mockResolvedValue(createMockResponse(mockData)),
      );

      const pages = await getPages(query);
      expect(pages).toEqual([]);
    });

    it('throws an error when response.ok is false', async () => {
      vi.stubGlobal(
        'fetch',
        vi.fn().mockResolvedValue(createMockResponse({}, false)),
      );

      await expect(getPages(query)).rejects.toThrow(
        'Failed to fetch IMSLP search results',
      );
    });
  });

  describe('getPageDetails()', () => {
    const page = 'Ludwig van Beethoven';
    const base = IMSLP_PROXY_URL + '/imslp/details?page=';

    it('returns page details when response is ok', async () => {
      const mockData = { parse: { wikitext: { '*': 'Mock content' } } };
      vi.stubGlobal(
        'fetch',
        vi.fn().mockResolvedValue(createMockResponse(mockData)),
      );

      const details = await getPageDetails(page);
      expect(details).toEqual(mockData);
      expect(fetch).toHaveBeenCalledWith(`${base}${encodeURIComponent(page)}`);
    });

    it('throws an error when response.ok is false', async () => {
      vi.stubGlobal(
        'fetch',
        vi.fn().mockResolvedValue(createMockResponse({}, false)),
      );

      await expect(getPageDetails(page)).rejects.toThrow(
        'Failed to fetch IMSLP item',
      );
    });
  });
});
