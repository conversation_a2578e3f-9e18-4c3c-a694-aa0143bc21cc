import { IMSLP_PROXY_URL } from '@/shared';

export const getPages = async (query: string): Promise<{ title: string }[]> => {
  const response = await fetch(
    `${IMSLP_PROXY_URL}/imslp/search?srsearch=${encodeURIComponent(query)}`,
  );

  if (!response.ok) {
    throw new Error('Failed to fetch IMSLP search results');
  }

  const data = await response.json();
  return data.query.search || [];
};

export const getPageDetails = async (
  page: string,
): Promise<{ parse: { wikitext: { '*': string } } }> => {
  const response = await fetch(
    `${IMSLP_PROXY_URL}/imslp/details?page=${encodeURIComponent(page)}`,
  );

  if (!response.ok) {
    throw new Error('Failed to fetch IMSLP item');
  }

  const data = await response.json();
  return data;
};
