import { IMSLP_PROXY_URL } from '@/shared';

interface IMSLPWorkInfo {
  workTitle: string | null;
  alternativeTitle: string | null;
  opusNumber: string | null;
  yearOfComposition: string | null;
  style: string | null;
  instrumentation: string | null;
}

interface IMSLPEdition {
  id: string;
  title: string;
  description: string;
  link: string;
  editor?: string;
  publisherInfo?: string;
  copyrightInfo?: string;
}

interface IMSLPEditionGroup {
  editor: string;
  publisherInfo: string;
  copyrightInfo: string;
  editions: IMSLPEdition[];
}

interface IMSLPParseResult {
  workInfo: IMSLPWorkInfo;
  editionGroups: IMSLPEditionGroup[];
}

// Clean wiki markup and HTML from text
const cleanWikiText = (text: string): string => {
  if (!text) return '';

  return (
    text
      // Remove HTML tags (br, strong, em, etc.)
      .replace(/<\/?[^>]+(>|$)/g, '')
      // Remove wiki links [[link|text]] -> text, [[link]] -> link
      .replace(/\[\[([^|\]]+)\|([^\]]+)\]\]/g, '$2')
      .replace(/\[\[([^\]]+)\]\]/g, '$1')
      // Remove wiki templates {{template|args}}
      .replace(/\{\{[^}]*\}\}/g, '')
      // Remove excess whitespace and normalize
      .replace(/\s+/g, ' ')
      .trim()
  );
};

// Enhanced editor/arranger parsing with better cleanup
const parseEditorField = (value: string): string => {
  const editors = value
    .split(/<br\s*\/?>/i)
    .map((editorValue) => {
      // Handle LinkEd/LinkArr templates
      const linkMatch = editorValue.match(
        /{{Link(?:Ed|Arr)\|(.+?)\|(.+?)(?:\|([^|}]*))?(?:\|([^|}]*))?}}/,
      );
      if (linkMatch) {
        const firstName = linkMatch[1];
        const lastName = linkMatch[2];
        const birthYear = linkMatch[3] || '';
        const deathYear = linkMatch[4] || '';
        return (
          `${lastName}, ${firstName}` +
          (birthYear || deathYear
            ? ` (${birthYear}${birthYear && deathYear ? '-' : ''}${deathYear})`
            : '')
        );
      }
      // Handle First Edition template
      else if (editorValue === '{{FE}}') {
        return 'First Edition';
      }
      // Handle other templates or clean text
      else {
        const cleaned = cleanWikiText(editorValue);
        return cleaned || '';
      }
    })
    .filter(Boolean);

  return editors.join('; ');
};

export const parseIMSLPWikitext = (wikitext: string): IMSLPParseResult => {
  const result: IMSLPParseResult = {
    workInfo: {
      workTitle: null,
      alternativeTitle: null,
      opusNumber: null,
      yearOfComposition: null,
      style: null,
      instrumentation: null,
    },
    editionGroups: [],
  };

  const extractSection = (text: string, sectionName: string): string => {
    const regex = new RegExp(
      `\\| \\*\\*\\*\\*\\*${sectionName}\\*\\*\\*\\*\\*[^\\n]*\\n([\\s\\S]*?)(?=\\| \\*\\*\\*\\*\\*)`,
      'i',
    );
    const match = text.match(regex);
    return match ? match[1].trim() : '';
  };

  const workInfoSection = extractSection(wikitext, 'WORK INFO');
  if (workInfoSection) {
    const lines = workInfoSection
      .split('\n')
      .filter((line) => line.includes('='));

    lines.forEach((line) => {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=').trim();

      if (!key || !value) return;

      const cleanKey = key.trim().replace(/^\|/, '');

      switch (cleanKey) {
        case 'Work Title':
          result.workInfo.workTitle = cleanWikiText(value);
          break;
        case 'Alternative Title':
          result.workInfo.alternativeTitle = cleanWikiText(value) || null;
          break;
        case 'Opus/Catalogue Number':
          result.workInfo.opusNumber = cleanWikiText(value);
          break;
        case 'Year/Date of Composition':
          result.workInfo.yearOfComposition = cleanWikiText(value);
          break;
        case 'Piece Style':
          result.workInfo.style = cleanWikiText(value);
          break;
        case 'Instrumentation':
        case 'InstrDetail':
          if (!result.workInfo.instrumentation) {
            result.workInfo.instrumentation = cleanWikiText(value);
          }
          break;
      }
    });
  }

  const filesSection = extractSection(wikitext, 'FILES');
  const editions: IMSLPEdition[] = [];
  if (filesSection) {
    const fileBlockRegex = /{{#fte:imslpfile([\s\S]*?)(?=\n===|\n{{#fte|$)/g;
    let fileBlockMatch;
    let editionIndex = 0;

    while ((fileBlockMatch = fileBlockRegex.exec(filesSection)) !== null) {
      const fileBlockContent = fileBlockMatch[1];
      const lines = fileBlockContent
        .split('\n')
        .filter((line) => line.trim() !== '');

      const fileNames: string[] = [];
      const fileDescriptions: string[] = [];
      let editor = '';
      let publisherInfo = '';
      let copyrightInfo = '';

      lines.forEach((line) => {
        const [key, ...valueParts] = line.split('=');
        const value = valueParts.join('=').trim();

        if (!key || !value) return;

        const cleanKey = key.trim().replace(/^\|/, '');

        if (cleanKey.startsWith('File Name ')) {
          const index = parseInt(cleanKey.replace('File Name ', ''), 10) - 1;
          fileNames[index] = value;
        } else if (cleanKey.startsWith('File Description ')) {
          const index =
            parseInt(cleanKey.replace('File Description ', ''), 10) - 1;
          // Store raw description, will clean when creating edition
          fileDescriptions[index] = value.replace(/<br\s*\/?>/gi, ', ');
        } else if (cleanKey === 'Editor' || cleanKey === 'Arranger') {
          editor = value; // Store raw, will clean when creating edition
        } else if (cleanKey === 'Publisher Information') {
          publisherInfo = value; // Store raw, will clean when creating edition
        } else if (cleanKey === 'Copyright') {
          copyrightInfo = value; // Store raw, will clean when creating edition
        }
      });

      for (let i = 0; i < fileNames.length; i++) {
        if (fileNames[i]) {
          editionIndex++;
          editions.push({
            id: `ed${editionIndex}`,
            title: cleanWikiText(fileDescriptions[i]) || `File ${i + 1}`,
            description: fileNames[i],
            link: `${IMSLP_PROXY_URL}/imslp/resolve?file=${fileNames[i]}`,
            editor: parseEditorField(editor) || undefined,
            publisherInfo: cleanWikiText(publisherInfo) || undefined,
            copyrightInfo: cleanWikiText(copyrightInfo) || undefined,
          });
        }
      }
    }
  }

  // Group all editions by editor, publisher, and copyright
  const groupMap = new Map<string, IMSLPEdition[]>();

  editions.forEach((edition) => {
    const groupKey = `${edition.editor || ''}_${edition.publisherInfo || ''}_${edition.copyrightInfo || ''}`;
    if (!groupMap.has(groupKey)) {
      groupMap.set(groupKey, []);
    }
    groupMap.get(groupKey)!.push(edition);
  });

  // Create edition groups for all groups (including single editions)
  groupMap.forEach((editions, key) => {
    const [editor, publisherInfo, copyrightInfo] = key.split('_');
    result.editionGroups.push({
      editor: editor || '',
      publisherInfo: publisherInfo || '',
      copyrightInfo: copyrightInfo || '',
      editions,
    });
  });

  return result;
};
