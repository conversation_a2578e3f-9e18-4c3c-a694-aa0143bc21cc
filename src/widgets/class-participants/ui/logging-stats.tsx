import { ParticipantLogs, RoomParticipant } from '@/features';
import { AnimatePresence, motion } from 'framer-motion';
import { FC } from 'react';
import { useUser } from '@/entities';

interface ILoggingStatsRendererProps {
  open: boolean;
  roomUser?: RoomParticipant;
  localUser: RoomParticipant | null;
  remoteParticipantsLogs: Record<string, ParticipantLogs>;
  audioLevels: Record<string, number | null>;
  audioInputsName: string;
  audioOutputName: string;
  roomUsers: RoomParticipant[];
}

interface NetworkStats {
  roundTrip: number;
  participants?: Record<
    string,
    {
      jitterBuffer?: number;
      minJitterBuffer?: number;
      jitterBufferDelta?: number;
      jitter?: number;
      fractionLoss?: number;
    }
  >;
}

type LogValue = string | number | boolean | NetworkStats | null | undefined;

const LoggingStatsRenderer: FC<ILoggingStatsRendererProps> = ({
  open,
  roomUser,
  localUser,
  remoteParticipantsLogs,
  audioLevels,
  audioInputsName,
  audioOutputName,
  roomUsers,
}) => {
  const { user } = useUser();

  const renderNetworkStats = (rawVal: NetworkStats) => (
    <div className="flex flex-col gap-2">
      <div className="flex items-end gap-2 p-2 bg-white/5 rounded-md justify-between">
        <h1 className="font-semibold">Round Trip</h1>
        <h1>{rawVal.roundTrip.toFixed(2)} ms</h1>
      </div>
      {Object.entries(rawVal.participants || {}).map(
        ([participantKey, stats]) => (
          <div
            key={participantKey}
            className="flex flex-col items-end gap-2 p-2 bg-white/5 rounded-md"
          >
            <div className="text-sm font-semibold">
              {`${participantKey} (${
                roomUsers.find((user) => user.identity === participantKey)
                  ?.profile?.displayName ??
                remoteParticipantsLogs[participantKey]?.displayName ??
                user!.displayName
              })`}
            </div>
            <div className="flex flex-col text-right">
              <h1>
                Jitter Buffer: {stats.jitterBuffer?.toFixed(2) ?? 'N/A'} ms
              </h1>
              <h1>
                Min Jitter Buffer: {stats.minJitterBuffer?.toFixed(2) ?? 'N/A'}{' '}
                ms
              </h1>
              <h1>
                Delta Jitter Buffer:{' '}
                {stats.jitterBufferDelta?.toFixed(2) ?? 'N/A'} ms
              </h1>
              <h1>Jitter: {stats.jitter?.toFixed(2) ?? 'N/A'} ms</h1>
              <h1>
                Fraction Loss: {stats.fractionLoss?.toFixed(2) ?? 'N/A'} %
              </h1>
            </div>
          </div>
        ),
      )}
    </div>
  );

  const formatValue = (key: string, value: LogValue): string | null => {
    if (key.includes('Bitrate')) {
      return `${Number(value).toFixed(2) || 'N/A'} kbps`;
    }

    if (typeof value === 'boolean') {
      return JSON.stringify(value);
    }

    if (typeof value === 'object') {
      return null;
    }

    return String(value);
  };

  const renderStatRow = (key: string, value: LogValue) => (
    <div className="flex items-center justify-between" key={key}>
      <div className="px-4 py-2 bg-white/10 font-medium rounded-full">
        {key}
      </div>
      <div>
        {key === 'networkStats'
          ? renderNetworkStats(value as NetworkStats)
          : formatValue(key, value)}
      </div>
    </div>
  );

  const renderAudioDevices = () => (
    <>
      <div className="flex items-center justify-between gap-2">
        <div className="px-4 py-2 bg-white/10 font-medium rounded-full">
          inputAudioDevice
        </div>
        <div className="break-all text-right">{audioInputsName}</div>
      </div>
      <div className="flex items-center justify-between gap-2">
        <div className="px-4 py-2 bg-white/10 font-medium rounded-full">
          outputAudioDevice
        </div>
        <div className="break-all text-right">{audioOutputName}</div>
      </div>
    </>
  );

  const sid = roomUser?.sid || localUser?.sid;

  if (!sid) return null;

  const remoteParticipantLogs = remoteParticipantsLogs[sid];
  const audioLevel = audioLevels[sid];

  if (!remoteParticipantLogs) return null;
  if (!audioLevel) return null;

  return (
    <AnimatePresence>
      {open && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="overflow-hidden"
        >
          <div className="px-3.5 py-3 border-t border-white/10 flex flex-col gap-2">
            {!roomUser && renderAudioDevices()}

            {Object.entries(remoteParticipantLogs).map(([key, value]) =>
              renderStatRow(key, value),
            )}

            {renderStatRow('audioLevel', `${audioLevel.toFixed(2)} Db`)}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LoggingStatsRenderer;
