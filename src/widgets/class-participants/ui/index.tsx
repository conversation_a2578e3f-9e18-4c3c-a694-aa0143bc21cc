import { Accordion } from '@/shadcn/components/ui/accordion';
import { Eye, GraduationCap, User, Users } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router';
import { ParticipantAccordionItem } from './participant-accordion-item';
import { selectAllRoomUsers } from '@/features';
import { useClassroom } from '@/entities';
import { Sidebar, useAppSelector } from '@/shared';

export const ClassParticipants = () => {
  const { t } = useTranslation();

  const { localUser } = useAppSelector((state) => state.livekit);
  const roomUsers = useAppSelector(selectAllRoomUsers);

  const { id: classroomId } = useParams<{
    id: string;
  }>();

  const { data: classroomData } = useClassroom(classroomId ?? '');

  return (
    <Sidebar
      disabled={false}
      title={t('class.tools.participants.title')}
      tooltip={t('class.tools.participants.tooltip')}
      triggerIcon={<Users />}
      contentClassName="min-[1200px]:min-w-[550px] min-[1200px]:!max-w-[550px]"
      indicator={roomUsers.length + (localUser ? 1 : 0)}
      indicator-testid="sidebar-indicator-participants-count"
      data-testid="action-bar-participants-button"
    >
      <Accordion type="single" collapsible className="w-full">
        {classroomData && (
          <>
            <ParticipantAccordionItem
              data-testid="action-bar-teachers-button"
              title={t('class.tools.participants.teachers')}
              icon={<GraduationCap />}
              participants={classroomData.participants.filter(
                (participant) =>
                  participant.role === 'teacher' ||
                  participant.role === 'lead_teacher',
              )}
            />
            <ParticipantAccordionItem
              data-testid="action-bar-students-button"
              title={t('class.tools.participants.students')}
              icon={<User />}
              participants={classroomData.participants.filter(
                (participant) => participant.role === 'student',
              )}
            />
            <ParticipantAccordionItem
              data-testid="action-bar-guests-button"
              title={t('class.tools.participants.guests')}
              icon={<Eye />}
              participants={classroomData.participants.filter(
                (participant) => participant.role === 'guest',
              )}
            />
          </>
        )}
      </Accordion>
    </Sidebar>
  );
};
