import { ReactElement, useMemo, useState } from 'react';
import { AccordionItem, getInitials, useAppSelector } from '@/shared';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/shadcn/components/ui/avatar';
import { Clock, Minus, Search, TriangleAlert } from 'lucide-react';
import { cn } from '@/shadcn/lib/utils';
import moment from 'moment';
import i18n from '@/app/i18n';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  RoomParticipant,
  selectAllRoomUsers,
  useClassLogger,
} from '@/features';
import LoggingStatsRenderer from './logging-stats';
import { Participant, ReportType } from '@/entities';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { useGetDeviceNameByID } from '../hooks';

interface IParticipantAccordionItemProps {
  title: string;
  icon: ReactElement<React.SVGProps<SVGSVGElement>>;
  participants: Participant[];
  'data-testid'?: string;
}

interface SearchFormValues {
  query: string;
}

export const ParticipantAccordionItem = ({
  title,
  icon,
  participants,
  'data-testid': testId,
}: IParticipantAccordionItemProps) => {
  const { t } = useTranslation();
  const roomUsers = useAppSelector(selectAllRoomUsers);
  const { localUser } = useAppSelector((state) => state.livekit);
  const { register, watch } = useForm<SearchFormValues>({
    defaultValues: { query: '' },
  });

  const query = watch('query')?.trim().toLowerCase();

  const filteredParticipants = useMemo(() => {
    if (!query) {
      return participants;
    }
    return participants.filter((p) => {
      const name = p.displayName?.toLowerCase() ?? '';
      const emailOrId = (p.userId ?? '').toLowerCase();
      return name.includes(query) || emailOrId.includes(query);
    });
  }, [participants, query]);

  return (
    <AccordionItem
      title={`${title} (${participants.length})`}
      value={title}
      icon={icon}
      data-testid={testId}
    >
      {participants.length > 5 && (
        <form
          className="w-full"
          onSubmit={(e) => {
            e.preventDefault();
          }}
        >
          <div className="relative">
            <Search className="absolute w-5 h-auto aspect-square left-3 top-1/2 transform -translate-y-1/2 text-white/70" />
            <input
              type="text"
              placeholder={t('class.tools.participants.search.placeholder')}
              {...register('query')}
              data-testid={`participants-search-input-${title.toLowerCase()}`}
              className="w-full pl-10 pr-4 py-2 bg-transparent text-base text-white placeholder-white/50 rounded-md border border-white/50 focus:outline-none focus:ring-1 focus:ring-white/70"
            />
          </div>
        </form>
      )}

      {filteredParticipants.length === 0 && (
        <div className="h-full flex justify-center py-7.5">
          <p className="text-sm text-white/50">
            {t('class.tools.participants.search.notFound')}
          </p>
        </div>
      )}
      <div className="w-full h-fit flex flex-col gap-2">
        {filteredParticipants
          .sort((a, b) => {
            const aRoomUser = roomUsers.find(
              (user) => user.identity === a.userId,
            );
            const aIsLive = aRoomUser || localUser?.identity === a.userId;

            const bRoomUser = roomUsers.find(
              (user) => user.identity === b.userId,
            );
            const bIsLive = bRoomUser || localUser?.identity === b.userId;

            if (aIsLive && !bIsLive) return -1;
            if (!aIsLive && bIsLive) return 1;

            return a.displayName?.localeCompare(b.displayName) || 0;
          })
          .map((participant, index) => (
            <ParticipantItem
              key={index}
              participant={participant}
              roomUsers={roomUsers}
              localUser={localUser}
            />
          ))}
      </div>
    </AccordionItem>
  );
};

const ParticipantItem = ({
  participant,
  roomUsers,
  localUser,
}: {
  participant: Participant;
  roomUsers: RoomParticipant[];
  localUser: RoomParticipant | null;
}) => {
  const {
    audioDevices: selectedAudioDevices,
    audioOutputDevice: selectedAudioOutputDevice,
  } = useAppSelector((state) => state.localMedia);
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();
  const { remoteParticipantsLogs, audioLevels } = useClassLogger();

  const roomUser = roomUsers.find(
    (user) => user.identity === participant.userId,
  );
  const isLive = roomUser || localUser?.identity === participant.userId;

  const audioOutputName = useGetDeviceNameByID(
    selectedAudioOutputDevice || 'default',
  );

  const audioInputsName = useGetDeviceNameByID(selectedAudioDevices);

  return (
    <div
      className="w-full h-fit bg-white/5 rounded-sm hover:bg-white/10"
      data-testid={`participant-${participant.userId}`}
    >
      <div
        className="px-3.5 py-2.5 grid grid-cols-2 cursor-pointer"
        onClick={() => {
          if (open) setOpen(false);
          if (isLive) {
            setOpen(!open);
          }
        }}
      >
        <div className="h-fit flex items-center gap-2.5 min-w-0">
          <Avatar className="w-10 h-auto aspect-square cursor-default">
            <AvatarImage
              className="object-cover"
              src={participant.profileImage ?? undefined}
            />
            <AvatarFallback className="text-lg font-medium">
              {getInitials(participant.displayName ?? 'Undefined')}
            </AvatarFallback>
          </Avatar>
          <div className="h-fit flex-1 flex flex-col justify-between items-start min-w-0">
            <h6
              className="text-sm text-white font-medium truncate w-full"
              data-testid="participant-name"
            >
              {participant.displayName}
            </h6>
            <div className="flex justify-center items-center gap-1.5 text-white/50">
              <Clock className="w-3 h-auto aspect-square" />
              <p className="text-xs" data-testid="participant-joined-at">
                {moment(
                  (participant.leftAt && participant.leftAt > 0
                    ? participant.leftAt
                    : participant.joinedAt) * 1000,
                )
                  .locale(i18n.language)
                  .format('MMMM D[,] HH:mm')}
              </p>
            </div>
          </div>
        </div>
        <div className="h-full flex justify-end items-center gap-5">
          <div
            className={cn(
              'py-0.5 px-2 rounded-full text-[10px] text-black font-medium',
              isLive ? 'bg-green-400/70' : 'bg-red-400/70',
            )}
            data-testid="participant-status"
          >
            {isLive
              ? t('class.tools.participants.participant.status.live')
              : t('class.tools.participants.participant.status.offline')}
          </div>
          <div className="flex justify-center items-center gap-2">
            <TooltipProvider>
              <button
                disabled
                className="w-5 h-auto aspect-square bg-white rounded-full flex justify-center items-center cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={(e) => e.stopPropagation()}
              >
                <Minus className="w-4 h-auto aspect-square text-black" />
              </button>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    data-testid="participant-report-button"
                    className="flex justify-center items-center cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={(e) => {
                      e.stopPropagation();
                      const queryParams = new URLSearchParams({
                        type: ReportType.User,
                        userId: participant.userId,
                        displayName: participant.displayName,
                      }).toString();

                      window.open(`/legal/report?${queryParams}`, '_blank');
                    }}
                  >
                    <TriangleAlert className="w-6 h-auto aspect-square text-destructive" />
                  </button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p className="text-center">
                    {t('class.tools.participants.participant.tooltips.report')}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>

      <LoggingStatsRenderer
        open={open}
        roomUser={roomUser}
        roomUsers={roomUsers}
        localUser={localUser}
        remoteParticipantsLogs={remoteParticipantsLogs}
        audioLevels={audioLevels}
        audioInputsName={audioInputsName}
        audioOutputName={audioOutputName}
      />
    </div>
  );
};
