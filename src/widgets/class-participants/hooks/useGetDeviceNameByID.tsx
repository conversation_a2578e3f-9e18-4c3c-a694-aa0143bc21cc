import { useMemo, useState } from 'react';

export const useGetDeviceNameByID = (deviceId: string | string[]) => {
  const [deviceName, setDeviceName] = useState<string>('Unknown Device');

  useMemo(() => {
    if (Array.isArray(deviceId)) {
      // Remove duplicates from the array
      const uniqueDeviceIds = [...new Set(deviceId)];

      // Return comma-separated list of device names for arrays
      navigator.mediaDevices.enumerateDevices().then((devices) => {
        const names = uniqueDeviceIds.map((id) => {
          const device = devices.find(
            (d) => d.deviceId === id || (id === '' && d.deviceId === 'default'),
          );
          return device?.label || 'Unknown Device';
        });
        setDeviceName(names.join(', '));
      });
    } else {
      // Handle single device ID as before
      navigator.mediaDevices.enumerateDevices().then((devices) => {
        const device = devices.find((d) => {
          return deviceId === ''
            ? d.deviceId === 'default'
            : d.deviceId === deviceId;
        });
        setDeviceName(device?.label || 'Unknown Device');
      });
    }
  }, [deviceId]);

  return deviceName;
};
