import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  <PERSON>barFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  useSidebar,
} from '@/shadcn/components/ui/sidebar';
import {
  Calendar,
  Home,
  GraduationCap,
  User,
  Music,
  Users,
  X,
  ClipboardPlus,
} from 'lucide-react';
import { Profile } from './profile';
import { Link, useLocation } from 'react-router';
import { useTranslation } from 'react-i18next';
import { useUser } from '@/entities';

const items = [
  {
    dataTestId: 'sidebar-main',
    title: 'sidebar.main',
    url: '/',
    icon: Home,
    protected: false,
    target: '_self',
  },
  {
    dataTestId: 'sidebar-news',
    title: 'sidebar.news',
    url: '#',
    icon: Music,
    protected: false,
    target: '_self',
  },
  {
    dataTestId: 'sidebar-online-classroom',
    title: 'sidebar.onlineClassroom',
    url: '/classrooms',
    icon: GraduationCap,
    protected: true,
    target: '_self',
  },
  {
    dataTestId: 'sidebar-practice-tools',
    title: 'sidebar.practiceTools',
    url: '#',
    icon: Music,
    protected: false,
    target: '_self',
  },
  {
    dataTestId: 'sidebar-music-items',
    title: 'sidebar.musicItems',
    url: '#',
    icon: Music,
    protected: false,
    target: '_self',
  },
  {
    dataTestId: 'sidebar-events',
    title: 'sidebar.events',
    url: '#',
    icon: Calendar,
    protected: false,
    target: '_self',
  },
  {
    dataTestId: 'sidebar-ensembles',
    title: 'sidebar.ensembles',
    url: '#',
    icon: Users,
    protected: false,
    target: '_self',
  },
  {
    dataTestId: 'sidebar-people',
    title: 'sidebar.people',
    url: '#',
    icon: User,
    protected: false,
    target: '_self',
  },
  {
    dataTestId: 'sidebar-institutions',
    title: 'sidebar.institutions',
    url: '#',
    icon: GraduationCap,
    protected: false,
    target: '_self',
  },
  {
    dataTestId: 'sidebar-venues',
    title: 'sidebar.venues',
    url: '#',
    icon: Calendar,
    protected: false,
    target: '_self',
  },
  {
    dataTestId: 'sidebar-financing',
    title: 'sidebar.financing',
    url: '#',
    icon: Music,
    protected: false,
    target: '_self',
  },
  {
    dataTestId: 'sidebar-report-form',
    title: 'sidebar.reportForm',
    url: '/legal/report',
    icon: ClipboardPlus,
    protected: false,
    target: '_blank',
  },
];

export const AppSidebar = () => {
  const { pathname } = useLocation();
  const activeItem = items.find((item) =>
    item.url === `/` ? pathname === `/` : pathname.startsWith(item.url),
  );
  const activeIndex = activeItem ? items.indexOf(activeItem) : -1;
  const { t } = useTranslation();

  const { toggleSidebar } = useSidebar();
  const { user } = useUser();

  return (
    <Sidebar>
      <SidebarHeader className="p-6">
        <div className="w-full flex items-center justify-between gap-2">
          <Link
            to="/"
            className="text-xl font-bold truncate"
            onClick={toggleSidebar}
          >
            VirtuosoHub
          </Link>

          <X className="cursor-pointer" onClick={toggleSidebar} />
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {items.map((item, index) => (
                <SidebarMenuItem
                  key={index}
                  className={
                    item.url === '#' || (item.protected && !user)
                      ? 'opacity-50 pointer-events-none'
                      : ''
                  }
                >
                  <SidebarMenuButton asChild isActive={activeIndex === index}>
                    <Link
                      to={item.url}
                      className="text-lg flex items-center gap-3 transition-colors duration-300"
                      onClick={toggleSidebar}
                      target={item.target}
                    >
                      <item.icon />
                      <span className="text-[1rem]">{t(item.title)}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter
        data-testid="sidebar-profile-button"
        className="px-2 pt-2 pb-4 flex flex-col gap-4"
      >
        {user && <Profile />}
        <Link
          to="/legal/copyright"
          target="_blank"
          className="text-primary/80 text-xs text-center"
        >
          © VirtuosoHub 2025
        </Link>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
};
