import {
  DropdownMenu,
  DropdownMenuGroup,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/shadcn/components/ui/dropdown-menu';
import { useSidebar } from '@/shadcn/components/ui/sidebar';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/shadcn/components/ui/avatar';
import { LogOut, Settings, User } from 'lucide-react';
import { getInitials } from '@/shared';
import { UserSettings } from '@/features';
import { useState } from 'react';
import { Link } from 'react-router';
import { useTranslation } from 'react-i18next';
import { useLogoutUser, useUser } from '@/entities';

export const Profile = () => {
  const [showSettings, setShowSettings] = useState(false);
  const { isMobile, toggleSidebar } = useSidebar();
  const { user } = useUser();
  const { logout } = useLogoutUser();
  const { t } = useTranslation();

  return (
    <>
      <UserSettings show={showSettings} setShow={setShowSettings} />

      <DropdownMenu data-testid="sidebar-profile-dropdown">
        <DropdownMenuTrigger asChild>
          <div className="items-center justify-between w-full cursor-pointer px-5 pb-5 pt-4 bg-[#FFFDFA] shadow-[0_0_12px_0_rgba(63,44,33,0.18)] rounded-3xl transition-all duration-300 hover:shadow-[0_0_4px_4px_rgba(94,64,50,0.08)] active:shadow-[0_0_4px_4px_rgba(94,64,50,0.00)]">
            <div className="grid grid-cols-[3rem_1fr] items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarImage src={''} />
                <AvatarFallback>
                  {getInitials(user!.displayName)}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col items-start min-w-0 overflow-hidden">
                <h3
                  data-testid="sidebar-profile-display-name"
                  className="text-sm font-semibold truncate"
                >
                  {user!.displayName}
                </h3>
                <h5
                  data-testid="sidebar-profile-email"
                  className="text-sm truncate max-w-[170px]"
                >
                  {user!.isAnonymous ? 'Guest Access' : user!.email}
                </h5>
              </div>
            </div>
          </div>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
          side={isMobile ? 'bottom' : 'bottom'}
          align="center"
          sideOffset={4}
        >
          <DropdownMenuGroup>
            <Link to={`/profile`}>
              <DropdownMenuItem
                data-testid="sidebar-profile-dropdown-profile-link"
                className="p-4 transition-colors duration-300 cursor-pointer hover:!bg-[#3022191a] hover:!text-primary"
                onClick={toggleSidebar}
              >
                <User className="text-primary duration-300" />
                {t('sidebar.profile')}
              </DropdownMenuItem>
            </Link>

            <DropdownMenuItem
              data-testid="sidebar-profile-settings"
              className="p-4 transition-colors duration-300 text-primary cursor-pointer hover:!bg-[#3022191a] hover:!text-primary"
              onClick={() => setShowSettings(true)}
            >
              <Settings className="text-primary duration-300 hover:!bg-[#3022191a] hover:!text-primary" />
              {t('sidebar.settings')}
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem
              data-testid="sidebar-profile-logout"
              className="p-4 text-destructive transition-colors duration-300 cursor-pointer hover:!bg-[#3022191a] hover:!text-primary"
              onClick={() => logout()}
            >
              <LogOut
                data-testid="sidebar-profile-logout-icon"
                className="text-destructive group-hover:text-primary duration-300"
              />
              {t('sidebar.logout')}
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};
