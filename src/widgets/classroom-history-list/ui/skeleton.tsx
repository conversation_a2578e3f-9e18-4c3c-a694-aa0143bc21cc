import { FC } from 'react';
import { ClassHistoryCardSkeleton } from '@/widgets/class-history-card';

export const ClassroomHistoryListSkeleton: FC = () => {
  const skeletonItems = Array.from({ length: 8 });

  return (
    <div className="grid grid-cols-1 gap-6 min-[550px]:grid-cols-2 min-[900px]:grid-cols-3 min-[1200px]:grid-cols-4 w-full animate-pulse">
      {skeletonItems.map((_, index) => (
        <ClassHistoryCardSkeleton key={index} />
      ))}
    </div>
  );
};
