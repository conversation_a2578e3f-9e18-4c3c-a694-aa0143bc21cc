import { Class } from '@/entities';
import { ClassHistoryCard } from '@/widgets/class-history-card';
import moment from 'moment';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';

interface IClassroomHistoryListProps {
  items: Class[];
}

export const ClassroomHistoryList: FC<IClassroomHistoryListProps> = ({
  items,
}) => {
  const { t } = useTranslation();

  return (
    <div className="grid grid-cols-1 gap-6 min-[550px]:grid-cols-2 min-[900px]:grid-cols-3 min-[1200px]:grid-cols-4">
      {items.map((item, index) => {
        const diffSec = item.endTime - item.startTime;
        const dur = moment.duration(diffSec * 1000);

        const hours = dur.hours();
        const minutes = dur.minutes();

        const parts: string[] = [];
        if (hours) {
          const unit =
            hours === 1
              ? t('classroom.history.units.hour')
              : t('classroom.history.units.hour_plural');
          parts.push(t('classroom.history.duration', { count: hours, unit }));
        }
        if (minutes) {
          const unit =
            minutes === 1
              ? t('classroom.history.units.minute')
              : t('classroom.history.units.minute_plural');
          parts.push(t('classroom.history.duration', { count: minutes, unit }));
        }
        const durationStr = parts.length
          ? parts.join(' ')
          : t('classroom.history.duration', {
              count: 0,
              unit: t('classroom.history.units.minute_plural'),
            });

        return (
          <ClassHistoryCard key={index} item={item} duration={durationStr} />
        );
      })}
    </div>
  );
};
