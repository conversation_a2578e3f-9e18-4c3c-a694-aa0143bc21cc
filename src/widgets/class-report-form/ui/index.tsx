import { useTranslation } from 'react-i18next';
import { ClipboardPlus } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { TriggerButton } from '@/shared';

export const ClassReportForm = () => {
  const { t } = useTranslation();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <a href="/legal/report" target="_blank" rel="noopener noreferrer">
            <TriggerButton icon={<ClipboardPlus />} disabled={false} />
          </a>
        </TooltipTrigger>
        <TooltipContent side="left">
          <p className="text-center">
            {t('class.tools.report.trigger.tooltip')}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
