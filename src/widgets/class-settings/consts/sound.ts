import { InstrumentType, RoomAcoustics } from '@/features';

export const INSTRUMENT_TYPES: Map<string, InstrumentType> = new Map<
  string,
  InstrumentType
>([
  [
    'class.tools.settings.audioSettings.instrument.type.string',
    InstrumentType.String,
  ],
  [
    'class.tools.settings.audioSettings.instrument.type.wind',
    InstrumentType.Wind,
  ],
  [
    'class.tools.settings.audioSettings.instrument.type.percussion',
    InstrumentType.Percussion,
  ],
  [
    'class.tools.settings.audioSettings.instrument.type.piano',
    InstrumentType.Piano,
  ],
  [
    'class.tools.settings.audioSettings.instrument.type.vocal',
    InstrumentType.Vocal,
  ],
  [
    'class.tools.settings.audioSettings.instrument.type.general',
    InstrumentType.General,
  ],
]);

export const ROOM_ACOUSTICS: Map<string, RoomAcoustics> = new Map<
  string,
  RoomAcoustics
>([
  [
    'class.tools.settings.audioSettings.roomAcoustics.type.dead',
    RoomAcoustics.Dead,
  ],
  [
    'class.tools.settings.audioSettings.roomAcoustics.type.normal',
    RoomAcoustics.Normal,
  ],
  [
    'class.tools.settings.audioSettings.roomAcoustics.type.live',
    RoomAcoustics.Live,
  ],
]);
