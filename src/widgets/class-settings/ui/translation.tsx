import { Languages } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import {
  AccordionItem,
  LANGUAGES,
  Select,
  Switch,
  useAppSelector,
} from '@/shared';
import { SelectItem } from '@/shadcn/components/ui/select';
import { FC, useEffect } from 'react';
import {
  useUpdateParticipantSettings,
  useUpdateUser,
  useUser,
} from '@/entities';
import { useTranslationContext } from '@/features';

interface ITranslationProps {
  classroomId: string;
  classId: string;
}

export const Translation: FC<ITranslationProps> = ({
  classroomId,
  classId,
}) => {
  const { t } = useTranslation();

  const { user } = useUser();
  const { mutate: updateUser } = useUpdateUser();

  const { participant } = useAppSelector((state) => state.conference);
  const { mutate: updateParticipantSettings, isPending } =
    useUpdateParticipantSettings(classroomId, classId);

  const { enabled, setEnabled, setCaptions, audio, setAudio } =
    useTranslationContext();

  useEffect(() => {
    setCaptions(participant?.settings.captionEnabled ?? false);
  }, []);

  return (
    <AccordionItem
      value="translation"
      title={t('class.tools.settings.translation.title')}
      icon={<Languages />}
    >
      <Select
        title={t('class.tools.settings.translation.items.language.title')}
        description={t('class.tools.settings.translation.items.language.text')}
        id="language"
        data-testid="translation-language-select"
        placeholder={t(
          'class.tools.settings.translation.items.language.placeholder',
        )}
        value={
          user?.preferences.defaultClassroomSettings.translationLanguage ??
          'English'
        }
        onValueChange={(value) => {
          updateUser({
            preferences: {
              defaultClassroomSettings: {
                ...user?.preferences.defaultClassroomSettings,
                translationLanguage: value,
              },
            },
          });
        }}
      >
        {Object.values(LANGUAGES).map((item, index) => (
          <SelectItem value={Object.keys(LANGUAGES)[index]} key={index}>
            {item}
          </SelectItem>
        ))}
      </Select>

      <Switch
        id="translation"
        data-testid="translation-auto-switch"
        title={t('class.tools.settings.translation.items.translate.title')}
        description={t('class.tools.settings.translation.items.translate.text')}
        value={enabled}
        setValue={(value) => {
          setEnabled(value);
        }}
      />

      <Switch
        id="captions"
        title={t('class.tools.settings.translation.items.captions.title')}
        description={t('class.tools.settings.translation.items.captions.text')}
        disabled={!enabled || isPending}
        value={participant?.settings.captionEnabled ?? false}
        setValue={(value) => {
          updateParticipantSettings(
            {
              settings: { captionEnabled: value },
            },
            {
              onSuccess: () => setCaptions(value),
            },
          );
        }}
      />

      <Switch
        id="audio"
        title={t('class.tools.settings.translation.items.audio.title')}
        description={t('class.tools.settings.translation.items.audio.text')}
        disabled={!enabled}
        value={audio}
        setValue={(value) => setAudio(value)}
      />
    </AccordionItem>
  );
};
