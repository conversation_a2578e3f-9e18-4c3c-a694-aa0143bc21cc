import { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SlidersHorizontal } from 'lucide-react';
import { centralAudioProcessing } from '@/features';
import { useUpdateParticipantSettings } from '@/entities';
import { AccordionItem, Switch, useAppSelector } from '@/shared';

interface IGeneralProps {
  classroomId: string;
  classId: string;
}

export const General: FC<IGeneralProps> = ({ classroomId, classId }) => {
  const { t } = useTranslation();
  const { participant } = useAppSelector((state) => state.conference);
  const [chatSound, setChatSound] = useState<boolean>(true);
  const [automaticParticipant, setAutomaticParticipant] =
    useState<boolean>(true);
  const { mutate: updateParticipantSettings, isPending } =
    useUpdateParticipantSettings(classroomId, classId);

  useEffect(() => {
    const enabled = localStorage.getItem('enableChatNotificationSound');
    const automaticParticipant = localStorage.getItem('automaticParticipant');

    if (enabled) setChatSound(enabled === 'true');
    if (automaticParticipant) {
      const on = automaticParticipant === 'true';
      setAutomaticParticipant(on);
      centralAudioProcessing
        .getRemoteAudioMixer()
        .getActiveSpeakerManager()
        .setIsEnabled(on);
    }
  }, []);

  useEffect(() => {
    localStorage.setItem(
      'enableChatNotificationSound',
      chatSound ? 'true' : 'false',
    );

    localStorage.setItem(
      'automaticParticipant',
      automaticParticipant ? 'true' : 'false',
    );
  }, [chatSound, automaticParticipant]);

  return (
    <AccordionItem
      value="chat"
      title={t('class.tools.settings.general.title')}
      icon={<SlidersHorizontal />}
    >
      <Switch
        id="notificationSound"
        data-testid="chat-notification-sound-switch"
        title={t('class.tools.settings.general.items.notificationSound.title')}
        description={t(
          'class.tools.settings.general.items.notificationSound.text',
        )}
        value={chatSound}
        setValue={setChatSound}
      />
      <Switch
        id="automaticParticipant"
        title={t(
          'class.tools.settings.general.items.automaticParticipant.title',
        )}
        description={t(
          'class.tools.settings.general.items.automaticParticipant.text',
        )}
        value={automaticParticipant}
        setValue={(value) => {
          setAutomaticParticipant(value);
          centralAudioProcessing
            .getRemoteAudioMixer()
            .getActiveSpeakerManager()
            .setIsEnabled(value);
        }}
      />

      <Switch
        id="sharing-consent"
        title={t('class.tools.settings.general.items.sharing.title')}
        description={t('class.tools.settings.general.items.sharing.text')}
        disabled={isPending}
        value={participant?.settings.sharingConsent ?? false}
        setValue={(value) => {
          updateParticipantSettings({ settings: { sharingConsent: value } });
        }}
      />
    </AccordionItem>
  );
};
