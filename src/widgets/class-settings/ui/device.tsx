import { Headset, Mic, Video } from 'lucide-react';
import { Checkbox } from '@/shadcn/components/ui/checkbox';
import { useTranslation } from 'react-i18next';
import { cn } from '@/shadcn/lib/utils';
import {
  addLocalMediaAudioDevices,
  centralAudioProcessing,
  removeLocalMediaAudioDevices,
  setLocalMediaAudioOutputDevice,
  setLocalMediaDeviceUpdating,
  setLocalMediaVideoDevice,
  useLocalMediaContext,
} from '@/features';
import { useEffect } from 'react';
import { AccordionItem, useAppSelector, useUpdateReduxState } from '@/shared';

export const Device = () => {
  const updateReduxState = useUpdateReduxState();
  const { t } = useTranslation();
  const {
    audioDevices: selectedAudioDevices,
    audioOutputDevice: selectedAudioOutputDevice,
    videoDevice: selectedVideoDevice,
    deviceUpdating,
  } = useAppSelector((state) => state.localMedia);

  const { availableDevices } = useLocalMediaContext();
  const { audioInputDevices, audioOutputDevices, videoDevices } =
    availableDevices;

  useEffect(() => {
    const sinkId = centralAudioProcessing
      .getAudioContextProvider()
      .getCurrentSinkId();

    if (sinkId === '' || sinkId === 'default') {
      updateReduxState(setLocalMediaAudioOutputDevice('default'));
    } else {
      updateReduxState(setLocalMediaAudioOutputDevice(sinkId));
    }
  }, []);

  return (
    <AccordionItem
      title={t('class.tools.settings.device.title')}
      value="devices"
      icon={<Video />}
    >
      {centralAudioProcessing.getAudioContextProvider().allowedSetSinkId() && (
        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <h1 className="text-sm">
              {t('class.tools.settings.device.audioOut.title')}
            </h1>
            <h3 className="text-xs text-white/70">
              {t('class.tools.settings.device.audioOut.text')}
            </h3>
          </div>

          <div className="flex flex-col gap-2">
            {audioOutputDevices.map((device, index) => {
              const checked =
                selectedAudioOutputDevice === device.deviceId ||
                (centralAudioProcessing
                  .getAudioContextProvider()
                  .getCurrentSinkId() === '' &&
                  device.deviceId === 'default');
              return (
                <label
                  htmlFor={'audioOut' + device.deviceId}
                  key={index}
                  data-testid={`audio-output-device-${device.deviceId}`}
                >
                  <div
                    key={index}
                    className={cn(
                      'border p-3 rounded-2xl w-full flex items-center justify-between gap-4 transition-colors duration-300',
                      checked
                        ? 'bg-white/10 border-white/30'
                        : 'bg-white/5 border-white/10',
                      !deviceUpdating &&
                        (checked ? 'hover:bg-white/10' : 'hover:bg-white/7'),
                      deviceUpdating ? 'cursor-not-allowed' : 'cursor-pointer',
                    )}
                    data-testid={`audio-output-device-container-${device.deviceId}`}
                  >
                    <div className="grid grid-cols-[auto_1fr] items-center gap-2">
                      <Headset className="text-white size-6" />
                      <div className="text-white text-sm">{device.label}</div>
                    </div>
                    <div className="size-11 flex items-center justify-center">
                      <Checkbox
                        disabled={deviceUpdating}
                        className="size-6 data-[state=unchecked]:border-white/30 data-[state=checked]:!bg-white data-[state=checked]:!text-black data-[state=checked]:[&>span]:[&>svg]:size-5 disabled:hover:bg-transparent disabled:cursor-not-allowed"
                        id={'audioOut' + device.deviceId}
                        data-testid={`audio-output-checkbox-${device.deviceId}`}
                        checked={checked}
                        onCheckedChange={(val) => {
                          if (val) {
                            updateReduxState(setLocalMediaDeviceUpdating(true));
                            updateReduxState(
                              setLocalMediaAudioOutputDevice(device.deviceId),
                            );
                            centralAudioProcessing
                              .getAudioContextProvider()
                              .setSinkId(device.deviceId)
                              .finally(() => {
                                updateReduxState(
                                  setLocalMediaDeviceUpdating(false),
                                );
                              });
                          }
                        }}
                      />
                    </div>
                  </div>
                </label>
              );
            })}
          </div>
        </div>
      )}

      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-2">
          <h1 className="text-sm">
            {t('class.tools.settings.device.audioIn.title')}
          </h1>
          <h3 className="text-xs text-white/70">
            {t('class.tools.settings.device.audioIn.text')}
          </h3>
        </div>

        <div className="flex flex-col gap-2">
          {audioInputDevices.map((device, index) => {
            const checked = selectedAudioDevices.includes(
              `${device.deviceId}_${device.groupId}`,
            );

            return (
              <label
                htmlFor={'audioIn' + device.deviceId}
                key={index}
                data-testid={`audio-input-device-${device.deviceId}`}
              >
                <div
                  key={index}
                  className={cn(
                    'border p-3 rounded-2xl w-full flex items-center justify-between gap-4 transition-colors duration-300',
                    checked
                      ? 'bg-white/10 border-white/30'
                      : 'bg-white/5 border-white/10',
                    !deviceUpdating &&
                      (checked ? 'hover:bg-white/10' : 'hover:bg-white/7'),
                    deviceUpdating ? 'cursor-not-allowed' : 'cursor-pointer',
                  )}
                  data-testid={`audio-input-device-container-${device.deviceId}`}
                >
                  <div className="grid grid-cols-[auto_1fr] items-center gap-2">
                    <Mic className="text-white size-6" />
                    <div className="text-white text-sm">{device.label}</div>
                  </div>
                  <div className="size-11 flex items-center justify-center">
                    <Checkbox
                      disabled={deviceUpdating}
                      id={'audioIn' + device.deviceId}
                      data-testid={`audio-input-checkbox-${device.deviceId}`}
                      className="size-6 data-[state=unchecked]:border-white/30 data-[state=checked]:!bg-white data-[state=checked]:!text-black data-[state=checked]:[&>span]:[&>svg]:size-5 disabled:hover:bg-transparent disabled:cursor-not-allowed"
                      checked={checked}
                      onCheckedChange={(checked) => {
                        const filteredDevices = selectedAudioDevices.filter(
                          (item, idx, arr) => arr.indexOf(item) === idx,
                        );

                        const isChecked = checked === true;

                        if (isChecked) {
                          updateReduxState(
                            addLocalMediaAudioDevices(
                              `${device.deviceId}_${device.groupId}`,
                            ),
                          );
                        } else {
                          if (filteredDevices.length <= 1) return;
                          updateReduxState(
                            removeLocalMediaAudioDevices(
                              `${device.deviceId}_${device.groupId}`,
                            ),
                          );
                        }

                        updateReduxState(setLocalMediaDeviceUpdating(true));
                      }}
                    />
                  </div>
                </div>
              </label>
            );
          })}
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-2">
          <h1 className="text-sm">
            {t('class.tools.settings.device.video.title')}
          </h1>
          <h3 className="text-xs text-white/70">
            {t('class.tools.settings.device.video.text')}
          </h3>
        </div>

        <div className="flex flex-col gap-2">
          {videoDevices.map((device, index) => {
            const checked = selectedVideoDevice === device.deviceId;
            return (
              <label
                htmlFor={'video' + device.deviceId}
                key={index}
                data-testid={`video-device-${device.deviceId}`}
              >
                <div
                  key={index}
                  className={cn(
                    'border p-3 rounded-2xl w-full flex items-center justify-between gap-4 transition-colors duration-300',
                    checked
                      ? 'bg-white/10 border-white/30'
                      : 'bg-white/5 border-white/10',
                    !deviceUpdating &&
                      (checked ? 'hover:bg-white/10' : 'hover:bg-white/7'),
                    deviceUpdating ? 'cursor-not-allowed' : 'cursor-pointer',
                  )}
                  data-testid={`video-device-container-${device.deviceId}`}
                >
                  <div className="grid grid-cols-[auto_1fr] items-center gap-2">
                    <Video className="text-white size-6" />
                    <div className="text-white text-sm">{device.label}</div>
                  </div>
                  <div className="size-11 flex items-center justify-center">
                    <Checkbox
                      disabled={deviceUpdating}
                      className="size-6 data-[state=unchecked]:border-white/30 data-[state=checked]:!bg-white data-[state=checked]:!text-black data-[state=checked]:[&>span]:[&>svg]:size-5 disabled:hover:bg-transparent disabled:cursor-not-allowed"
                      id={'video' + device.deviceId}
                      data-testid={`video-checkbox-${device.deviceId}`}
                      checked={checked}
                      onCheckedChange={(val) => {
                        if (val) {
                          updateReduxState(setLocalMediaDeviceUpdating(true));
                          updateReduxState(
                            setLocalMediaVideoDevice(device.deviceId),
                          );
                        }
                      }}
                    />
                  </div>
                </div>
              </label>
            );
          })}
        </div>
      </div>
    </AccordionItem>
  );
};
