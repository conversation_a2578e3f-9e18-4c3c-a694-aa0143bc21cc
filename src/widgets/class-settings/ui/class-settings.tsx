import { Settings } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useUpdateClassSettings } from '@/entities';
import { FC } from 'react';
import { AccordionItem, Switch, useClassroomSettings } from '@/shared';

interface IClassSettingsProps {
  classroomId: string;
  classId: string;
}

export const ClassSettings: FC<IClassSettingsProps> = ({
  classroomId,
  classId,
}) => {
  const { t } = useTranslation();
  const { mutate: updateClassSettings, isPending } = useUpdateClassSettings(
    classroomId,
    classId,
  );
  const classroomSettings = useClassroomSettings();

  return (
    <AccordionItem
      title={t('class.tools.settings.class-settings.title')}
      value="class-settings"
      icon={<Settings />}
    >
      <Switch
        id="sharing"
        title={t('class.tools.settings.class-settings.items.sharing.title')}
        description={t(
          'class.tools.settings.class-settings.items.sharing.text',
        )}
        disabled={isPending}
        value={classroomSettings?.allowSharing || false}
        setValue={(checked) => {
          updateClassSettings({
            settings: {
              allowSharing: checked,
              allowChat: classroomSettings?.allowChat || false,
            },
          });
        }}
      />
    </AccordionItem>
  );
};
