import { AudioMeter } from './audio-meter';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Slider } from '@/shadcn/components/ui/slider';
import { centralAudioProcessing } from '@/features';

export const VolumeAdjuster = () => {
  const { t } = useTranslation();
  const [volume, setVolume] = useState(1);

  useEffect(() => {
    if (!centralAudioProcessing.isInitialized()) return;
    setVolume(
      centralAudioProcessing
        .getLocalAudioMixer()
        .getAudioLevelMonitor()
        .getGain(),
    );
  }, []);

  return (
    <div className="space-y-5">
      <div className="flex flex-col items-start justify-center gap-1">
        <p className="text-base text-white">
          {t('class.tools.settings.audioSettings.outputVolume.title')}
        </p>
        <p className="text-md text-white/60">
          {t('class.tools.settings.audioSettings.outputVolume.description')}
        </p>
      </div>
      <Slider
        min={0}
        max={3}
        step={0.01}
        value={[volume]}
        data-testid="audio-settings-volume-slider"
        onValueChange={(values) => {
          setVolume(values[0]);
          centralAudioProcessing
            .getLocalAudioMixer()
            .getAudioLevelMonitor()
            .setGain(values[0]);
        }}
      />
      <AudioMeter />
    </div>
  );
};
