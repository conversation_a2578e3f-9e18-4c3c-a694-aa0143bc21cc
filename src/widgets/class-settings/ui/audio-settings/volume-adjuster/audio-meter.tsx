import { useEffect, useRef, useState } from 'react';
import { cn } from '@/shadcn/lib/utils';
import { useTranslation } from 'react-i18next';
import { centralAudioProcessing } from '@/features';

export const AudioMeter = () => {
  const { t } = useTranslation();
  const [level, setLevel] = useState(-Infinity);
  const [peak, setPeak] = useState(-Infinity);

  const latestLevelRef = useRef(-Infinity);
  const latestPeakRef = useRef(-Infinity);
  const monitorIdRef = useRef<string | null>(null);
  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const peakTimer = useRef<NodeJS.Timeout | null>(null);

  const DISPLAY_THRESHOLD_DB = -48;
  const UPDATE_INTERVAL_MS = 100;

  const linearToDb = (value: number) =>
    value <= 0 ? -Infinity : 20 * Math.log10(value);
  const dbToPercent = (db: number, min = -48, max = 3) =>
    Math.max(0, Math.min(100, ((db - min) / (max - min)) * 100));

  useEffect(() => {
    if (!centralAudioProcessing.isInitialized()) return;

    monitorIdRef.current = centralAudioProcessing
      .getLocalAudioMixer()
      .getAudioLevelMonitor()
      .startMonitoring((audioLevel, peakLevel) => {
        latestLevelRef.current = linearToDb(audioLevel);
        latestPeakRef.current = linearToDb(peakLevel);
      });

    updateIntervalRef.current = setInterval(() => {
      const targetLevel = latestLevelRef.current;
      const targetPeak = latestPeakRef.current;

      setLevel((prev) => {
        if (!isFinite(prev)) return targetLevel;
        if (targetLevel > prev) return targetLevel;
        return prev + (targetLevel - prev) * 0.2;
      });

      setPeak((prev) => {
        if (targetPeak > prev) return targetPeak;
        return prev + (targetPeak - prev) * 0.1;
      });
    }, UPDATE_INTERVAL_MS);

    return () => {
      if (monitorIdRef.current) {
        centralAudioProcessing
          .getLocalAudioMixer()
          .getAudioLevelMonitor()
          .stopMonitoring(monitorIdRef.current);
      }
      if (updateIntervalRef.current) clearInterval(updateIntervalRef.current);
      if (peakTimer.current) clearTimeout(peakTimer.current);
    };
  }, []);

  const clampedLevel = Math.max(level, DISPLAY_THRESHOLD_DB);
  const levelPercent = dbToPercent(clampedLevel);
  const peakPercent = dbToPercent(peak);
  const isClipping = level >= 0;

  const dbMarks = [3, 0, -6, -12, -18, -24, -30, -36, -42];
  const zones = [
    { start: -48, end: -20, color: 'bg-green-500' },
    { start: -20, end: -12, color: 'bg-yellow-500' },
    { start: -12, end: -6, color: 'bg-orange-400' },
    { start: -6, end: 0, color: 'bg-orange-500' },
    { start: 0, end: 3, color: 'bg-red-500' },
  ];

  return (
    <div className="w-full rounded-lg border bg-transparent border-white/10 p-3">
      {/* dB Scale */}
      <div className="relative h-4 mb-1.5">
        {dbMarks.map((db) => (
          <div
            key={db}
            className="absolute -translate-x-1/2"
            style={{ left: `${dbToPercent(db)}%` }}
          >
            {[0, -12, -24, -36].includes(db) && (
              <span className="text-xs text-white/50">{db}</span>
            )}
          </div>
        ))}
        <span className="absolute left-1 text-[10px] text-white/50">
          {t('class.tools.settings.audioSettings.outputVolume.audioMeter.db')}
        </span>
      </div>

      {/* Meter Bar */}
      <div className="relative h-10 rounded-md overflow-hidden">
        {/* Zones */}
        <div className="absolute inset-0 flex z-0">
          {zones.map((zone, index) => {
            const width = dbToPercent(zone.end) - dbToPercent(zone.start);
            return (
              <div
                key={index}
                className={cn(zone.color, 'opacity-20')}
                style={{ width: `${width}%` }}
              />
            );
          })}
        </div>

        {/* Tick Marks */}
        {dbMarks.map((db) => (
          <div
            key={`tick-${db}`}
            className="absolute top-0 bottom-0 w-px bg-white/25 z-10"
            style={{ left: `${dbToPercent(db)}%` }}
          />
        ))}

        {/* Level Bar */}
        <div className="absolute inset-0 z-20 flex items-center">
          <div
            className="h-full transition-[width] duration-75 ease-in-out rounded-md"
            style={{
              width: `${levelPercent}%`,
              background:
                level >= 0
                  ? '#ff3333'
                  : level >= -6
                    ? '#ff6633'
                    : level >= -12
                      ? '#ffaa33'
                      : level >= -20
                        ? '#aaff33'
                        : '#33ff66',
            }}
          />
        </div>

        {/* Peak Marker */}
        {peak > DISPLAY_THRESHOLD_DB && (
          <div
            className="absolute top-1.5 bottom-1.5 w-0.5 bg-white/60 z-30 duration-75 ease-in-out"
            style={{ left: `${peakPercent}%` }}
          />
        )}

        {/* Clipping Indicator */}
        {isClipping && (
          <div className="absolute inset-0 z-40 flex items-center justify-end pr-3">
            <span className="text-xs font-bold text-red-400 bg-red-900/80 px-1.5 py-0.5 rounded animate-pulse">
              {t(
                'class.tools.settings.audioSettings.outputVolume.audioMeter.clip',
              )}
            </span>
          </div>
        )}
      </div>

      {/* Level Info */}
      <div className="mt-2 flex justify-between text-xs text-white/60">
        <span>
          {t(
            'class.tools.settings.audioSettings.outputVolume.audioMeter.level',
          ) +
            ':' +
            (level > DISPLAY_THRESHOLD_DB ? `${level.toFixed(1)} dB` : '-')}
        </span>
        <span>
          {t(
            'class.tools.settings.audioSettings.outputVolume.audioMeter.peak',
          ) +
            ':' +
            (peak > DISPLAY_THRESHOLD_DB ? `${peak.toFixed(1)} dB` : '-')}
        </span>
      </div>
    </div>
  );
};
