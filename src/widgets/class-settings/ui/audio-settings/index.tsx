import { AudioLines, Settings2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { SelectItem } from '@/shadcn/components/ui/select';
import {
  AccordionItem,
  Select,
  useAppSelector,
  useUpdateReduxState,
} from '@/shared';
import { VolumeAdjuster } from './volume-adjuster';

import {
  InstrumentType,
  RoomAcoustics,
  setLocalMediaInstrumentType,
  setLocalMediaRoomAcoustics,
} from '@/features';
import { Accordion } from '@/shadcn/components/ui/accordion';
import { CompressorControlPanel } from './compressor-control-panel';
import { JitterBuffer } from './jitter-buffer';
import { INSTRUMENT_TYPES, ROOM_ACOUSTICS } from '../../consts';

export const AudioSettings = () => {
  const { t } = useTranslation();
  const updateReduxState = useUpdateReduxState();
  const { instrumentType, roomAcoustics } = useAppSelector(
    (state) => state.localMedia,
  );

  return (
    <AccordionItem
      title={t('class.tools.settings.audioSettings.title')}
      value="audio-settings"
      icon={<AudioLines />}
    >
      <div className="grid grid-cols-1 gap-4">
        <Select
          title={t('class.tools.settings.audioSettings.instrument.title')}
          description={t(
            'class.tools.settings.audioSettings.instrument.description',
          )}
          id="instrument"
          data-testid="audio-settings-instrument-select"
          placeholder={t(
            (Array.from(INSTRUMENT_TYPES.entries()).find(
              ([, value]) => value === InstrumentType.General,
            ) ?? [''])[0],
          )}
          value={instrumentType}
          onValueChange={(value) =>
            updateReduxState(
              setLocalMediaInstrumentType(value as InstrumentType),
            )
          }
        >
          {Array.from(INSTRUMENT_TYPES.entries()).map(([key, value]) => (
            <SelectItem key={value} value={value}>
              {t(key.toString())}
            </SelectItem>
          ))}
        </Select>

        <Select
          title={t('class.tools.settings.audioSettings.roomAcoustics.title')}
          description={t(
            'class.tools.settings.audioSettings.roomAcoustics.description',
          )}
          id="room-acoustics"
          data-testid="audio-settings-room-acoustics-select"
          placeholder={t(
            (Array.from(ROOM_ACOUSTICS.entries()).find(
              ([, value]) => value === RoomAcoustics.Normal,
            ) ?? [''])[0],
          )}
          value={roomAcoustics}
          onValueChange={(value) =>
            updateReduxState(setLocalMediaRoomAcoustics(value as RoomAcoustics))
          }
        >
          {Array.from(ROOM_ACOUSTICS.entries()).map(([key, value]) => (
            <SelectItem key={value} value={value}>
              {t(key.toString())}
            </SelectItem>
          ))}
        </Select>
        <VolumeAdjuster />

        <Accordion
          type="single"
          collapsible
          className="w-full border-1 rounded-lg border-white/10 px-5"
          data-testid="audio-settings-advanced-accordion"
        >
          <AccordionItem
            title={t(
              'class.tools.settings.audioSettings.advancedAuioSettings.title',
            )}
            value="advanced-audio-settings"
            icon={<Settings2 className="w-4 h-4" />}
            data-testid="audio-settings-advanced-accordion-item"
          >
            <CompressorControlPanel />
            <JitterBuffer />
          </AccordionItem>
        </Accordion>
      </div>
    </AccordionItem>
  );
};
