import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { networkAdaptiveJitterBuffer } from '@/features';
import { Slider } from '@/shadcn/components/ui/slider';

export const JitterBuffer = () => {
  const { t } = useTranslation();
  const [bufferSize, setBufferSize] = useState(300);

  useEffect(() => {
    const currentBuffer = networkAdaptiveJitterBuffer.getCurrentBuffer();
    setBufferSize(currentBuffer);
  }, []);

  return (
    <div className="space-y-4 text-white">
      <div>
        <p className="text-base font-semibold">
          {t(
            'class.tools.settings.audioSettings.advancedAuioSettings.jitterBuffer.title',
          )}
        </p>
        <p className="text-sm text-white/60 mt-1">
          {t(
            'class.tools.settings.audioSettings.advancedAuioSettings.jitterBuffer.description',
          )}
        </p>
      </div>
      <div className="space-y-3">
        <p className="text-xs text-white/50">
          {t(
            'class.tools.settings.audioSettings.advancedAuioSettings.jitterBuffer.currentBuffer',
          )}
          : {bufferSize}ms
        </p>
        <Slider
          min={100}
          max={1000}
          step={50}
          value={[bufferSize]}
          onValueChange={(values) => {
            setBufferSize(values[0]);
            networkAdaptiveJitterBuffer.setFixedBuffer(values[0]);
          }}
        />
      </div>
    </div>
  );
};
