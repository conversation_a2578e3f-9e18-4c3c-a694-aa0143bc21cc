import { useEffect, useState } from 'react';
import { SliderControl } from './slide-control';
import { useTranslation } from 'react-i18next';
import { centralAudioProcessing } from '@/features';

export const CompressorControlPanel = () => {
  const { t } = useTranslation();
  const [threshold, setThreshold] = useState(-24);
  const [knee, setKnee] = useState(30);
  const [ratio, setRatio] = useState(4);
  const [attack, setAttack] = useState(0.003);
  const [release, setRelease] = useState(0.25);

  useEffect(() => {
    if (!centralAudioProcessing.isInitialized()) return;
    const compressor = centralAudioProcessing
      .getLocalAudioMixer()
      .getAudioLevelMonitor()
      .getCompressor();

    if (!compressor) return;

    compressor.threshold.value = threshold;
    compressor.knee.value = knee;
    compressor.ratio.value = ratio;
    compressor.attack.value = attack;
    compressor.release.value = release;
  }, [threshold, knee, ratio, attack, release]);

  return (
    <div className="space-y-6 text-white">
      <div>
        <p className="text-base font-semibold">
          {t(
            'class.tools.settings.audioSettings.advancedAuioSettings.compressor.title',
          )}
        </p>
        <p className="text-sm text-white/60 mt-1">
          {t(
            'class.tools.settings.audioSettings.advancedAuioSettings.compressor.description',
          )}
        </p>
      </div>

      <SliderControl
        label={t(
          'class.tools.settings.audioSettings.advancedAuioSettings.compressor.threshold.title',
        )}
        min={-100}
        max={0}
        step={1}
        value={threshold}
        onChange={setThreshold}
        testId="compressor-threshold-slider"
        description={t(
          'class.tools.settings.audioSettings.advancedAuioSettings.compressor.threshold.description',
        )}
      />

      <SliderControl
        label={t(
          'class.tools.settings.audioSettings.advancedAuioSettings.compressor.knee.title',
        )}
        min={0}
        max={40}
        step={1}
        value={knee}
        onChange={setKnee}
        testId="compressor-knee-slider"
        description={t(
          'class.tools.settings.audioSettings.advancedAuioSettings.compressor.knee.description',
        )}
      />

      <SliderControl
        label={t(
          'class.tools.settings.audioSettings.advancedAuioSettings.compressor.ratio.title',
        )}
        min={1}
        max={20}
        step={0.1}
        value={ratio}
        onChange={setRatio}
        testId="compressor-ratio-slider"
        description={t(
          'class.tools.settings.audioSettings.advancedAuioSettings.compressor.ratio.description',
        )}
      />

      <SliderControl
        label={t(
          'class.tools.settings.audioSettings.advancedAuioSettings.compressor.attack.title',
        )}
        min={0}
        max={1}
        step={0.001}
        value={attack}
        onChange={setAttack}
        testId="compressor-attack-slider"
        description={t(
          'class.tools.settings.audioSettings.advancedAuioSettings.compressor.attack.description',
        )}
      />

      <SliderControl
        label={t(
          'class.tools.settings.audioSettings.advancedAuioSettings.compressor.release.title',
        )}
        min={0}
        max={1}
        step={0.01}
        value={release}
        onChange={setRelease}
        testId="compressor-release-slider"
        description={t(
          'class.tools.settings.audioSettings.advancedAuioSettings.compressor.release.description',
        )}
      />
    </div>
  );
};
