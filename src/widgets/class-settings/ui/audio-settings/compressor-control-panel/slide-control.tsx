import { Slider } from '@/shadcn/components/ui/slider';

export const SliderControl = ({
  label,
  min,
  max,
  step,
  value,
  onChange,
  description,
  testId,
}: {
  label: string;
  min: number;
  max: number;
  step: number;
  value: number;
  onChange: (val: number) => void;
  description?: string;
  testId?: string;
}) => (
  <div className="space-y-1">
    <label className="block text-md text-white">
      {label}: {value}
    </label>
    {description && (
      <p className="text-sm text-white/60 -mt-1">{description}</p>
    )}
    <Slider
      min={min}
      max={max}
      step={step}
      value={[value]}
      data-testid={testId}
      onValueChange={(values) => onChange(values[0])}
    />
  </div>
);
