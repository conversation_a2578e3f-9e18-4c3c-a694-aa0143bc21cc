import { Accordion } from '@/shadcn/components/ui/accordion';
import { Settings as SettingsIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useUser } from '@/entities';
import { Sidebar, useACL } from '@/shared';
import { useParams } from 'react-router';
import { ClassSettings } from './class-settings';
import { General } from './general';
import { Device } from './device';
import { Translation } from './translation';
import { AudioSettings } from './audio-settings';

export const Settings = () => {
  const { roleType } = useUser();
  const { t } = useTranslation();
  const acl = useACL();

  const { id: classroomId, classId } = useParams<{
    id: string;
    classId: string;
  }>();

  return (
    <Sidebar
      disabled={false}
      title={t('class.tools.settings.text')}
      tooltip={t('class.tools.settings.tooltip')}
      triggerIcon={<SettingsIcon />}
      contentClassName="min-[1200px]:min-w-[550px] min-[1200px]:!max-w-[550px]"
      data-testid="settings-sidebar"
    >
      <Accordion
        type="single"
        collapsible
        className="w-full"
        data-testid="settings-accordion"
      >
        {acl && acl.canUpdateClass && (
          <ClassSettings
            classroomId={classroomId ?? ''}
            classId={classId ?? ''}
          />
        )}
        <General classroomId={classroomId ?? ''} classId={classId ?? ''} />

        <Translation classroomId={classroomId ?? ''} classId={classId ?? ''} />

        {roleType !== 'watcher' && (
          <>
            <Device />
            <AudioSettings />
          </>
        )}
      </Accordion>
    </Sidebar>
  );
};
