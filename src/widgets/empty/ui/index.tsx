import { FC } from 'react';

interface IEmptyProps {
  title: string;
  placeholder: string;
  description: string;
}

export const Empty: FC<IEmptyProps> = ({ title, description, placeholder }) => {
  return (
    <div className="w-full py-12 flex flex-col items-center justify-center gap-4">
      <img
        src={placeholder}
        alt="Nothing here yet"
        className="w-75 h-auto aspect-square object-fit rounded-4xl max-[1200px]:w-65 max-[800px]:w-55"
      />
      <div className="flex flex-col gap-2 items-center">
        <h1 className="text-xl font-semibold text-center">{title}</h1>
        <h2>{description}</h2>
      </div>
    </div>
  );
};
