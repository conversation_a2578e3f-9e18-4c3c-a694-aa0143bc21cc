import { GraduationCap, Languages, MicIcon, MicOffIcon } from 'lucide-react';
import { cn } from '@/shadcn/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { cloneElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SoundControl } from './sound-control';
import { MediaStreamView, RoomParticipant } from '@/features';

interface IConferenceParticipantProps {
  dataTestId: string;
  participant: RoomParticipant;
  translation: boolean;
  mediaStream: MediaStream;
  isLocal?: boolean;
  selected?: boolean;
  onClick?: () => void;
}

export const ConferenceParticipant = ({
  dataTestId,
  participant,
  translation,
  mediaStream,
  isLocal,
  selected,
  onClick,
}: IConferenceParticipantProps) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const MicStyledIcon = cloneElement(
    participant.isMicrophoneOn ? (
      <MicIcon data-testid={`${dataTestId}-mic-on-icon`} />
    ) : (
      <MicOffIcon data-testid={`${dataTestId}-mic-off-icon`} />
    ),
    {
      className:
        'w-4 h-auto aspect-square text-primary-foreground max-[1000px]:w-3 shrink-0',
    },
  );

  return (
    <div
      data-testid={`${dataTestId}-container`}
      className={cn(
        'relative rounded-xl min-h-40 aspect-video scrollbar bg-black/40 border-2 max-[1500px]:min-h-35 max-[1200px]:landscape:min-h-30 max-[1000px]:min-h-25 max-[1000px]:landscape:min-h-22.5 max-[700px]:landscape:min-h-20 max-[1000px]:rounded-lg max-[1000px]:border-1.5',
        isLocal
          ? 'border-accent'
          : selected
            ? 'border-white/90'
            : 'border-white/30',
      )}
      onClick={onClick}
      onMouseEnter={() => setOpen(true)}
      onMouseLeave={() => setOpen(false)}
    >
      <div
        data-testid={`${dataTestId}-video-container`}
        className="absolute w-full h-full flex justify-center items-center overflow-hidden rounded-[inherit] max-[1000px]:rounded-md"
      >
        <MediaStreamView
          dataTestId={dataTestId}
          participant={participant}
          mediaStream={mediaStream}
        />
      </div>
      <div
        data-testid={`${dataTestId}-info-overlay`}
        className="backdrop-blur-xs optimized-blur rounded-b-[inherit] absolute bottom-0 w-full bg-black/40 px-3 py-1.5 flex justify-between items-center z-1 max-[1000px]:px-2.5 max-[1000px]:py-1 max-[1000px]:rounded-b-md"
      >
        <p
          data-testid={`${dataTestId}-name`}
          className="text-primary-foreground text-xs truncate max-[1000px]:text-[10px]"
        >
          {isLocal
            ? t('class.you', { name: participant.profile?.displayName ?? '' })
            : (participant.profile?.displayName ?? '')}
        </p>
        {MicStyledIcon}
      </div>
      {participant.profile?.role === 'teacher' && (
        <div
          data-testid={`${dataTestId}-teacher-badge`}
          className="absolute top-1.5 right-1.5 p-2 rounded-full bg-black/40 z-1 max-[1000px]:p-1 max-[1000px]:top-1 max-[1000px]:right-1"
        >
          <GraduationCap
            data-testid={`${dataTestId}-teacher-icon`}
            className="w-5 h-auto aspect-square text-primary-foreground max-[1000px]:w-3"
          />
        </div>
      )}
      {translation && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div
                data-testid={`${dataTestId}-translation-badge`}
                className="absolute top-1.5 left-1.5 p-2 rounded-full bg-accent z-1 max-[1000px]:p-1max-[1000px]:top-1 max-[1000px]:right-1"
              >
                <Languages
                  data-testid={`${dataTestId}-translation-icon`}
                  className="w-5 h-auto aspect-square text-primary-foreground max-[1000px]:w-3"
                />
              </div>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>{`Speaking ...`}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {!isLocal && (
        <SoundControl
          dataTestId={`${dataTestId}-sound-control`}
          sid={participant.sid}
          open={open}
          setOpen={setOpen}
        />
      )}
    </div>
  );
};
