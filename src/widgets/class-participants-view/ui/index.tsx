import { ConferenceParticipant } from './conference-participant';
import {
  centralAudioProcessing,
  ConferenceStatus,
  selectAllRoomUsers,
  useLivekitContext,
  useLocalMediaVideoStream,
} from '@/features';
import { useAppSelector } from '@/shared';

export const ClassParticipantsView = () => {
  const {
    remoteVideoStreams: remoteStreams,
    selectedRemoteSid,
    setSelectedRemoteSid,
  } = useLivekitContext();
  const { localUser } = useAppSelector((state) => state.livekit);
  const { status } = useAppSelector((state) => state.conference);

  const { localVideoStream } = useLocalMediaVideoStream();
  const roomUsers = useAppSelector(selectAllRoomUsers);

  return (
    <div
      className="w-fit scrollbar flex flex-col gap-3 p-3 overflow-auto max-[1200px]:flex-row max-[1200px]:landscape:flex-col max-[1200px]:w-full max-[1200px]:landscape:w-fit max-[1200px]:p-2 max-[1200px]:gap-2"
      style={{ marginLeft: 'env(safe-area-inset-left)' }}
    >
      {localVideoStream && localUser && status === ConferenceStatus.Ready && (
        <ConferenceParticipant
          dataTestId={`participant-view-local`}
          isLocal={true}
          participant={localUser}
          translation={false}
          mediaStream={localVideoStream}
        />
      )}
      {roomUsers.map((participant, index) => {
        const remote = remoteStreams.find(
          (remote) => remote.sid === participant.sid,
        );

        if (!remote || participant.roleType !== 'party') return;

        return (
          <ConferenceParticipant
            key={index}
            dataTestId={`participant-view-${participant.sid}`}
            selected={selectedRemoteSid === participant.sid}
            participant={participant}
            translation={false}
            mediaStream={remote.stream}
            onClick={() => {
              setSelectedRemoteSid(participant.sid);
              centralAudioProcessing
                .getRemoteAudioMixer()
                .getActiveSpeakerManager()
                .setIsEnabled(false);

              setTimeout(
                () =>
                  centralAudioProcessing
                    .getRemoteAudioMixer()
                    .getActiveSpeakerManager()
                    .setIsEnabled(false),
                60000,
              );
            }}
          />
        );
      })}
    </div>
  );
};
