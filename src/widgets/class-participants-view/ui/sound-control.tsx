import { Slider } from '@/shadcn/components/ui/slider';
import { cn } from '@/shadcn/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { Volume2 } from 'lucide-react';
import { FC, useEffect, useState } from 'react';
import { centralAudioProcessing } from '@/features';

interface ISoundControlProps {
  dataTestId: string;
  sid: string;
  open: boolean;
  setOpen: (open: boolean) => void;
}

export const SoundControl: FC<ISoundControlProps> = ({
  dataTestId,
  sid,
  open,
  setOpen,
}) => {
  const participantGain = centralAudioProcessing
    .getRemoteAudioMixer()
    ?.getParticipant(sid)
    ?.getGainNode().gain.value;

  const [volume, setVolume] = useState(1.0);

  useEffect(() => {
    setVolume(participantGain ? participantGain : 1.0);
  }, []);

  return (
    <div
      data-testid={`${dataTestId}-wrapper`}
      className="flex items-center absolute top-1.5 left-1.5 gap-2 w-[70%]"
      onClick={(event) => {
        event.stopPropagation();
      }}
    >
      <div
        data-testid={`${dataTestId}-button`}
        className={cn(
          'rounded-full bg-black/40 z-1 max-[1000px]:p-1 max-[1000px]:top-1 max-[1000px]:right-1 cursor-pointer hover:bg-black/50 transition-all duration-300 p-2',
        )}
        onClick={(event) => {
          event.stopPropagation();
          setOpen(!open);
        }}
      >
        <Volume2
          data-testid={`${dataTestId}-icon`}
          className="w-5 h-auto aspect-square text-primary-foreground max-[1000px]:w-3"
        />
      </div>

      <AnimatePresence>
        {open && (
          <motion.div
            data-testid={`${dataTestId}-slider-container`}
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: '100%', opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
          >
            <Slider
              data-testid={`${dataTestId}-slider`}
              min={0}
              max={2}
              step={0.1}
              className="w-full rounded-full slider-accent"
              value={[volume]}
              onValueChange={(value: number[]) => {
                setVolume(value[0]);
                centralAudioProcessing
                  .getRemoteAudioMixer()
                  ?.getParticipant(sid)
                  ?.setGain(value[0]);
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
