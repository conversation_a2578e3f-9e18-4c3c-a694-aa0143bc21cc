import { Classroom, useUser } from '@/entities';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { ClassroomCard, ClassroomCardSkeleton } from '../../classroom-card';

interface IClassroomCardGridProps {
  classrooms: Classroom[];
  isLoading: boolean;
}

export const ClassroomCardGrid: FC<IClassroomCardGridProps> = ({
  classrooms,
  isLoading,
}) => {
  const { t } = useTranslation();
  const { user } = useUser();

  if (isLoading) {
    return (
      <div className="grid max-[770px]:grid-cols-1 max-[1110px]:grid-cols-2 max-[1450px]:grid-cols-3 max-[1780px]:grid-cols-4 grid-cols-5 gap-6">
        {Array.from({ length: 10 }).map((_, i) => (
          <ClassroomCardSkeleton key={i} />
        ))}
      </div>
    );
  }

  if (classrooms.length === 0) {
    return (
      <div className="text-center py-8 text-primary/60">
        {t('classrooms.empty.all.title')}
      </div>
    );
  }

  return (
    <div className="grid max-[770px]:grid-cols-1 max-[1110px]:grid-cols-2 max-[1450px]:grid-cols-3 max-[1780px]:grid-cols-4 grid-cols-5 gap-6">
      {classrooms.map((classroom) => (
        <ClassroomCard
          data-testid={`classroom-card-${classroom.id}`}
          key={classroom.id}
          id={classroom.id}
          name={classroom.title}
          description={classroom.description}
          canUpdate={classroom.participants.some(
            (p) => p.userId === user!.id && p.acl.canUpdateClass,
          )}
          createdBy={classroom.createdBy}
          activeClassId={classroom.activeClasses?.[0]}
          teachers={
            classroom.participants.filter(
              (p) => p.role === 'teacher' || p.role === 'lead_teacher',
            ).length
          }
          students={
            classroom.participants.filter((p) => p.role === 'student').length
          }
        />
      ))}
    </div>
  );
};
