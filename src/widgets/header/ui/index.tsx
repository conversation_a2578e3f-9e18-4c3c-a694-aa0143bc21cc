import type { FC } from 'react';
import { SidebarTrigger, useSidebar } from '@/shadcn/components/ui/sidebar';
import { useTranslation } from 'react-i18next';
import { cn } from '../../../shadcn/lib/utils';

export const AppHeader: FC<{
  title?: string;
  option?: 'sidebar' | 'shady';
}> = ({ title, option = 'sidebar' }) => {
  const { t } = useTranslation();
  const { open } = useSidebar();

  return (
    <header
      className={cn(
        'w-full sticky top-0 z-2 p-5 flex items-center justify-between border-b-1 border-b-border max-[1000px]:p-4',
        option === 'shady'
          ? 'bg-black/30 bacdrop-blur-md text-white !border-none'
          : 'bg-sidebar',
      )}
      style={{
        paddingLeft: 'max(env(safe-area-inset-left), 1.25rem)',
        paddingRight: 'max(env(safe-area-inset-right), 1.25rem)',
      }}
    >
      <div className="flex items-center justify-start gap-4 flex-1 min-w-0">
        <SidebarTrigger
          data-testid="sidebar-trigger"
          className={cn(
            `[&_svg]:size-6 hover:bg-transparent transition-opacity duration-300 ${!open ? 'opacity-90 hover:opacity-100' : 'opacity-0 pointer-events-none'}`,
            option === 'shady'
              ? 'text-white hover:text-white'
              : 'text-primary hover:text-primary',
          )}
        />
        <h1 className="text-2xl font-bold truncate">
          {title ? title : t('header.dashboard')}
        </h1>
      </div>
    </header>
  );
};
