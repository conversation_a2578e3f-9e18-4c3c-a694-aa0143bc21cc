import { useTranslation } from 'react-i18next';
import { Participant } from '@/entities';
import { ClassroomParticipantCard } from '@/widgets/classroom-participant-card';

interface IClassroomParticipantsProps {
  id: string;
  ownerId: string;
  name: string;
  participants: Participant[];
}

export const ClassroomParticipants = ({
  id,
  ownerId,
  participants,
  name,
}: IClassroomParticipantsProps) => {
  const { t } = useTranslation();

  const categories: {
    dataTestId: string;
    title: string;
    type: 'teacher' | 'student' | 'guest';
    participants: Participant[];
    image: string;
  }[] = [
    {
      dataTestId: `participants-teacher-${id}`,
      title: t('classroom.participants.categories.teacher'),
      type: 'teacher',
      participants: participants.filter(
        (item) => item.role === 'teacher' || item.role === 'lead_teacher',
      ),
      image: '/assets/images/teacher.jpg',
    },
    {
      dataTestId: `participants-student-${id}`,
      title: t('classroom.participants.categories.student'),
      type: 'student',
      participants: participants.filter((item) => item.role === 'student'),
      image: '/assets/images/student.jpg',
    },
    {
      dataTestId: `participants-guest-${id}`,
      title: t('classroom.participants.categories.guest'),
      type: 'guest',
      participants: participants.filter((item) => item.role === 'guest'),
      image: '/assets/images/guest.jpg',
    },
  ];

  return (
    <div className="grid gap-6 grid-cols-1 min-[620px]:grid-cols-2 min-[910px]:grid-cols-3 min-[1200px]:grid-cols-4">
      {categories.map((category, index) => (
        <ClassroomParticipantCard
          key={index}
          id={id}
          name={name}
          category={category}
          ownerId={ownerId}
        />
      ))}
    </div>
  );
};
