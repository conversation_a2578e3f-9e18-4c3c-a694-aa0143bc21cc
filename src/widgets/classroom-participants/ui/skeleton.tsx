import { FC } from 'react';
import { ClassroomParticipantCardSkeleton } from '@/widgets/classroom-participant-card';

export const ClassroomParticipantsSkeleton: FC = () => {
  const skeletonCards = Array.from({ length: 3 });

  return (
    <div className="grid grid-cols-4 gap-6 max-[1200px]:grid-cols-3 max-[900px]:grid-cols-2 max-[600px]:grid-cols-1 w-full animate-pulse">
      {skeletonCards.map((_, index) => (
        <ClassroomParticipantCardSkeleton key={index} />
      ))}
    </div>
  );
};
