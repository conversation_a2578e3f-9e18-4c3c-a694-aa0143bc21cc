import { Skeleton } from '@/shadcn/components/ui/skeleton';

export const ClassHistoryCardSkeleton = () => {
  return (
    <div className="bg-card rounded-xl border border-primary/10 flex flex-col">
      <div className="py-4 px-6 flex flex-col gap-1">
        <Skeleton className="h-6 w-1/2 rounded-md" />
        <Skeleton className="h-4 w-1/3 rounded-md" />
      </div>

      <Skeleton className="w-full aspect-square rounded-none" />

      <div className="py-4 px-6 flex flex-col gap-3 flex-grow">
        <div className="flex flex-col gap-2">
          <Skeleton className="h-4 w-1/4 rounded-md" />
          <Skeleton className="h-4 w-full rounded-md" />
        </div>
        <div className="w-full flex items-center justify-end">
          <Skeleton className="h-8 w-24 rounded-lg" />
        </div>
      </div>
    </div>
  );
};
