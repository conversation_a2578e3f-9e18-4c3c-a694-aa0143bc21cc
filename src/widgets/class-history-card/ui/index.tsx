import { Class } from '@/entities';
import { ClassHistoryDialog } from '../../class-history-dialog';
import moment from 'moment';
import { useTranslation } from 'react-i18next';

interface IClassHistoryCardProps {
  item: Class;
  duration: string;
}

export const ClassHistoryCard = ({
  item,
  duration,
}: IClassHistoryCardProps) => {
  const { t, i18n } = useTranslation();

  const getRandomHistoryImage = () => {
    const historyImages = [
      '/assets/images/history_1.jpg',
      '/assets/images/history_2.jpg',
      '/assets/images/history_3.jpg',
      '/assets/images/history_4.jpg',
    ];

    const randomIndex = Math.floor(Math.random() * historyImages.length);
    return historyImages[randomIndex];
  };

  return (
    <div className="grid grid-rows-[auto_auto_1fr] bg-card rounded-xl duration-300 transition-colors hover:border-primary/20 border border-primary/10">
      <div className="py-4 px-6 flex flex-col gap-1">
        <h1
          className="text-primary/80 font-bold text-xl truncate"
          data-testid={`history-title-${item.id}`}
        >
          {item.title}
        </h1>
        <h3 className="text-sm text-primary">
          {moment(item.createdAt * 1000)
            .locale(i18n.language)
            .format('dddd, MMMM D[,] HH:mm')}
        </h3>
      </div>
      <img
        draggable="false"
        src={getRandomHistoryImage()}
        alt={item.title}
        className="aspect-square w-full object-cover"
      />
      <div className="py-4 px-6 flex flex-col gap-3">
        <div className="flex flex-col gap-2 h-full">
          <h4 className="text-sm font-semibold text-primary/70">
            {t('classroom.history.durationLabel')}:{' '}
            <span className="font-normal">{duration}</span>
          </h4>

          <div className="text-primary/80 font-semibold line-clamp-3">
            {item.summary ?? t('classroom.history.noSummary')}
          </div>
        </div>

        <div className="w-full flex items-center justify-end">
          <ClassHistoryDialog id={item.roomId} classId={item.id} />
        </div>
      </div>
    </div>
  );
};
