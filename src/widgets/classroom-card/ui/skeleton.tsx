import { Skeleton } from '@/shadcn/components/ui/skeleton';

export const ClassroomCardSkeleton = () => {
  return (
    <div
      aria-busy="true"
      aria-label="Loading classroom card"
      className="
        w-full h-full
        px-5 pb-5 pt-4
        rounded-[36px]
        bg-card
        flex flex-col justify-between
        gap-8
        min-w-[310px] max-w-[380px] flex-[1_1_310px]
        shadow-[0_0_12px_0_rgba(63,44,33,0.12)]
        border border-transparent
        animate-pulse
      "
    >
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-5 w-40 rounded-md" />
          <Skeleton className="h-6 w-16 rounded-full" />
        </div>

        <div className="h-[4.5rem] flex flex-col justify-between">
          <Skeleton className="h-5 w-full rounded-md" />
          <Skeleton className="h-5 w-5/6 rounded-md" />
          <Skeleton className="h-5 w-2/3 rounded-md" />
        </div>

        <div className="flex flex-wrap items-center gap-8">
          <div className="flex items-center gap-2 h-6">
            <Skeleton className="size-5 rounded-full" />
            <Skeleton className="h-4 w-24 rounded-md" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="size-5 rounded-full" />
            <Skeleton className="h-4 w-24 rounded-md" />
          </div>
        </div>
      </div>

      <Skeleton className="h-12 w-full rounded-2xl" />
    </div>
  );
};
