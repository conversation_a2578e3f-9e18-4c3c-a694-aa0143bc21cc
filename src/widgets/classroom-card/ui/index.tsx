import { Eye, GraduationCap, Loader2, Users } from 'lucide-react';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { cn } from '@/shadcn/lib/utils';
import { useJoinClass } from '@/entities';
import { Button } from '@/shadcn/components/ui/button';
import { EditClassroom } from '@/features';

interface IClassroomCard {
  id: string;
  name: string;
  description: string;
  students: number;
  teachers: number;
  canUpdate: boolean;
  createdBy: string;
  activeClassId?: string;
}

export const ClassroomCard: FC<IClassroomCard> = ({
  id,
  name,
  description,
  students,
  teachers,
  canUpdate,
  createdBy,
  activeClassId,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { mutate: joinClass, isPending } = useJoinClass();

  return (
    <div
      onClick={() => navigate(`/classrooms/${id}`)}
      className={cn(
        'cursor-pointer w-full h-full px-5 pb-5 pt-4 rounded-[36px] bg-card flex flex-col justify-between gap-8 flex-[1_1_310px] transition-all duration-300',
        activeClassId
          ? 'shadow-[0_0_12px_0_rgba(242,131,79,0.5)] border-1 border-accent/80 hover:border-accent hover:shadow-[0_0_12px_0_rgba(242,131,79,0.3)] active:shadow-[0_0_12px_0_rgba(242,131,79,0.0)]'
          : 'shadow-[0_0_12px_0_rgba(63,44,33,0.12)] hover:shadow-[0_0_4px_4_rgba(63,44,33,0.08)] active:shadow-[0_0_4px_4_rgba(63,44,33,0.08)] border-1 hover:border-[#E2E2E2]',
      )}
      data-testid={`classroom-card-${id}`}
    >
      <div className="flex flex-col gap-4">
        {activeClassId ? (
          <div className="flex items-start justify-end h-8">
            <div className="flex items-center gap-2 bg-accent rounded-full py-1 pl-[0.4rem] pr-2">
              <div className="bg-white rounded-full size-3" />
              <h5 className="text-xs font-semibold text-white">
                {t('classrooms.card.live')}
              </h5>
            </div>
          </div>
        ) : (
          <div className="h-8 w-full flex items-center justify-end">
            {canUpdate && (
              <EditClassroom
                ownerId={createdBy}
                classroomId={id}
                title={name}
                description={description}
              />
            )}
          </div>
        )}

        <h1 className="line-clamp-3 h-[4.5rem] leading-6 text-lg font-bold text-primary overflow-hidden break-words">
          {name}
        </h1>

        <div className="flex flex-wrap items-center gap-8">
          <div className="flex items-center gap-2">
            <GraduationCap strokeWidth="2px" className="text-primary size-5" />
            <h4 className="text-primary">
              {teachers} {t('classrooms.card.teachers')}
            </h4>
          </div>
          <div className="flex items-center gap-2">
            <Users strokeWidth="2px" className="text-primary size-5" />
            <h4 className="text-primary">
              {students} {t('classrooms.card.students')}
            </h4>
          </div>
        </div>
      </div>

      {activeClassId ? (
        <Button
          data-testid={`classroom-card-${id}-join-button`}
          size={'lg'}
          className="!h-[unset] py-2 leading-6 w-full opacity-90 hover:opacity-95 active:opacity-100 duration-300 transition-all"
          onClick={(event) => {
            event.preventDefault();
            event.stopPropagation();
            joinClass({ classroomId: id, classId: activeClassId });
          }}
          disabled={isPending}
        >
          {isPending ? (
            <Loader2 className="animate-spin size-5" />
          ) : (
            t('classrooms.card.actions.join')
          )}
        </Button>
      ) : (
        <Button
          size={'lg'}
          type="button"
          variant="monochrome_outline"
          className="!h-[unset] py-2 leading-6 w-full font-semibold !bg-[#fff2e51a] !border-[#8F7E72] hover:!bg-[#fff2e580] active:!bg-[#FFF2E5]"
        >
          <Eye className="text-primary size-5" strokeWidth="2px" />
          {t('classrooms.card.actions.open')}
        </Button>
      )}
    </div>
  );
};
