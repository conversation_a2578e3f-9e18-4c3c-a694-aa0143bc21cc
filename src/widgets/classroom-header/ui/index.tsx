import { TooltipProvider } from '@/shadcn/components/ui/tooltip';
import { useTranslation } from 'react-i18next';
import { PencilLine } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { cn } from '@/shadcn/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { useUpdateClassroom } from '@/entities';
import { CreateClassButton } from '@/features';
import { ClassroomSettings } from '../../classroom-settings';
import { useACL } from '@/shared';

interface IHeaderProps {
  id: string;
  title: string;
  description: string;
  teacher: string;
  hasActiveClass: boolean;
}

export const ClassroomHeader = ({
  id,
  title,
  description,
  teacher,
  hasActiveClass,
}: IHeaderProps) => {
  const { t } = useTranslation();
  const titleRef = useRef<HTMLHeadingElement>(null);
  const [descriptionText, setDescriptionText] = useState(description);
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  const titleContainerRef = useRef<HTMLDivElement>(null);
  const descriptionContainerRef = useRef<HTMLDivElement>(null);
  const [editingTitle, setEditingTitle] = useState(false);
  const [editingDescription, setEditingDescription] = useState(false);
  const { mutate: updateClassroom } = useUpdateClassroom(id);
  const acl = useACL();

  const TITLE_MIN_LENGTH = 3;
  const TITLE_MAX_LENGTH = 30;
  const DESCRIPTION_MAX_LENGTH = 200;

  const handleTitleInput = (e: React.FormEvent<HTMLHeadingElement>) => {
    const currentText = e.currentTarget.textContent || '';

    if (currentText.length > TITLE_MAX_LENGTH) {
      e.preventDefault();
      const trimmedText = currentText.slice(0, TITLE_MAX_LENGTH);
      e.currentTarget.textContent = trimmedText;
      const range = document.createRange();
      const selection = window.getSelection();
      range.selectNodeContents(e.currentTarget);
      range.collapse(false);
      selection?.removeAllRanges();
      selection?.addRange(range);
    }
  };

  const handleDescriptionInput = (e: React.FormEvent<HTMLParagraphElement>) => {
    const currentText = e.currentTarget.textContent || '';

    if (currentText.length > DESCRIPTION_MAX_LENGTH) {
      e.preventDefault();
      const trimmedText = currentText.slice(0, DESCRIPTION_MAX_LENGTH);
      e.currentTarget.textContent = trimmedText;
      const range = document.createRange();
      const selection = window.getSelection();
      range.selectNodeContents(e.currentTarget);
      range.collapse(false);
      selection?.removeAllRanges();
      selection?.addRange(range);
    }
  };

  const handleTitleBlur = () => {
    if (editingTitle) {
      setEditingTitle(false);
      const newTitle = titleRef.current?.textContent?.trim() || '';

      if (newTitle.trim().length === 0 && titleRef.current) {
        titleRef.current.innerHTML = title;
      }

      if (
        newTitle !== title &&
        titleRef.current &&
        newTitle.trim().length >= TITLE_MIN_LENGTH
      ) {
        titleRef.current.innerHTML = newTitle;
        updateClassroom({ title: newTitle, description });
      } else if (
        newTitle.trim().length < TITLE_MIN_LENGTH &&
        newTitle.trim().length > 0 &&
        titleRef.current
      ) {
        titleRef.current.innerHTML = title;
      }
    }
  };

  const handleDescriptionBlur = () => {
    if (editingDescription) {
      setEditingDescription(false);

      const newDescription = descriptionRef.current?.textContent?.trim() || '';

      if (descriptionRef.current) {
        if (newDescription !== descriptionText) {
          setDescriptionText(newDescription);
          updateClassroom({ title, description: newDescription });
        }
      }
    }
  };

  const handleTitleChangeStart = () => {
    if (!acl || !acl.canUpdateClass) return;

    setEditingTitle(true);
    titleRef.current?.focus();
  };

  const handleDescriptionChangeStart = () => {
    if (!acl || !acl.canUpdateClass) return;

    setEditingDescription(true);

    if (descriptionRef.current) {
      if (!descriptionText || descriptionText.trim().length === 0) {
        descriptionRef.current.innerHTML = '';
      }
      descriptionRef.current.focus();
    }
  };

  useEffect(() => {
    setDescriptionText(description);
  }, [description]);

  return (
    <TooltipProvider>
      <div className="flex items-start justify-between gap-6 max-[650px]:flex-col">
        <div className="w-full flex flex-col gap-4 text-primary">
          <div
            className="group flex items-center gap-3"
            onClick={handleTitleChangeStart}
            ref={titleContainerRef}
          >
            <h1
              className={cn(
                'min-w-[1px] text-3xl text-[#444444] font-bold break-all w-fit outline-none transition-opacity duration-300',
                acl &&
                  acl.canUpdateClass &&
                  'group-focus-within:opacity-70 group-hover:opacity-70',
              )}
              data-testid="classroom-title"
              contentEditable={acl && acl.canUpdateClass}
              suppressContentEditableWarning
              ref={titleRef}
              onInput={handleTitleInput}
              onBlur={handleTitleBlur}
            >
              {title}
            </h1>

            {acl && acl.canUpdateClass && (
              <AnimatePresence>
                {!editingTitle && (
                  <motion.div
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <PencilLine className="size-7 opacity-0 transition-opacity duration-300 group-hover:opacity-70 text-[#444444]" />
                  </motion.div>
                )}
              </AnimatePresence>
            )}
          </div>

          <div
            ref={descriptionContainerRef}
            className="flex items-center gap-2 group"
            onClick={handleDescriptionChangeStart}
          >
            <p
              className={cn(
                'min-w-[1px] max-w-[80%] max-[800px]:max-w-full font-medium text-primary/70 text-sm break-all transition-opacity duration-300 w-fit outline-none',
                acl &&
                  acl.canUpdateClass &&
                  !editingDescription &&
                  'group-hover:opacity-70',
              )}
              contentEditable={acl && acl.canUpdateClass}
              suppressContentEditableWarning
              ref={descriptionRef}
              data-testid="classroom-description"
              onInput={handleDescriptionInput}
              onBlur={handleDescriptionBlur}
            >
              {editingDescription
                ? descriptionText && descriptionText.trim() === ''
                  ? ''
                  : descriptionText
                : !descriptionText || descriptionText.trim() === ''
                  ? acl && acl.canUpdateClass
                    ? t('classroom.header.addDescription')
                    : t('classroom.header.noDescription')
                  : descriptionText}
            </p>

            {acl && acl.canUpdateClass && (
              <AnimatePresence>
                {!editingDescription && (
                  <motion.div
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <PencilLine className="size-4 opacity-0 transition-opacity duration-300 group-hover:opacity-70 text-[#444444]" />
                  </motion.div>
                )}
              </AnimatePresence>
            )}
          </div>

          <div className="flex items-center gap-2">
            <h3 className="font-medium text-primary/60">
              <span className="font-bold text-primary/80">
                {t('classroom.header.teacherLabel', { name: teacher })}
              </span>
            </h3>
          </div>
        </div>

        <div className="flex items-center gap-4 max-[430px]:flex max-[430px]:flex-col max-[430px]:items-start max-[650px]:w-full max-[650px]:grid-cols-2 max-[650px]:grid">
          {acl && acl.canUpdateClass && (
            <ClassroomSettings
              classroomId={id}
              classroomTitle={title}
              classroomDescription={description}
            />
          )}

          <CreateClassButton
            id={id}
            disabled={hasActiveClass}
            triggerPlace="header"
          />
        </div>
      </div>
    </TooltipProvider>
  );
};
