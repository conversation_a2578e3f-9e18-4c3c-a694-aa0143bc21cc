import { FC } from 'react';
import { Skeleton } from '@/shadcn/components/ui/skeleton';

export const ClassroomHeaderSkeleton: FC = () => {
  return (
    <div className="flex items-start justify-between gap-6 w-full max-[650px]:flex-col animate-pulse">
      <div className="flex-1 flex flex-col gap-4 text-primary max-[650px]:w-full">
        <Skeleton className="h-10 w-1/3 rounded-md" />
        <div className="flex flex-col gap-1">
          <Skeleton className="h-4 w-full max-w-[50%] max-[800px]:max-w-full rounded-md" />
          <Skeleton className="h-4 w-full max-w-[30%] max-[800px]:max-w-full rounded-md" />
        </div>
        <Skeleton className="h-6 w-1/4 rounded-md" />
      </div>

      <div className="flex items-center gap-4 max-[400px]:flex-col max-[400px]:items-start">
        <Skeleton className="h-12 w-32 rounded-lg" />
        <Skeleton className="h-12 w-32 rounded-lg" />
      </div>
    </div>
  );
};
