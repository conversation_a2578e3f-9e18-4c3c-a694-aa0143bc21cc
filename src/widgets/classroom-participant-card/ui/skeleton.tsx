import { Skeleton } from '@/shadcn/components/ui/skeleton';

export const ClassroomParticipantCardSkeleton = () => {
  return (
    <div className="bg-card rounded-xl border border-primary/10 flex flex-col h-full">
      <div className="py-4 px-6 flex flex-col gap-1">
        <Skeleton className="h-6 w-1/2 rounded-md" />
        <Skeleton className="h-4 w-1/4 rounded-md" />
      </div>

      <Skeleton className="w-full aspect-square rounded-none" />

      <div className="py-4 px-6 flex flex-col gap-2 flex-grow">
        <div className="flex flex-col gap-2">
          <Skeleton className="h-4 w-1/3 rounded-md" />
          <div className="flex -space-x-2">
            {Array.from({ length: 4 }).map((__, idx) => (
              <Skeleton
                key={idx}
                className="h-12 w-12 rounded-full border-2 border-white"
              />
            ))}
          </div>
        </div>

        <div className="mt-auto w-full flex items-center justify-end">
          <Skeleton className="h-10 w-24 rounded-lg" />
        </div>
      </div>
    </div>
  );
};
