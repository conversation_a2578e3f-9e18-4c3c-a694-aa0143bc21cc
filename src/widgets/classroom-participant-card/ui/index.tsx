import { Participant } from '@/entities';
import { DisabledTooltip, InviteDialog } from '@/features';
import { AvatarGroup } from '@/shadcn/components/ui/avatar-group';
import { useACL, useClassroomSettings } from '@/shared';
import { ClassroomParticipantsListDialog } from '../../classroom-participants-list-dialog';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';

interface IClassroomParticipantCardProps {
  id: string;
  ownerId: string;
  name: string;
  category: {
    dataTestId: string;
    title: string;
    type: 'teacher' | 'student' | 'guest';
    participants: Participant[];
    image: string;
  };
}

export const ClassroomParticipantCard: FC<IClassroomParticipantCardProps> = ({
  id,
  name,
  category,
  ownerId,
}) => {
  const { t } = useTranslation();
  const acl = useACL();
  const classroomSettings = useClassroomSettings();

  return (
    <div
      className="bg-[#FFFAF1] rounded-4xl duration-300 transition-colors hover:border-primary/20 border border-primary/10 flex flex-col h-full py-4 gap-4"
      data-testid={category.dataTestId}
    >
      <div className="px-6 flex flex-col gap-1">
        <h1 className="text-primary/80 font-semibold text-xl">
          {category.title}
        </h1>
        <h3
          className="text-sm text-primary font-semibold"
          data-testid={`participants-summary-${category.type}`}
        >
          {t('classroom.participants.summary', {
            count: category.participants.length,
            category: category.title,
          })}
        </h3>
      </div>

      <img
        draggable="false"
        src={category.image}
        alt={category.title}
        className="aspect-square w-full object-cover"
      />

      <div className="flex flex-col justify-between h-full gap-4 px-4">
        {category.participants.length > 0 ? (
          <div className="flex items-center gap-0.5">
            <div className="flex -space-x-3">
              <AvatarGroup
                users={category.participants.map((p) => ({
                  id: p.userId,
                  name: p.displayName,
                  avatar: p.profileImage,
                }))}
                maxDisplayed={4}
                size="w-11 h-11"
              />
            </div>

            <ClassroomParticipantsListDialog
              classroomId={id}
              participants={category.participants}
              ownerId={ownerId}
              type={category.type}
            />
          </div>
        ) : (
          <div className="h-10 flex items-center">
            <h1 className="text-primary/80">
              {category.type === 'teacher'
                ? t('classroom.participants.empty.noTeachers')
                : category.type === 'student'
                  ? t('classroom.participants.empty.noStudents')
                  : t('classroom.participants.empty.noGuests')}
            </h1>
          </div>
        )}

        <DisabledTooltip
          tooltipText={`globals.permissions.${category.type === 'guest' ? 'canGuestInvite' : 'canParticipantInvite'}`}
          disabled={
            category.type === 'guest'
              ? acl && classroomSettings
                ? !(classroomSettings.allowInviteGuests && acl.canGuestInvite)
                : true
              : acl
                ? !acl.canParticipantInvite
                : true
          }
        >
          <InviteDialog
            defaultRole={category.type}
            name={name}
            toClass={false}
            id={id}
          />
        </DisabledTooltip>
      </div>
    </div>
  );
};
