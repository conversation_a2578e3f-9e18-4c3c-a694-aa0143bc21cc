import { FC } from 'react';
import { Skeleton } from '@/shadcn/components/ui/skeleton';

export const ClassroomMaterialsSkeleton: FC = () => {
  const items = Array.from({ length: 8 });

  return (
    <div className="grid grid-cols-4 max-[1350px]:grid-cols-3 max-[1100px]:grid-cols-2 max-[850px]:grid-cols-1 gap-6 w-full animate-pulse">
      {items.map((_, idx) => (
        <div
          key={idx}
          className="bg-card rounded-xl p-6 flex flex-col gap-4 justify-between border border-primary/10"
        >
          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-4">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="flex flex-col gap-1 flex-1">
                <Skeleton className="h-6 w-1/2 rounded-md" />
                <Skeleton className="h-4 w-1/4 rounded-md" />
              </div>
            </div>
            <Skeleton className="h-4 w-full rounded-md" />
          </div>

          <div className="flex flex-wrap items-center gap-2">
            <Skeleton className="h-8 w-16 rounded-full" />
            <Skeleton className="h-8 w-16 rounded-full" />
          </div>
        </div>
      ))}
    </div>
  );
};
