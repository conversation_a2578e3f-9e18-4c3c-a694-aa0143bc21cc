import { Material, ReportType } from '@/entities';
import { DeleteMaterialDialog, MaterialViewerWrapper } from '@/features';
import { cn } from '@/shadcn/lib/utils';
import { cva } from 'class-variance-authority';
import { FileText, Music2, Play, TriangleAlert } from 'lucide-react';
import moment from 'moment';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../../../shadcn/components/ui/tooltip';

const previewVariants = cva(
  'size-10 rounded-full flex items-center justify-center',
  {
    variants: {
      type: {
        pdf: 'bg-red-200',
        mp3: 'bg-green-200',
        mp4: 'bg-blue-200',
      },
    },
    defaultVariants: {
      type: 'pdf',
    },
  },
);

interface IClassroomMaterialsProps {
  classroomId: string;
  materials: Material[];
}

export const ClassroomMaterials: FC<IClassroomMaterialsProps> = ({
  materials,
  classroomId,
}) => {
  const { t } = useTranslation();

  const parsedMaterials = materials.map((material) => ({
    ...material,
    type: (material.tags?.[0] || 'pdf') as 'pdf' | 'mp3' | 'mp4',
  }));

  return (
    <div
      data-testid="classroom-materials-container"
      className="grid grid-cols-1 gap-6 min-[600px]:grid-cols-2 min-[900px]:grid-cols-3 min-[1350px]:grid-cols-4"
    >
      {parsedMaterials.map((material, index) => (
        <div
          data-testid={`material-card-${material.id}`}
          data-material-title={material.title}
          data-material-type={material.type}
          className="bg-card rounded-xl p-6 flex flex-col gap-4 justify-between duration-300 transition-colors hover:border-primary/20 border border-primary/10"
          key={index}
        >
          <div
            data-testid={`material-content-${material.id}`}
            className="flex flex-col gap-4"
          >
            <div className="grid grid-cols-[auto_1fr_auto] items-center gap-4">
              <div
                data-testid={`material-icon-${material.id}`}
                className={cn(previewVariants({ type: material.type }))}
              >
                {material.type === 'pdf' ? (
                  <FileText
                    className="size-4"
                    data-testid={`material-pdf-icon-${material.id}`}
                  />
                ) : material.type === 'mp3' ? (
                  <Music2
                    className="size-4"
                    data-testid={`material-audio-icon-${material.id}`}
                  />
                ) : (
                  <Play
                    className="size-4"
                    data-testid={`material-video-icon-${material.id}`}
                  />
                )}
              </div>

              <div className="flex flex-col gap-1">
                <h3
                  data-testid={`material-title-${material.id}`}
                  className="text-primary font-semibold text-lg break-all"
                >
                  {material.title}
                </h3>
                <h4
                  data-testid={`material-metadata-${material.id}`}
                  className="text-primary/80 text-xs"
                >
                  {t(`classroom.materials.types.${material.type}`) +
                    ' • ' +
                    moment(material.createdAt * 1000).format('DD.MM.YYYY')}
                </h4>
              </div>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      data-testid={`material-report-button-${material.id}`}
                      className="flex justify-center items-center cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={(e) => {
                        e.stopPropagation();
                        const queryParams = new URLSearchParams({
                          type: ReportType.Content,
                          classroomId: classroomId,
                          contentType: 'file',
                          contentId: material.id,
                        }).toString();

                        window.open(`/legal/report?${queryParams}`, '_blank');
                      }}
                    >
                      <TriangleAlert className="w-6 h-auto aspect-square text-destructive" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p className="text-center">
                      {t('classroom.materials.tooltips.report')}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            <div
              data-testid={`material-description-${material.id}`}
              className="text-primary/80 text-base"
            >
              {material.description}
            </div>
          </div>

          <div
            data-testid={`material-actions-${material.id}`}
            className="flex flex-wrap items-center justify-between gap-2"
          >
            <MaterialViewerWrapper
              material={material}
              classroomId={classroomId}
            />

            <DeleteMaterialDialog
              classroomId={classroomId}
              material={material}
            />
          </div>
        </div>
      ))}
    </div>
  );
};
