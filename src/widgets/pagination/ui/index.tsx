import {
  Pagination as ShadcnPagination,
  PaginationContent,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
} from '@/shadcn/components/ui/pagination';
import { useTranslation } from 'react-i18next';
import { FC } from 'react';

interface IPaginationProps {
  currentPage: number;
  totalPages: number;
  handlePageChange: (page: number) => void;
}

export const Pagination: FC<IPaginationProps> = ({
  currentPage,
  totalPages,
  handlePageChange,
}) => {
  const { t } = useTranslation();

  const renderPaginationItems = () => {
    const items = [];
    const isMobile = window.innerWidth < 768;
    const maxVisiblePages = isMobile ? 3 : 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        items.push(
          <PaginationItem key={i}>
            <PaginationLink
              onClick={() => handlePageChange(i)}
              isActive={currentPage === i}
              className="cursor-pointer"
            >
              {i}
            </PaginationLink>
          </PaginationItem>,
        );
      }
      return items;
    }

    if (isMobile) {
      if (currentPage > 1) {
        items.push(
          <PaginationItem key={1}>
            <PaginationLink
              onClick={() => handlePageChange(1)}
              isActive={false}
              className="cursor-pointer"
            >
              1
            </PaginationLink>
          </PaginationItem>,
        );
        if (currentPage > 2) {
          items.push(<PaginationEllipsis key="ellipsis-start" />);
        }
      }

      items.push(
        <PaginationItem key={currentPage}>
          <PaginationLink
            onClick={() => handlePageChange(currentPage)}
            isActive={true}
            className="cursor-pointer"
          >
            {currentPage}
          </PaginationLink>
        </PaginationItem>,
      );

      if (currentPage < totalPages) {
        if (currentPage < totalPages - 1) {
          items.push(<PaginationEllipsis key="ellipsis-end" />);
        }
        items.push(
          <PaginationItem key={totalPages}>
            <PaginationLink
              onClick={() => handlePageChange(totalPages)}
              isActive={false}
              className="cursor-pointer"
            >
              {totalPages}
            </PaginationLink>
          </PaginationItem>,
        );
      }
      return items;
    }

    const showFirst = currentPage > 3;
    const showLast = currentPage < totalPages - 2;
    const showEllipsisStart = currentPage > 4;
    const showEllipsisEnd = currentPage < totalPages - 3;

    if (showFirst) {
      items.push(
        <PaginationItem key={1}>
          <PaginationLink
            onClick={() => handlePageChange(1)}
            isActive={false}
            className="cursor-pointer"
          >
            1
          </PaginationLink>
        </PaginationItem>,
      );
    }

    if (showEllipsisStart) {
      items.push(<PaginationEllipsis key="ellipsis-start" />);
    }

    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      items.push(
        <PaginationItem key={i}>
          <PaginationLink
            onClick={() => handlePageChange(i)}
            isActive={currentPage === i}
            className="cursor-pointer"
          >
            {i}
          </PaginationLink>
        </PaginationItem>,
      );
    }

    if (showEllipsisEnd) {
      items.push(<PaginationEllipsis key="ellipsis-end" />);
    }

    if (showLast) {
      items.push(
        <PaginationItem key={totalPages}>
          <PaginationLink
            onClick={() => handlePageChange(totalPages)}
            isActive={false}
            className="cursor-pointer"
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>,
      );
    }

    return items;
  };

  return (
    totalPages > 1 && (
      <ShadcnPagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => handlePageChange(currentPage - 1)}
              className={`cursor-pointer ${
                currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              text={t('pagination.previous')}
            />
          </PaginationItem>

          {renderPaginationItems()}

          <PaginationItem>
            <PaginationNext
              onClick={() => handlePageChange(currentPage + 1)}
              className={`cursor-pointer ${
                currentPage === totalPages
                  ? 'opacity-50 cursor-not-allowed'
                  : ''
              }`}
              text={t('pagination.next')}
            />
          </PaginationItem>
        </PaginationContent>
      </ShadcnPagination>
    )
  );
};
