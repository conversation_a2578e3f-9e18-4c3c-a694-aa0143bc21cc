import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteParticipant } from '../api';
import { useNotifications } from '@/shared';

export const useDeleteParticipant = (classroomId: string) => {
  const { addToast, addNotification } = useNotifications();
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['participants', classroomId],
    mutationFn: async ({ participantId }: { participantId: string }) => {
      return await deleteParticipant(classroomId, participantId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['participants', classroomId],
      });
      queryClient.invalidateQueries({
        queryKey: ['classroom', classroomId],
      });
      queryClient.invalidateQueries({
        queryKey: ['classrooms'],
      });
      addToast?.({ text: 'notifications.success.participantRemoved' });
    },
    onError: () => {
      addNotification({
        title: 'notifications.errors.serverError.title',
        description: 'notifications.errors.serverError.description',
      });
    },
  });
};
