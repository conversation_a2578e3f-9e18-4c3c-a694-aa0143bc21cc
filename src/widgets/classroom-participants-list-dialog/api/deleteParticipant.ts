import { api } from '@/shared';
import axios from 'axios';

export const deleteParticipant = async (
  classroomId: string,
  participantId: string,
) => {
  try {
    const response = await api.delete(
      `/classrooms/${classroomId}/participants/${participantId}`,
    );

    return response.data.data;
  } catch (error) {
    let errorMessage = 'Failed to delete participant!';

    if (axios.isAxiosError(error) && error.response) {
      const responseError = error.response.data?.error;
      if (
        responseError &&
        typeof responseError === 'string' &&
        responseError.trim()
      ) {
        errorMessage = responseError;
      }
    }

    throw new Error(errorMessage);
  }
};
