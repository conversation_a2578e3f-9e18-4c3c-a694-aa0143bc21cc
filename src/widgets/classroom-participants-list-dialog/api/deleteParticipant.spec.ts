import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios, { type AxiosError } from 'axios';
import { api } from '@/shared';
import { deleteParticipant } from './deleteParticipant';

vi.mock('axios');
vi.mock('@/shared', () => ({
  api: { delete: vi.fn() },
}));

describe('Delete Participant API', () => {
  let mockDelete: ReturnType<typeof vi.fn>;
  let isAxiosErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockDelete = api.delete as ReturnType<typeof vi.fn>;
    vi.clearAllMocks();
    isAxiosErrorSpy = vi
      .spyOn(axios, 'isAxiosError')
      .mockImplementation(
        (error: unknown): error is AxiosError =>
          typeof error === 'object' && error !== null && 'response' in error,
      );
  });

  afterEach(() => {
    isAxiosErrorSpy.mockRestore();
  });

  describe('deleteParticipant', () => {
    it('should delete participant successfully and return response data', async () => {
      const mockResponseData = {
        message: 'Participant deleted successfully',
        deletedParticipantId: 'user-123',
      };

      mockDelete.mockResolvedValueOnce({
        data: { data: mockResponseData },
      });

      const result = await deleteParticipant('classroom123', 'user-123');

      expect(mockDelete).toHaveBeenCalledWith(
        '/classrooms/classroom123/participants/user-123',
      );
      expect(result).toEqual(mockResponseData);
    });

    it('should handle successful deletion with empty response data', async () => {
      mockDelete.mockResolvedValueOnce({
        data: { data: null },
      });

      const result = await deleteParticipant('classroom456', 'user-789');

      expect(mockDelete).toHaveBeenCalledWith(
        '/classrooms/classroom456/participants/user-789',
      );
      expect(result).toBeNull();
    });

    it('should handle successful deletion with undefined response data', async () => {
      mockDelete.mockResolvedValueOnce({
        data: { data: undefined },
      });

      const result = await deleteParticipant('classroom789', 'user-456');

      expect(mockDelete).toHaveBeenCalledWith(
        '/classrooms/classroom789/participants/user-456',
      );
      expect(result).toBeUndefined();
    });

    it('should work with different classroom and participant IDs', async () => {
      const testCases = [
        { classroomId: 'abc-123', participantId: 'user-001' },
        { classroomId: 'classroom-xyz-789', participantId: 'participant-456' },
        { classroomId: '12345', participantId: 'guest-999' },
        {
          classroomId: 'special-chars_classroom',
          participantId: 'lead-teacher-123',
        },
      ];

      for (const { classroomId, participantId } of testCases) {
        mockDelete.mockResolvedValueOnce({ data: { data: {} } });

        await deleteParticipant(classroomId, participantId);

        expect(mockDelete).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/participants/${participantId}`,
        );
      }
    });

    describe('error handling', () => {
      it('should throw provided error message for participant not found', async () => {
        const errorResponse = {
          response: { data: { error: 'Participant not found' } },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteParticipant('classroom123', 'nonexistent-user'),
        ).rejects.toThrow('Participant not found');
      });

      it('should throw provided error message for permission denied', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Insufficient permissions to delete participant' },
          },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteParticipant('classroom123', 'user-123'),
        ).rejects.toThrow('Insufficient permissions to delete participant');
      });

      it('should throw provided error message for classroom not found', async () => {
        const errorResponse = {
          response: { data: { error: 'Classroom not found' } },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteParticipant('nonexistent-classroom', 'user-123'),
        ).rejects.toThrow('Classroom not found');
      });

      it('should throw provided error message for cannot delete last teacher', async () => {
        const errorResponse = {
          response: {
            data: { error: 'Cannot delete the last teacher from classroom' },
          },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteParticipant('classroom123', 'last-teacher'),
        ).rejects.toThrow('Cannot delete the last teacher from classroom');
      });

      it('should throw default message when API error has empty string error', async () => {
        const errorResponse = {
          response: { data: { error: '   ' } }, // whitespace only
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteParticipant('classroom123', 'user-123'),
        ).rejects.toThrow('Failed to delete participant!');
      });

      it('should throw default message when API error has null error', async () => {
        const errorResponse = {
          response: { data: { error: null } },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteParticipant('classroom123', 'user-123'),
        ).rejects.toThrow('Failed to delete participant!');
      });

      it('should throw default message when API error has undefined error', async () => {
        const errorResponse = {
          response: { data: { error: undefined } },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteParticipant('classroom123', 'user-123'),
        ).rejects.toThrow('Failed to delete participant!');
      });

      it('should throw default message when API error has non-string error', async () => {
        const errorResponse = {
          response: { data: { error: { message: 'Some object error' } } },
        };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteParticipant('classroom123', 'user-123'),
        ).rejects.toThrow('Failed to delete participant!');
      });

      it('should throw default message when API error has no response data', async () => {
        const errorResponse = { response: { data: {} } };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteParticipant('classroom123', 'user-123'),
        ).rejects.toThrow('Failed to delete participant!');
      });

      it('should throw default message when API error has no response', async () => {
        const errorResponse = { response: undefined };
        mockDelete.mockRejectedValueOnce(errorResponse as AxiosError);

        await expect(
          deleteParticipant('classroom123', 'user-123'),
        ).rejects.toThrow('Failed to delete participant!');
      });

      it('should throw default message when error is not axios error', async () => {
        isAxiosErrorSpy.mockReturnValueOnce(false);
        mockDelete.mockRejectedValueOnce(new Error('Network failure'));

        await expect(
          deleteParticipant('classroom123', 'user-123'),
        ).rejects.toThrow('Failed to delete participant!');
      });

      it('should handle generic error correctly', async () => {
        mockDelete.mockRejectedValueOnce(new Error('Some generic error'));

        await expect(
          deleteParticipant('classroom123', 'user-123'),
        ).rejects.toThrow('Failed to delete participant!');
      });

      it('should handle network timeout error', async () => {
        const timeoutError = {
          response: { data: { error: 'Request timeout' } },
        };
        mockDelete.mockRejectedValueOnce(timeoutError as AxiosError);

        await expect(
          deleteParticipant('classroom123', 'user-123'),
        ).rejects.toThrow('Request timeout');
      });

      it('should handle server error', async () => {
        const serverError = {
          response: { data: { error: 'Internal server error' } },
        };
        mockDelete.mockRejectedValueOnce(serverError as AxiosError);

        await expect(
          deleteParticipant('classroom123', 'user-123'),
        ).rejects.toThrow('Internal server error');
      });
    });

    describe('URL construction validation', () => {
      it('should construct correct URL for standard IDs', async () => {
        mockDelete.mockResolvedValueOnce({ data: { data: {} } });

        await deleteParticipant('classroom123', 'user456');

        expect(mockDelete).toHaveBeenCalledWith(
          '/classrooms/classroom123/participants/user456',
        );
      });

      it('should handle IDs with special characters', async () => {
        const classroomId = 'classroom-with-dashes_and_underscores';
        const participantId = 'user-with-special-chars_123';

        mockDelete.mockResolvedValueOnce({ data: { data: {} } });

        await deleteParticipant(classroomId, participantId);

        expect(mockDelete).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/participants/${participantId}`,
        );
      });

      it('should handle numeric IDs', async () => {
        mockDelete.mockResolvedValueOnce({ data: { data: {} } });

        await deleteParticipant('12345', '67890');

        expect(mockDelete).toHaveBeenCalledWith(
          '/classrooms/12345/participants/67890',
        );
      });

      it('should handle UUID format IDs', async () => {
        const classroomId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
        const participantId = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';

        mockDelete.mockResolvedValueOnce({ data: { data: {} } });

        await deleteParticipant(classroomId, participantId);

        expect(mockDelete).toHaveBeenCalledWith(
          `/classrooms/${classroomId}/participants/${participantId}`,
        );
      });
    });

    describe('response data handling', () => {
      it('should return success message when provided', async () => {
        const successData = {
          message: 'Participant successfully removed from classroom',
          timestamp: '2024-01-01T00:00:00Z',
        };

        mockDelete.mockResolvedValueOnce({ data: { data: successData } });

        const result = await deleteParticipant('classroom123', 'user-123');

        expect(result).toEqual(successData);
        expect(result.message).toBe(
          'Participant successfully removed from classroom',
        );
      });

      it('should return deletion confirmation data', async () => {
        const confirmationData = {
          deleted: true,
          participantId: 'user-123',
          classroomId: 'classroom123',
        };

        mockDelete.mockResolvedValueOnce({ data: { data: confirmationData } });

        const result = await deleteParticipant('classroom123', 'user-123');

        expect(result).toEqual(confirmationData);
        expect(result.deleted).toBe(true);
      });

      it('should handle empty object response', async () => {
        mockDelete.mockResolvedValueOnce({ data: { data: {} } });

        const result = await deleteParticipant('classroom123', 'user-123');

        expect(result).toEqual({});
      });

      it('should handle boolean response', async () => {
        mockDelete.mockResolvedValueOnce({ data: { data: true } });

        const result = await deleteParticipant('classroom123', 'user-123');

        expect(result).toBe(true);
      });
    });
  });
});
