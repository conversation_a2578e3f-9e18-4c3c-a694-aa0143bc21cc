import { Participant, useUser } from '@/entities';
import { Avatar, AvatarFallback } from '@/shadcn/components/ui/avatar';
import { Button } from '@/shadcn/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shadcn/components/ui/dialog';
import { getInitials, useACL } from '@/shared';
import { ChevronDown, CircleX } from 'lucide-react';
import { FC, useState } from 'react';
import { useDeleteParticipant } from '../hooks';
import { useTranslation } from 'react-i18next';
import { cn } from '@/shadcn/lib/utils';

interface IClassroomParticipantsListDialogProps {
  classroomId: string;
  participants: Participant[];
  ownerId: string;
  type: 'teacher' | 'student' | 'guest';
}

export const ClassroomParticipantsListDialog: FC<
  IClassroomParticipantsListDialogProps
> = ({ classroomId, participants, ownerId, type }) => {
  const { t } = useTranslation();

  const [deleteTarget, setDeleteTarget] = useState<string | null>(null);
  const { mutate: deleteParticipant, isPending } =
    useDeleteParticipant(classroomId);

  const acl = useACL();
  const { user } = useUser();

  const isTouchDevice =
    typeof window !== 'undefined' && 'ontouchstart' in window;

  return (
    <Dialog>
      <DialogTrigger asChild>
        <button className="size-10 rounded-full flex items-center justify-center cursor-pointer hover:bg-[#FFF2E4] active:[#FFE4C7] group transition-all duration-300">
          <ChevronDown className="size-6 text-[#4D4D4D] group-hover:text-[#3B3B3B] group-active:text-black transition-all duration-300" />
        </button>
      </DialogTrigger>

      <DialogContent className="gap-4 bg-white min-[720px]:!max-w-[670px] max-[720px]:max-w-[calc(100%-2rem)]">
        <DialogHeader>
          <DialogTitle className="text-left">
            {t('classroom.participants.participantManagement.' + type)}
          </DialogTitle>
          <DialogDescription />
        </DialogHeader>

        <div
          className="grid gap-x-4 gap-y-3"
          style={{
            gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
          }}
        >
          {participants.map((participant) => (
            <div
              key={participant.userId}
              className="pr-2 py-1 pl-1 rounded-full w-full flex items-center justify-between max-w-[280px] group transition-all duration-300 hover:bg-[#FFF6F1] gap-2"
              onMouseLeave={() => setDeleteTarget(null)}
            >
              <div className="flex items-center gap-2 min-w-0">
                <Avatar className="size-6">
                  <AvatarFallback className="size-6 text-[0.5rem] font-semibold text-primary">
                    {getInitials(participant.displayName)}
                  </AvatarFallback>
                </Avatar>

                <h1 className="text-primary text-sm truncate">
                  {participant.displayName}
                </h1>
              </div>

              {acl &&
                acl.canKickParticipant &&
                participant.userId !== ownerId &&
                participant.userId !== user!.id &&
                (deleteTarget === participant.userId ? (
                  <button
                    className="opacity-100 pointer-events-auto transition-all duration-300 text-sm text-[#FF4D65] font-medium cursor-pointer"
                    disabled={isPending}
                    onClick={() => {
                      deleteParticipant(
                        { participantId: participant.userId },
                        {
                          onSuccess: () => {
                            setDeleteTarget(null);
                          },
                          onError: () => {
                            setDeleteTarget(null);
                          },
                        },
                      );
                    }}
                  >
                    {isPending
                      ? t(
                          'classroom.participants.participantManagement.buttons.pending',
                        )
                      : t(
                          'classroom.participants.participantManagement.buttons.remove',
                        )}
                  </button>
                ) : (
                  <CircleX
                    className={cn(
                      'w-4 h-4 text-primary cursor-pointer hover:text-[#FF4D65] transition-all duration-300 flex-shrink-0',
                      isTouchDevice
                        ? 'opacity-100 pointer-events-auto'
                        : 'opacity-0 pointer-events-none group-hover:opacity-100 group-hover:pointer-events-auto',
                    )}
                    onClick={() => setDeleteTarget(participant.userId)}
                  />
                ))}
            </div>
          ))}
        </div>

        <DialogFooter className="items-end">
          <DialogClose asChild>
            <Button size="lg">
              {t('classroom.participants.participantManagement.buttons.close')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
