import { CreateClassroomButton } from '@/features';
import { useTranslation } from 'react-i18next';

export const NoClassrooms = () => {
  const { t } = useTranslation();

  return (
    <div className="w-full h-full flex flex-col items-center justify-center gap-6 p-4">
      <img
        src="/assets/images/empty-dashboard.jpg"
        className="aspect-square size-70 select-none landscape:max-[1000px]:hidden"
        draggable={false}
      />

      <div className="flex flex-col gap-2 items-center justify-center">
        <h1 className="text-primary text-2xl font-semibold text-center">
          {t('classrooms.empty.all.title')}
        </h1>
        <h2 className="text-primary/70 font-medium text-center">
          {t('classrooms.empty.all.description')}
        </h2>
      </div>

      <CreateClassroomButton responsive={false} />
    </div>
  );
};
