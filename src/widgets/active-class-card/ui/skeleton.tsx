import { FC } from 'react';
import { Skeleton } from '@/shadcn/components/ui/skeleton';

export const ActiveClassCardSkeleton: FC = () => {
  return (
    <div className="p-4 rounded-xl bg-card/20 border border-card flex items-center justify-between gap-4 max-[600px]:flex-col max-[600px]:items-start w-full animate-pulse">
      <div className="flex flex-col gap-4 w-full">
        <div className="flex items-center justify-start gap-4 max-[500px]:flex-col max-[500px]:items-start w-full">
          <div className="flex items-center justify-center gap-2 bg-card/40 text-accent-foreground px-4 py-1 rounded-full text-sm font-semibold">
            <Skeleton className="h-2 w-2 rounded-full" />
            <Skeleton className="h-4 w-12 rounded-md" />
          </div>
          <Skeleton className="h-6 w-1/3 rounded-md" />
        </div>
        <Skeleton className="h-4 w-1/4 rounded-md text-primary/60" />
      </div>

      <Skeleton className="h-12 w-32 rounded-lg" />
    </div>
  );
};
