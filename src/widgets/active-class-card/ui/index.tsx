import { Loader2 } from 'lucide-react';
import moment from 'moment';
import { Button } from '@/shadcn/components/ui/button';
import { useTranslation } from 'react-i18next';
import { Class, useJoinClass } from '@/entities';

interface IActiveProps {
  classroomId: string;
  activeClass: Class;
}

export const ActiveClassCard = ({ classroomId, activeClass }: IActiveProps) => {
  const { mutate: joinClass, isPending } = useJoinClass();

  const { t } = useTranslation();

  const startTime = moment(activeClass.createdAt * 1000).format('HH:mm:ss');

  return (
    <div className="p-5 rounded-3xl border border-destructive bg-destructive/10 flex items-center justify-between gap-4 max-[600px]:flex-col max-[600px]:items-start">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-center gap-4 max-[500px]:flex-col max-[500px]:items-start">
          <div className="bg-destructive text-accent-foreground px-4 py-1 rounded-full text-sm font-semibold flex items-center justify-center gap-2 whitespace-nowrap">
            <div className="size-2 bg-white animate-pulse rounded-full" />
            {t('classroom.active.live')}
          </div>

          <h1 className="text-primary font-semibold text-xl">
            {activeClass.title}
          </h1>
        </div>
        <h3 className="text-primary/60 font-medium text-sm">
          {t('classroom.active.startedAt', { time: startTime })}
        </h3>
      </div>

      <Button
        data-testid="classroom-active-join-button"
        size="lg"
        className="font-bold"
        onClick={() => joinClass({ classroomId, classId: activeClass.id })}
      >
        {isPending ? (
          <Loader2 className="animate-spin h-5 w-5" />
        ) : (
          <>{t('classroom.active.buttonJoin')}</>
        )}
      </Button>
    </div>
  );
};
