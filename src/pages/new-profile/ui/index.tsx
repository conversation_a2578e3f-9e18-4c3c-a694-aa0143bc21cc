import { AppHeader } from '@/widgets';
import { useTranslation } from 'react-i18next';

export const NewProfilePage = () => {
  const { t } = useTranslation();
  return (
    <>
      <AppHeader title={t('profile.title')} />

      <div className="max-w-[1400px] w-full mx-auto px-2 pt-[130px] flex flex-col gap-20">
        {/* <ProfileHeader />

        <div className="grid grid-cols-[auto_1fr] gap-[44px] h-[20000px]">
          <ProfileNavigation />
          <div className="flex flex-col gap-6">
            <ProfileDetails />
          </div>
        </div> */}
      </div>
    </>
  );
};
