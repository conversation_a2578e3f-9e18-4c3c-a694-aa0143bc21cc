import { ReportTypeSelection } from './report-type-selection';
import { ReportFormProvider, useReportFormContext } from '../context';
import { AppHeader } from '../../../widgets';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router';
import { useEffect } from 'react';
import { ReportType } from '../../../entities';
import { ReportForm } from './report-form';
import { SuccessMessage } from './success-message';

const ReportPageContent = () => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const { step, setStep, setType } = useReportFormContext();

  const getCurrentStep = () => {
    switch (step) {
      case 'success':
        return <SuccessMessage />;
      case 'report-form':
        return <ReportForm />;
      case 'report-type':
      default:
        return <ReportTypeSelection />;
    }
  };

  useEffect(() => {
    const type = searchParams.get('type');
    if (type) {
      setType(type as ReportType);
      setStep('report-form');
    }
  }, []);

  return (
    <>
      <AppHeader title={t('report.header')} />
      <div
        data-testid="report-page-container"
        className="mx-auto max-w-[1536px] h-full w-full py-8 flex justify-center items-center"
        style={{
          paddingLeft: 'max(env(safe-area-inset-left), 2rem)',
          paddingRight: 'max(env(safe-area-inset-right), 2rem)',
        }}
      >
        <div data-testid="report-content-wrapper" className="h-fit flex flex-col jsutify-start items-center gap-10">
          {getCurrentStep()}
        </div>
      </div>
    </>
  );
};

export const ReportPage = () => {
  return (
    <ReportFormProvider>
      <ReportPageContent />
    </ReportFormProvider>
  );
};
