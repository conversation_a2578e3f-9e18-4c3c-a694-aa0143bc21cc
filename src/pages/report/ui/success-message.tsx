import { useTranslation } from 'react-i18next';
import { Button } from '@/shadcn/components/ui/button';
import { HomeIcon } from 'lucide-react';
import { useNavigate } from 'react-router';

export const SuccessMessage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <div data-testid="report-success-container" className="text-center space-y-2">
      <div data-testid="report-success-emoji" className="text-[100px]">🎉</div>
      <h3 data-testid="report-success-title" className="text-2xl font-bold">{t('report.steps.success.title')}</h3>
      <p data-testid="report-success-description" className="text-primary">{t('report.steps.success.description')}</p>
      <Button
        data-testid="report-success-home-button"
        variant="default"
        size="lg"
        className="w-full max-w-[300px] mt-3"
        onClick={() => navigate('/')}
      >
        <HomeIcon data-testid="report-success-home-icon" className="size-5 text-accent-foreground" />
        {t('report.steps.success.buttons.home')}
      </Button>
    </div>
  );
};
