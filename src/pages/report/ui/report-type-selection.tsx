import { useTranslation } from 'react-i18next';
import { ReactNode } from 'react';
import { AlertTriangleIcon, BugIcon, UserIcon } from 'lucide-react';
import { useReportFormContext } from '../context';
import { ReportType } from '@/entities';

export const ReportTypeSelection = () => {
  const { t } = useTranslation();
  const { setType, setStep } = useReportFormContext();

  const types: {
    title: string;
    description: string;
    icon: ReactNode;
    onClick: () => void;
  }[] = [
    {
      title: 'report.steps.typeSelection.types.bug.title',
      description: 'report.steps.typeSelection.types.bug.description',
      icon: <BugIcon className="size-20 text-accent" />,
      onClick: () => setType(ReportType.Bug),
    },
    {
      title: 'report.steps.typeSelection.types.user.title',
      description: 'report.steps.typeSelection.types.user.description',
      icon: <UserIcon className="size-20 text-accent" />,
      onClick: () => setType(ReportType.User),
    },
    {
      title: 'report.steps.typeSelection.types.content.title',
      description: 'report.steps.typeSelection.types.content.description',
      icon: <AlertTriangleIcon className="size-20 text-accent" />,
      onClick: () => setType(ReportType.Content),
    },
  ];

  return (
    <>
      <h3 data-testid="report-type-selection-title" className="text-3xl font-bold text-center">
        {t('report.steps.typeSelection.title')}
      </h3>
      <div data-testid="report-type-selection-container" className="flex items-start flex-row gap-8 max-[700px]:flex-col">
        {types.map((type, index) => {
          const getTestId = () => {
            if (index === 0) return 'report-type-bug';
            if (index === 1) return 'report-type-user';
            return 'report-type-content';
          };
          
          return (
            <div
              key={index}
              data-testid={getTestId()}
              className="flex flex-col justify-center items-center gap-3 max-w-60 cursor-pointer"
              onClick={() => {
                type.onClick();
                setStep('report-form');
              }}
            >
              <h6 data-testid={`${getTestId()}-title`} className="text-xl font-medium">{t(type.title)}</h6>
              <div data-testid={`${getTestId()}-icon`} className="aspect-square h-35 border-2 border-accent/50 bg-accent/10 flex justify-center items-center rounded-lg">
                {type.icon}
              </div>
              <p data-testid={`${getTestId()}-description`} className="text-md text-center">{t(type.description)}</p>
            </div>
          );
        })}
      </div>
    </>
  );
};
