import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Textarea } from '@/shadcn/components/ui/textarea';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/shadcn/components/ui/popover';
import { Info, Loader2 } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shadcn/components/ui/tooltip';
import { useReportFormContext } from '../context';
import { ReportType, useCreateReport } from '@/entities';
import { UploadFiles } from '@/features';
import { useState } from 'react';
import { Button } from '@/shadcn/components/ui/button';
import { useSearchParams } from 'react-router';

type FormValues = {
  description: string;
};

export const ReportForm = () => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<FormValues>({ mode: 'onChange' });
  const { type, setStep } = useReportFormContext();
  const [files, setFiles] = useState<File[]>([]);

  const { mutate, isPending } = useCreateReport(() => {
    setStep('success');
  });

  const onSubmit = (data: FormValues) => {
    mutate({
      report: {
        report_type: type,
        description: data.description,
        reported_user_id: searchParams.get('userId') ?? undefined,
        reported_username: searchParams.get('displayName') ?? undefined,
        classroom_id: searchParams.get('classroomId') ?? undefined,
        content_type: searchParams.get('file') ?? undefined,
        content_id: searchParams.get('contentId') ?? undefined,
      },
      files: files.length > 0 ? files : undefined,
    });
  };

  const getPopoverText = () => {
    const renderListItem = (labelKey: string, textKey: string) => (
      <li className="text-sm">
        <span className="font-semibold">{t(labelKey)}</span>
        {t(textKey)}
      </li>
    );

    switch (type) {
      case ReportType.Bug:
        return (
          <div>
            <p className="text-md font-bold">
              {t('report.steps.form.description.popover.bug.title')}
            </p>
            <ul className="list-disc list-inside pl-2">
              {renderListItem(
                'report.steps.form.description.popover.bug.summary.label',
                'report.steps.form.description.popover.bug.summary.text',
              )}
              {renderListItem(
                'report.steps.form.description.popover.bug.steps.label',
                'report.steps.form.description.popover.bug.steps.text',
              )}
              {renderListItem(
                'report.steps.form.description.popover.bug.expectation.label',
                'report.steps.form.description.popover.bug.expectation.text',
              )}
              {renderListItem(
                'report.steps.form.description.popover.bug.context.label',
                'report.steps.form.description.popover.bug.context.text',
              )}
              {renderListItem(
                'report.steps.form.description.popover.bug.evidence.label',
                'report.steps.form.description.popover.bug.evidence.text',
              )}
            </ul>
          </div>
        );
      case ReportType.User:
        return (
          <div>
            <p className="text-md font-bold">
              {t('report.steps.form.description.popover.user.title')}
            </p>
            <ul className="list-disc list-inside pl-2">
              {renderListItem(
                'report.steps.form.description.popover.user.username.label',
                'report.steps.form.description.popover.user.username.text',
              )}
              {renderListItem(
                'report.steps.form.description.popover.user.behavior.label',
                'report.steps.form.description.popover.user.behavior.text',
              )}
              {renderListItem(
                'report.steps.form.description.popover.user.time.label',
                'report.steps.form.description.popover.user.time.text',
              )}
              {renderListItem(
                'report.steps.form.description.popover.user.examples.label',
                'report.steps.form.description.popover.user.examples.text',
              )}
              {renderListItem(
                'report.steps.form.description.popover.user.impact.label',
                'report.steps.form.description.popover.user.impact.text',
              )}
            </ul>
          </div>
        );
      case ReportType.Content:
      default:
        return (
          <div>
            <p className="text-md font-bold">
              {t('report.steps.form.description.popover.content.title')}
            </p>
            <ul className="list-disc list-inside pl-2">
              {renderListItem(
                'report.steps.form.description.popover.content.link.label',
                'report.steps.form.description.popover.content.link.text',
              )}
              {renderListItem(
                'report.steps.form.description.popover.content.reason.label',
                'report.steps.form.description.popover.content.reason.text',
              )}
              {renderListItem(
                'report.steps.form.description.popover.content.location.label',
                'report.steps.form.description.popover.content.location.text',
              )}
              {renderListItem(
                'report.steps.form.description.popover.content.time.label',
                'report.steps.form.description.popover.content.time.text',
              )}
              {renderListItem(
                'report.steps.form.description.popover.content.context.label',
                'report.steps.form.description.popover.content.context.text',
              )}
            </ul>
          </div>
        );
    }
  };

  return (
    <>
      <h3 data-testid="report-form-title" className="text-3xl font-bold text-center">
        {t('report.steps.form.title')}
      </h3>
      <form data-testid="report-form" onSubmit={handleSubmit(onSubmit)} className=" w-full space-y-6">
        <div data-testid="report-form-description-section" className="flex flex-col gap-2">
          <div className="w-full flex justify-between items-center">
            <h5 data-testid="report-form-description-label" className="text-lg font-medium">
              {t('report.steps.form.description.label')}
            </h5>
            <TooltipProvider>
              <Popover>
                <PopoverTrigger data-testid="report-form-info-popover-trigger">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info data-testid="report-form-info-icon" className="size-5" />
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p className="text-center">
                        {t('report.steps.form.description.info.tooltip')}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </PopoverTrigger>
                <PopoverContent data-testid="report-form-info-popover-content">{getPopoverText()}</PopoverContent>
              </Popover>
            </TooltipProvider>
          </div>
          <Textarea
            data-testid="report-form-description-textarea"
            id="description"
            rows={5}
            placeholder={t('report.steps.form.description.placeholder')}
            className={`${errors.description ? 'border-destructive' : 'border-[#00000030]'} scrollbar resize-none bg-card`}
            {...register('description', {
              required: t('report.steps.form.description.required'),
              maxLength: {
                value: 200,
                message: t('report.steps.form.description.maxLength'),
              },
              validate: (value) => {
                const trimmed = value.trim();
                if (!trimmed) {
                  return t('report.steps.form.description.required');
                }
                return true;
              },
            })}
          />
          {errors.description && (
            <p data-testid="report-form-description-error" className="text-sm text-destructive px-1">
              {errors.description.message}
            </p>
          )}
        </div>
        <div data-testid="report-form-attachments-section" className="flex flex-col gap-2">
          <h5 data-testid="report-form-attachments-label" className="text-lg font-medium">
            {t('report.steps.form.attachments.label')}
          </h5>
          <span data-testid="report-form-upload-wrapper" className="bg-card rounded-lg">
            <UploadFiles
              onFileUpload={(file) => setFiles((prev) => [...prev, file])}
            />
          </span>
        </div>
        <div data-testid="report-form-buttons-section" className="w-full grid grid-cols-2 max-[500px]:grid-rows-2 max-[500px]:grid-cols-1 items-center gap-2">
          <Button
            data-testid="report-form-back-button"
            type="button"
            size="lg"
            variant="outline"
            className="w-full"
            disabled={isPending}
            onClick={() => setStep('report-type')}
          >
            {t('report.steps.form.buttons.back')}
          </Button>
          <Button
            data-testid="report-form-submit-button"
            type="submit"
            variant="default"
            size="lg"
            disabled={!isValid || isPending}
            className="w-full"
          >
            {isPending ? (
              <Loader2 data-testid="report-form-submit-loader" className="animate-spin h-5 w-5" />
            ) : (
              t('report.steps.form.buttons.submit')
            )}
          </Button>
        </div>
      </form>
    </>
  );
};
