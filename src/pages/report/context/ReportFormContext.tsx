import { createContext, useContext, useState, ReactNode } from 'react';
import { ReportType } from '@/entities';

type ReportFormContextType = {
  step: 'report-type' | 'report-form' | 'success';
  setStep: (step: 'report-type' | 'report-form' | 'success') => void;
  type: ReportType;
  setType: (type: ReportType) => void;
};

const ReportFormContext = createContext<ReportFormContextType | undefined>(
  undefined,
);

export const ReportFormProvider = ({ children }: { children: ReactNode }) => {
  const [step, setStep] = useState<'report-type' | 'report-form' | 'success'>(
    'report-type',
  );
  const [type, setType] = useState<ReportType>(ReportType.Bug);

  return (
    <ReportFormContext.Provider
      value={{
        step,
        setStep,
        type,
        setType,
      }}
    >
      {children}
    </ReportFormContext.Provider>
  );
};

export const useReportFormContext = (): ReportFormContextType => {
  const context = useContext(ReportFormContext);
  if (!context) {
    throw new Error('useReportForm must be used within a ReportFormProvider');
  }
  return context;
};
