import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import { useTranslation } from 'react-i18next';
import { useQueryClient } from '@tanstack/react-query';
import { GraduationCap, History as HistoryIcon, Users } from 'lucide-react';
import { Heading } from './heading';
import {
  ActiveClassCard,
  ActiveClassCardSkeleton,
  AppHeader,
  ClassroomHeader,
  ClassroomHeaderSkeleton,
  ClassroomHistoryList,
  ClassroomHistoryListSkeleton,
  ClassroomMaterials,
  ClassroomMaterialsSkeleton,
  ClassroomParticipants,
  ClassroomParticipantsSkeleton,
  Empty,
  Pagination,
} from '@/widgets';
import { CreateClassButton, UploadMaterialDialog } from '@/features';
import {
  useActiveClass,
  useClasses,
  useClassroom,
  useMaterials,
} from '@/entities';
import { useACL, useClassroomSettings, useNotifications } from '@/shared';

export const ClassroomPage = () => {
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const acl = useACL();
  const classroomSettings = useClassroomSettings();
  const [fetchMaterialsEnabled, setFetchMaterialsEnabled] = useState(false);

  const {
    data: classroom,
    isPending: isPendingClassroom,
    error: classroomError,
  } = useClassroom(id!);

  const { data: activeClass, isPending: isPendingActiveClass } = useActiveClass(
    id!,
  );

  const {
    materials,
    currentPage: materialsCurrentPage,
    totalPages: materialsTotalPages,
    handlePageChange: materialsHandlePageChange,
    isPending: isPendingMaterials,
  } = useMaterials(
    id!,
    {
      limit: 8,
    },
    fetchMaterialsEnabled,
  );

  const {
    classes,
    totalPages: classesTotalPages,
    currentPage: classesCurrentPage,
    handlePageChange: classesHandlePageChange,
    isPending: isPendingClasses,
  } = useClasses(id!, { limit: 8 });

  const { addNotification } = useNotifications();

  const teacher =
    (classroom &&
      classroom.participants.filter(
        (item) => item.role === 'lead_teacher',
      )[0]) ||
    (classroom &&
      classroom.participants.filter((item) => item.role === 'teacher')[0]);

  useEffect(() => {
    if (classroomError) {
      switch (classroomError.message) {
        case 'not_found': {
          navigate('/classrooms');
          addNotification?.({
            title: 'notifications.errors.classroomNotFound.title',
            description: 'notifications.errors.classroomNotFound.description',
          });
          break;
        }
        case 'invalid classroom ID':
          navigate('/classrooms');
          addNotification?.({
            title: 'notifications.errors.classroomNotFound.title',
            description: 'notifications.errors.classroomNotFound.description',
          });
          break;
        case 'access denied': {
          navigate('/classrooms');
          break;
        }
      }
    }
  }, [classroomError]);

  useEffect(() => {
    queryClient.invalidateQueries({
      queryKey: ['classes', id!],
    });

    return () => {
      queryClient.removeQueries({
        queryKey: ['classroom', id!],
      });
    };
  }, []);

  useEffect(() => {
    if (acl) {
      if (acl.canDownloadMaterials) setFetchMaterialsEnabled(true);
    }
  }, [acl]);

  return (
    <>
      <AppHeader
        title={classroom ? classroom.title : t('classroom.header.loading')}
      />
      <div
        className="mx-auto max-w-[1536px] w-full py-8 flex flex-col gap-8"
        style={{
          paddingLeft: 'max(env(safe-area-inset-left), 2rem)',
          paddingRight: 'max(env(safe-area-inset-right), 2rem)',
        }}
      >
        {isPendingClassroom ? (
          <ClassroomHeaderSkeleton />
        ) : (
          classroom && (
            <ClassroomHeader
              id={classroom.id}
              title={classroom.title}
              description={classroom.description}
              teacher={teacher?.displayName ?? '--'}
              hasActiveClass={!!activeClass}
            />
          )
        )}
        {isPendingActiveClass ? (
          <ActiveClassCardSkeleton />
        ) : (
          activeClass &&
          classroom && (
            <ActiveClassCard
              classroomId={classroom.id}
              activeClass={activeClass}
            />
          )
        )}

        <Heading title={'classroom.participants.title'} icon={<Users />} />
        {isPendingClassroom ? (
          <ClassroomParticipantsSkeleton />
        ) : (
          classroom && (
            <ClassroomParticipants
              id={classroom.id}
              ownerId={classroom.createdBy}
              name={classroom.title}
              participants={classroom.participants}
            />
          )
        )}

        <div className="flex items-center justify-between gap-2 max-[430px]:flex-col max-[430px]:items-start">
          <Heading title={'classroom.history.title'} icon={<HistoryIcon />} />
          {!isPendingClasses && (
            <CreateClassButton
              triggerPlace="history"
              id={id!}
              disabled={!!activeClass}
            />
          )}
        </div>
        {isPendingClasses ? (
          <ClassroomHistoryListSkeleton />
        ) : classes.length > 0 ? (
          <div className="space-y-6">
            <ClassroomHistoryList items={classes} />
            <Pagination
              currentPage={classesCurrentPage}
              totalPages={classesTotalPages}
              handlePageChange={classesHandlePageChange}
            />
          </div>
        ) : (
          <Empty
            title={t('classroom.empty.history.title')}
            description={t('classroom.empty.history.description')}
            placeholder="/assets/images/empty-history.jpg"
          />
        )}

        {acl &&
          classroomSettings &&
          classroomSettings.allowMaterialsDownload &&
          acl.canDownloadMaterials && (
            <>
              <div className="flex items-center justify-between gap-2 max-[550px]:flex-col max-[550px]:items-start">
                <Heading
                  title={'classroom.materials.title'}
                  icon={<GraduationCap />}
                />
                {!isPendingClassroom && (
                  <UploadMaterialDialog classroomId={id!} />
                )}
              </div>
              {isPendingMaterials ? (
                <ClassroomMaterialsSkeleton />
              ) : materials && materials.length > 0 ? (
                <div className="space-y-6">
                  <ClassroomMaterials materials={materials} classroomId={id!} />
                  <Pagination
                    currentPage={materialsCurrentPage}
                    totalPages={materialsTotalPages}
                    handlePageChange={materialsHandlePageChange}
                  />
                </div>
              ) : (
                <Empty
                  title={t('classroom.empty.materials.title')}
                  placeholder="/assets/images/empty-materials-placeholder.jpg"
                  description={t('classroom.empty.materials.description')}
                />
              )}
            </>
          )}
      </div>
    </>
  );
};

export default ClassroomPage;
