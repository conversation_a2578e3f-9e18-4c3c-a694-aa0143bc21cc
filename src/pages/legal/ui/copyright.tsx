import { AppHeader } from '@/widgets';
import { useTranslation } from 'react-i18next';

export const CopyrightPage = () => {
  const { t } = useTranslation();

  return (
    <>
      <AppHeader title={t('legal.copyright')} />

      <div
        className="h-full justify-start mx-auto my-4 rounded-xl max-w-[1200px] w-full p-6 flex flex-col gap-6 bg-card"
        style={{
          paddingLeft: 'max(env(safe-area-inset-left), 1.5rem)',
          paddingRight: 'max(env(safe-area-inset-right), 1.5rem)',
        }}
      >
        <h1 className="text-3xl font-bold">Copyright & Third-Party Notices</h1>

        <h2 className="text-2xl font-bold text-primary/80">1. Copyright</h2>
        <p>Copyright © 2025 VirtuosoHub All rights reserved.</p>
        <p>
          This software and associated documentation are the property of
          VirtuosoHub. No part may be copied, modified, or distributed without
          express permission, except as permitted under applicable open-source
          licenses referenced below.
        </p>
        <hr />

        <h2 className="text-2xl font-bold text-primary/80">
          2. Third-Party Open Source Credits
        </h2>
        <p>
          This product includes software developed by third parties. Each
          component is licensed separately, as detailed below.
        </p>

        <h3 className="text-xl font-semibold mt-6">MIT Licensed Components</h3>
        <p>
          The following components are licensed under the permissive MIT
          License. The full MIT License text is provided at{' '}
          <a
            href="https://dev.virtuosohub.ai/legal/license"
            className="underline text-accent"
            rel="noopener noreferrer"
            target="_blank"
          >
            https://dev.virtuosohub.ai/legal/license
          </a>{' '}
          and applies to all listed components.
        </p>
        <p className="mt-2">Examples include (but are not limited to):</p>
        <ul className="list-inside flex flex-col gap-1 mt-2">
          <li>• React, React DOM, React Router</li>
          <li>• Redux Toolkit, React Redux</li>
          <li>• Radix UI (@radix-ui/*)</li>
          <li>• Tailwind CSS, Tailwind Merge, @tailwindcss/vite</li>
          <li>• Axios</li>
          <li>• TanStack Query & Virtual</li>
          <li>• Framer Motion</li>
          <li>• Lucide React</li>
          <li>• Date-fns</li>
          <li>• html2canvas-pro</li>
          <li>• clsx, class-variance-authority</li>
          <li>• i18next & related plugins</li>
        </ul>

        <h3 className="text-xl font-semibold mt-6">ISC Licensed Components</h3>
        <p>
          The following components are licensed under the ISC License. The full
          ISC License text is provided at{' '}
          <a
            href="https://dev.virtuosohub.ai/legal/license"
            className="underline text-accent"
            rel="noopener noreferrer"
            target="_blank"
          >
            https://dev.virtuosohub.ai/legal/license
          </a>{' '}
          and applies to all listed components.
        </p>
        <ul className="list-inside flex flex-col gap-1 mt-2">
          <li>• Lucide React (ISC)</li>
        </ul>

        <h3 className="text-xl font-semibold mt-6">
          Apache-2.0 Licensed Components
        </h3>
        <p>
          The following components are licensed under the Apache License 2.0.
          Both the license text and any NOTICE file provided by these projects
          must be included.
        </p>
        <ul className="list-inside flex flex-col gap-1 mt-2">
          <li>• pdfjs-dist (Apache-2.0)</li>
          <li>• livekit-client (Apache-2.0)</li>
          <li>• (Development only) puppeteer (Apache-2.0)</li>
          <li>• (Development only) @playwright/test (Apache-2.0)</li>
        </ul>

        <h4 className="text-lg font-semibold mt-4">LiveKit Client Notice</h4>
        <div className="bg-primary/10 p-4 rounded-lg font-mono text-sm">
          <p>Copyright 2023 LiveKit, Inc.</p>
          <br />
          <p>
            Licensed under the Apache License, Version 2.0 (the
            &quot;License&quot;); you may not use this file except in compliance
            with the License. You may obtain a copy of the License at
          </p>
          <br />
          <p className="ml-4">http://www.apache.org/licenses/LICENSE-2.0</p>
          <br />
          <p>
            Unless required by applicable law or agreed to in writing, software
            distributed under the License is distributed on an &quot;AS IS&quot;
            BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
            or implied. See the License for the specific language governing
            permissions and limitations under the License.
          </p>
        </div>

        <h4 className="text-lg font-semibold mt-4">Playwright Test Notice</h4>
        <div className="bg-primary/10 p-4 rounded-lg font-mono text-sm">
          <p>Portions Copyright (c) Microsoft Corporation.</p>
          <p>Portions Copyright 2017 Google Inc.</p>
          <br />
          <p>
            Licensed under the Apache License, Version 2.0 (the
            &quot;License&quot;); you may not use this file except in compliance
            with the License. You may obtain a copy of the License at
          </p>
          <br />
          <p className="ml-4">http://www.apache.org/licenses/LICENSE-2.0</p>
          <br />
          <p>
            Unless required by applicable law or agreed to in writing, software
            distributed under the License is distributed on an &quot;AS IS&quot;
            BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
            or implied. See the License for the specific language governing
            permissions and limitations under the License.
          </p>
        </div>

        <div className="bg-primary/10 p-4 rounded-lg font-mono text-sm mt-4">
          <p>AWS SDK for Go</p>
          <p>
            Copyright 2015 Amazon.com, Inc. or its affiliates. All Rights
            Reserved.
          </p>
          <p>Copyright 2014-2015 Stripe, Inc.</p>
        </div>

        <div className="space-y-4 mt-4">
          <div>
            <p>
              <b>LiveKit Protocol</b>
            </p>
            <p>License: Apache License 2.0</p>
            <div className="bg-primary/10 p-3 rounded-lg font-mono text-sm mt-2">
              <p>Copyright 2023 LiveKit, Inc.</p>
            </div>
          </div>

          <div>
            <p>
              <b>LiveKit Server SDK (Go)</b>
            </p>
            <p>License: Apache License 2.0</p>
            <div className="bg-primary/10 p-3 rounded-lg font-mono text-sm mt-2">
              <p>Copyright 2023 LiveKit, Inc.</p>
            </div>
          </div>

          <div>
            <p>
              <b>MongoDB Go Driver</b>
            </p>
            <p>
              License: Apache License 2.0 and BSD-style notices (for included
              components)
            </p>
            <p>
              Includes third-party code such as BSON library and Go standard
              components under BSD/MIT-style licenses.
            </p>
            <div className="bg-primary/10 p-3 rounded-lg font-mono text-sm mt-2">
              <p>
                <b>Example (BSON library):</b>
              </p>
              <p>BSON library for Go</p>
              <p>
                Copyright (c) 2010-2013 - Gustavo Niemeyer
                &lt;<EMAIL>&gt;
              </p>
              <p>All rights reserved.</p>
            </div>
          </div>

          <div>
            <p>
              <b>Mailgun Go (v4)</b>
            </p>
            <p>License: BSD 3-Clause License</p>
            <div className="bg-primary/10 p-3 rounded-lg font-mono text-sm mt-2">
              <p>Copyright (c) 2013-2016, Michael Banzon</p>
              <p>All rights reserved.</p>
            </div>
            <p className="mt-2">
              Mailgun&apos;s Go SDK (v4) is distributed under the BSD 3-Clause
              License, which allows use, modification, and redistribution in
              source or binary form, provided that attribution is retained and
              names are not used for endorsement without permission.
            </p>
          </div>
        </div>

        <h3 className="text-xl font-semibold mt-6">Other Licenses</h3>
        <p>
          At the time of this release, no GPL, LGPL, or MPL components are
          included.
        </p>
        <hr />

        <h2 className="text-2xl font-bold text-primary/80">3. License Texts</h2>
        <p>
          Go to{' '}
          <a
            href="https://dev.virtuosohub.ai/legal/license"
            className="underline text-accent"
            rel="noopener noreferrer"
            target="_blank"
          >
            https://dev.virtuosohub.ai/legal/license
          </a>{' '}
          for full licence texts
        </p>
        <hr />

        <h2 className="text-2xl font-bold text-primary/80">4. Other Credits</h2>
        <div className="space-y-3">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/aws/aws-sdk-go v1.55.6</b>
              </p>
              <p>License: Apache-2.0</p>
              <p>NOTICE: Included above (Stripe credit)</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/gin-contrib/cors v1.7.5</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/gin-gonic/gin v1.10.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/golang-jwt/jwt v3.2.2+incompatible</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/golang-jwt/jwt/v5 v5.2.3</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/google/uuid v1.6.0</b>
              </p>
              <p>License: BSD-3-Clause</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/joho/godotenv v1.5.1</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>
                  github.com/livekit/protocol
                  v1.36.2-0.20250415074849-d67a6a9f9604
                </b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/livekit/server-sdk-go/v2 v2.6.0</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/mailgun/mailgun-go/v4 v4.23.0</b>
              </p>
              <p>License: BSD-3-Clause</p>
              <p>NOTICE: N/A. (Section above added.)</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/sashabaranov/go-openai v1.38.2</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/stretchr/testify v1.10.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>go.mongodb.org/mongo-driver v1.17.3</b>
              </p>
              <p>License: Apache-2.0 (+ BSD for legacy BSON)</p>
              <p>NOTICE: (MongoDB section above)</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>go.uber.org/zap v1.27.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>golang.org/x/crypto v0.37.0</b>
              </p>
              <p>License: BSD-style</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>golang.org/x/exp v0.0.0-20250408133849-7e4ce0ab07d0</b>
              </p>
              <p>License: BSD-style</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>golang.org/x/net v0.39.0</b>
              </p>
              <p>License: BSD-style</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>golang.org/x/oauth2 v0.29.0</b>
              </p>
              <p>License: BSD-style</p>
            </div>
          </div>
        </div>

        <h3 className="text-xl font-semibold mt-6">Indirect Dependencies</h3>
        <div className="space-y-3">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>
                  buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go
                  v1.36.6-20250307204501-0409229c3780.1
                </b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>buf.build/go/protoyaml v0.3.2</b>
              </p>
              <p>License: Apache-2.0 if present</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>cel.dev/expr v0.23.1</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/antlr4-go/antlr/v4 v4.13.1</b>
              </p>
              <p>License: BSD-3-Clause</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/benbjohnson/clock v1.3.5</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/bep/debounce v1.2.1</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/bufbuild/protovalidate-go v0.9.3</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/bytedance/sonic v1.13.2</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/bytedance/sonic/loader v0.2.4</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/cespare/xxhash/v2 v2.3.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/cloudwego/base64x v0.1.5</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/davecgh/go-spew v1.1.1</b>
              </p>
              <p>License: ISC</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/dennwc/iters v1.0.1</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>
                  github.com/dgryski/go-rendezvous
                  v0.0.0-20200823014737-9f7001d12a5f
                </b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/frostbyte73/core v0.1.1</b>
              </p>
              <p>License: Apache-2.0</p>
              <p>NOTICE: Copyright 2023 LiveKit, Inc.</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/fsnotify/fsnotify v1.9.0</b>
              </p>
              <p>License: BSD-3-Clause</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/gabriel-vasile/mimetype v1.4.9</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/gammazero/deque v1.0.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/gin-contrib/sse v1.1.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/go-chi/chi/v5 v5.2.1</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/go-jose/go-jose/v3 v3.0.4</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/go-logr/logr v1.4.2</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/go-logr/stdr v1.2.2</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/go-playground/locales v0.14.1</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/go-playground/universal-translator v0.18.1</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/go-playground/validator/v10 v10.26.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/goccy/go-json v0.10.5</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/golang/snappy v1.0.0</b>
              </p>
              <p>License: BSD-3-Clause</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/google/cel-go v0.24.1</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/gorilla/websocket v1.5.3</b>
              </p>
              <p>License: BSD-2-Clause</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/hashicorp/go-cleanhttp v0.5.2</b>
              </p>
              <p>License: MPL-2.0</p>
              <p>NOTICE: Copyright (c) 2015 HashiCorp, Inc.</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/hashicorp/go-retryablehttp v0.7.7</b>
              </p>
              <p>License: MPL-2.0</p>
              <p>NOTICE: Copyright (c) 2015 HashiCorp, Inc.</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/jmespath/go-jmespath v0.4.0</b>
              </p>
              <p>License: Apache-2.0</p>
              <p>NOTICE: go-jmespath Copyright 2015 James Saryerwinnie</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/json-iterator/go v1.1.12</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/jxskiss/base62 v1.1.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/klauspost/compress v1.18.0</b>
              </p>
              <p>License: BSD-3-Clause</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/klauspost/cpuid/v2 v2.2.10</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/leodido/go-urn v1.4.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/lithammer/shortuuid/v4 v4.2.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>
                  github.com/livekit/mageutil v0.0.0-20230125210925-54e8a70427c1
                </b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>
                  github.com/livekit/mediatransportutil
                  v0.0.0-20250416051815-7632a9d1b50d
                </b>
              </p>
              <p>License: Apache-2.0</p>
              <p>NOTICE: Copyright 2023 LiveKit, Inc.</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>
                  github.com/livekit/psrpc v0.6.1-0.20250205181828-a0beed2e4126
                </b>
              </p>
              <p>License: Apache-2.0</p>
              <p>
                Copyright 2023 LiveKit, Inc. Portions of internal and
                protoc-gen-psrpc originated from Twirp
                (https://github.com/twitchtv/twirp/). Copyright 2018 Twitch
                Interactive, Inc. under Apache-2.0 License.
              </p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/magefile/mage v1.15.0</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/mailgun/errors v0.4.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/mattn/go-isatty v0.0.20</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>
                  github.com/modern-go/concurrent
                  v0.0.0-20180306012644-bacd9c7ef1dd
                </b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/modern-go/reflect2 v1.0.2</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/montanaflynn/stats v0.7.1</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/nats-io/nats.go v1.41.2</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/nats-io/nkeys v0.4.11</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/nats-io/nuid v1.0.1</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pelletier/go-toml/v2 v2.2.4</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/datachannel v1.5.10</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/dtls/v3 v3.0.6</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/ice/v4 v4.0.10</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/interceptor v0.1.37</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/logging v0.2.3</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/mdns/v2 v2.0.7</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/randutil v0.1.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/rtcp v1.2.15</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/rtp v1.8.13</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/sctp v1.8.38</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/sdp/v3 v3.0.11</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/srtp/v3 v3.0.4</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/stun/v3 v3.0.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/transport/v3 v3.0.7</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/turn/v4 v4.0.1</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pion/webrtc/v4 v4.0.15</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/pmezard/go-difflib v1.0.0</b>
              </p>
              <p>License: BSD-2-Clause</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/puzpuzpuz/xsync/v3 v3.5.1</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/redis/go-redis/v9 v9.7.3</b>
              </p>
              <p>License: BSD-2-Clause</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/stoewer/go-strcase v1.3.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/stretchr/objx v0.5.2</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/twitchtv/twirp v8.1.3+incompatible</b>
              </p>
              <p>License: Apache-2.0</p>
              <p>
                NOTICE: Twirp Copyright 2018 Twitch Interactive, Inc. All Rights
                Reserved.
              </p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/twitchyliquid64/golang-asm v0.15.1</b>
              </p>
              <p>License: BSD-3-Clause</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/ugorji/go/codec v1.2.12</b>
              </p>
              <p>License: BSD-3-Clause</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/wlynxg/anet v0.0.5</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/xdg-go/pbkdf2 v1.0.0</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/xdg-go/scram v1.1.2</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/xdg-go/stringprep v1.0.4</b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>
                  github.com/youmark/pkcs8 v0.0.0-20240726163527-a2c0da244d78
                </b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>github.com/zeebo/xxh3 v1.0.2</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>go.uber.org/atomic v1.11.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>go.uber.org/multierr v1.11.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>go.uber.org/zap/exp v0.3.0</b>
              </p>
              <p>License: MIT</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>golang.org/x/arch v0.16.0</b>
              </p>
              <p>License: BSD-style</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>golang.org/x/sync v0.13.0</b>
              </p>
              <p>License: BSD-style</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>golang.org/x/sys v0.32.0</b>
              </p>
              <p>License: BSD-style</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>golang.org/x/text v0.24.0</b>
              </p>
              <p>License: BSD-style</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>
                  google.golang.org/genproto/googleapis/api
                  v0.0.0-20250414145226-207652e42e2e
                </b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>
                  google.golang.org/genproto/googleapis/rpc
                  v0.0.0-20250414145226-207652e42e2e
                </b>
              </p>
              <p>License: Apache-2.0</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>google.golang.org/grpc v1.71.1</b>
              </p>
              <p>License: Apache-2.0</p>
              <p>NOTICE: Copyright 2014 gRPC authors</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>google.golang.org/protobuf v1.36.6</b>
              </p>
              <p>License: BSD-3-Clause</p>
            </div>

            <div className="bg-primary/10 p-3 rounded-lg text-sm">
              <p>
                <b>gopkg.in/yaml.v3 v3.0.1</b>
              </p>
              <p>License: MIT + Apache-2.0 (mixed by file)</p>
              <p>NOTICE: Copyright 2011-2016 Canonical Ltd.</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
