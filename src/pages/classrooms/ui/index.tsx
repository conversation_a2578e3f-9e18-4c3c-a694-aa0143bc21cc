import { AppHeader } from '@/widgets/header';
import { ListFilter } from 'lucide-react';
import { ClassroomCardGrid, NoClassrooms, Pagination } from '@/widgets';
import { Button } from '@/shadcn/components/ui/button';
import { useTranslation } from 'react-i18next';
import { CookieConsent } from '@/features';
import { CreateClassroomButton, DisabledTooltip } from '@/features';
import { useClassrooms } from '@/entities';

export const ClassroomsPage = () => {
  const { t } = useTranslation();
  const { classrooms, currentPage, totalPages, handlePageChange, isPending } =
    useClassrooms({ limit: 10 });

  if (!isPending && classrooms.length === 0)
    return (
      <>
        <AppHeader title={t('classrooms.title')} />
        <NoClassrooms />
        <CookieConsent />
      </>
    );

  return (
    <>
      <AppHeader title={t('classrooms.title')} />
      <div className="max-w-[1900px] h-full justify-start mx-auto w-full py-[2rem] flex flex-col gap-6 px-[var(--safe-padding-x)]">
        <div className="flex items-center justify-between gap-2">
          <h1 />
          <div className="flex items-center gap-2">
            <DisabledTooltip tooltipText="classrooms.actions.filter.disabled">
              <Button
                variant="monochrome_outline"
                size="lg"
                className="rounded-xl"
                disabled
              >
                <ListFilter className="size-5 text-primary" />
                <p className="max-[420px]:hidden">
                  {t('classrooms.actions.filter.title')}
                </p>
              </Button>
            </DisabledTooltip>

            <CreateClassroomButton />
          </div>
        </div>

        <ClassroomCardGrid classrooms={classrooms} isLoading={isPending} />
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          handlePageChange={handlePageChange}
        />
      </div>

      <CookieConsent />
    </>
  );
};
