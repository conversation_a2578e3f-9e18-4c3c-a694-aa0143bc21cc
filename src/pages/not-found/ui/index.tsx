import { SidebarTrigger, useSidebar } from '@/shadcn/components/ui/sidebar';
import { NotFound } from '@/widgets';

export const NotFoundPage = () => {
  const { open } = useSidebar();

  return (
    <div className="w-screen h-[100dvh] relative">
      <SidebarTrigger
        className={`absolute top-4 left-4 z-10 [&_svg]:size-7 text-primary hover:bg-transparent hover:text-primary transition-opacity duration-300 ${!open ? 'opacity-90 hover:opacity-100' : 'opacity-0 pointer-events-none'}`}
      />

      <NotFound />
    </div>
  );
};
