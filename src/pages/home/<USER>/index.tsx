import { SidebarTrigger, useSidebar } from '@/shadcn/components/ui/sidebar';
import { Background } from './background';
import { AuthForm } from '@/widgets';
import { CookieConsent } from '@/features';

export const HomePage = () => {
  const { open } = useSidebar();
  return (
    <div className="w-full min-h-[100dvh] relative flex items-center justify-center duration-0">
      <Background />
      <SidebarTrigger
        className={`fixed top-[calc(1rem+env(safe-area-inset-top))] left-[calc(1rem+env(safe-area-inset-left))] z-10 [&_svg]:size-7 text-white hover:bg-transparent transition-opacity duration-300 ${!open ? 'opacity-90 hover:opacity-100' : 'opacity-0 pointer-events-none'}`}
      />
      <AuthForm />
      <CookieConsent />
    </div>
  );
};
