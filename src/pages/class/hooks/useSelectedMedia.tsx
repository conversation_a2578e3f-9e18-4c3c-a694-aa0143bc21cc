import { useEffect } from 'react';
import { selectAllRoomUsers, useLivekitContext } from '@/features';
import { useAppSelector } from '@/shared';

export const useSelectedMedia = () => {
  const roomUsers = useAppSelector(selectAllRoomUsers);
  const { remoteVideoStreams, selectedRemoteSid, setSelectedRemoteSid } =
    useLivekitContext();

  const remoteUsers = roomUsers.filter((user) => user.roleType === 'party');
  const selectedParticipant = remoteUsers.find(
    (u) => u.sid === selectedRemoteSid,
  );

  // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
  const selectedVideoStream = remoteVideoStreams.find(
    (r) => r.sid === selectedParticipant?.sid,
  )?.stream!;

  useEffect(() => {
    if (remoteUsers.length !== 0) {
      const selectedUser = remoteUsers.find(
        (remote) => remote.sid === selectedRemoteSid,
      );

      if (!selectedUser) {
        setSelectedRemoteSid(remoteUsers[remoteUsers.length - 1].sid);
      }
    } else {
      setSelectedRemoteSid(null);
    }
  }, [remoteUsers]);

  return { selectedParticipant, selectedVideoStream };
};
