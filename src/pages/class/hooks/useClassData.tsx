import { Role, useAppSelector, useUpdateReduxState } from '@/shared';
import { useEffect } from 'react';
import { useParams } from 'react-router';
import {
  selectAllRoomUsers,
  setRoomUserProfile,
  setConferenceStatus,
  setConferenceParticipant,
  ConferenceStatus,
} from '@/features';
import { useClass, setUserRole, useUser, setUserRoleType } from '@/entities';

export const useClassData = () => {
  const updateReduxState = useUpdateReduxState();
  const { id: classroomId, classId } = useParams<{
    id: string;
    classId: string;
  }>();

  const { user } = useUser();
  const { status } = useAppSelector((state) => state.conference);
  const roomUsers = useAppSelector(selectAllRoomUsers);

  const { data: classDetails, isPending } = useClass(
    classId ?? '',
    classroomId ?? '',
  );

  // Set local user role on initial render
  useEffect(() => {
    if (!classDetails || !user || status !== ConferenceStatus.Fetching) return;
    classDetails.participants.map((participant) => {
      if (participant.userId === user.id) {
        const role = participant.role as Role;
        const roleType = role !== Role.Guest ? 'party' : 'watcher';

        updateReduxState(setUserRole(role));
        updateReduxState(setUserRoleType(roleType));
        updateReduxState(
          setConferenceStatus(
            roleType === 'party'
              ? ConferenceStatus.LocalMedia
              : ConferenceStatus.Livekit,
          ),
        );
      }
    });
  }, [classDetails, user, status]);

  useEffect(() => {
    if (!classDetails || !user) return;

    classDetails.participants.map((participant) => {
      if (participant.userId === user.id) {
        updateReduxState(setConferenceParticipant(participant));
      }
    });
  }, [classDetails, user]);

  // Remote profile update on join
  useEffect(() => {
    if (classDetails) {
      roomUsers.map((user) => {
        if (user.profile === null) {
          const profile = classDetails.participants.find(
            (participant) => participant.userId === user.identity,
          );

          if (profile)
            updateReduxState(setRoomUserProfile({ sid: user.sid, profile }));
        }
      });
    }
  }, [classDetails, roomUsers]);

  return { classDetails, isPending };
};
