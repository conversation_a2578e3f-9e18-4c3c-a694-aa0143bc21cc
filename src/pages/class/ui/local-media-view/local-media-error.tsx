import { Button } from '@/shadcn/components/ui/button';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Off } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import {
  detectBrowser,
  LocalMediaStatus,
  useLocalMediaContext,
} from '@/features';

const isMobileDevice = () =>
  /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent);

interface ILocalMediaErrorProps {
  localStream: MediaStream | null;
}

export function LocalMediaError({ localStream }: ILocalMediaErrorProps) {
  const { status } = useLocalMediaContext();
  const browser = detectBrowser();
  const mobile = isMobileDevice();
  const { t } = useTranslation();
  const handleRefresh = () => window.location.reload();

  const hasAudioTrack = localStream?.getAudioTracks().length !== 0;
  const hasVideoTrack = localStream?.getVideoTracks().length !== 0;

  const permissionInstructions = () => {
    if (mobile) {
      switch (browser) {
        case 'chrome':
        case 'edge':
          return (
            <ol className="list-decimal list-inside text-sm space-y-1 text-left">
              <li data-testid="local-media-error-mobile-chrome-edge-text1">
                {t('class.localMediaError.mobile.chrome_edge.text1')}
              </li>
              <li>{t('class.localMediaError.mobile.chrome_edge.text2')}</li>
              <li>{t('class.localMediaError.mobile.chrome_edge.text3')}</li>
              <li>{t('class.localMediaError.mobile.chrome_edge.text4')}</li>
            </ol>
          );
        case 'yandex':
          return (
            <ol className="list-decimal list-inside text-sm space-y-1 text-left">
              <li data-testid="local-media-error-mobile-yandex-text1">
                {t('class.localMediaError.mobile.yandex.text1')}
              </li>
              <li>{t('class.localMediaError.mobile.yandex.text2')}</li>
              <li>{t('class.localMediaError.mobile.yandex.text3')}</li>
            </ol>
          );
        case 'firefox':
          return (
            <ol className="list-decimal list-inside text-sm space-y-1 text-left">
              <li data-testid="local-media-error-mobile-firefox-text1">
                {t('class.localMediaError.mobile.firefox.text1')}
              </li>
              <li>{t('class.localMediaError.mobile.firefox.text2')}</li>
              <li>{t('class.localMediaError.mobile.firefox.text3')}</li>
              <li>{t('class.localMediaError.mobile.firefox.text4')}</li>
            </ol>
          );
        case 'safari':
          return (
            <ol className="list-decimal list-inside text-sm space-y-1 text-left">
              <li data-testid="local-media-error-mobile-safari-text1">
                {t('class.localMediaError.mobile.safari.text1')}
              </li>
              <li>{t('class.localMediaError.mobile.safari.text2')}</li>
              <li>{t('class.localMediaError.mobile.safari.text3')}</li>
            </ol>
          );
        default:
          return (
            <p className="text-sm">
              {t('class.localMediaError.mobile.default.text1')}
            </p>
          );
      }
    } else {
      switch (browser) {
        case 'edge':
        case 'yandex':
          return (
            <ol className="list-decimal list-inside text-sm space-y-1 text-left">
              <li data-testid="local-media-error-pc-edge-yandex-text1">
                {t('class.localMediaError.pc.edge_yandex.text1')}
              </li>
              <li>{t('class.localMediaError.pc.edge_yandex.text2')}</li>
              <li>{t('class.localMediaError.pc.edge_yandex.text3')}</li>
            </ol>
          );
        case 'chrome':
          return (
            <ol className="list-decimal list-inside text-sm space-y-1 text-left">
              <li data-testid="local-media-error-pc-chrome-text1">
                {t('class.localMediaError.pc.chrome.text1')}
              </li>
              <li>{t('class.localMediaError.pc.chrome.text2')}</li>
              <li>{t('class.localMediaError.pc.chrome.text3')}</li>
            </ol>
          );
        case 'firefox':
          return (
            <ol className="list-decimal list-inside text-sm space-y-1 text-left">
              <li data-testid="local-media-error-pc-firefox-text1">
                {t('class.localMediaError.pc.firefox.text1')}
              </li>
              <li>{t('class.localMediaError.pc.firefox.text2')}</li>
              <li>{t('class.localMediaError.pc.firefox.text3')}</li>
              <li>{t('class.localMediaError.pc.firefox.text4')}</li>
            </ol>
          );
        case 'safari':
          return (
            <ol className="list-decimal list-inside text-sm space-y-1 text-left">
              <li data-testid="local-media-error-pc-safari-text1">
                {t('class.localMediaError.pc.safari.text1')}
              </li>
              <li>{t('class.localMediaError.pc.safari.text2')}</li>
              <li>{t('class.localMediaError.pc.safari.text3')}</li>
              <li>{t('class.localMediaError.pc.safari.text4')}</li>
            </ol>
          );
        default:
          return (
            <p className="text-sm">
              <span data-testid="local-media-error-pc-default-text1">
                {t('class.localMediaError.pc.default.text1')}
              </span>
            </p>
          );
      }
    }
  };

  return (
    <div className="p-6 max-w-md text-center h-fit flex flex-col items-center justify-center gap-5 text-foreground">
      {status === LocalMediaStatus.PermissionError && (
        <div className="flex flex-col justify-center gap-2">
          <div className="flex justify-center gap-4">
            <MicOff className="w-11 h-11 text-red-500" />
            <CameraOff className="w-11 h-11 text-red-500" />
          </div>
          <h2
            data-testid="local-media-error-blocked-title"
            className="text-lg font-semibold"
          >
            {t('class.localMediaError.blocked.title')}
          </h2>
          <p
            className="text-sm"
            dangerouslySetInnerHTML={{
              __html: t('class.localMediaError.blocked.description'),
            }}
          />
          {permissionInstructions()}
        </div>
      )}

      {status === LocalMediaStatus.DeviceError && (
        <div className="flex flex-col justify-center gap-2">
          <div className="flex justify-center gap-4">
            {!hasAudioTrack && <MicOff className="w-11 h-11 text-yellow-500" />}
            {!hasVideoTrack && (
              <VideoOff className="w-11 h-11 text-yellow-500" />
            )}
          </div>
          <h2
            data-testid="local-media-error-not-found-title"
            className="text-lg font-semibold"
          >
            {t('class.localMediaError.notFound.title')}
          </h2>
          <p className="text-sm flex flex-col">
            {!hasAudioTrack && (
              <span
                dangerouslySetInnerHTML={{
                  __html: t('class.localMediaError.notFound.noAudio'),
                }}
              />
            )}
            {!hasVideoTrack && (
              <span
                dangerouslySetInnerHTML={{
                  __html: t('class.localMediaError.notFound.noVideo'),
                }}
              />
            )}
            {t('class.localMediaError.notFound.text')}
          </p>
        </div>
      )}

      {status === LocalMediaStatus.TakenDeviceError && (
        <div className="flex flex-col items-center justify-center gap-2">
          <AlertTriangle className="w-12 h-12 text-orange-500" />
          <h2
            data-testid="local-media-error-in-use-title"
            className="text-lg font-semibold"
          >
            {t('class.localMediaError.inUse.title')}
          </h2>
          <p className="text-sm">{t('class.localMediaError.inUse.text')}</p>
        </div>
      )}

      <Button
        data-testid="local-media-error-reload-button"
        onClick={handleRefresh}
      >
        {t('class.localMediaError.reload')}
      </Button>
    </div>
  );
}
