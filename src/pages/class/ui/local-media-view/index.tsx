import { LocalMediaStatus, useLocalMediaContext } from '@/features';
import { LoadingState } from '../loading-state';
import { LocalMediaError } from './local-media-error';

export const LocalMediaView = () => {
  const { localStream, status } = useLocalMediaContext();

  return status === LocalMediaStatus.PermissionError ||
    status === LocalMediaStatus.DeviceError ||
    status === LocalMediaStatus.TakenDeviceError ? (
    <div className="w-full h-full bg-background flex-col flex items-center justify-center z-99">
      <LocalMediaError localStream={localStream} />
    </div>
  ) : (
    <LoadingState text={'class.status.permissions'} />
  );
};
