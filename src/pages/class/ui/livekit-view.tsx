import { XCircle } from 'lucide-react';
import { LoadingState } from './loading-state';
import { Button } from '@/shadcn/components/ui/button';
import { useTranslation } from 'react-i18next';
import { ConnectionStatus, setLivekitJoinStatus } from '@/features';
import { useAppSelector, useUpdateReduxState } from '@/shared';

export const LivekitView = () => {
  const updateReduxState = useUpdateReduxState();
  const { t } = useTranslation();
  const { status } = useAppSelector((state) => state.livekit);

  return (
    <>
      {status === ConnectionStatus.Connecting && (
        <LoadingState text={'class.status.livekit.connecting'} />
      )}
      {status === ConnectionStatus.Failed && (
        <div className="h-full w-full bg-background flex flex-col items-center justify-center gap-4 p-4 z-99">
          <XCircle className="h-20 w-20 text-destructive" />
          <span
            className="text-base text-center text-foreground max-w-sm"
            dangerouslySetInnerHTML={{
              __html: t('class.status.livekit.failed'),
            }}
          />
          <Button
            variant="default"
            className="w-30"
            onClick={() => {
              updateReduxState(setLivekitJoinStatus('joined'));
            }}
          >
            {t('class.buttons.retry')}
          </Button>
        </div>
      )}
    </>
  );
};
