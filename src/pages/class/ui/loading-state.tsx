import { Loader2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface LoadingState {
  text: string;
}

export const LoadingState = ({ text }: LoadingState) => {
  const { t } = useTranslation();

  return (
    <div className="w-full h-full flex flex-col items-center justify-center bg-black/70 gap-2 z-99">
      <Loader2 className="animate-spin h-8 w-8 text-white" />
      <span className="text-base text-center text-white">{t(text)}</span>
    </div>
  );
};
