import { useEffect, useMemo } from 'react';
import { useClassData, useSelectedMedia } from '../hooks';
import { useAppSelector, useUpdateReduxState } from '@/shared';
import { Clock4 } from 'lucide-react';
import {
  App<PERSON><PERSON>er,
  WelcomeDialogProvider,
  WelcomeDialog,
  ActionBar,
  ClassParticipantsView,
} from '@/widgets';
import { ToolSidebar } from './tools';
import { useTranslation } from 'react-i18next';
import {
  centralAudioProcessing,
  LivekitProvider,
  LocalMediaProvider,
  useLivekit,
  useLocalMedia,
  resetMessages,
  resetConference,
  useParticipantLogs,
  Captions,
  resetLocalMedia,
  resetLivekitConnection,
  MediaStreamView,
  TranslationProvider,
  useAudioTranslation,
  useAudioContext,
  ConferenceStatus,
} from '@/features';
import { LoadingState } from './loading-state';
import { LivekitView } from './livekit-view';
import { LocalMediaView } from './local-media-view';
import { Helmet } from 'react-helmet';
import { useCookies } from 'react-cookie';
import { useJoinClass } from '@/entities';
import { useParams } from 'react-router';

const ClassPageContent = () => {
  const updateReduxState = useUpdateReduxState();
  const { t } = useTranslation();

  const [cookies] = useCookies(['livekit-token', 'livekit-url']);
  const { id: classroomId, classId } = useParams<{
    id: string;
    classId: string;
  }>();
  const { mutate: joinClass } = useJoinClass();

  const { status } = useAppSelector((state) => state.conference);
  const { classDetails, isPending } = useClassData();
  const { selectedParticipant, selectedVideoStream } = useSelectedMedia();

  useEffect(() => {
    if (!cookies['livekit-token'] || !cookies['livekit-url']) {
      joinClass({ classroomId: classroomId ?? '', classId: classId ?? '' });
    }
  }, [cookies]);

  // Initializes audio context
  useAudioContext();

  // Manages Livekit room setup and functioning
  useLivekit(
    cookies['livekit-url'],
    cookies['livekit-token'],
    classDetails?.participants ?? [],
  );

  // Manages local media stream
  useLocalMedia();

  // Collects participants logs and metrics
  useParticipantLogs({ classDetails, isPending });

  // Provides audio translation
  useAudioTranslation();

  useEffect(() => {
    return () => {
      updateReduxState(resetConference());
      updateReduxState(resetLocalMedia());
      updateReduxState(resetMessages());
      updateReduxState(resetLivekitConnection());

      (async () => await centralAudioProcessing.destroy())();
    };
  }, []);

  return (
    <div
      id="main-class-area"
      className="w-full h-[100dvh] max-h-[100dvh] overflow-hidden fixed flex justify-center items-center"
    >
      <div className="absolute inset-0 z-1 grid grid-rows-[minmax(0,1fr)_auto] grid-cols-1">
        <div className="flex flex-col relative">
          <span className="max-[1200px]:landscape:hidden">
            <AppHeader title={classDetails?.title ?? ''} option="shady" />
          </span>
          <ClassParticipantsView />
          <Captions />
        </div>
        <ActionBar name={classDetails?.title ?? ''} />
      </div>
      <ToolSidebar />
      <div className="absolute inset-0 z-0 bg-black">
        {useMemo(
          () =>
            selectedParticipant && selectedVideoStream ? (
              <MediaStreamView
                participant={selectedParticipant}
                mediaStream={selectedVideoStream}
                selected={true}
                dataTestId="selected-media-stream"
              />
            ) : (
              <div className="text-white flex flex-col items-center justify-center p-12 gap-2 w-full h-full">
                <Clock4 className="w-15 h-auto aspect-square animate-pulse" />
                <span
                  className="text-base text-center animate-pulse"
                  dangerouslySetInnerHTML={{
                    __html: t('class.status.waitingForOthers'),
                  }}
                />
              </div>
            ),
          [selectedParticipant, selectedVideoStream],
        )}
      </div>
      {(status === ConferenceStatus.Dialog ||
        status === ConferenceStatus.Ready) && <WelcomeDialog />}
      {status === ConferenceStatus.AudioContext && (
        <LoadingState text={'class.status.audioProcessing'} />
      )}
      {status === ConferenceStatus.Livekit && <LivekitView />}
      {status === ConferenceStatus.LocalMedia && <LocalMediaView />}
      {status === ConferenceStatus.Fetching && (
        <LoadingState text={'class.status.fetching'} />
      )}
    </div>
  );
};

export const ClassPage = () => {
  return (
    <>
      <Helmet>
        <style type="text/css">{`
        html, body {
          overscroll-behavior: none;
        }
      `}</style>
      </Helmet>

      <LivekitProvider>
        <LocalMediaProvider>
          <WelcomeDialogProvider>
            <TranslationProvider>
              <ClassPageContent />
            </TranslationProvider>
          </WelcomeDialogProvider>
        </LocalMediaProvider>
      </LivekitProvider>
    </>
  );
};
