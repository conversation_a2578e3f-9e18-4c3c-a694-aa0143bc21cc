import {
  ClassMaterialsWrapper as Materials,
  ClassChatWrapper as Chat,
  ClassParticipants as Participants,
  ClassSettings as Settings,
  ClassMetronome as Metronome,
  ClassReportForm as ReportForm,
} from '@/widgets';

export const ToolSidebar = () => {
  const tools = [
    Materials,
    Metronome,
    Chat,
    Participants,
    Settings,
    ReportForm,
  ];

  return (
    <div
      className="absolute max-h-[90%] top-1/2 -translate-y-1/2 backdrop-blur-md optimized-blur z-2 flex flex-col flex-wrap-reverse justify-start items-cetner gap-2 p-2 bg-black/30 rounded-tl-lg rounded-bl-lg max-[1200px]:p-1.5 max-[1200px]:gap-1.5 max-[1000px]:landscape:top-0 max-[1000px]:landscape:-translate-y-0"
      style={{ right: 'env(safe-area-inset-right)' }}
    >
      {tools.map((Tool, index) => (
        <Tool key={index} />
      ))}
    </div>
  );
};
