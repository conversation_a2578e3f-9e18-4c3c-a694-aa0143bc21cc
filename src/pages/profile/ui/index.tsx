import { AppHeader } from '@/widgets';
import { BasicInformation, AdvancedInformation, Actions } from '@/features';
import { useTranslation } from 'react-i18next';

export const ProfilePage = () => {
  const { t } = useTranslation();

  return (
    <>
      <AppHeader title={t('profile.title')} />

      <div className="mx-auto max-w-[1236px] w-full p-6 flex flex-col gap-6">
        <div className="grid grid-cols-[400px_1fr] gap-6 max-[1100px]:grid-cols-1">
          <div className="flex flex-col gap-6">
            <BasicInformation />
            <Actions />
          </div>
          <AdvancedInformation />
        </div>
      </div>
    </>
  );
};
