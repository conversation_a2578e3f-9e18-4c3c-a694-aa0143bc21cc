# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.order

package-lock.json
flat_folder.py
.order

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Lighthouse reports
/lighthouse-report/
/lighthouse-results/
lighthouse-report.*
lighthouse-results.*
*.lighthouse.html
*.lighthouse.json
unlighthouse-report/
.unlighthouse/

.env

tsconfig.node.tsbuildinfo
vite.config.js
