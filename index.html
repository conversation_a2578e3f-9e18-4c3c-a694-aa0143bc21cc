<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, viewport-fit=cover"
    />
    <title>VirtuosoHub - Connect and learn across language barriers</title>
    <meta
      name="description"
      content="VirtuosoHub - Connect and learn across language barriers"
    />

    <meta property="og:title" content="VirtuosoHub" />
    <meta
      property="og:description"
      content="VirtuosoHub - Connect and learn across language barriers"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://test.virtuosohub.ai" />
    <meta property="og:image" content="/og.jpg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="VirtuosoHub" />
    <meta
      name="twitter:description"
      content="VirtuosoHub - Connect and learn across language barriers"
    />
    <meta name="twitter:image" content="https://test.virtuosohub.ai" />
    <link href="/src/styles.css" rel="stylesheet" />

    <!-- Smartlook Analytics -->
    <script type="text/javascript">
      window.smartlook ||
        (function (d) {
          var o = (smartlook = function () {
              o.api.push(arguments);
            }),
            h = d.getElementsByTagName('head')[0];
          var c = d.createElement('script');
          o.api = new Array();
          c.async = true;
          c.type = 'text/javascript';
          c.charset = 'utf-8';
          c.src = 'https://web-sdk.smartlook.com/recorder.js';
          h.appendChild(c);
        })(document);
      smartlook('init', '65e2178d0ae9ec7d8aa3791bccd2b93256b7ac57', {
        region: 'eu',
      });
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
