{"name": "virtuoso-hub-prod", "private": true, "version": "0.0.0", "type": "module", "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --cache --fix"}, "scripts": {"dev": "vite --host", "dev:http": "vite --host --mode test", "build": "tsc -b && vite build", "build:linux": "tsc -b && NODE_OPTIONS='--max-old-space-size=6144' vite build", "build:windows": "tsc -b && set NODE_OPTIONS=--max-old-space-size=6144 && vite build", "build:ci": "tsc -b && NODE_OPTIONS='--max-old-space-size=8192' vite build --mode production", "preview": "vite preview", "start": "serve -s dist -l $PORT", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "npm run lint -- --fix", "prepare": "husky install || echo 'Skipping husky install (not installed)'", "test": "vitest run", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "audit": "start-server-and-test \"npx serve -s dist -l 4173\" http://localhost:4173 \"npx unlighthouse-ci\"", "audit:html": "start-server-and-test \"npx serve -s dist -l 4173\" http://localhost:4173 \"npx unlighthouse --no-open\"", "audit:force-html": "start-server-and-test \"npx serve -s dist -l 4173\" http://localhost:4173 \"npx unlighthouse --build-static --no-open --no-server\""}, "overrides": {"css-select": "4.3.0", "cheerio": "1.0.0-rc.10", "cheerio-select": "1.6.0"}, "dependencies": {"@main/ui-kit": "^0.5.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.8.1", "@tailwindcss/vite": "^4.1.6", "@tanstack/react-query": "^5.75.7", "@tanstack/react-virtual": "^3.13.10", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "flubber": "^0.4.2", "framer-motion": "^12.10.5", "hls.js": "^1.6.10", "html2canvas-pro": "^1.5.11", "husky": "^9.1.7", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "jspdf": "^3.0.1", "livekit-client": "^2.11.4", "lucide-react": "^0.509.0", "moment": "^2.30.1", "next-themes": "^0.4.6", "pdfjs-dist": "5.4.54", "qrcode": "^1.5.4", "react": "^19.1.0", "react-cookie": "^8.0.1", "react-day-picker": "^9.8.1", "react-dom": "^19.1.0", "react-ga4": "^2.1.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.56.3", "react-i18next": "^15.5.1", "react-redux": "^9.2.0", "react-router": "^7.6.0", "serve": "^14.2.4", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.6", "vaul": "^1.1.2", "zod": "^4.1.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@playwright/test": "^1.53.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/flubber": "^0.4.0", "@types/jest": "^29.5.14", "@types/moment": "^2.11.29", "@types/node": "^22.15.17", "@types/node-fetch": "^2.6.12", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-helmet": "^6.1.11", "@types/webpack": "^5.28.5", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.1.0", "mailslurp-client": "^15.24.1", "prettier": "^3.5.3", "puppeteer": "^24.8.2", "rollup": "^3.29.5", "start-server-and-test": "^2.0.0", "tw-animate-css": "^1.2.9", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "unlighthouse": "^0.16.3", "vite": "^6.3.5", "vite-plugin-mkcert": "^1.17.8", "vitest": "^3.1.4", "wait-on": "^8.0.3"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.40.0"}}